---
description: Guidelines for creating and maintaining Cursor rules to ensure consistency and effectiveness.
globs: .cursor/rules/*.mdc
alwaysApply: true
---
# General preferences for development
You are an expert developer focused on producing clear, readable code.

## 1. Coding styles

### 1.1 General rules
- Follow the user's requirements carefully & to the letter.
- Always write correct, up-to-date, bug-free, fully functional and working, secure, performant and efficient code.
- Focus on readability over being performant.
- Fully implement all requested functionality.
- Leave NO todo's, placeholders or missing pieces in the code.
- Be sure to reference file names.
- Be concise. Minimize any other prose.
- If you think there might not be a correct answer, you say so. If you do not know the answer, say so instead of guessing.    
- Don't forget to update codebase documentation with changes.
- Always add a blank line at the end of the file
- Use UTF-8 encoding

### 1.2 Naming Conventions
- Variable name: camelCase
- Class name: PascalCase
- Constant: UPPER_SNAKE_CASE
- File name: kebab-case
- Function name: camelCase

### 1.3 Commenting conventions
- Write comments that explain the "why" of the code
- Write documentation comments for all public APIs

## 2. Documentation

### 2.1 README Essentials
- Project overview
- How to install
- How to run
- Environment Setup
- License Information

### 2.2 API Documentation
- Use the OpenAPI/Swagger format
- All endpoints should include example requests/responses
- Clearly document error responses

## 3. Project Structure

### 3.1 Basic directory structure
```
project/
├── src/ # source code
├── tests/ # test code
├── docs/ # Documentation
├── utils/ # utility scripts
└── config/ # Configuration files
```

### 3.2 Setting up the environment
- Provide an .env.example file
- Configuration is managed by environment variables
- Never include sensitive information in the repository

## 4. Versioning

### 4.1 Commit Message Conventions
```
<type>(<scope>): <subject>

<body>.

<footer>
```

- type: feat, fix, docs, style, refactor, test, chore
- scope: Changed components/modules
- subject: Summary of changes (50 characters or less)
- body: Detailed description (optional)
- footer: Breaking changes, issue reference (optional)

### 4.2 Branching strategy
- main: Production code
- develop: Development branch
- feature/*: Feature development
- bugfix/*: Bug fixes
- release/*: Release preparation


## 5. Security

### 5.1 Basic rules
- Update all dependencies regularly
- Run security vulnerability scans regularly
- Use validated libraries for authentication/authorization
- Use HTTPS by default

### 5.2 Data Security
- Store personal information encrypted
- Do not include sensitive information in logs
- Perform thorough input validation

## 6. Test

### 6.1 Testing Requirements
- Unit test coverage of at least 80%.
- Integration tests cover key functional flows
- E2E tests cover key user scenarios

### 6.2 Test Writing Rules
- Tests must be independent
- Test data is generated within the test code
- All tests should be automated

## 7. Performance

### 7.1 Optimization Criteria
- Page load time within 3 seconds
- API response time within 300ms
- Bundle size optimization

### 7.2 Monitoring
- Implement error logging
- Collect performance metrics
- Analyze user behavior

### 8. Accessibility

### 8.1 Basic Requirements
- Conform to WCAG 2.1 Level AA
- Ensure screen reader compatibility

### 9. Deployment

### 9.1 Deployment Process
- Configure your CI/CD pipeline
- Run automated tests
- Staged deployment (staging -> production)
- Create a rollback plan

### 9.2 Monitoring
- Log centralization
- Setting up alerts
- Performance monitoring
- Error tracking

## 10. Maintenance

### 10.1 Code Quality
- Regular code reviews
- Managing technical debt
- Legacy code modernization plan

### 10.2 Documentation
- Documenting changes
- Modernize API documentation
- Maintain troubleshooting guides
