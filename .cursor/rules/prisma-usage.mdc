---
description:
globs:
alwaysApply: false
---
# Prisma 사용 가이드

이 프로젝트는 데이터베이스 관리를 위해 [Prisma ORM](https://www.prisma.io/)을 사용합니다. Prisma는 타입 안전성을 제공하며, 데이터베이스 스키마 관리, 마이그레이션, 데이터 접근 등을 용이하게 합니다.

## 주요 구성 요소 및 파일

1.  **Prisma 스키마**:
    *   **위치**: [`prisma/schema.prisma`](mdc:prisma/schema.prisma)
    *   **내용**: 데이터베이스 연결 정보(datasource), Prisma Client 생성 설정(generator), 데이터 모델 정의, 관계, 인덱스 등이 포함됩니다.
    *   **데이터베이스**: 현재 PostgreSQL을 사용하고 있습니다.
    *   **Client 출력 경로**: 생성된 Prisma Client는 `src/lib/prisma/generated/client` 디렉토리에 위치합니다.

2.  **Prisma Client**:
    *   **초기화 및 인스턴스**: Prisma Client는 [`src/lib/prisma/index.ts`](mdc:src/lib/prisma/index.ts) 파일에서 초기화되고 `prisma`라는 이름으로 export되어 프로젝트 전역에서 사용됩니다. 개발 환경에서 Hot Module Replacement(HMR) 시 여러 인스턴스가 생성되는 것을 방지하는 패턴이 적용되어 있습니다.
    *   **타입 안전성**: Prisma Client는 스키마를 기반으로 TypeScript 타입을 자동 생성하여, 쿼리 작성 시 타입 추론 및 자동 완성을 지원합니다.

3.  **데이터베이스 마이그레이션**:
    *   데이터베이스 스키마 변경 사항은 Prisma Migrate를 사용하여 관리합니다.
    *   **개발 중 마이그레이션 생성 및 적용**: 터미널에서 다음 명령어를 주로 사용합니다.
        ```bash
        npx prisma migrate dev --name <migration_name>
        ```
        이 명령어는 새로운 SQL 마이그레이션 파일을 생성하고, 개발 데이터베이스에 변경 사항을 적용하며, Prisma Client를 최신 상태로 재생성합니다.
    *   **프로덕션 환경 배포**: 프로덕션 환경에서는 일반적으로 다음 명령어를 사용하여 마이그레이션을 적용합니다.
        ```bash
        npx prisma migrate deploy
        ```

4.  **Prisma Client 생성**:
    *   `schema.prisma` 파일 변경 후 또는 명시적으로 Prisma Client를 다시 생성해야 할 경우 다음 명령어를 사용합니다.
        ```bash
        npx prisma generate
        ```
    *   `prisma migrate dev` 명령어 실행 시 자동으로 `prisma generate`가 호출됩니다.

5.  **데이터 시딩 (Seeding)**:
    *   데이터베이스 초기 데이터나 테스트 데이터를 채우기 위해 시딩을 사용합니다.
    *   Prisma는 `prisma db seed` 명령어를 지원하며, 이는 `package.json`의 `prisma.seed` 필드에 지정된 스크립트를 실행합니다.
    *   이 프로젝트에는 커스텀 시딩 스크립트 [`scripts/seed-users.ts`](mdc:scripts/seed-users.ts)가 있으며, `pnpm run seed:users` (또는 `npm run seed:users`) 명령어로 실행할 수 있습니다. 이 스크립트 내부에서 Prisma Client를 사용하여 데이터를 삽입할 것입니다.

## 일반적인 사용 패턴

*   **Server Actions 및 API 핸들러**:
    *   데이터베이스 CRUD 작업은 주로 [Server Actions](mdc:.cursor/rules/server-actions.mdc) 또는 API 라우트 핸들러 내에서 Prisma Client를 사용하여 수행됩니다.
    *   예시 (Server Action 내에서 사용자 조회):
        ```typescript
        // src/actions/users.ts
        import { prisma } from '@/lib/prisma/index'; // Prisma Client 임포트

        export const getUserById = async (id: string) => {
          return await prisma.user.findUnique({
            where: { id },
          });
        };
        ```
*   **트랜잭션**: 여러 데이터베이스 작업을 원자적으로 처리해야 할 경우, Prisma의 인터랙티브 트랜잭션 (`prisma.$transaction(async (tx) => { ... })`)을 사용합니다.

## 개발 시 참고사항

*   `schema.prisma` 파일을 수정한 후에는 반드시 `npx prisma migrate dev` (개발 환경) 또는 `npx prisma generate`를 실행하여 변경사항을 Prisma Client에 반영해야 합니다.
*   데이터베이스 연결 정보(`DATABASE_URL`)는 `.env` 파일에 환경 변수로 관리됩니다.
*   Prisma Studio (`npx prisma studio`)를 사용하면 웹 브라우저를 통해 데이터베이스의 데이터를 쉽게 확인하고 편집할 수 있습니다.
