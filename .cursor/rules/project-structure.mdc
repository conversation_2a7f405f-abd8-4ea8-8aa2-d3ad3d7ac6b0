---
description: 
globs: 
alwaysApply: false
---
# 프로젝트 구조 가이드

이 문서는 현재 프로젝트의 주요 구조와 사용된 기술 스택에 대한 개요를 제공합니다.

## 주요 기술 스택

*   **프레임워크**: [Next.js](mdc:next.config.mjs)
*   **언어**: [TypeScript](mdc:tsconfig.json)
*   **스타일링**: [Tailwind CSS](mdc:tailwind.config.ts)
*   **린팅**: [ESLint](mdc:eslint.config.mjs) ([.eslintrc.json](mdc:.eslintrc.json) 설정 포함)
*   **포매팅**: [Prettier](mdc:.prettierrc)
*   **패키지 매니저**: [pnpm](mdc:pnpm-lock.yaml) ([package.json](mdc:package.json)으로 의존성 관리)

## 주요 디렉토리 및 파일

*   **소스 코드**: `src/` 디렉토리에 주요 애플리케이션 로직이 위치합니다.
    *   Next.js App Router를 사용하며, 라우팅 및 페이지 관련 파일은 `src/app` 디렉토리에 위치합니다.
    *   공통 React 컴포넌트는 `src/components/` 디렉토리에 위치할 가능성이 높습니다. (UI 라이브러리 컴포넌트는 `src/components/ui` 등 별도 경로일 수 있습니다.)
*   **공용 정적 파일**: `public/` 디렉토리에 이미지, 폰트 등 정적 에셋이 위치합니다.
*   **설정 파일**:
    *   `next.config.mjs`: Next.js 관련 설정
    *   `tsconfig.json`: TypeScript 컴파일러 설정
    *   `tailwind.config.ts`: Tailwind CSS 설정
    *   `eslint.config.mjs` / `.eslintrc.json`: ESLint 설정
    *   `.prettierrc`: Prettier 코드 포매터 설정
    *   `components.json`: UI 컴포넌트 관련 설정 (shadcn/ui 등)
*   **데이터베이스**:
    *   `prisma/`: Prisma ORM 관련 파일들이 위치합니다.
        *   주요 데이터베이스 스키마는 [prisma/schema.prisma](mdc:prisma/schema.prisma) 파일에 정의됩니다.
    *   `supabase/`: Supabase 관련 설정 및 마이그레이션 파일
*   **문서**:
    *   `docs/`: 프로젝트 관련 추가 문서
    *   [README.md](mdc:README.md): 프로젝트 개요 및 시작 가이드
*   **스크립트**: `scripts/` 디렉토리에 빌드, 테스트 등 자동화 스크립트가 위치할 수 있습니다.
*   **Cursor 규칙**: `.cursor/rules/` 디렉토리에 이와 같은 추가적인 규칙 파일들이 저장됩니다.

## 기타 중요 파일

*   `Makefile`: 프로젝트 빌드 및 관리 작업을 위한 Make 명령어 정의
*   `.gitignore`: Git 버전 관리에서 제외할 파일 및 디렉토리 목록
*   `pnpm-lock.yaml`: pnpm 의존성 잠금 파일
