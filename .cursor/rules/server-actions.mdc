---
description:
globs:
alwaysApply: false
---
# Server Actions 가이드

이 프로젝트는 Next.js의 Server Actions를 사용하여 클라이언트와 서버 간의 상호작용을 처리합니다. Server Actions는 API 엔드포인트를 명시적으로 생성할 필요 없이 서버 측 로직을 직접 호출할 수 있게 해줍니다.

## 주요 특징 및 사용 패턴

1.  **`'use server';` 지시어**:
    *   Server Action 함수를 포함하는 파일의 최상단에 `'use server';` 지시어를 선언해야 합니다. 이를 통해 해당 파일 내의 export된 함수들이 클라이언트 컴포넌트에서 직접 호출 가능한 Server Actions로 취급됩니다.

2.  **파일 위치**:
    *   Server Actions는 주로 `src/actions/` 디렉토리 내에 기능별 또는 모듈별 파일로 구성됩니다. (예: [`users.ts`](mdc:src/actions/users.ts), [`posts.ts`](mdc:src/actions/posts.ts))

3.  **입력 유효성 검사 (Zod)**:
    *   Server Action으로 전달되는 입력 데이터의 유효성 검사를 위해 [Zod](mdc:.cursor/rules/zod-usage.mdc)를 사용합니다.
    *   각 액션 파일 상단 또는 관련 스키마 파일에 `const ActionNameSchema = z.object({ ... });` 형태로 Zod 스키마를 정의합니다.
    *   이 스키마는 `actionHandler` 또는 `actionHandlerWithAuth` 헬퍼 함수에 전달되어 자동으로 유효성 검사를 수행합니다.

4.  **헬퍼 함수 (`actionHandler`, `actionHandlerWithAuth`)**:
    *   반복적인 로직(입력 유효성 검사, 표준화된 응답 형식, 에러 처리, 인증)을 처리하기 위해 [`src/lib/actions/action-utils.ts`](mdc:src/lib/actions/action-utils.ts)에 정의된 헬퍼 함수를 사용합니다.
    *   `actionHandler`: Zod 스키마 기반 유효성 검사 및 일관된 성공/실패 응답 객체를 반환합니다.
    *   `actionHandlerWithAuth`: `actionHandler`의 기능에 더해, [NextAuth.js](mdc:src/auth.ts) 세션을 확인하여 인증된 사용자만 액션을 실행하도록 보호합니다.

5.  **인증 및 인가**:
    *   인증이 필요한 액션은 `actionHandlerWithAuth`를 사용합니다.
    *   헬퍼 함수를 사용하지 않고 직접 인증 로직을 구현해야 하는 경우, [`@/auth`](mdc:src/auth.ts)의 `auth()` 함수를 호출하여 세션 정보를 확인하고, 필요한 경우 접근을 제어합니다. (예: `updateUserProfile` 액션 참고)

6.  **데이터베이스 상호작용**:
    *   데이터베이스 작업은 [Prisma ORM](mdc:.cursor/rules/project-structure.mdc) (클라이언트 인스턴스: [`@/lib/prisma`](mdc:src/lib/prisma.ts))을 통해 수행됩니다.

7.  **에러 처리**:
    *   `actionHandler` 및 `actionHandlerWithAuth` 사용 시, 내부적으로 에러를 포착하여 표준화된 에러 응답을 반환합니다.
    *   커스텀 에러 (예: `BadRequestException`, `UnauthorizedException` 등 - [`src/lib/api/types/error.ts`](mdc:src/lib/api/types/error.ts)에 정의됨)를 사용하여 특정 상황에 맞는 에러 정보를 클라이언트에 전달할 수 있습니다.
    *   직접 구현하는 액션에서는 `try...catch`를 사용하여 예외를 적절히 처리해야 합니다.

8.  **캐시 무효화 (Cache Revalidation)**:
    *   데이터를 변경하는 Server Action (CUD 작업)을 실행한 후에는 `next/cache`의 `revalidatePath(path)` 또는 `revalidateTag(tag)` 함수를 호출하여 관련 데이터의 캐시를 무효화해야 합니다. 이를 통해 사용자는 항상 최신 데이터를 볼 수 있습니다.
    *   예: `revalidatePath('/community/posts');`

9.  **반환 값 구조**:
    *   `actionHandler`를 통해 실행된 Server Action은 일반적으로 다음과 같은 구조의 Promise를 반환합니다:
        ```typescript
        Promise<{
          success: boolean;
          data?: TData; // 성공 시 데이터
          error?: TError; // 실패 시 에러 정보 (예: ZodError.flatten())
          message?: string; // 사용자에게 표시될 수 있는 메시지
        }>
        ```

## Server Action 작성 가이드라인

*   Server Action 함수는 비동기 함수(`async function`)여야 합니다.
*   클라이언트 컴포넌트에서는 Server Action 함수를 직접 `import`하여 이벤트 핸들러 등에서 호출할 수 있습니다.
*   보안을 위해 항상 입력 값 유효성 검사를 수행하고, 인증/인가 로직을 철저히 적용합니다.
*   Server Action은 서버 환경에서만 실행되므로, 브라우저 전용 API(DOM 접근 등)를 사용해서는 안 됩니다.
