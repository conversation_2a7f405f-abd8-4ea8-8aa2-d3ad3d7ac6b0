---
description: 
globs: 
alwaysApply: false
---
# shadcn/ui 가이드

이 프로젝트는 UI 컴포넌트를 위해 [shadcn/ui](mdc:https:/ui.shadcn.com)를 사용합니다.
모든 UI는 기본적으로 @shadcn/ui를 우선적으로 사용합니다.

## 주요 정보

*   **설정 파일**: shadcn/ui 관련 설정은 루트 디렉토리의 [components.json](mdc:components.json) 파일에서 관리됩니다. 이 파일은 컴포넌트 라이브러리의 경로, 스타일, 기본 값 등을 정의합니다.
*   **컴포넌트 위치**: UI 컴포넌트들은 `src/components/ui/` 디렉토리에 위치합니다. 새로운 shadcn/ui 컴포넌트를 추가(`npx shadcn-ui@latest add [component_name]`)하면 이 디렉토리 내에 해당 컴포넌트 파일이 생성됩니다.
*   **사용법**: 컴포넌트는 일반적으로 해당 경로에서 직접 가져와서 사용합니다. 예: `import { Button } from "@/components/ui/button";` (프로젝트의 경로 별칭 설정에 따라 `@/` 부분은 다를 수 있습니다.)

## 스타일링

shadcn/ui 컴포넌트는 [Tailwind CSS](mdc:tailwind.config.ts)를 기반으로 스타일링됩니다. 컴포넌트의 시각적 스타일은 Tailwind 유틸리티 클래스를 통해 커스터마이징할 수 있습니다.
