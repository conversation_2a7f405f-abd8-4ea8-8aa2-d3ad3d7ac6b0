{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    },
    "project": "./tsconfig.json"
  },
  "env": {
    "browser": true,
    "node": true,
    "es6": true
  },
  "ignorePatterns": ["build", "dist", "public", "node_modules/"],
  "extends": [
    "airbnb",
    "airbnb-typescript",
    "airbnb/hooks",
    "plugin:@next/next/recommended",
    "next/core-web-vitals",
    "plugin:only-warn/recommended",
    "plugin:@typescript-eslint/recommended", // ts 권장
    "plugin:prettier/recommended", // eslint의 포매팅을 prettier로 사용
    "prettier", // eslint-config-prettier prettier와 중복된 eslint 규칙 제거
    "eslint-config-prettier",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "settings": {
    "import/resolver": {
      "typescript": {}
    },
    "import/parsers": { "@typescript-eslint/parser": [".ts"] }
  },
  "plugins": [
    "import",
    "@typescript-eslint",
    "react",
    "prettier",
    "react-hooks"
  ],
  "rules": {
    "@next/next/no-sync-scripts": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "react/react-in-jsx-scope": "off", // react 17부턴 import 안해도돼서 기능 끔
    "react/prop-types": "off",
    // 경고표시, 파일 확장자를 .ts나 .tsx 모두 허용함
    "react/jsx-filename-extension": ["warn", { "extensions": [".ts", ".tsx"] }],
    "react/function-component-definition": [
      2,
      { "namedComponents": ["arrow-function", "function-declaration"] }
    ],
    "sort-imports": [
      "error",
      {
        "ignoreCase": false,
        "ignoreDeclarationSort": false,
        "ignoreMemberSort": false,
        "memberSyntaxSortOrder": ["none", "all", "multiple", "single"],
        "allowSeparatedGroups": false
      }
    ],
    "import/order": [
      "error",
      {
        "groups": [
          ["builtin", "external"],
          "internal",
          ["parent", "sibling"],
          "index",
          "object"
        ],
        "pathGroups": [
          {
            "pattern": "~/**",
            "group": "external",
            "position": "before"
          },
          {
            "pattern": "@*",
            "group": "external",
            "position": "after"
          },
          {
            "pattern": "@*/**",
            "group": "external",
            "position": "after"
          }
        ],
        "pathGroupsExcludedImportTypes": ["react"],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ]
  }
}
