name: Deployment Notifications

on:
  workflow_run:
    workflows: ["Deploy to Vercel"]
    types:
      - completed

jobs:
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get deployment status
        id: deployment
        run: |
          if [ "${{ github.event.workflow_run.conclusion }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "emoji=:white_check_mark:" >> $GITHUB_OUTPUT
            echo "message=배포가 성공적으로 완료되었습니다." >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "emoji=:x:" >> $GITHUB_OUTPUT
            echo "message=배포 중 오류가 발생했습니다." >> $GITHUB_OUTPUT
          fi

      # Slack 알림
      - name: Send Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.deployment.outputs.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            ${{ steps.deployment.outputs.emoji }} *${{ github.repository }}* 배포 알림
            ${{ steps.deployment.outputs.message }}
            
            • *브랜치:* ${{ github.ref_name }}
            • *커밋:* ${{ github.sha }}
            • *작성자:* ${{ github.actor }}
            • *워크플로우:* ${{ github.workflow }}
            • *배포 URL:* https://sodamm.vercel.app
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: ${{ secrets.SLACK_WEBHOOK_URL != '' }}

      # Discord 알림
      - name: Send Discord notification
        uses: Ilshidur/action-discord@master
        with:
          args: |
            ${{ steps.deployment.outputs.emoji }} **${{ github.repository }}** 배포 알림
            ${{ steps.deployment.outputs.message }}
            
            • **브랜치:** ${{ github.ref_name }}
            • **커밋:** ${{ github.sha }}
            • **작성자:** ${{ github.actor }}
            • **워크플로우:** ${{ github.workflow }}
            • **배포 URL:** https://sodamm.vercel.app
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK_URL }}
        if: ${{ secrets.DISCORD_WEBHOOK_URL != '' }}

      # 이메일 알림 (선택 사항)
      - name: Send email notification
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.MAIL_SERVER }}
          server_port: ${{ secrets.MAIL_PORT }}
          username: ${{ secrets.MAIL_USERNAME }}
          password: ${{ secrets.MAIL_PASSWORD }}
          subject: ${{ steps.deployment.outputs.emoji }} ${{ github.repository }} 배포 알림
          body: |
            ${{ steps.deployment.outputs.message }}
            
            • 브랜치: ${{ github.ref_name }}
            • 커밋: ${{ github.sha }}
            • 작성자: ${{ github.actor }}
            • 워크플로우: ${{ github.workflow }}
            • 배포 URL: https://sodamm.vercel.app
          to: ${{ secrets.MAIL_RECIPIENTS }}
          from: ${{ secrets.MAIL_SENDER }}
        if: ${{ secrets.MAIL_SERVER != '' }}
