{"semi": true, "trailingComma": "es5", "singleQuote": true, "singleAttributePerLine": true, "tabWidth": 2, "useTabs": false, "printWidth": 80, "quoteProps": "consistent", "arrowParens": "always", "endOfLine": "auto", "bracketSpacing": true, "jsxSingleQuote": false, "bracketSameLine": false, "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "plugins": ["prettier-plugin-organize-imports", "prettier-plugin-tailwindcss"]}