---
description:
globs:
alwaysApply: false
---
# AI 엔진 최적화 (AIEO) 및 코드베이스 AI 지원 가이드

이 문서는 두 가지 측면에서의 AI 최적화를 다룹니다:
1.  **Answer Engine Optimization (AEO)**: 검색 엔진 및 답변 엔진이 콘텐츠의 의미를 더 잘 이해하고 사용자 질문에 대한 직접적인 답변으로 활용할 수 있도록 최적화합니다.
2.  **코드베이스 AI 지원**: 이 AI 어시스턴트와 같은 도구가 코드베이스를 더 효과적으로 이해하고 개발을 지원할 수 있도록 합니다.

## 1. Answer Engine Optimization (AEO) 전략

AEO는 사용자의 질문에 직접적으로 답변하는 것을 목표로 하며, 이는 검색 결과에서 추천 스니펫(Featured Snippet) 등으로 나타날 수 있습니다. 프로젝트의 AEO 전략은 [`docs/seo-aeo-optimization.md`](mdc:docs/seo-aeo-optimization.md) 문서에 자세히 기술되어 있을 수 있으며, 주요 내용은 다음과 같습니다:

*   **구조화된 데이터 (Structured Data)**:
    *   **Schema.org 마크업**: 게시글, 이벤트, 제품 등 다양한 콘텐츠 유형에 대해 Schema.org 어휘를 사용한 JSON-LD 형식의 구조화된 데이터를 제공합니다. 이는 검색 엔진이 콘텐츠의 의미와 맥락을 명확하게 파악하는 데 도움을 줍니다.
    *   관련 컴포넌트: [`src/components/json-ld.tsx`](mdc:src/components/json-ld.tsx)
*   **추천 스니펫 최적화 (Featured Snippet Optimization)**:
    *   콘텐츠를 명확한 질문과 답변(Q&A) 형식으로 구조화합니다.
    *   FAQ 스키마 등을 활용하여 자주 묻는 질문과 답변을 제공합니다.
    *   설명적인 머리글(header)과 목록(list)을 사용합니다.
    *   음성 검색과 같은 자연어 질문에 대응할 수 있도록 콘텐츠를 작성합니다.

## 2. 코드베이스 AI 지원 최적화

AI 개발 어시스턴트가 코드베이스를 효과적으로 이해하고 지원하기 위해서는 다음과 같은 사항을 고려합니다:

*   **Roo Code Rules 활용**:
    *   이 `.roo/rules/` 디렉토리에 있는 규칙 파일들은 AI 어시스턴트에게 프로젝트의 구조, 주요 기술, 코딩 패턴 등에 대한 핵심 정보를 제공합니다.
    *   새로운 기술 스택 도입, 주요 아키텍처 변경, 또는 중요한 코딩 컨벤션이 있을 경우 관련 규칙을 생성하거나 업데이트하여 AI의 이해도를 높입니다.
    *   예시 규칙:
        *   [`project-structure.md`](mdc:.roo/rules/project-structure.md)
        *   [`seo-optimization.md`](mdc:.roo/rules/seo-optimization.md)
        *   [`shadcn-ui.md`](mdc:.roo/rules/shadcn-ui.md)
        *   [`zod-usage.md`](mdc:.roo/rules/zod-usage.md)
*   **명확하고 일관성 있는 코드 작성**:
    *   변수명, 함수명, 클래스명 등을 명확하고 예측 가능하게 작성합니다.
    *   모듈화된 설계를 지향하여 코드의 각 부분이 특정 기능을 수행하도록 합니다.
    *   일관된 코딩 스타일과 포맷을 유지합니다 ([`.prettierrc`](mdc:.prettierrc), [`eslint.config.mjs`](mdc:eslint.config.mjs) 설정 준수).
*   **주석 (Comments)**:
    *   복잡한 로직, 비즈니스 규칙, 또는 특정 결정에 대한 이유 등 코드만으로는 파악하기 어려운 부분에 명확하고 간결한 주석을 작성합니다.
    *   JSDoc, TSDoc과 같은 형식을 활용하여 함수, 클래스, 타입 등에 대한 설명을 제공하면 AI의 이해도를 높일 수 있습니다.
*   **타입 정의 (Type Definitions)**:
    *   TypeScript를 사용하는 경우, 명확하고 정확한 타입 정의([`src/types/`](mdc:src/types) 등)는 코드의 의도를 명확히 하고 AI가 코드를 분석하거나 생성할 때 오류를 줄이는 데 크게 기여합니다.
*   **문서화**:
    *   API 엔드포인트, 주요 아키텍처, 설정 방법 등은 `README.md` 또는 `docs/` 디렉토리 내의 관련 문서에 잘 기록해둡니다.

이러한 AEO 및 코드베이스 AI 지원 전략을 통해 검색 엔진에서의 가시성을 높이고, AI 어시스턴트와의 협업 효율성을 극대화할 수 있습니다.
