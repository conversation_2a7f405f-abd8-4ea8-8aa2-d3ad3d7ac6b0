---
description:
globs:
alwaysApply: false
---
# 검색 엔진 최적화 (SEO) 가이드

이 프로젝트는 검색 엔진 가시성을 높이기 위해 다양한 SEO 전략을 사용합니다.

## 주요 SEO 전략 및 파일

*   **메타데이터 관리**:
    *   **기본 메타데이터**: 루트 레이아웃 파일인 [`src/app/layout.tsx`](mdc:src/app/layout.tsx)에서 Next.js의 `Metadata` 객체를 사용하여 사이트 전체의 기본 제목, 설명, Open Graph 태그 등을 설정합니다.
    *   **동적 메타데이터**: 개별 페이지(예: 게시글 상세 페이지 [`src/app/community/post/[id]/page.tsx`](mdc:src/app/community/post/[id]/page.tsx))에서는 `generateMetadata` 함수를 사용하여 콘텐츠에 맞는 동적 메타 태그(제목, 설명, Open Graph 등)를 생성합니다.
*   **사이트맵 (Sitemap)**:
    *   동적 사이트맵이 [`src/app/sitemap.ts`](mdc:src/app/sitemap.ts) 파일을 통해 생성됩니다. 이 파일은 정적 경로와 동적 경로(예: 게시글)를 포함하여 검색 엔진이 사이트 구조를 효과적으로 크롤링할 수 있도록 돕습니다.
*   **robots.txt**:
    *   `robots.txt` 파일은 검색 엔진 크롤러에게 어떤 페이지를 크롤링하고 인덱싱할 수 있는지에 대한 지침을 제공합니다. (현재 프로젝트에서는 `public/robots.txt` 또는 `src/app/robots.ts` 파일을 확인하여 구체적인 설정을 파악해야 합니다. `docs/seo-aeo-optimization.md` 문서에 관련 계획이 있을 수 있습니다.)
*   **구조화된 데이터 (Structured Data)**:
    *   JSON-LD 형식을 사용하여 구조화된 데이터를 제공할 수 있습니다. [`src/components/json-ld.tsx`](mdc:src/components/json-ld.tsx) 컴포넌트는 이를 지원하기 위해 사용될 수 있습니다. 구조화된 데이터는 검색 결과에서 리치 스니펫으로 표시될 가능성을 높입니다.
*   **콘텐츠 SEO**:
    *   의미론적 HTML 마크업 (시맨틱 태그 사용), 적절한 제목 계층 구조(H1, H2 등), 이미지 최적화 (`next/image` 사용 권장) 등을 통해 콘텐츠의 SEO 가치를 높입니다.
*   **성능 최적화**:
    *   Next.js의 기능을 활용하여 페이지 로드 속도, Core Web Vitals (LCP, FID, CLS) 등을 최적화합니다. 이는 사용자 경험뿐만 아니라 SEO 순위에도 중요한 요소입니다. 관련 설정은 [`next.config.mjs`](mdc:next.config.mjs)에서 찾을 수 있습니다.
*   **국제화 (i18n)**:
    *   `next-intl`을 사용한 다국어 지원 시, `hreflang` 태그 등을 적절히 사용하여 각 언어별 콘텐츠가 올바르게 인덱싱되도록 합니다. (관련 설정은 `next-intl` 설정 파일 및 `src/i18n/` 디렉토리 참고)

## 추가 정보 및 문서

*   프로젝트의 SEO 및 AEO (Answer Engine Optimization)에 대한 상세 계획은 [`docs/seo-aeo-optimization.md`](mdc:docs/seo-aeo-optimization.md) 문서에 기술되어 있을 수 있습니다.
*   일반적인 Next.js SEO 가이드라인은 [`docs/README-seo.md`](mdc:docs/README-seo.md) 문서에서 찾아볼 수 있습니다.

SEO 관련 작업을 할 때는 위의 요소들을 고려하고, 필요에 따라 관련 문서를 참고하여 일관성 있는 최적화를 진행합니다.
