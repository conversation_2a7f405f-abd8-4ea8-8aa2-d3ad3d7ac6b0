---
description:
globs:
alwaysApply: false
---
# Zod 사용 가이드

이 프로젝트는 데이터 유효성 검사를 위해 [Zod](https://zod.dev/)를 적극적으로 활용합니다.

## 주요 사용처 및 패턴

1.  **Server Actions 입력 유효성 검사**:
    *   **위치**: `src/actions/` 디렉토리 내의 각 액션 파일 (예: `[users.ts](mdc:src/actions/users.ts)`, `[posts.ts](mdc:src/actions/posts.ts)`) 상단에 Zod 스키마가 정의됩니다.
    *   **패턴**: `const ActionNameSchema = z.object({ ... });` 형태로 스키마를 정의하고, 이를 `actionHandler` 또는 `actionHandlerWithAuth` 헬퍼 함수 ([`action-utils.ts`](mdc:src/lib/actions/action-utils.ts) 참고)와 함께 사용하여 액션 핸들러로 전달되는 입력 데이터의 유효성을 검사합니다.
    *   예시:
        ```typescript
        // src/actions/someActions.ts
        import { z } from 'zod';
        import { actionHandler } from '@/lib/actions/action-utils';

        const SomeActionSchema = z.object({
          id: z.string().uuid(),
          name: z.string().min(1),
        });

        export const someAction = actionHandler(async (input) => {
          // input is validated against SomeActionSchema
          // ... action logic ...
        }, SomeActionSchema);
        ```

2.  **API 라우트 핸들러 입력 유효성 검사**:
    *   **위치**: API 라우트 핸들러 (주로 `src/app/api/` 경로 또는 `src/lib/api/server/`의 헬퍼 함수)에서 요청 본문(body) 등의 유효성을 검사하는 데 사용될 수 있습니다.
    *   **패턴**: `apiHandlerWithAuth` ([`api-handler-with-auth.ts`](mdc:src/lib/api/server/api-handler-with-auth.ts) 참고)와 같은 헬퍼 함수에서 Zod 스키마를 선택적으로 받아 처리합니다.

## 스키마 정의 가이드라인

*   새로운 데이터 유효성 검사가 필요한 경우, 관련된 Server Action 파일이나 API 핸들러 로직 근처에 Zod 스키마를 명확하게 정의합니다.
*   재사용 가능한 복잡한 스키마는 `src/lib/validations/` 또는 `src/types/schemas/`와 같은 공통 디렉토리로 분리하는 것을 고려할 수 있습니다 (현재 프로젝트에서는 주로 각 액션 파일 내에 정의되어 있음).
*   Zod의 `infer`를 사용하여 스키마로부터 TypeScript 타입을 추론하여 코드 안정성을 높입니다.
    ```typescript
    const MySchema = z.object({ name: z.string() });
    type MyType = z.infer<typeof MySchema>; // type MyType = { name: string }
    ```
