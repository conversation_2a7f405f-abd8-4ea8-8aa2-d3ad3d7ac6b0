# 백업 및 스케일링 전략 가이드

이 문서는 Sodamm 프로젝트의 백업 및 스케일링 전략을 설명합니다.

## 목차

1. [데이터베이스 백업 전략](#데이터베이스-백업-전략)
2. [콘텐츠 백업 전략](#콘텐츠-백업-전략)
3. [스케일링 전략](#스케일링-전략)
4. [부하 테스트](#부하-테스트)
5. [리소스 모니터링](#리소스-모니터링)

## 데이터베이스 백업 전략

### 자동 백업 설정

#### Supabase 자동 백업

Supabase는 다음과 같은 백업 옵션을 제공합니다:

1. **일일 백업**: 모든 Supabase 프로젝트는 기본적으로 일일 백업을 제공합니다.
2. **시점 복구(PITR)**: Pro 플랜 이상에서는 시점 복구 기능을 사용할 수 있습니다.

Supabase 프로젝트 설정에서 백업 설정을 구성하세요:

1. Supabase 대시보드에서 프로젝트를 선택합니다.
2. "Settings" > "Database"로 이동합니다.
3. "Backups" 섹션에서 백업 설정을 구성합니다.

#### 수동 백업 스크립트

정기적인 수동 백업을 위한 스크립트를 설정하려면 다음 단계를 따르세요:

1. 프로젝트 루트에 `scripts/backup` 디렉토리를 생성합니다.
2. 다음 내용으로 `backup-database.sh` 스크립트를 생성합니다:

```bash
#!/bin/bash

# 환경 변수 로드
source .env

# 백업 파일 이름 설정
BACKUP_DATE=$(date +%Y-%m-%d-%H-%M-%S)
BACKUP_FILE="database-backup-$BACKUP_DATE.sql"

# 백업 디렉토리 생성
BACKUP_DIR="./backups/database"
mkdir -p $BACKUP_DIR

# PostgreSQL 데이터베이스 덤프
pg_dump $DATABASE_URL > "$BACKUP_DIR/$BACKUP_FILE"

# 백업 파일 압축
gzip "$BACKUP_DIR/$BACKUP_FILE"

# 백업 파일을 S3에 업로드 (선택 사항)
if [ -n "$AWS_S3_BUCKET" ]; then
  aws s3 cp "$BACKUP_DIR/$BACKUP_FILE.gz" "s3://$AWS_S3_BUCKET/database-backups/$BACKUP_FILE.gz"
fi

# 오래된 백업 파일 정리 (30일 이상 된 파일)
find $BACKUP_DIR -name "*.gz" -type f -mtime +30 -delete

echo "Database backup completed: $BACKUP_FILE.gz"
```

3. 스크립트에 실행 권한을 부여합니다:

```bash
chmod +x scripts/backup/backup-database.sh
```

4. cron 작업으로 스크립트를 예약합니다:

```bash
# 매일 오전 3시에 백업 실행
0 3 * * * /path/to/project/scripts/backup/backup-database.sh >> /path/to/project/logs/backup.log 2>&1
```

### 백업 저장소 구성

백업 파일을 안전하게 저장하기 위해 다음 옵션을 고려하세요:

1. **AWS S3**: 내구성이 높고 확장 가능한 스토리지
2. **Google Cloud Storage**: 글로벌 분산 스토리지
3. **Azure Blob Storage**: Microsoft의 클라우드 스토리지 솔루션

#### AWS S3 설정

1. AWS 계정을 생성하고 S3 버킷을 생성합니다.
2. IAM 사용자를 생성하고 S3 버킷에 대한 액세스 권한을 부여합니다.
3. 다음 환경 변수를 `.env` 파일에 추가합니다:

```
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=your-bucket-name
AWS_REGION=your-region
```

## 콘텐츠 백업 전략

### 정적 자산 백업

정적 자산(이미지, 비디오, 문서 등)은 다음과 같은 방법으로 백업할 수 있습니다:

1. **Supabase Storage 백업**: Supabase Storage에 저장된 파일을 정기적으로 백업합니다.
2. **S3 버킷 복제**: AWS S3를 사용하는 경우 버킷 복제를 설정하여 다른 리전에 자동으로 복제합니다.

#### Supabase Storage 백업 스크립트

```bash
#!/bin/bash

# 환경 변수 로드
source .env

# 백업 파일 이름 설정
BACKUP_DATE=$(date +%Y-%m-%d-%H-%M-%S)
BACKUP_DIR="./backups/storage"
mkdir -p $BACKUP_DIR

# Supabase Storage에서 파일 다운로드
supabase storage download --project-ref $SUPABASE_PROJECT_ID --bucket $SUPABASE_BUCKET --output-dir "$BACKUP_DIR/$BACKUP_DATE"

# 백업 파일 압축
tar -czf "$BACKUP_DIR/storage-backup-$BACKUP_DATE.tar.gz" -C "$BACKUP_DIR" "$BACKUP_DATE"

# 압축 후 원본 파일 삭제
rm -rf "$BACKUP_DIR/$BACKUP_DATE"

# 백업 파일을 S3에 업로드 (선택 사항)
if [ -n "$AWS_S3_BUCKET" ]; then
  aws s3 cp "$BACKUP_DIR/storage-backup-$BACKUP_DATE.tar.gz" "s3://$AWS_S3_BUCKET/storage-backups/storage-backup-$BACKUP_DATE.tar.gz"
fi

# 오래된 백업 파일 정리 (30일 이상 된 파일)
find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +30 -delete

echo "Storage backup completed: storage-backup-$BACKUP_DATE.tar.gz"
```

### 사용자 업로드 파일 백업

사용자가 업로드한 파일은 다음과 같은 방법으로 백업할 수 있습니다:

1. **중복 스토리지**: 업로드된 파일을 여러 스토리지 서비스에 동시에 저장합니다.
2. **정기적인 동기화**: 주 스토리지와 백업 스토리지 간에 정기적으로 동기화합니다.

## 스케일링 전략

### Vercel 서버리스 함수 최적화

Vercel 서버리스 함수의 성능을 최적화하려면 다음 전략을 고려하세요:

1. **콜드 스타트 최소화**: 함수를 정기적으로 호출하여 웜 상태를 유지합니다.
2. **함수 크기 최소화**: 필요한 종속성만 포함하여 함수 크기를 줄입니다.
3. **메모리 할당 최적화**: 함수에 적절한 메모리를 할당합니다.
4. **데이터베이스 연결 풀링**: 데이터베이스 연결을 효율적으로 관리합니다.

### Cloudflare 캐싱 규칙 설정

Cloudflare 캐싱을 최적화하려면 다음 설정을 구성하세요:

1. **브라우저 캐시 TTL**: 정적 자산의 브라우저 캐시 TTL을 설정합니다.
2. **Edge 캐싱**: Cloudflare 엣지에서 콘텐츠를 캐싱합니다.
3. **캐시 규칙**: 파일 유형별로 캐시 규칙을 설정합니다.

#### Cache-Control 헤더 최적화

Next.js 애플리케이션에서 Cache-Control 헤더를 최적화하려면 다음 설정을 구성하세요:

```typescript
// next.config.ts
import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  async headers() {
    return [
      {
        // 정적 자산에 대한 캐시 헤더 설정
        source: '/(images|fonts|icons)/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        // CSS 및 JS 파일에 대한 캐시 헤더 설정
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        // API 경로에 대한 캐시 헤더 설정
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ];
  },
};

// Sentry 웹팩 플러그인 설정
const sentryWebpackPluginOptions = {
  // 추가 옵션은 여기에 설정
  silent: true, // 빌드 중 Sentry 출력 숨기기
};

// Sentry 설정으로 Next.js 설정 감싸기
export default withSentryConfig(nextConfig, sentryWebpackPluginOptions);
```

### 이미지 최적화 설정

Next.js의 이미지 최적화 기능을 활용하려면 다음 설정을 구성하세요:

```typescript
// next.config.ts
import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    domains: ['your-domain.com', 'your-storage-domain.com'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};

// Sentry 웹팩 플러그인 설정
const sentryWebpackPluginOptions = {
  // 추가 옵션은 여기에 설정
  silent: true, // 빌드 중 Sentry 출력 숨기기
};

// Sentry 설정으로 Next.js 설정 감싸기
export default withSentryConfig(nextConfig, sentryWebpackPluginOptions);
```

## 부하 테스트

### 부하 테스트 도구

다음 도구를 사용하여 애플리케이션의 부하 테스트를 수행할 수 있습니다:

1. **k6**: 현대적이고 개발자 친화적인 부하 테스트 도구
2. **Apache JMeter**: 강력하고 기능이 풍부한 부하 테스트 도구
3. **Artillery**: Node.js 기반 부하 테스트 도구

#### k6 부하 테스트 스크립트 예시

```javascript
// scripts/load-test.js
import http from 'k6/http';
import { sleep } from 'k6';

export const options = {
  vus: 10, // 가상 사용자 수
  duration: '30s', // 테스트 지속 시간
};

export default function () {
  // 홈페이지 로드
  http.get('https://sodamm.com/');
  sleep(1);
  
  // API 엔드포인트 테스트
  http.get('https://sodamm.com/api/posts');
  sleep(1);
  
  // 인증이 필요한 엔드포인트 테스트
  const payload = JSON.stringify({
    email: '<EMAIL>',
    password: 'password',
  });
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  http.post('https://sodamm.com/api/auth/login', payload, params);
  sleep(1);
}
```

### 병목 현상 식별

부하 테스트 결과를 분석하여 다음과 같은 병목 현상을 식별할 수 있습니다:

1. **서버 응답 시간**: 서버가 응답하는 데 걸리는 시간
2. **데이터베이스 쿼리 성능**: 데이터베이스 쿼리 실행 시간
3. **메모리 사용량**: 서버의 메모리 사용량
4. **CPU 사용량**: 서버의 CPU 사용량

## 리소스 모니터링

### 모니터링 도구

다음 도구를 사용하여 애플리케이션의 리소스 사용량을 모니터링할 수 있습니다:

1. **Vercel Analytics**: Vercel 대시보드에서 제공하는 기본 분석 도구
2. **Sentry Performance**: Sentry에서 제공하는 성능 모니터링 도구
3. **Datadog**: 종합적인 모니터링 및 분석 플랫폼
4. **New Relic**: 애플리케이션 성능 모니터링 도구

### 알림 설정

리소스 사용량이 임계값을 초과할 때 알림을 받으려면 다음 설정을 구성하세요:

1. **Vercel 알림**: Vercel 대시보드에서 알림을 설정합니다.
2. **Sentry 알림**: Sentry 대시보드에서 성능 알림을 설정합니다.
3. **Datadog 알림**: Datadog 대시보드에서 알림을 설정합니다.

---

이 가이드를 따라 Sodamm 프로젝트의 백업 및 스케일링 전략을 구현할 수 있습니다. 문제가 발생하면 각 서비스의 공식 문서를 참조하세요.
