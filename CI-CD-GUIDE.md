# CI/CD 파이프라인 가이드

이 문서는 Sodamm 프로젝트의 CI/CD 파이프라인 설정 및 사용 방법을 설명합니다.

## 목차

1. [CI/CD 파이프라인 개요](#cicd-파이프라인-개요)
2. [GitHub Actions 워크플로우](#github-actions-워크플로우)
3. [브랜치 전략](#브랜치-전략)
4. [환경별 설정](#환경별-설정)
5. [배포 알림 설정](#배포-알림-설정)

## CI/CD 파이프라인 개요

Sodamm 프로젝트는 다음과 같은 CI/CD 파이프라인을 사용합니다:

1. **코드 품질 검사**: ESLint, TypeScript 타입 체크
2. **테스트**: 단위 테스트, 통합 테스트
3. **빌드**: Next.js 애플리케이션 빌드
4. **프리뷰 배포**: PR에 대한 프리뷰 환경 배포
5. **프로덕션 배포**: main 브랜치에 대한 프로덕션 환경 배포

## GitHub Actions 워크플로우

### 1. CI 워크플로우 (`ci.yml`)

- **트리거**: 모든 브랜치에 대한 푸시, main 및 development 브랜치에 대한 PR
- **작업**:
  - 코드 린팅 (ESLint)
  - 타입 체크 (TypeScript)
  - 빌드 테스트

### 2. 프리뷰 배포 워크플로우 (`preview.yml`)

- **트리거**: main 및 development 브랜치에 대한 PR
- **작업**:
  - Vercel 프리뷰 환경에 배포
  - PR에 배포 URL 댓글 추가

### 3. 프로덕션 배포 워크플로우 (`deploy.yml`)

- **트리거**: main 브랜치에 대한 푸시
- **작업**:
  - Vercel 프로덕션 환경에 배포

## 브랜치 전략

Sodamm 프로젝트는 다음과 같은 브랜치 전략을 사용합니다:

1. **main**: 프로덕션 환경에 배포되는 안정적인 코드
2. **development**: 개발 환경에 배포되는 개발 중인 코드
3. **feature/\***: 새로운 기능 개발을 위한 브랜치
4. **bugfix/\***: 버그 수정을 위한 브랜치
5. **hotfix/\***: 긴급 버그 수정을 위한 브랜치

### 워크플로우

1. 개발자는 `feature/*`, `bugfix/*`, `hotfix/*` 브랜치에서 작업합니다.
2. 작업이 완료되면 `development` 브랜치로 PR을 생성합니다.
3. PR이 승인되면 `development` 브랜치로 병합됩니다.
4. `development` 브랜치의 변경 사항이 테스트되고 안정적이라고 판단되면 `main` 브랜치로 PR을 생성합니다.
5. PR이 승인되면 `main` 브랜치로 병합되고 프로덕션 환경에 자동으로 배포됩니다.

## 환경별 설정

### 1. 개발 환경 (Development)

- **브랜치**: development
- **URL**: dev.sodamm.com 또는 sodamm-git-development-username.vercel.app
- **환경 변수**:
  - `NODE_ENV=development`
  - 개발용 데이터베이스 및 API 키

### 2. 스테이징 환경 (Staging)

- **브랜치**: staging (또는 PR에서 자동 생성)
- **URL**: staging.sodamm.com 또는 sodamm-git-staging-username.vercel.app
- **환경 변수**:
  - `NODE_ENV=production`
  - 스테이징용 데이터베이스 및 API 키

### 3. 프로덕션 환경 (Production)

- **브랜치**: main
- **URL**: sodamm.com 또는 sodamm.vercel.app
- **환경 변수**:
  - `NODE_ENV=production`
  - 프로덕션용 데이터베이스 및 API 키

## Vercel 환경 변수 설정

Vercel에서 환경별로 다른 환경 변수를 설정하려면 다음 단계를 따르세요:

1. Vercel 대시보드에서 프로젝트를 선택합니다.
2. "Settings" > "Environment Variables"로 이동합니다.
3. 환경 변수를 추가할 때 "Environment"를 선택합니다:
   - Production: 프로덕션 환경에만 적용
   - Preview: 프리뷰 환경에만 적용
   - Development: 개발 환경에만 적용
   - All: 모든 환경에 적용

## GitHub Secrets 설정

GitHub Actions 워크플로우에서 사용할 시크릿을 설정하려면 다음 단계를 따르세요:

1. GitHub 저장소의 "Settings" > "Secrets and variables" > "Actions"로 이동합니다.
2. "New repository secret" 버튼을 클릭합니다.
3. 다음 시크릿을 추가합니다:
   - `VERCEL_TOKEN`: Vercel API 토큰
   - `VERCEL_ORG_ID`: Vercel 조직 ID
   - `VERCEL_PROJECT_ID`: Vercel 프로젝트 ID

## 배포 알림 설정

### Slack 알림

1. Slack 워크스페이스에 새 앱을 생성합니다.
2. 수신 웹훅을 설정하고 웹훅 URL을 복사합니다.
3. GitHub 저장소의 "Settings" > "Secrets and variables" > "Actions"로 이동합니다.
4. `SLACK_WEBHOOK_URL` 시크릿을 추가합니다.
5. 워크플로우 파일에 다음 단계를 추가합니다:

```yaml
- name: Send Slack notification
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    fields: repo,message,commit,author,action,eventName,ref,workflow
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
  if: always()
```

### Discord 알림

1. Discord 서버에 웹훅을 생성하고 웹훅 URL을 복사합니다.
2. GitHub 저장소의 "Settings" > "Secrets and variables" > "Actions"로 이동합니다.
3. `DISCORD_WEBHOOK_URL` 시크릿을 추가합니다.
4. 워크플로우 파일에 다음 단계를 추가합니다:

```yaml
- name: Send Discord notification
  uses: Ilshidur/action-discord@master
  with:
    args: '{{ EVENT_PAYLOAD.repository.full_name }} - {{ EVENT_PAYLOAD.workflow }} completed with status: ${{ job.status }}'
  env:
    DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK_URL }}
  if: always()
```

---

이 가이드를 따라 Sodamm 프로젝트의 CI/CD 파이프라인을 설정하고 사용할 수 있습니다. 문제가 발생하면 GitHub Actions 및 Vercel 공식 문서를 참조하세요.
