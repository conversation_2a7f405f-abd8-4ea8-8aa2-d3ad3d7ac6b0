# 배포 가이드

이 문서는 Sodamm 프로젝트를 Vercel과 Cloudflare를 사용하여 배포하는 방법을 설명합니다.

## 목차

1. [Vercel 배포](#vercel-배포)
2. [Cloudflare 설정](#cloudflare-설정)
3. [환경 변수 설정](#환경-변수-설정)
4. [GitHub Actions CI/CD](#github-actions-cicd)
5. [커스텀 도메인 설정](#커스텀-도메인-설정)

## Vercel 배포

### 1. Vercel 계정 생성 및 로그인

1. [Vercel 웹사이트](https://vercel.com)에 접속합니다.
2. GitHub, GitLab, Bitbucket 계정으로 로그인하거나 새 계정을 생성합니다.

### 2. 프로젝트 가져오기

1. Vercel 대시보드에서 "Add New..." > "Project" 버튼을 클릭합니다.
2. GitHub 계정을 연결하고 Sodamm 저장소를 선택합니다.
3. "Import" 버튼을 클릭합니다.

### 3. 프로젝트 설정

1. **Framework Preset**: "Next.js"를 선택합니다.
2. **Root Directory**: 기본값(/)을 사용합니다.
3. **Build Command**: `pnpm run build`를 입력합니다.
4. **Output Directory**: `.next`를 입력합니다.
5. **Install Command**: `pnpm install`을 입력합니다.

### 4. 환경 변수 설정

1. "Environment Variables" 섹션에서 필요한 환경 변수를 추가합니다:
   - `DATABASE_URL`: PostgreSQL 데이터베이스 연결 URL
   - `AUTH_SECRET`: Auth.js 보안 키 (openssl rand -base64 32 명령어로 생성 가능)
   - `AUTH_GOOGLE_ID`: Google OAuth 클라이언트 ID
   - `AUTH_GOOGLE_SECRET`: Google OAuth 클라이언트 시크릿
   - `NODE_ENV`: "production"

### 5. 배포

1. 모든 설정을 확인한 후 "Deploy" 버튼을 클릭합니다.
2. 배포가 완료되면 제공된 URL로 접속하여 애플리케이션이 정상적으로 작동하는지 확인합니다.

## Cloudflare 설정

### 1. Cloudflare 계정 생성 및 로그인

1. [Cloudflare 웹사이트](https://cloudflare.com)에 접속합니다.
2. 계정을 생성하거나 기존 계정으로 로그인합니다.

### 2. 도메인 추가

1. Cloudflare 대시보드에서 "Add a Site" 버튼을 클릭합니다.
2. 사용할 도메인 이름을 입력하고 "Add Site" 버튼을 클릭합니다.
3. 무료 플랜을 선택하거나 필요에 따라 유료 플랜을 선택합니다.
4. Cloudflare에서 제공하는 네임서버 정보를 도메인 등록 업체에 설정합니다.
5. 네임서버 변경이 적용될 때까지 기다립니다(최대 24시간 소요될 수 있음).

### 3. DNS 설정

1. Cloudflare 대시보드에서 도메인을 선택합니다.
2. "DNS" 탭을 클릭합니다.
3. "Add Record" 버튼을 클릭합니다.
4. 다음과 같이 CNAME 레코드를 추가합니다:
   - Type: CNAME
   - Name: @ (루트 도메인) 또는 원하는 서브도메인(예: www)
   - Target: Vercel에서 제공한 도메인(예: sodamm.vercel.app)
   - Proxy status: Proxied (활성화)

### 4. SSL/TLS 설정

1. "SSL/TLS" 탭을 클릭합니다.
2. "Overview" 섹션에서 "Full" 또는 "Full (Strict)" 모드를 선택합니다.
3. "Edge Certificates" 섹션에서 "Always Use HTTPS" 옵션을 활성화합니다.

### 5. 페이지 규칙 및 캐싱 설정

1. "Rules" > "Page Rules" 탭을 클릭합니다.
2. "Create Page Rule" 버튼을 클릭합니다.
3. 다음과 같은 페이지 규칙을 추가할 수 있습니다:
   - URL 패턴: `*yourdomain.com/*`
   - 설정: "Cache Level" > "Standard"
   - 설정: "Edge Cache TTL" > "2 hours"

## 환경 변수 설정

프로젝트에 필요한 환경 변수는 다음과 같습니다:

1. `DATABASE_URL`: PostgreSQL 데이터베이스 연결 URL
   - 형식: `postgresql://username:password@host:port/database?schema=public`
   - 예시: `postgresql://postgres:password@localhost:5432/sodamm?schema=public`

2. `AUTH_SECRET`: Auth.js 보안 키
   - 생성 방법: `openssl rand -base64 32` 명령어 실행
   - 예시: `KJH3kjhKJH3kjh2KJH3kjh2KJH3kjh2KJH3kjh2KJH3k`

3. `AUTH_GOOGLE_ID`: Google OAuth 클라이언트 ID
   - [Google Cloud Console](https://console.cloud.google.com/)에서 OAuth 클라이언트 ID 생성

4. `AUTH_GOOGLE_SECRET`: Google OAuth 클라이언트 시크릿
   - Google Cloud Console에서 OAuth 클라이언트 시크릿 생성

## GitHub Actions CI/CD

GitHub Actions를 사용하여 CI/CD 파이프라인을 설정하려면 다음 단계를 따르세요:

1. GitHub 저장소의 "Settings" > "Secrets and variables" > "Actions"로 이동합니다.
2. "New repository secret" 버튼을 클릭합니다.
3. 다음 시크릿을 추가합니다:
   - `VERCEL_TOKEN`: Vercel API 토큰 (Vercel 계정 설정에서 생성)
   - `DATABASE_URL`: 데이터베이스 연결 URL
   - `AUTH_SECRET`: Auth.js 보안 키
   - `AUTH_GOOGLE_ID`: Google OAuth 클라이언트 ID
   - `AUTH_GOOGLE_SECRET`: Google OAuth 클라이언트 시크릿

## 커스텀 도메인 설정

### Vercel에서 커스텀 도메인 설정

1. Vercel 대시보드에서 프로젝트를 선택합니다.
2. "Settings" > "Domains"로 이동합니다.
3. 사용할 도메인을 입력하고 "Add" 버튼을 클릭합니다.
4. Vercel에서 제공하는 DNS 설정 지침을 따릅니다.

### Cloudflare에서 커스텀 도메인 설정

1. Cloudflare 대시보드에서 도메인을 선택합니다.
2. "DNS" 탭을 클릭합니다.
3. Vercel에서 제공한 DNS 레코드를 추가합니다.
4. 변경 사항이 적용될 때까지 기다립니다(최대 몇 분 소요될 수 있음).

---

이 가이드를 따라 Sodamm 프로젝트를 Vercel과 Cloudflare를 사용하여 성공적으로 배포할 수 있습니다. 문제가 발생하면 Vercel 및 Cloudflare 공식 문서를 참조하세요.
