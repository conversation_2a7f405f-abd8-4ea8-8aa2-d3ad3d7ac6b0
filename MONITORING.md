# 모니터링 및 로깅 가이드

이 문서는 Sodamm 프로젝트의 모니터링 및 로깅 시스템 설정 및 사용 방법을 설명합니다.

## 목차

1. [Sentry 설정](#sentry-설정)
2. [에러 모니터링](#에러-모니터링)
3. [성능 모니터링](#성능-모니터링)
4. [사용자 행동 추적](#사용자-행동-추적)
5. [알림 설정](#알림-설정)

## Sentry 설정

### 1. Sentry 계정 생성 및 프로젝트 설정

1. [Sentry 웹사이트](https://sentry.io)에 접속합니다.
2. 계정을 생성하거나 기존 계정으로 로그인합니다.
3. 새 프로젝트를 생성합니다:
   - 플랫폼: "Next.js"
   - 프로젝트 이름: "sodamm"
4. 프로젝트 생성 후 DSN(Data Source Name)을 복사합니다.

### 2. 환경 변수 설정

다음 환경 변수를 `.env` 파일과 Vercel 프로젝트 설정에 추가합니다:

```
SENTRY_DSN="your-sentry-dsn"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"
SENTRY_PROJECT="your-sentry-project"
SENTRY_ORG="your-sentry-org"
```

### 3. Sentry CLI 설정

Sentry CLI를 사용하여 소스맵을 업로드하고 릴리스를 관리하려면 다음 단계를 따르세요:

1. Sentry 계정 설정에서 Auth Token을 생성합니다.
2. 프로젝트 루트에 `.sentryclirc` 파일을 생성합니다:

```ini
[auth]
token=your-sentry-auth-token

[defaults]
org=your-sentry-org
project=your-sentry-project
```

## 에러 모니터링

### 에러 캡처 및 보고

에러를 캡처하고 Sentry에 보고하려면 `captureError` 함수를 사용하세요:

```typescript
import { captureError, ErrorSeverity } from '@/lib/error';

try {
  // 위험한 작업 수행
} catch (error) {
  captureError(
    error,
    {
      userId: 'user-123',
      tags: { component: 'UserProfile' },
      extra: { additionalData: 'some-data' }
    },
    ErrorSeverity.ERROR
  );
}
```

### 에러 처리 래퍼 함수

비동기 함수에서 에러를 자동으로 캡처하려면 `withErrorHandling` 함수를 사용하세요:

```typescript
import { withErrorHandling } from '@/lib/error';

async function fetchUserData(userId: string) {
  return withErrorHandling(
    async () => {
      // 데이터 가져오기 로직
      return data;
    },
    {
      userId,
      tags: { operation: 'fetchUserData' }
    }
  );
}
```

## 성능 모니터링

### Web Vitals 측정

Next.js 애플리케이션의 Web Vitals를 측정하고 Sentry에 보고하기 위해 `reportWebVitals` 함수가 이미 설정되어 있습니다.

### 사용자 정의 성능 메트릭

사용자 정의 성능 메트릭을 측정하고 보고하려면 `reportPerformanceMetrics` 함수를 사용하세요:

```typescript
import { reportPerformanceMetrics } from '@/lib/monitoring';

// 성능 측정 시작
const startTime = performance.now();

// 작업 수행
await someOperation();

// 성능 측정 종료 및 보고
const endTime = performance.now();
reportPerformanceMetrics(
  {
    operationTime: endTime - startTime
  },
  'custom-operation'
);
```

### 트랜잭션 측정

복잡한 작업의 성능을 측정하려면 Sentry 트랜잭션을 사용하세요:

```typescript
import { startTransaction } from '@/lib/error';

async function complexOperation() {
  const transaction = startTransaction('complexOperation', 'task');
  
  try {
    // 작업 수행
    const span = transaction.startChild({ op: 'subtask', description: 'Step 1' });
    await step1();
    span.finish();
    
    // 다른 작업 수행
    const span2 = transaction.startChild({ op: 'subtask', description: 'Step 2' });
    await step2();
    span2.finish();
    
    return result;
  } finally {
    transaction.finish();
  }
}
```

## 사용자 행동 추적

사용자 행동을 추적하려면 `trackUserAction` 함수를 사용하세요:

```typescript
import { trackUserAction } from '@/lib/monitoring';

// 버튼 클릭 추적
function handleButtonClick() {
  trackUserAction('click', 'button', 'submit-form', 1);
  
  // 작업 수행
}

// 페이지 방문 추적
useEffect(() => {
  trackUserAction('view', 'page', 'user-profile');
}, []);
```

## 알림 설정

### Sentry 알림 설정

1. Sentry 대시보드에서 프로젝트를 선택합니다.
2. "Settings" > "Alerts"로 이동합니다.
3. "Create Alert Rule" 버튼을 클릭합니다.
4. 알림 규칙을 설정합니다:
   - 이벤트 빈도 (예: "이벤트가 10분 내에 10회 이상 발생")
   - 이벤트 속성 (예: "심각도가 error 이상")
   - 알림 대상 (이메일, Slack, Discord 등)

### Slack 알림 설정

1. Sentry 대시보드에서 "Settings" > "Integrations"로 이동합니다.
2. "Slack" 통합을 찾아 "Configure" 버튼을 클릭합니다.
3. Slack 워크스페이스에 Sentry 앱을 설치합니다.
4. 알림을 받을 채널을 선택합니다.

### 이메일 알림 설정

1. Sentry 대시보드에서 "Settings" > "Alerts"로 이동합니다.
2. "Create Alert Rule" 버튼을 클릭합니다.
3. 알림 규칙을 설정하고 "Actions" 섹션에서 "Email" 액션을 선택합니다.
4. 알림을 받을 이메일 주소를 입력합니다.

---

이 가이드를 따라 Sodamm 프로젝트의 모니터링 및 로깅 시스템을 설정하고 사용할 수 있습니다. 문제가 발생하면 Sentry 공식 문서를 참조하세요.
