# task-master ai

1. 프로젝트 초기 설정 및 환경 구성 (태스크 1, 2)

- Next.js 15 (App Router) 기반 프로젝트 생성
- Supabase 설정 및 데이터베이스 스키마 설계

expand

1. GitHub 저장소 생성 및 Next.js 15 프로젝트 초기화

- GitHub 저장소 생성
- pnpm으로 Next.js 15 프로젝트 초기화 (App Router 사용)
- TypeScript 설정
- 저장소 연결 및 초기 커밋

2. 스타일링 도구 설정 (Tailwind CSS v4 및 Shadcn UI)

- Tailwind CSS v4 설치 및 구성
- Shadcn UI 설치 및 초기화
  기본 테마 설정
  코드 품질 도구 설정 (ESLint 및 Prettier)
  ESLint 및 Prettier 패키지 설치
  설정 파일 구성
  VS Code 설정
  프로젝트 폴더 구조 설계
  App Router 기반 폴더 구조 설계
  주요 디렉토리 생성 (components, lib/utils, types 등)
  각 폴더에 README.md 추가
  환경 변수 설정 및 개발 환경 테스트
  환경 변수 파일 구성
  개발 서버 실행 및 테스트
  프로젝트 문서화

2. 핵심 기능 구현 (태스크 3, 4, 5 등)

- Auth.js 인증 시스템
- 다국어 지원 시스템
- 기본 레이아웃 및 UI 컴포넌트

3. 주요 콘텐츠 시스템 (태스크 8, 9, 10, 11 등)

- 카테고리 관리 시스템
- 생활 정보 콘텐츠 관리
- 한국 서비스 이용 가이드
- 외국인 근로자 대상 정부 정보

4. 사용자 상호작용 기능 (태스크 12, 13, 14, 15 등)

- 구인·구직 정보 시스템
- 커뮤니티 게시판
- 검색 시스템
- 북마크 및 저장 기능

5. 기술 통합 및 최적화 (태스크 18, 19, 20, 21 등)

- Supabase Storage 통합
- Tiptap 리치 텍스트 에디터
- SWR 데이터 페칭 및 캐싱
- Server Actions 구현

6. 품질 및 배포 (태스크 22, 23, 24, 25)

- SEO 최적화
- 성능 최적화
- 접근성 개선
- 배포 및 인프라 설정
