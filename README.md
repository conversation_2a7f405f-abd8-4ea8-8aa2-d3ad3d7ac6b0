# hello-nextjs-15-boilerplate

## supabase studio

http://127.0.0.1:54323

## Nextjs 15

https://nextjs.org/docs/app/getting-started/installation

```bash
npx create-next-app@latest
? What is your project named? › hello-nextjs-15-boilerplate

npx create-next-app@latest
✔ What is your project named? … hello-nextjs-15-boilerplate
✔ Would you like to use TypeScript? … No / Yes
✔ Would you like to use ESLint? … No / Yes
✔ Would you like to use Tailwind CSS? … No / Yes
✔ Would you like your code inside a `src/` directory? … No / Yes
✔ Would you like to use App Router? (recommended) … No / Yes
✔ Would you like to use Turbopack for `next dev`? … No / Yes
✔ Would you like to customize the import alias (`@/*` by default)? … No / Yes
Creating a new Next.js app in /Users/<USER>/labs/workspace/hello-nextjs-15-boilerplate.
```

## Authjs 5

https://authjs.dev/getting-started/installation?framework=pnpm

```bash
❯ pnpm add next-auth@beta
❯ npx auth secret
📝 Created /Users/<USER>/labs/workspace/hello-nextjs-15-boilerplate/.env.local with `AUTH_SECRET`.

```

.env.local

```
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

AUTH_SECRET="<yours>" # Added by `npx auth`. Read more: https://cli.authjs.dev

AUTH_GOOGLE_ID=<yours>
AUTH_GOOGLE_SECRET=<yours>
```

## Next Intl

https://next-intl.dev/docs/getting-started/app-router/without-i18n-routing

## Prisma

https://authjs.dev/getting-started/adapters/prisma#configuration

```
# local
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres?pgbouncer=true&connection=10
DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
```

```bash
❯ pnpm add prisma --save-dev
Packages: +11
+++++++++++
Progress: resolved 445, reused 364, downloaded 0, added 11, done

devDependencies:
+ prisma 6.5.0

╭ Warning ───────────────────────────────────────────────────────────────────────────╮
│                                                                                    │
│   Ignored build scripts: @prisma/engines, esbuild, prisma.                         │
│   Run "pnpm approve-builds" to pick which dependencies should be allowed to run    │
│   scripts.                                                                         │
│                                                                                    │
╰────────────────────────────────────────────────────────────────────────────────────╯

Done in 1.6s using pnpm v10.6.3
 ~/labs/workspace/hello-nextjs-15-boilerplate  G  main  1↑ ⭑  
❯ npx prisma init --datasource-provider postgresql

✔ Your Prisma schema was created at prisma/schema.prisma
  You can now open it in your favorite editor.

warn You already have a .gitignore file. Don't forget to add `.env` in it to not commit any private information.

Next steps:
1. Set the DATABASE_URL in the .env file to point to your existing database. If your database has no tables yet, read https://pris.ly/d/getting-started
2. Run prisma db pull to turn your database schema into a Prisma schema.
3. Run prisma generate to generate the Prisma Client. You can then start querying your database.
4. Tip: Explore how you can extend the ORM with scalable connection pooling, global caching, and real-time database events. Read: https://pris.ly/cli/beyond-orm

More information in our documentation:
https://pris.ly/d/getting-started


```

## Supabase

https://authjs.dev/getting-started/adapters/supabase
https://supabase.com/docs/guides/local-development/overview

- 여러 프로젝트를 동시에 개발할 경우 `supabase/config.toml` 변경
- `product_id` 변경, `port =` 로 검색해서 port 변경

```bash
❯ npx supabase init
Generate VS Code settings for Deno? [y/N] y
Generated VS Code settings in .vscode/settings.json. Please install the recommended extension!
Finished supabase init.
```

```bash
❯ make supabase-start
npx supabase start
WARNING: analytics requires mounting default docker socket: /var/run/docker.sock
Started supabase local development setup.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
  S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
   S3 Access Key: 625729a08b95bf1b7ff351a663f3a23c
   S3 Secret Key: 850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
       S3 Region: local
```

## Supabase Schema

1. supabase/config.toml 파일에서 스키마 설정 수정

```
schemas = ["public", "graphql_public", "sodamm"]
extra_search_path = ["public", "extensions", "sodamm"]
```

2. Supabase 재시작

```
make supabase-restart
```

3. .env 파일에서 데이터베이스 URL 업데이트

```
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/sodamm?pgbouncer=true&connection=10
DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54322/sodamm
```

4. schema 생성

```sql
CREATE SCHEMA IF NOT EXISTS sodamm;
```

## Next.js Third Parties

This project uses `@next/third-parties` for optimized integration of third-party scripts.

```bash
pnpm add @next/third-parties
```

### Available Components

- Google Analytics: `<GoogleAnalytics gaId="GA-MEASUREMENT-ID" />`
- Google Tag Manager: `<GoogleTagManager gtmId="GTM-ID" />`
- YouTube Embed: `<YouTubeEmbed videoid="VIDEO_ID" />`
- And more...

### Usage Example

In your root layout (for analytics):

```tsx
import { GoogleAnalytics } from '@next/third-parties/google';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        {children}
        <GoogleAnalytics gaId="GA-MEASUREMENT-ID" />
      </body>
    </html>
  );
}
```

Check out the demo page at `/third-parties-demo` for more examples.

## Tiptap Editor

This project includes a rich text editor built with [Tiptap](https://tiptap.dev/).

```bash
pnpm add @tiptap/react @tiptap/pm @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-link @tiptap/extension-placeholder @tiptap/extension-underline @tiptap/extension-text-align @tiptap/extension-color
```

### Features

- Text formatting (bold, italic, underline, strikethrough)
- Headings (H1, H2, H3)
- Lists (ordered and unordered)
- Text alignment
- Links and images
- Code blocks and blockquotes
- Text color
- Undo/redo

### Usage Example

```tsx
import TiptapEditor, { useEditorState } from '@/components/editor/TiptapEditor';

// With the useEditorState hook
const { content, handleChange } = useEditorState();

<TiptapEditor
  content={content}
  onChange={handleChange}
  placeholder="Start writing..."
/>;
```

Check out the demo page at `/examples/tiptap-editor` for more examples.

## State Management

This project uses [Zustand](https://github.com/pmndrs/zustand) for state management with an optimized architecture.

### Store Structure

The state management is organized into domain-specific stores:

- **UI Store**: Manages UI-related state (theme, modals, toasts, sidebar, loading)
- **User Store**: Manages user-related state (session, preferences, authentication)
- **Community Store**: Manages community-related state (posts, filters, drafts)
- **Data Store**: Manages data caching and loading/error states

### Usage

```tsx
// Import from the centralized API
import { UIStateAPI, useTheme, useSetTheme } from '@/store/api';

// Using hooks in components
function ThemeToggle() {
  const theme = useTheme();
  const setTheme = useSetTheme();

  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      {theme === 'dark' ? 'Switch to Light' : 'Switch to Dark'}
    </button>
  );
}

// Using direct API in non-component code
function resetUIState() {
  UIStateAPI.reset();
}
```

### Optimization Features

- **Selector Pattern**: Prevents unnecessary re-renders
- **Immer Integration**: Simplifies state updates while maintaining immutability
- **Performance Monitoring**: Tracks re-renders and state changes in development
- **Memoization**: Optimizes derived state calculations

### Development Tools

In development mode, you can use the State Management Dashboard to monitor state:

```tsx
import { StateManagementDashboard } from '@/components/dev/state-management-dashboard';

// Add to your layout or page
<StateManagementDashboard />;
```

For more details, see the [State Management Guide](src/store/docs/state-management-guide.md).

## mcp

```
{
  "mcpServers": {
    "browser-tools": {
      "command": "npx",
      "args": ["-y", "@agentdeskai/browser-tools-mcp@1.2.0"]
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    },
    "supabase": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1c2FwYWJhc2V0YWlsc2VjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTUxNjI1NjgsImV4cCI6MjAyMDczODU2OH0.0000000000000000000000000000000000000000"
      ]
    },
    "taskmaster-ai": {
      "command": "npx",
      "args": [
        "-y",
        "--package=task-master-ai",
        "task-master-ai"
      ],
      "env": {
        "ANTHROPIC_API_KEY": "",
        "PERPLEXITY_API_KEY": "",
        "MODEL": "claude-3-7-sonnet-20250219",
        "PERPLEXITY_MODEL": "sonar-pro",
        "MAX_TOKENS": "64000",
        "TEMPERATURE": "0.2",
        "DEFAULT_SUBTASKS": "5",
        "DEFAULT_PRIORITY": "medium"
      }
    }
  }
}
```

pixelcut ai
https://www.pixelcut.ai/background-remover
