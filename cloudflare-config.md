# Cloudflare 설정 가이드

이 문서는 Sodamm 프로젝트를 위한 Cloudflare 설정 가이드입니다.

## DNS 설정

### 기본 DNS 레코드

| 유형 | 이름 | 콘텐츠 | 프록시 상태 |
|------|------|--------|------------|
| CNAME | @ | sodamm.vercel.app | 프록시됨 |
| CNAME | www | sodamm.vercel.app | 프록시됨 |

## SSL/TLS 설정

### SSL/TLS 모드

- **권장 설정**: Full (Strict)
- **설명**: 이 모드는 Cloudflare와 원본 서버(Vercel) 간의 연결이 유효한 SSL 인증서로 암호화되도록 합니다.

### Edge 인증서

- **Always Use HTTPS**: 활성화
- **Minimum TLS Version**: TLS 1.2
- **Opportunistic Encryption**: 활성화
- **TLS 1.3**: 활성화
- **Automatic HTTPS Rewrites**: 활성화

## 캐싱 설정

### 브라우저 캐시 TTL

- **권장 설정**: 4시간
- **설명**: 브라우저가 정적 자산을 캐싱하는 시간을 설정합니다.

### 캐시 규칙

다음 파일 확장자에 대해 Edge 캐싱을 활성화합니다:

- .jpg, .jpeg, .png, .gif, .ico, .svg
- .css, .js
- .woff, .woff2, .ttf, .otf, .eot

## 페이지 규칙

### 정적 자산 캐싱

- **URL 패턴**: `*yourdomain.com/*.(jpg|jpeg|png|gif|ico|svg|css|js|woff|woff2|ttf|otf|eot)`
- **설정**:
  - Cache Level: Cache Everything
  - Edge Cache TTL: 1주
  - Browser Cache TTL: 1일

### API 경로 캐싱 비활성화

- **URL 패턴**: `*yourdomain.com/api/*`
- **설정**:
  - Cache Level: Bypass
  - Disable Security: 비활성화

### 인증 경로 캐싱 비활성화

- **URL 패턴**: `*yourdomain.com/auth/*`
- **설정**:
  - Cache Level: Bypass
  - Disable Security: 비활성화

## 보안 설정

### 웹 애플리케이션 방화벽 (WAF)

- **모드**: 활성화
- **관리형 규칙**: 모두 활성화
- **민감도**: 중간

### DDoS 보호

- **DDoS 보호**: 활성화
- **Under Attack 모드**: 필요시 활성화

### Bot Fight Mode

- **Bot Fight Mode**: 활성화
- **Super Bot Fight Mode**: 필요시 활성화 (Business 플랜 이상 필요)

### 브라우저 무결성 검사

- **브라우저 무결성 검사**: 활성화

## 성능 최적화

### Auto Minify

- **HTML**: 활성화
- **CSS**: 활성화
- **JavaScript**: 활성화

### Brotli 압축

- **Brotli**: 활성화

### HTTP/3 (QUIC)

- **HTTP/3**: 활성화

### Rocket Loader

- **Rocket Loader**: 활성화

### 이미지 최적화

- **Polish**: 활성화 (Pro 플랜 이상 필요)
- **Mirage**: 활성화 (Pro 플랜 이상 필요)

## 모니터링 및 분석

### 트래픽 분석

- **Web Analytics**: 활성화

### 로그 분석

- **Logpush**: 필요시 설정 (Enterprise 플랜 필요)

## 추가 설정

### 모바일 리디렉션

- **URL 패턴**: `*yourdomain.com/*`
- **설정**: Mobile Redirect (필요한 경우)

### CORS 헤더

- **URL 패턴**: `*yourdomain.com/api/*`
- **설정**: CORS Header 추가

---

이 가이드를 따라 Cloudflare 설정을 최적화하여 Sodamm 프로젝트의 성능, 보안 및 가용성을 향상시킬 수 있습니다.
