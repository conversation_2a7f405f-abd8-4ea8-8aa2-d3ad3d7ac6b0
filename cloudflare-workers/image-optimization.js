/**
 * <PERSON>flare Worker 스크립트: 이미지 최적화
 * 
 * 이 Worker는 이미지 요청을 가로채서 Cloudflare Image Resizing 서비스를 통해 최적화합니다.
 * Cloudflare 대시보드에서 Workers 섹션에 이 스크립트를 추가하고 이미지 경로 패턴을 설정하세요.
 * 참고: Cloudflare Pro 플랜 이상이 필요합니다.
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // 이미지 파일 확장자 확인
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  const path = url.pathname
  const extension = path.split('.').pop().toLowerCase()
  
  // 이미지 파일이 아닌 경우 원본 요청 처리
  if (!imageExtensions.includes(extension)) {
    return fetch(request)
  }
  
  // 이미지 최적화 파라미터 설정
  const imageParams = {
    width: url.searchParams.get('width') || 'auto',
    height: url.searchParams.get('height') || 'auto',
    fit: url.searchParams.get('fit') || 'cover',
    quality: url.searchParams.get('quality') || '85',
    format: url.searchParams.get('format') || 'auto',
  }
  
  // 이미지 최적화 URL 생성
  const imageURL = new URL(request.url)
  
  // 이미지 최적화 파라미터 추가
  Object.entries(imageParams).forEach(([key, value]) => {
    if (value !== 'auto' && value !== null) {
      imageURL.searchParams.set(key, value)
    }
  })
  
  // Cloudflare Image Resizing 서비스 사용
  const resizingRequest = new Request(imageURL.toString(), request)
  
  // cf 객체에 이미지 최적화 옵션 추가
  resizingRequest.cf = {
    image: {
      width: imageParams.width !== 'auto' ? parseInt(imageParams.width) : undefined,
      height: imageParams.height !== 'auto' ? parseInt(imageParams.height) : undefined,
      fit: imageParams.fit,
      quality: parseInt(imageParams.quality),
      format: imageParams.format !== 'auto' ? imageParams.format : 'auto',
      sharpen: 1.0,
      minify: true,
    }
  }
  
  // 최적화된 이미지 응답 반환
  const response = await fetch(resizingRequest)
  
  // 캐싱 헤더 추가
  const newResponse = new Response(response.body, response)
  newResponse.headers.set('Cache-Control', 'public, max-age=31536000')
  
  return newResponse
}
