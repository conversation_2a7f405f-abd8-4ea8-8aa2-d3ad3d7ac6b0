/**
 * Cloudflare Worker 스크립트: 보안 헤더 추가
 * 
 * 이 Worker는 모든 응답에 보안 헤더를 추가하여 웹 애플리케이션의 보안을 강화합니다.
 * Cloudflare 대시보드에서 Workers 섹션에 이 스크립트를 추가하고 경로 패턴을 설정하세요.
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  // 원본 서버에서 응답 가져오기
  const response = await fetch(request)
  
  // 응답 복제 및 보안 헤더 추가
  const newResponse = new Response(response.body, response)
  
  // 보안 헤더 설정
  newResponse.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  newResponse.headers.set('X-Content-Type-Options', 'nosniff')
  newResponse.headers.set('X-Frame-Options', 'DENY')
  newResponse.headers.set('X-XSS-Protection', '1; mode=block')
  newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  newResponse.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()')
  
  // Content-Security-Policy 설정
  // 필요에 따라 조정하세요
  newResponse.headers.set('Content-Security-Policy', `
    default-src 'self';
    script-src 'self' https://vercel.com https://vercel.live https://*.vercel-scripts.com 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob: https://*.vercel-storage.com;
    font-src 'self';
    connect-src 'self' https://*.vercel.app https://vercel.com https://vercel.live;
    frame-ancestors 'none';
    form-action 'self';
    base-uri 'self';
    object-src 'none';
  `.replace(/\s+/g, ' ').trim())
  
  return newResponse
}
