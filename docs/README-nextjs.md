# Next.js 프로젝트 설정

https://www.heropy.dev/p/n7JHmI

## create-next-app

```bash
npx create-next-app@latest <프로젝트이름>
✔ Would you like to use TypeScript? … Yes  # 타입스크립트 사용 여부
✔ Would you like to use ESLint? … Yes  # ESLint 사용 여부
✔ Would you like to use Tailwind CSS? … Yes  # Tailwind CSS 사용 여부
✔ Would you like your code inside a `src/` directory? … Yes  # src/ 디렉토리 사용 여부
✔ Would you like to use App Router? (recommended) … Yes  # App Router 사용 여부
✔ Would you like to use Turbopack for next dev? … No  # Turbopack 사용 여부
✔ Would you like to customize the import alias (@/* by default)? … No  # `@/*` 외 경로 별칭 사용 여부
```

## Prettier

```bash
pnpm i -D prettier eslint-config-prettier prettier-plugin-tailwindcss
```

.eslintrc.json

```json
{
  "extends": ["next/core-web-vitals", "next/typescript", "prettier"]
}
```

.prettierrc

```json
{
  "semi": false,
  "singleQuote": true,
  "singleAttributePerLine": true,
  "bracketSameLine": true,
  "endOfLine": "lf",
  "trailingComma": "none",
  "arrowParens": "avoid",
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

.vscode/settings.json

```json
{
  "[javascript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```
