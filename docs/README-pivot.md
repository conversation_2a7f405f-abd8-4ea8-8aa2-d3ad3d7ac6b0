# NOANSWERCRAFT v1

next.js 14
next.js app router
typescript
tailwind css
shadcn-ui
pnpm
supabase
prisma": "^5.20.0"
fetch http client 사용

visual studio code
llama3.2

서비스 이름은 NOANSWERCRAFT이고, 우리의 비전은 "답이 없기에 모든 것이 가능한 곳"이야.

우리가 해결하고자 하는 것은 단순히 창의력을 높이는 공간을 넘어서, AI 시대를 살아가는 새로운 사고 방식을 훈려하는 플랫폼.

기존에 있던 것들은 이미 답이 존재하는데, 우리는 답이 없는 것을 만드는게 목적이야. 답이 없는 것을 만든다는 것은 기존에 없던 새로운 것을 만든다는 이야기이고 이 때 필요한 것은 창의성이라 생각해.

NOANSWER

- 아주 보통의 하루(aboha): 아주 보통의 평벙한 하루에서 발겨하는 모든 이야기
- 이건 못하겠지(human-only): 인간만이 할 수 있는 것에 대한 이야기
- Draft: 아이디어의 초안을 공유하는 공간, "미완성"을 두려워하지 않는 훈련장. 완벽하지 않아도 괜찮아요.
- High Concept: 단 하나의 질문에서 시작하는 상상
- Flow: 몰입
- Magic If: 나를 다른 상황에 대입하여 새로운 관점 발견하기
- First Principle Thinking: 모든 것을 원점에서 다시 생각하는 근본적 사고의 실험
- Observation: 관찰은 단순히 보는 것에 그치지 않습니다. 그것은 발견, 이해, 그리고 개선의 과정을 포함합니다
- Slow thinking: 시간 압박이 없는 상태에서의 사고는 더 많은 아이디어 생성을 가능케 합니다
- Blank Out: 의도적인 휴식을 통해 창의성을 깨우는 시간
- Proto: 질문을 작성하면 질문이 얼마나 창의적인지 LLM이 답변
- Nova: 질문을 작성하면 질문을 확장할 수 있는 질문을 해준다.
- Prism: 어떤 주제에 대해 사용자들이 창의적인 답변을 하는 기능

'안녕하세요.',
'인공지능 시대에 필요한 교육에 관심이 많은 개발자입니다.\n\n',
'우리는 지금까지 정답을 찾기 위해 이미 존재하는 지식을 습득하는 데 집중해왔습니다. \n하지만 단순한 지식의 습득은 더 이상 의미가 없습니다.\n인공지능이 우리보다 더 빠르고 정확하게 지식을 습득하고 활용하기 때문입니다.\n\n',
'이런 고민을 하던 중 문득 우리나라의 온라인 커뮤니티 문화가 눈에 들어왔습니다.',
'댓글에서 펼쳐지는 한국인들의 재치 있는 표현과 기발한 상상력, 그리고 그것을 함께 발전시켜나가는 모습이 새롭게 다가왔습니다.',
'정해진 답이 없는 상황에서도 자유롭게 생각을 나누고, 서로의 아이디어에 영감을 받아 더 새로운 것을 만들어내는 이 모습이야말로 인공지능 시대에 우리에게 진정으로 필요한 능력이라 생각합니다.\n\n',
"'NOANSWERCRAFT'는 이런 고민과 영감에서 시작되었습니다.",
"'답이 없기에 모든 것이 가능한 곳'이라는 비전처럼, 우리는 지금까지의 교육처럼 정해진 답을 찾아가는 것이 아니라, 답이 정해지지 않은 질문들을 통해 무한한 상상력을 펼치고자 합니다.",
'이곳에서 여러분들과 함께 자유롭게 생각을 나누고, 서로에게 영감을 주고받으며, 인공지능이 대체할 수 없는 우리만의 이야기를 만들어가고 싶습니다.\n\n',
'감사합니다.',

# NOANSWERCRAFT v2

next.js 14
next.js app router
typescript
tailwind css
shadcn-ui
pnpm
supabase
prisma": "^5.20.0"
fetch http client 사용

visual studio code
llama3.2

서비스 이름은 NOANSWERCRAFT이고, 우리의 비전은 "답이 없는 미래를 만드는 솔로프리너를 위한 커뮤니티"이야.
솔로프리너들은 혼자서 모든 것을 해결해야해, 그래서 많은 과제들을 혼자서 판단해야하고 혼자서 해결해야해.
여기서 우리가 해결해야 할 문제를 찾을 수 있어.
솔로프리너들은 혼자서 많은 것들을 해결하려면 시간이 부족해, 그런 시간들을 아껴줄 수 있게 도와주는거야.
자동화하는 방법이라던가, 브랜딩, 마케팅, 인사이트들을 NOANSWERCRAFT에서 찾아 볼 수 있는거지.
