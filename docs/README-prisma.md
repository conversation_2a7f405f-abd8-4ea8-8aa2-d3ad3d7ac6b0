# Prisma

## postgresql

https://supabase.com/partners/integrations/prisma

```
❯ npx prisma init --datasource-provider postgresql

✔ Your Prisma schema was created at prisma/schema.prisma
  You can now open it in your favorite editor.

warn Prisma would have added DATABASE_URL but it already exists in .env
warn You already have a .gitignore file. Don't forget to add `.env` in it to not commit any private information.

Next steps:
1. Set the DATABASE_URL in the .env file to point to your existing database. If your database has no tables yet, read https://pris.ly/d/getting-started
2. Run prisma db pull to turn your database schema into a Prisma schema.
3. Run prisma generate to generate the Prisma Client. You can then start querying your database.
4. Tip: Explore how you can extend the ORM with scalable connection pooling, global caching, and real-time database events. Read: https://pris.ly/cli/beyond-orm

More information in our documentation:
https://pris.ly/d/getting-started

```

## supabase local 환경 설정

```
prisma migrate deploy:

Migration 히스토리를 생성하고 추적합니다
Migration 파일들을 순서대로 실행합니다
Production 환경에서 권장되는 방식입니다
팀원들과 migration 히스토리를 공유할 수 있습니다
schema 변경사항을 명시적으로 관리할 수 있습니다

prisma db push:

Migration 히스토리를 생성하지 않습니다
현재 schema를 데이터베이스에 직접 적용합니다
개발 환경에서 빠른 테스트용으로 적합합니다
팀원들과 migration 히스토리를 공유할 수 없습니다
schema 변경사항을 추적하기 어렵습니다
```

1. supabase init

```
❯ make supabase-destory
npx supabase stop
Stopped supabase local development setup.
A new version of Supabase CLI is available: v1.226.3 (currently installed v1.204.3)
We recommend updating regularly for new features and bug fixes: https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli
Local data are backed up to docker volume. Use docker to show them: docker volume ls --filter label=com.supabase.cli.project=noanswercraft
npx supabase stop --no-backup
Stopped supabase local development setup.
A new version of Supabase CLI is available: v1.226.3 (currently installed v1.204.3)
We recommend updating regularly for new features and bug fixes: https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli
docker volume prune
WARNING! This will remove anonymous local volumes not used by at least one container.
Are you sure you want to continue? [y/N]  y
Total reclaimed space: 0B
```

2. supabase start

```
 ~/labs/workspace/noanswercraft  G  develop  @1  ⭑ - ?  
❯ make supabase-start
npx supabase start
Seeding globals from roles.sql...
Seeding data from supabase/seed.sql...
WARNING: analytics requires mounting default docker socket: /var/run/docker.sock
Creating Storage bucket: images
Uploading: supabase/images/users/me.png => images/users/me.png
Started supabase local development setup.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
  S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
   S3 Access Key: 625729a08b95bf1b7ff351a663f3a23c
   S3 Secret Key: 850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
       S3 Region: local
A new version of Supabase CLI is available: v1.226.3 (currently installed v1.204.3)
We recommend updating regularly for new features and bug fixes: https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli
```

3. prisma migrate deploy
