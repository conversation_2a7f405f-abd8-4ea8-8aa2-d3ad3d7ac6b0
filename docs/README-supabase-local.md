## supabase local

> supabase와 prisma를 혼동하지 말것.

## supabase

#### local 개발 환경 설정 후 해야 할 일

- prisma migration
- auth.users trigger 생성(supabase/local.sql)
- storage 권한 확인
- 각 table RLS 설정(supabase/local.sql)

```
supabase-login:
	npx supabase login
supabase-init:
	npx supabase init
supabase-link:
	npx supabase link
supabase-pull:
	npx supabase db pull --schema auth,storage
supabase-start:
	npx supabase start
supabase-stop:
	npx supabase stop
supabase-restart: supabase-stop supabase-start
```

#### .env, .env.local

```
# .env

DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres?pgbouncer=true
DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
```

```
# .env.local

NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_VERCEL_URL=http://localhost:3000

NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
NEXT_PUBLIC_SUPABASE_BUCKET_URL=http://127.0.0.1:54321/storage/v1/object/public/

NEXT_PUBLIC_GA_ID=
NEXT_PUBLIC_GOOGLE_ADSENSE_ID=

SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET=<yours>
SUPABASE_AUTH_EXTERNAL_TWITTER_SECRET=<yours>
SUPABASE_AUTH_EXTERNAL_KAKAO_SECRET=<yours>

SENTRY_AUTH_TOKEN=
```

## supabase destroy

```
supabase-stop-no-backup:
	npx supabase stop --no-backup
docker-volume-prune:
	docker volume prune
supabase-destory: supabase-stop supabase-stop-no-backup docker-volume-prune
```

