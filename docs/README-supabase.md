# supabase

## supabase oauth

https://supabase.com/docs/guides/auth/server-side/nextjs
https://supabase.com/docs/guides/auth/social-login/auth-twitter?queryGroups=environment&environment=client

## local development

https://supabase.com/docs/guides/local-development/cli/getting-started

### make supabase-login

```
❯ make supabase-login
npx supabase login
Hello from Supabase! Press Enter to open browser and login automatically.

Here is your login link in case browser did not open https://supabase.com/dashboard/cli/login?session_id=a74bdc33-6b78-4a04-bcef-05f18caca77f&token_name=cli_raiiz@Raizs-MacBook-Pro.local_1728702063&public_key=0443b2f99e948a63d122f39f17b9a844b5cd4bafb9dbe9ade661943da4ceb2c6ebe42796cb322f9d5ed8b60fe89b078cfb154475054e3b59a9124f71a3f7c38cb6

Token cli_raiiz@Raizs-MacBook-Pro.local_1728702063 created successfully.

You are now logged in. Happy coding!
```

### make supabase-link

```
❯ npx supabase link
Selected project: nhmvjdigaysdqdhthlfz
Enter your database password (or leave blank to skip):
Finished supabase link.
```

### make supabase-init

```
❯ make supabase-init
npx supabase init
Generate VS Code settings for Deno? [y/N] y
Generated VS Code settings in .vscode/settings.json. Please install the recommended extension!
Finished supabase init.
```

supabase 디렉토리 생성

```
supabase
├── .gitignore
├── .temp
│   └── cli-latest
└── config.toml
```

### supabase start

```bash
❯ make supabase-start
npx supabase start
*********: Pulling from supabase/postgres
failed to display json stream: toomanyrequests: Rate exceeded
Retrying after 4s: public.ecr.aws/supabase/postgres:*********
failed to pull docker image: Error response from daemon: toomanyrequests: Rate exceeded
Retrying after 8s: public.ecr.aws/supabase/postgres:*********
*********: Pulling from supabase/postgres
f02209be4ee5: Pull complete
3494031d77c2: Pull complete
3b5b97d7bd46: Pull complete

...

Started supabase local development setup.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
  S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
   S3 Access Key: 625729a08b95bf1b7ff351a663f3a23c
   S3 Secret Key: 850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
       S3 Region: local
A new version of Supabase CLI is available: v1.204.3 (currently installed v1.192.5)
We recommend updating regularly for new features and bug fixes: https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli
```

### .env.local

```
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_VERCEL_URL=http://localhost:3000

NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
NEXT_PUBLIC_SUPABASE_BUCKET_URL=http://127.0.0.1:54321/storage/v1/s3
```

### supabase auth

https://www.albertosadde.com/blog/local-auth-with-subapase/

## supabase trigger and function

```sql
-- This trigger automatically creates a profile entry when a new user signs up via Supabase Auth.
-- See https://supabase.com/docs/guides/auth/managing-user-data#using-triggers for more details.

-- inserts a row into public.profiles
create function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = ''
as $$
begin
  insert into public.users(
    id,
    provider,
    provider_id,
    provider_account,
    email,
    name,
    picture
  ) values (
    new.id,
    new.raw_app_meta_data->>'provider',
    new.raw_user_meta_data->>'provider_id',
    new.raw_user_meta_data->>'user_name',
    new.raw_user_meta_data->>'email',
    new.raw_user_meta_data->>'name',
    new.raw_user_meta_data->>'picture'
  );
  return new;
end;
$$;

-- trigger the function every time a user is created
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

```

-- pg_trgm extension

```sql
CREATE EXTENSION IF NOT EXISTS pg_trgm;
SELECT * FROM pg_extension WHERE extname = 'pg_trgm';
```

# bucket

CREATE POLICY "public upload to images" ON storage.objects FOR SELECT TO anon USING (bucket_id = 'images');
CREATE POLICY "authenticated can upload to images" ON storage.objects FOR INSERT TO anon WITH CHECK (bucket_id = 'images');
