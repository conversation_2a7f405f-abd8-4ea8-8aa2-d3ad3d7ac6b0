# ESLint Configuration for Next.js TypeScript Project

This project uses ESLint v9 with the new flat configuration format to provide linting for TypeScript and Next.js.

## Configuration Files

- `eslint.config.mjs` - Main ESLint configuration file (flat config format for ESLint v9+)
- `.eslintrc.json` - Legacy configuration file (kept for reference)
- `src/types/eslint-plugins.d.ts` - TypeScript declarations for ESLint plugins

## Features

The ESLint configuration includes:

- TypeScript support with `typescript-eslint`
- React and React Hooks rules
- Next.js specific rules
- Import sorting and organization
- Code style consistency

## Usage

### Running ESLint

```bash
# Run ESLint on the entire project
pnpm lint

# Run ESLint with auto-fix
pnpm lint -- --fix
```

### VS Code Integration

For VS Code users, install the ESLint extension and add the following to your `.vscode/settings.json`:

```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ]
}
```

## Rules Overview

### TypeScript Rules

- Allows `any` type (`@typescript-eslint/no-explicit-any`: off)
- Warns about unused variables with special patterns ignored (`@typescript-eslint/no-unused-vars`)

### React Rules

- No need to import React (`react/react-in-jsx-scope`: off)
- Enforces consistent component definitions
- Enforces React Hooks rules

### Import Rules

- Organizes imports by groups
- Enforces consistent import order
- Adds newlines between import groups

### Next.js Rules

- Prevents using HTML links for internal navigation
- Allows synchronous scripts

## Fixing Common Issues

The most common issues found by ESLint in this project are related to import ordering. You can fix them automatically by running:

```bash
pnpm lint -- --fix
```

Common import ordering issues include:

1. Missing empty lines between import groups
2. Incorrect order of imports (external libraries should come before internal imports)
3. Incorrect alphabetical sorting within import groups

## Customization

To modify the ESLint configuration, edit the `eslint.config.mjs` file. The configuration uses the compatibility layer to support both traditional ESLint configurations and the new flat config format.

The main sections of the configuration are:

1. Ignore patterns for files that should not be linted
2. Base configurations extended from popular ESLint plugins
3. Custom rules for TypeScript, React, Next.js, and imports

You can customize any of these sections to suit your project's needs.
