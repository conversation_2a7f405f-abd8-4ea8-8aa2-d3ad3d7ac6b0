# 성능 최적화 가이드

이 문서는 SODAMM 프로젝트의 성능 최적화 방법과 적용된 최적화 기법에 대해 설명합니다.

## 목차

1. [컴포넌트 메모이제이션](#컴포넌트-메모이제이션)
2. [상태 관리 최적화](#상태-관리-최적화)
3. [코드 스플리팅](#코드-스플리팅)
4. [이미지 최적화](#이미지-최적화)
5. [번들 사이즈 최적화](#번들-사이즈-최적화)
6. [성능 모니터링](#성능-모니터링)

## 컴포넌트 메모이제이션

React.memo를 사용하여 컴포넌트를 메모이제이션하면 불필요한 리렌더링을 방지할 수 있습니다. 특히 부모 컴포넌트가 리렌더링될 때 자식 컴포넌트의 props가 변경되지 않았다면 자식 컴포넌트는 리렌더링되지 않습니다.

### 최적화된 컴포넌트

다음 컴포넌트들은 React.memo로 최적화되었습니다:

- `PostCommentDisplay`: 댓글 표시 컴포넌트
- `PostCommentList`: 댓글 목록 컴포넌트
- `PostDisplay`: 게시글 표시 컴포넌트
- `PostDisplayFooter`: 게시글 푸터 컴포넌트
- `PostDisplayHeader`: 게시글 헤더 컴포넌트
- `PostDisplayMain`: 게시글 본문 컴포넌트
- `PostDisplayMeta`: 게시글 메타 정보 컴포넌트
- `FeedSection`: 피드 섹션 컴포넌트
- `UserPostList`: 사용자 게시글 목록 컴포넌트
- `TiptapEditor`: 에디터 컴포넌트

### 사용 방법

최적화된 컴포넌트를 사용하려면 다음과 같이 import 합니다:

```tsx
// 기존 방식
import PostDisplay from '@/components/community/feed/post-display';

// 최적화된 방식
import { PostDisplay } from '@/components/community/feed/optimized';
```

## 상태 관리 최적화

Zustand 스토어를 사용할 때 선택자 함수를 사용하여 필요한 상태만 구독하면 불필요한 리렌더링을 방지할 수 있습니다.

### 최적화 방법

1. **선택자 함수 사용**:

```tsx
// 비효율적인 방식 (전체 상태 구독)
const { todos, toggleTodo, removeTodo } = useTodoStore();

// 효율적인 방식 (필요한 상태만 구독)
const todos = useTodoStore(state => state.todos);
const toggleTodo = useTodoStore(state => state.toggleTodo);
const removeTodo = useTodoStore(state => state.removeTodo);
```

2. **useCallback과 useMemo 사용**:

```tsx
// 이벤트 핸들러 메모이제이션
const handleClick = useCallback(() => {
  // 이벤트 처리 로직
}, [dependency1, dependency2]);

// 계산 결과 메모이제이션
const filteredItems = useMemo(() => {
  return items.filter(item => item.isActive);
}, [items]);
```

## 코드 스플리팅

Next.js의 dynamic import를 사용하여 코드를 분할하면 초기 로딩 시간을 단축할 수 있습니다.

### 사용 방법

```tsx
import dynamic from 'next/dynamic';

// 동적으로 컴포넌트 가져오기
const DynamicComponent = dynamic(() => import('@/components/heavy-component'), {
  loading: () => <p>로딩 중...</p>,
  ssr: false, // 클라이언트 사이드에서만 렌더링
});

function MyComponent() {
  return (
    <div>
      <DynamicComponent />
    </div>
  );
}
```

## 이미지 최적화

Next.js의 Image 컴포넌트를 사용하여 이미지를 최적화하면 페이지 로딩 속도를 향상시킬 수 있습니다.

### 사용 방법

```tsx
import Image from 'next/image';

function MyComponent() {
  return (
    <Image
      src="/images/profile.jpg"
      alt="프로필 이미지"
      width={500}
      height={300}
      priority={false}
      loading="lazy"
    />
  );
}
```

## 번들 사이즈 최적화

webpack-bundle-analyzer를 사용하여 번들 크기를 분석하고 최적화할 수 있습니다.

### 사용 방법

```bash
# 번들 분석
pnpm analyze

# 클라이언트 번들만 분석
pnpm analyze:client

# 서버 번들만 분석
pnpm analyze:server
```

## 성능 모니터링

Core Web Vitals를 측정하고 모니터링하여 성능을 지속적으로 개선할 수 있습니다.

### 사용 방법

```tsx
// app/layout.tsx에 WebVitalsReporter 추가
import { WebVitalsReporter } from '@/components/performance/web-vitals-reporter';

export default function RootLayout({ children }) {
  return (
    <html lang="ko">
      <body>
        {children}
        <WebVitalsReporter />
      </body>
    </html>
  );
}
```

### 성능 측정 유틸리티

사용자 정의 성능 지표를 측정하려면 다음과 같이 사용합니다:

```tsx
import { startPerformanceMeasure, endPerformanceMeasure } from '@/lib/performance/web-vitals';

function MyComponent() {
  useEffect(() => {
    // 성능 측정 시작
    startPerformanceMeasure('component-render');
    
    // 작업 수행
    
    // 성능 측정 종료
    endPerformanceMeasure('component-render', { logToConsole: true });
  }, []);
  
  return <div>내 컴포넌트</div>;
}
```

## 추가 최적화 기법

- **디바운스 및 쓰로틀**: 입력 이벤트나 스크롤 이벤트와 같이 자주 발생하는 이벤트를 최적화합니다.
- **가상 스크롤**: 대량의 데이터를 표시할 때 화면에 보이는 항목만 렌더링합니다.
- **지연 로딩**: 필요할 때만 컴포넌트나 데이터를 로드합니다.
- **서버 사이드 렌더링(SSR)**: 초기 로딩 시간을 단축하고 SEO를 개선합니다.
- **정적 사이트 생성(SSG)**: 빌드 시점에 페이지를 생성하여 서버 부하를 줄이고 로딩 속도를 향상시킵니다.
