# Prettier 설정 가이드

이 프로젝트는 코드 스타일 일관성을 위해 Prettier를 사용합니다.

## 설치된 패키지

- `prettier`: 코드 포맷팅 도구
- `prettier-plugin-tailwindcss`: Tailwind CSS 클래스를 자동으로 정렬해주는 플러그인
- `eslint-config-prettier`: ESLint와 Prettier 간의 충돌을 방지하는 설정

## 설정 파일

- `.prettierrc`: Prettier 설정 파일
- `.prettierignore`: Prettier가 무시할 파일 목록

## Prettier 설정 내용

```json
{
  "semi": true, // 세미콜론 사용
  "trailingComma": "es5", // ES5에서 유효한 후행 쉼표 사용
  "singleQuote": true, // 작은따옴표 사용
  "singleAttributePerLine": true, // JSX 속성을 한 줄에 하나씩 표시
  "tabWidth": 2, // 탭 너비 2칸
  "useTabs": false, // 탭 대신 공백 사용
  "printWidth": 80, // 한 줄 최대 길이
  "quoteProps": "consistent", // 객체 속성 따옴표 일관되게 사용
  "arrowParens": "always", // 화살표 함수 매개변수 항상 괄호 사용
  "endOfLine": "auto", // 줄 끝 문자 자동 감지
  "bracketSpacing": true, // 객체 리터럴의 중괄호 사이에 공백 추가
  "jsxSingleQuote": false, // JSX에서 큰따옴표 사용
  "bracketSameLine": false, // HTML 태그의 닫는 꺾쇠를 다음 줄에 배치
  "htmlWhitespaceSensitivity": "css", // HTML 공백 처리 방식
  "embeddedLanguageFormatting": "auto", // 내장 언어 포맷팅 자동 처리
  "plugins": ["prettier-plugin-tailwindcss"] // Tailwind CSS 플러그인 사용
}
```

## 사용 방법

### 명령어

```bash
# 모든 파일 포맷팅
pnpm format

# 포맷팅 문제 확인 (수정하지 않음)
pnpm format:check
```

### VS Code 통합

VS Code에서 Prettier를 사용하려면:

1. VS Code에 [Prettier 확장 프로그램](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) 설치
2. 프로젝트의 `.vscode/settings.json` 파일에 다음 설정이 포함되어 있음:
   ```json
   {
     "editor.formatOnSave": true,
     "editor.defaultFormatter": "esbenp.prettier-vscode",
     "prettier.requireConfig": true
   }
   ```

## ESLint와 함께 사용

이 프로젝트는 ESLint와 Prettier를 함께 사용합니다:

- `eslint-config-prettier`를 사용하여 ESLint와 Prettier 간의 충돌을 방지
- VS Code 설정에서 저장 시 ESLint 자동 수정 활성화:
  ```json
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
  ```

## 주의사항

- `.prettierignore` 파일에 나열된 파일과 디렉토리는 포맷팅에서 제외됩니다.
- Tailwind CSS 클래스는 `prettier-plugin-tailwindcss` 플러그인에 의해 자동으로 정렬됩니다.
