# Next.js Third Parties Integration

이 프로젝트는 `@next/third-parties` 패키지를 사용하여 다양한 서드파티 서비스를 최적화된 방식으로 통합합니다.

## 설치

```bash
pnpm add @next/third-parties
```

## 지원되는 서비스

### Google

- **Google Analytics**

  ```tsx
  import { GoogleAnalytics } from '@next/third-parties/google';

  // 루트 레이아웃에 추가
  <GoogleAnalytics gaId="GA-MEASUREMENT-ID" />;
  ```

- **Google Tag Manager**

  ```tsx
  import { GoogleTagManager } from '@next/third-parties/google';

  // 루트 레이아웃에 추가
  <GoogleTagManager gtmId="GTM-ID" />;
  ```

- **YouTube Embed**

  ```tsx
  import { YouTubeEmbed } from '@next/third-parties/google';

  <YouTubeEmbed
    videoid="VIDEO_ID"
    height={390}
    width={640}
    params=""
    title="YouTube video player"
  />;
  ```

### Facebook

- **Facebook Pixel**

  ```tsx
  import { FacebookPixel } from '@next/third-parties/facebook';

  // 루트 레이아웃에 추가
  <FacebookPixel pixelId="PIXEL_ID" />;
  ```

### Twitter

- **Twitter Tweet Embed**

  ```tsx
  import { TwitterTweetEmbed } from '@next/third-parties/twitter';

  <TwitterTweetEmbed tweetId="TWEET_ID" />;
  ```

### Spotify

- **Spotify Embed**

  ```tsx
  import { SpotifyEmbed } from '@next/third-parties/spotify';

  <SpotifyEmbed
    spotifyId="SPOTIFY_ID"
    width="100%"
    height="352"
  />;
  ```

## 장점

1. **성능 최적화**: 서드파티 스크립트가 필요할 때만 로드되어 페이지 로딩 속도 향상
2. **표준화된 인터페이스**: 다양한 서드파티 서비스를 일관된 방식으로 통합
3. **타입 안전성**: TypeScript 지원으로 개발 시 오류 방지
4. **SEO 친화적**: 최적화된 로딩으로 Core Web Vitals 점수 향상

## 예제

전체 예제는 다음 경로에서 확인할 수 있습니다:

- `/third-parties-demo` - 기본 데모 페이지
- `/examples/third-parties` - 다양한 서드파티 통합 예제
- `/examples/kakao-map` - 카카오맵 API 통합 예제

### 카카오맵 예제

카카오맵 API를 Next.js에서 사용하는 예제입니다. 이 예제는 `next/script`를 사용하여 최적화된 방식으로 카카오맵을 로드합니다.

```tsx
// KakaoMap 컴포넌트 사용 예제
import KakaoMap from '@/components/maps/KakaoMap'

// 환경 변수에서 API 키를 가져오는 것이 좋습니다
const KAKAO_API_KEY = process.env.NEXT_PUBLIC_KAKAO_API_KEY

<KakaoMap
  apiKey={KAKAO_API_KEY}
  latitude={37.5665}
  longitude={126.9780}
  level={3}
  height={400}
/>
```

## 참고 문서

- [Next.js Third Parties 공식 문서](https://nextjs.org/docs/app/building-your-application/optimizing/third-party-libraries)
