# Tiptap Editor Integration

이 프로젝트는 [Tiptap](https://tiptap.dev/)을 사용하여 강력한 WYSIWYG 에디터를 제공합니다. Tiptap은 ProseMirror 기반의 모듈식 에디터 프레임워크입니다.

## 설치

```bash
pnpm add @tiptap/react @tiptap/pm @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-link @tiptap/extension-placeholder @tiptap/extension-underline @tiptap/extension-text-align @tiptap/extension-color
```

## 기본 사용법

```tsx
import TiptapEditor from '@/components/editor/TiptapEditor'

// 기본 사용법
<TiptapEditor
  content="<p>초기 콘텐츠</p>"
  onChange={(html) => console.log(html)}
  placeholder="내용을 입력하세요..."
/>

// useEditorState 훅 사용
const { content, handleChange } = useEditorState()
<TiptapEditor
  content={content}
  onChange={handleChange}
/>

// 콘텐츠 표시
<div dangerouslySetInnerHTML={{ __html: content }} />
```

## 컴포넌트 구조

프로젝트에는 다음과 같은 Tiptap 관련 컴포넌트가 포함되어 있습니다:

1. **TiptapEditor.tsx** - 메인 에디터 컴포넌트
2. **EditorMenuBar.tsx** - 에디터 도구 모음 컴포넌트
3. **editor.css** - 에디터 스타일링

## 지원 기능

현재 구현된 에디터는 다음 기능을 지원합니다:

- **텍스트 서식**: 굵게, 기울임꼴, 밑줄, 취소선
- **제목**: H1, H2, H3
- **목록**: 순서 있는 목록, 순서 없는 목록
- **텍스트 정렬**: 왼쪽, 가운데, 오른쪽, 양쪽 정렬
- **링크**: URL 삽입 및 편집
- **이미지**: URL을 통한 이미지 삽입
- **코드 블록**: 코드 블록 및 인라인 코드
- **인용구**: 블록 인용구
- **텍스트 색상**: 다양한 색상 선택
- **실행 취소/다시 실행**: 변경 사항 관리

## 확장 방법

Tiptap은 확장 가능한 아키텍처를 가지고 있어 필요에 따라 기능을 추가할 수 있습니다:

```tsx
// 추가 확장 기능 설치
// pnpm add @tiptap/extension-table @tiptap/extension-highlight

import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Highlight from '@tiptap/extension-highlight';

// 에디터 확장 기능 추가
const editor = useEditor({
  extensions: [
    StarterKit,
    // 기존 확장 기능...

    // 테이블 지원 추가
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableCell,
    TableHeader,

    // 하이라이트 기능 추가
    Highlight,
  ],
  // ...
});
```

## 예제

전체 예제는 다음 경로에서 확인할 수 있습니다:

- `/examples/tiptap-editor` - Tiptap 에디터 데모 페이지

## 참고 문서

- [Tiptap 공식 문서](https://tiptap.dev/docs/editor/introduction)
- [ProseMirror](https://prosemirror.net/)
- [Tiptap React 예제](https://tiptap.dev/docs/editor/react)
