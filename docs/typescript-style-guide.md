# TypeScript 스타일 가이드

이 문서는 Sodamm 프로젝트의 TypeScript 코드 작성 스타일 가이드입니다. 일관된 코드 스타일과 타입 시스템을 유지하기 위해 이 가이드를 따라주세요.

## 목차

1. [명명 규칙](#1-명명-규칙)
2. [타입 정의 구조](#2-타입-정의-구조)
3. [타입 가드 및 단언](#3-타입-가드-및-단언)
4. [제네릭 타입](#4-제네릭-타입)
5. [문서화](#5-문서화)
6. [Zod 스키마](#6-zod-스키마)

## 1. 명명 규칙

### 1.1 인터페이스 (Interface)

- 인터페이스 이름은 `I` 접두사를 사용합니다.
- 명사 또는 명사구를 사용합니다.
- PascalCase를 사용합니다.

```typescript
// 좋은 예
export interface IUserProfile {
  id: string;
  name: string;
  email: string;
}

// 나쁜 예
export interface userProfile {
  id: string;
  name: string;
  email: string;
}
```

### 1.2 타입 별칭 (Type Alias)

- 타입 별칭 이름은 `T` 접두사를 사용합니다.
- PascalCase를 사용합니다.

```typescript
// 좋은 예
export type TUserRole = 'admin' | 'user' | 'guest';

// 나쁜 예
export type UserRole = 'admin' | 'user' | 'guest';
```

### 1.3 열거형 (Enum)

- 열거형 이름은 `E` 접두사를 사용합니다.
- PascalCase를 사용합니다.

```typescript
// 좋은 예
export enum EUserStatus {
  Active = 'active',
  Inactive = 'inactive',
  Suspended = 'suspended'
}

// 나쁜 예
export enum UserStatus {
  Active = 'active',
  Inactive = 'inactive',
  Suspended = 'suspended'
}
```

### 1.4 타입 매개변수 (Type Parameters)

- 단일 문자 또는 의미 있는 이름을 사용합니다.
- PascalCase를 사용합니다.

```typescript
// 단일 문자 사용 (간단한 경우)
function identity<T>(value: T): T {
  return value;
}

// 의미 있는 이름 사용 (복잡한 경우)
function createPair<TFirst, TSecond>(first: TFirst, second: TSecond): [TFirst, TSecond] {
  return [first, second];
}
```

## 2. 타입 정의 구조

### 2.1 디렉토리 구조

타입 정의는 다음과 같은 디렉토리 구조를 따릅니다:

```
src/types/
├── index.ts                # 모든 타입 배럴 내보내기
├── common/                 # 공통 타입 정의
│   ├── index.ts            # 공통 타입 배럴 내보내기
│   ├── utility.ts          # 유틸리티 타입
│   └── pagination.ts       # 페이지네이션 관련 타입
├── api/                    # API 관련 타입
│   ├── index.ts            # API 타입 배럴 내보내기
│   ├── response.ts         # API 응답 타입
│   ├── request.ts          # API 요청 타입
│   └── error.ts            # API 에러 타입
├── user/                   # 사용자 관련 타입
│   ├── index.ts            # 사용자 타입 배럴 내보내기
│   └── auth.ts             # 인증 관련 타입
└── ...                     # 기타 도메인별 타입
```

### 2.2 배럴 내보내기 (Barrel Exports)

각 디렉토리에는 `index.ts` 파일을 두어 배럴 내보내기를 구현합니다:

```typescript
// src/types/user/index.ts
export * from './auth';
export * from './profile';
```

### 2.3 타입 확장 및 합성

타입 확장과 합성을 활용하여 중복을 줄입니다:

```typescript
// 확장 (extends)
export interface IBaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUser extends IBaseEntity {
  name: string;
  email: string;
}

// 합성 (intersection)
export type TUserWithRoles = IUser & {
  roles: string[];
};

// 유틸리티 타입 활용
export type TUserSummary = Pick<IUser, 'id' | 'name'>;
```

## 3. 타입 가드 및 단언

### 3.1 타입 가드 사용

타입 단언(`as Type`) 대신 타입 가드(`is Type`)를 사용합니다:

```typescript
// 좋은 예: 타입 가드 함수 사용
export function isUser(obj: any): obj is IUser {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string'
  );
}

// 사용 예
if (isUser(data)) {
  // 여기서 data는 IUser 타입으로 처리됨
  console.log(data.name);
}

// 나쁜 예: 타입 단언 사용
const user = data as IUser;
console.log(user.name); // 런타임 에러 가능성 있음
```

### 3.2 조건부 타입 체크

타입 단언 대신 조건부 타입 체크를 사용합니다:

```typescript
// 좋은 예
const id = typeof user.id === 'string' ? user.id : '';

// 나쁜 예
const id = user.id as string;
```

## 4. 제네릭 타입

### 4.1 제네릭 함수

함수가 여러 타입에서 작동할 수 있도록 제네릭을 사용합니다:

```typescript
export function getFirstItem<T>(array: T[]): T | undefined {
  return array.length > 0 ? array[0] : undefined;
}

// 사용 예
const firstUser = getFirstItem<IUser>(users);
const firstNumber = getFirstItem<number>([1, 2, 3]);
```

### 4.2 제네릭 인터페이스

재사용 가능한 인터페이스를 위해 제네릭을 사용합니다:

```typescript
export interface IApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// 사용 예
const userResponse: IApiResponse<IUser> = {
  data: { id: '1', name: 'John' },
  status: 200,
  message: 'Success'
};
```

### 4.3 제네릭 제약 조건

제네릭 타입에 제약 조건을 추가하여 타입 안전성을 높입니다:

```typescript
export interface IWithId {
  id: string;
}

export function findById<T extends IWithId>(items: T[], id: string): T | undefined {
  return items.find(item => item.id === id);
}
```

## 5. 문서화

### 5.1 JSDoc 주석

모든 타입 정의에 JSDoc 주석을 추가합니다:

```typescript
/**
 * 사용자 인터페이스
 * 
 * 사용자의 기본 정보를 정의합니다.
 */
export interface IUser {
  /** 사용자 고유 ID */
  id: string;
  /** 사용자 이름 */
  name: string;
  /** 사용자 이메일 */
  email: string;
}
```

### 5.2 복잡한 타입에 대한 예제

복잡한 타입에는 사용 예제를 추가합니다:

```typescript
/**
 * 페이지네이션 파라미터 인터페이스
 * 
 * @example
 * ```typescript
 * const params: IPaginationParams = {
 *   page: 1,
 *   size: 10,
 *   sort: 'createdAt',
 *   order: 'desc'
 * };
 * ```
 */
export interface IPaginationParams {
  /** 페이지 번호 */
  page?: number;
  /** 페이지 크기 */
  size?: number;
  /** 정렬 기준 */
  sort?: string;
  /** 정렬 방향 */
  order?: 'asc' | 'desc';
}
```

## 6. Zod 스키마

### 6.1 Zod 스키마 정의

Zod 스키마는 관련 타입 정의와 함께 배치합니다:

```typescript
import { z } from 'zod';

/**
 * 사용자 생성 입력 인터페이스
 */
export interface ICreateUserInput {
  /** 사용자 이메일 */
  email: string;
  /** 사용자 이름 */
  name: string;
  /** 사용자 비밀번호 */
  password: string;
}

/**
 * 사용자 생성 입력 Zod 스키마
 */
export const createUserSchema = z.object({
  email: z.string().email('유효한 이메일 주소를 입력해주세요.'),
  name: z.string().min(2, '이름은 2자 이상이어야 합니다.'),
  password: z.string().min(8, '비밀번호는 8자 이상이어야 합니다.'),
});

/**
 * Zod 스키마에서 타입 추론
 */
export type TCreateUserInput = z.infer<typeof createUserSchema>;
```

### 6.2 Zod 스키마 재사용

스키마 재사용을 위해 부분 스키마를 정의합니다:

```typescript
// 기본 사용자 스키마
const userBaseSchema = z.object({
  email: z.string().email(),
  name: z.string().min(2),
});

// 생성 스키마
export const createUserSchema = userBaseSchema.extend({
  password: z.string().min(8),
});

// 업데이트 스키마
export const updateUserSchema = userBaseSchema
  .partial() // 모든 필드를 선택적으로 만듦
  .extend({
    id: z.string().uuid(),
  });
```
