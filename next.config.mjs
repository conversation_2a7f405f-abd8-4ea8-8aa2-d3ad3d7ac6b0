import createNextIntlPlugin from 'next-intl/plugin';

/**
 * 번들 분석기 설정
 * ANALYZE=true 환경 변수가 설정된 경우에만 활성화됩니다.
 */
const withBundleAnalyzer = (() => {
  try {
    // 동적으로 번들 분석기 가져오기 시도
    const analyzer = require('@next/bundle-analyzer');
    return analyzer({
      enabled: process.env.ANALYZE === 'true',
    });
  } catch (e) {
    // 번들 분석기가 설치되지 않은 경우 기본 설정 반환
    return (config) => config;
  }
})();

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  // Edge Runtime 관련 설정
  serverExternalPackages: ['@prisma/client', 'bcrypt'],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // 트리 쉐이킹 최적화
  // swcMinify: true, // Next.js 15에서는 기본값이미로 삭제
  // 번들 크기 최적화
  compiler: {
    // 프로덕션 빌드에서 console.log, console.info 등 제거
    removeConsole:
      process.env.NODE_ENV === 'production'
        ? {
            exclude: ['error', 'warn'],
          }
        : false,
  },
};

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');
// 번들 분석기와 Next-Intl 플러그인 함께 적용
export default withBundleAnalyzer(withNextIntl(nextConfig));
