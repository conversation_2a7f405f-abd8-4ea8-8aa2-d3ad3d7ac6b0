import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const nextConfig: NextConfig = {
  /* config options here */

  // 이미지 최적화 설정
  images: {
    domains: [
      "sodamm.com",
      "sodamm.vercel.app",
      "127.0.0.1",
      "localhost",
      "supabase.co",
      "supabase.in",
      "supabase.com",
    ],
    formats: ["image/avif", "image/webp"],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 캐시 헤더 설정
  async headers() {
    return [
      {
        // 정적 자산에 대한 캐시 헤더 설정
        source: "/(images|fonts|icons)/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        // CSS 및 JS 파일에 대한 캐시 헤더 설정
        source: "/_next/static/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        // API 경로에 대한 캐시 헤더 설정
        source: "/api/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "no-store, max-age=0",
          },
        ],
      },
    ];
  },
};

// Sentry 웹팩 플러그인 설정
const sentryWebpackPluginOptions = {
  // 추가 옵션은 여기에 설정
  silent: true, // 빌드 중 Sentry 출력 숨기기
};

// next-intl 플러그인 생성
const withNextIntl = createNextIntlPlugin();

// Sentry 설정으로 Next.js 설정 감싸기
export default withSentryConfig(
  withNextIntl(nextConfig),
  sentryWebpackPluginOptions
);
