datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  output   = "../src/lib/prisma/generated/client"
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
  @@map("verification_tokens")
}

// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
  @@map("authenticators")
}

model User {
  id            String    @id @default(cuid())
  name          String    @unique @db.VarChar(255)
  email         String?   @unique @db.VarChar(255)
  emailVerified DateTime? @map("email_verified")
  image String?   @db.Text @map("image")
  displayName   String?   @map("display_name") @db.VarChar(255)
  bio           String?   @db.Text
  step Int       @default(0) @map("level")
  postCount        Int       @default(0) @map("post_count")
  postCommentCount Int       @default(0) @map("post_comment_count")
  likeCount         Int       @default(0) @map("like_count")
  isTester          Boolean   @default(false) @map("is_tester")
  isAdmin           Boolean   @default(false) @map("is_admin")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  accounts      Account[]
  authenticators Authenticator[]
  sessions      Session[]

  posts         Post[]
  postComments PostComment[]

  @@map("users")
}

model Post {
  id String @id @default(cuid())
  type String @default("NORMAL") @db.VarChar(32)
  country String @default("kr") @db.VarChar(32)
  userId String @map("user_id")
  content String @db.Text
  contentHtml String @db.Text @map("content_html") @default("<p></p>")
  preview Json? @db.Json
  tags String[] @db.Text
  commentCount Int @default(0) @map("comment_count")
  likeCount Int @default(0) @map("like_count")
  viewCount Int @default(0) @map("view_count")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  postComments PostComment[]
  postLikes PostLike[]
  postTags PostTag[]

  @@index([type])
  @@index([userId])

  @@map("posts")
}

model PostComment {
  id Int @id @default(autoincrement())
  userId String @map("user_id")
  postId String @map("post_id")
  content String @db.Text
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([postId])

  @@map("post_comments")
}

model PostLike {
  id Int @id @default(autoincrement())
  userId String @map("user_id")
  postId String @map("post_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@index([userId])
  @@index([postId])
  @@map("post_likes")
}

model Tag {
  id Int @id @default(autoincrement())
  name String @unique @db.VarChar(64)
  useCount Int @default(0) @map("use_count")
  isSearchable Boolean @default(false) @map("is_searchable")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  postTags PostTag[]

  @@index([name])
  @@map("tags")
}

model PostTag {
  id Int @id @default(autoincrement())
  postId String @map("post_id")
  tagId Int @map("tag_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag Tag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([postId, tagId])
  @@index([postId])
  @@index([tagId])
  @@map("post_tags")
}

model News {
  id String @id @default(cuid())
  title String @db.Text
  content String? @db.Text
  contentHtml String? @db.Text @map("content_html") @default("<p></p>")
  link String @db.Text
  publishedAt DateTime? @map("published_at")
  viewCount Int @default(0) @map("view_count")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("news")
}

model Country {
  id Int @id @default(autoincrement())
  name String @db.VarChar(32)
  code String @db.VarChar(32) @unique
  displayName String @db.VarChar(255) @map("display_name")
  imageUrl String? @db.Text @map("image_url")
  enabled Boolean @default(true) @map("enabled")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("countries")
}