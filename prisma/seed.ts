import { PrismaClient } from '../src/lib/prisma/generated/client';

// 하드코딩된 국가 데이터 (이전 constants/countries.ts에서 가져온 데이터)
const countriesData = [
  { code: '', name: '전체', displayName: '전체', enabled: true },
  { code: 'kr', name: '한국', displayName: '한국', enabled: true },
  // 현재 활성화된 국가들
  { code: 'vn', name: '베트남', displayName: 'Việt Nam', enabled: true },
  { code: 'th', name: '태국', displayName: 'ประเทศไทย', enabled: true },
  { code: 'ph', name: '필리핀', displayName: 'Pilipinas', enabled: true },
  { code: 'mn', name: '몽골', displayName: 'Монгол', enabled: true },

  // 추가된 국가들 (한국 거주 외국인 국적 고려)
  { code: 'cn', name: '중국', displayName: '中国', enabled: true }, // 중국 (China)
  { code: 'us', name: '미국', displayName: 'United States', enabled: true }, // 미국 (USA)
  { code: 'jp', name: '일본', displayName: '日本', enabled: true }, // 일본 (Japan)
  { code: 'ru', name: '러시아', displayName: 'Россия', enabled: true }, // 러시아 (Russia)
  { code: 'tw', name: '대만', displayName: '臺灣', enabled: true }, // 대만 (Taiwan)

  // 기존 비활성화 국가들
  {
    code: 'uz',
    name: '우즈베키스탄',
    displayName: "O'zbekiston",
    enabled: true,
  },
  { code: 'np', name: '네팔', displayName: 'नेपाल', enabled: true },
  {
    code: 'id',
    name: '인도네시아',
    displayName: 'Indonesia',
    enabled: false,
  },
  { code: 'kh', name: '캄보디아', displayName: 'កម្ពុជា', enabled: true },
  { code: 'mm', name: '미얀마', displayName: 'မြန်မာ', enabled: true },
  {
    code: 'kz',
    name: '카자흐스탄',
    displayName: 'Қазақстан',
    enabled: true,
  },
  {
    code: 'lk',
    name: '스리랑카',
    displayName: 'ශ්‍රී ලංකාව',
    enabled: true,
  },
  {
    code: 'bd',
    name: '방글라데시',
    displayName: 'বাংলাদেশ',
    enabled: true,
  },
  { code: 'pk', name: '파키스탄', displayName: 'پاکستان', enabled: true },
  { code: 'in', name: '인도', displayName: 'भारत', enabled: true },
  {
    code: 'my',
    name: '말레이시아',
    displayName: 'Malaysia',
    enabled: true,
  },
  { code: 'la', name: '라오스', displayName: 'ລາວ', enabled: true },
  {
    code: 'kg',
    name: '키르기스스탄',
    displayName: 'Кыргызстан',
    enabled: true,
  },
  {
    code: 'tr',
    name: '튀르키예(터키)',
    displayName: 'Türkiye',
    enabled: true,
  },
];

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding countries...');

  // 기존 국가 데이터 삭제 (선택 사항)
  await prisma.country.deleteMany({});

  // 하드코딩된 데이터로 국가 테이블 채우기
  for (const country of countriesData) {
    if (country.code !== '') {
      // '전체' 옵션은 DB에 저장하지 않음
      await prisma.country.create({
        data: {
          code: country.code.toLowerCase(), // 코드는 소문자로 저장
          name: country.name,
          displayName: country.displayName,
          enabled: country.enabled,
        },
      });
    }
  }

  console.log('Countries seeding completed!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
