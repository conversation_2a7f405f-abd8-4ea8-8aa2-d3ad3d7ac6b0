#!/bin/bash

# 환경 변수 로드
source .env

# 백업 파일 이름 설정
BACKUP_DATE=$(date +%Y-%m-%d-%H-%M-%S)
BACKUP_FILE="database-backup-$BACKUP_DATE.sql"

# 백업 디렉토리 생성
BACKUP_DIR="./backups/database"
mkdir -p $BACKUP_DIR

# PostgreSQL 데이터베이스 덤프
echo "Backing up database to $BACKUP_DIR/$BACKUP_FILE..."
pg_dump $DATABASE_URL > "$BACKUP_DIR/$BACKUP_FILE"

# 백업 파일 압축
echo "Compressing backup file..."
gzip "$BACKUP_DIR/$BACKUP_FILE"

# 백업 파일을 S3에 업로드 (선택 사항)
if [ -n "$AWS_S3_BUCKET" ]; then
  echo "Uploading backup file to S3..."
  aws s3 cp "$BACKUP_DIR/$BACKUP_FILE.gz" "s3://$AWS_S3_BUCKET/database-backups/$BACKUP_FILE.gz"
fi

# 오래된 백업 파일 정리 (30일 이상 된 파일)
echo "Cleaning up old backup files..."
find $BACKUP_DIR -name "*.gz" -type f -mtime +30 -delete

echo "Database backup completed: $BACKUP_FILE.gz"
