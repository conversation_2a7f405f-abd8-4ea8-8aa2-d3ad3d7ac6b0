#!/bin/bash

# 환경 변수 로드
source .env

# 백업 파일 이름 설정
BACKUP_DATE=$(date +%Y-%m-%d-%H-%M-%S)
BACKUP_DIR="./backups/storage"
mkdir -p $BACKUP_DIR

# Supabase Storage에서 파일 다운로드
echo "Downloading files from Supabase Storage..."
supabase storage download --project-ref $SUPABASE_PROJECT_ID --bucket $SUPABASE_BUCKET --output-dir "$BACKUP_DIR/$BACKUP_DATE"

# 백업 파일 압축
echo "Compressing backup files..."
tar -czf "$BACKUP_DIR/storage-backup-$BACKUP_DATE.tar.gz" -C "$BACKUP_DIR" "$BACKUP_DATE"

# 압축 후 원본 파일 삭제
echo "Cleaning up temporary files..."
rm -rf "$BACKUP_DIR/$BACKUP_DATE"

# 백업 파일을 S3에 업로드 (선택 사항)
if [ -n "$AWS_S3_BUCKET" ]; then
  echo "Uploading backup file to S3..."
  aws s3 cp "$BACKUP_DIR/storage-backup-$BACKUP_DATE.tar.gz" "s3://$AWS_S3_BUCKET/storage-backups/storage-backup-$BACKUP_DATE.tar.gz"
fi

# 오래된 백업 파일 정리 (30일 이상 된 파일)
echo "Cleaning up old backup files..."
find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +30 -delete

echo "Storage backup completed: storage-backup-$BACKUP_DATE.tar.gz"
