import { initializeBuckets, StorageBucket, BUCKET_CONFIGS } from '../src/lib/storage';

/**
 * Supabase Storage 초기화 스크립트
 * 
 * 이 스크립트는 Supabase Storage에 필요한 버킷을 생성합니다.
 * 
 * 실행 방법:
 * ```
 * npx tsx scripts/init-storage.ts
 * ```
 */
async function main() {
  console.log('Supabase Storage 초기화 중...');
  
  try {
    // 모든 버킷 초기화
    const success = await initializeBuckets();
    
    if (success) {
      console.log('Supabase Storage 초기화 완료!');
      console.log('\n생성된 버킷 목록:');
      
      // 버킷 목록 출력
      Object.entries(BUCKET_CONFIGS).forEach(([bucket, config]) => {
        console.log(`- ${bucket}: ${config.description} (공개: ${config.isPublic ? '예' : '아니오'})`);
      });
    } else {
      console.error('일부 버킷 생성에 실패했습니다.');
    }
  } catch (error) {
    console.error('Supabase Storage 초기화 중 오류 발생:', error);
    process.exit(1);
  }
}

main();
