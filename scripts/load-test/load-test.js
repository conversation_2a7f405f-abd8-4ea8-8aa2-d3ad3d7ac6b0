import http from 'k6/http';
import { sleep, check } from 'k6';
import { Counter } from 'k6/metrics';

// 사용자 정의 메트릭
const successfulLogins = new Counter('successful_logins');
const failedLogins = new Counter('failed_logins');

// 테스트 설정
export const options = {
  // 기본 테스트 설정
  vus: 10, // 가상 사용자 수
  duration: '30s', // 테스트 지속 시간
  
  // 단계별 부하 테스트 설정
  stages: [
    { duration: '1m', target: 10 }, // 1분 동안 10명의 사용자로 증가
    { duration: '3m', target: 50 }, // 3분 동안 50명의 사용자로 증가
    { duration: '1m', target: 100 }, // 1분 동안 100명의 사용자로 증가
    { duration: '2m', target: 100 }, // 2분 동안 100명의 사용자 유지
    { duration: '1m', target: 0 }, // 1분 동안 0명의 사용자로 감소
  ],
  
  // 임계값 설정
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%의 요청이 500ms 이내에 완료되어야 함
    http_req_failed: ['rate<0.1'], // 실패율이 10% 미만이어야 함
    successful_logins: ['count>10'], // 성공적인 로그인이 10회 이상이어야 함
  },
};

// 테스트 실행 함수
export default function () {
  // 홈페이지 로드
  const homeRes = http.get('https://sodamm.com/');
  check(homeRes, {
    'homepage status is 200': (r) => r.status === 200,
    'homepage has correct title': (r) => r.body.includes('Sodamm'),
  });
  sleep(1);
  
  // 게시물 목록 로드
  const postsRes = http.get('https://sodamm.com/api/posts');
  check(postsRes, {
    'posts status is 200': (r) => r.status === 200,
    'posts response is JSON': (r) => r.headers['Content-Type'].includes('application/json'),
  });
  sleep(1);
  
  // 로그인 테스트
  const loginPayload = JSON.stringify({
    email: '<EMAIL>',
    password: 'password',
  });
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  const loginRes = http.post('https://sodamm.com/api/auth/login', loginPayload, params);
  
  if (loginRes.status === 200) {
    successfulLogins.add(1);
    
    // 인증이 필요한 API 호출
    const authToken = JSON.parse(loginRes.body).token;
    const authParams = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    };
    
    // 사용자 프로필 로드
    const profileRes = http.get('https://sodamm.com/api/profile', authParams);
    check(profileRes, {
      'profile status is 200': (r) => r.status === 200,
      'profile response is JSON': (r) => r.headers['Content-Type'].includes('application/json'),
    });
    sleep(1);
    
    // 게시물 생성
    const postPayload = JSON.stringify({
      title: 'Test Post',
      content: 'This is a test post created by load test.',
    });
    
    const createPostRes = http.post('https://sodamm.com/api/posts', postPayload, authParams);
    check(createPostRes, {
      'create post status is 201': (r) => r.status === 201,
      'create post response is JSON': (r) => r.headers['Content-Type'].includes('application/json'),
    });
    sleep(1);
  } else {
    failedLogins.add(1);
  }
  
  // 검색 테스트
  const searchRes = http.get('https://sodamm.com/api/search?q=test');
  check(searchRes, {
    'search status is 200': (r) => r.status === 200,
    'search response is JSON': (r) => r.headers['Content-Type'].includes('application/json'),
  });
  sleep(1);
}
