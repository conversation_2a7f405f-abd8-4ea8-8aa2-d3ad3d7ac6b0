/**
 * 이 스크립트는 기존 Post 테이블의 country 필드 값을 모두 소문자로 변환합니다.
 * 
 * 실행 방법:
 * 1. 터미널에서 다음 명령어를 실행합니다:
 *    npx ts-node scripts/migrate-country-to-lowercase.ts
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateCountryToLowercase() {
  try {
    console.log('기존 Post 테이블의 country 필드 값을 소문자로 변환 중...');

    // 모든 포스트 조회
    const posts = await prisma.post.findMany({
      select: {
        id: true,
        country: true,
      },
    });

    console.log(`총 ${posts.length}개의 포스트를 찾았습니다.`);

    // 대문자가 포함된 country 값을 가진 포스트 필터링
    const postsToUpdate = posts.filter(
      (post) => post.country !== post.country.toLowerCase()
    );

    console.log(`${postsToUpdate.length}개의 포스트를 업데이트해야 합니다.`);

    // 각 포스트의 country 값을 소문자로 변환
    for (const post of postsToUpdate) {
      await prisma.post.update({
        where: { id: post.id },
        data: { country: post.country.toLowerCase() },
      });
      console.log(`포스트 ID ${post.id}의 country 값을 ${post.country.toLowerCase()}로 변환했습니다.`);
    }

    console.log('모든 포스트의 country 값이 소문자로 변환되었습니다.');
  } catch (error) {
    console.error('마이그레이션 중 오류가 발생했습니다:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateCountryToLowercase();
