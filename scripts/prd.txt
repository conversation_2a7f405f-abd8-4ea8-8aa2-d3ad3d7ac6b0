<context>
# 참고사항
1. task-manager ai로 task 생성시 한글로 작성한다
2. 새로운 task(또는 sub task) 시작할 때 항상 현재 task에 대한 설명을 먼저한다.
3. 에러가 발생하면 에러를 해결하고 다음 단계로 넘어간다.
4. UI는 직접 구현하지 말고 shadcn-ui를 우선적으로 사용한다

# 개요
소담(SODAMM)은 한국에 거주하는 외국인 근로자들이 한국 생활에 더 쉽고 빠르게 적응할 수 있도록 지원하는 온라인 커뮤니티 플랫폼입니다.
생활 정보, 한국 공공 서비스 및 편의 서비스 이용 안내, 정부 제공 외국인 근로자 지원 정보, 구인·구직 정보 등을 통합적으로 제공하며,
사용자 간의 자유로운 정보 교류 및 소통 공간을 마련하여 외국인 근로자들의 성공적인 한국 생활 정착을 돕는 것을 목표로 합니다.

# 핵심 기능
1. 생활 정보 통합 제공
- 기능 설명: 한국 생활에 필수적인 정보(교통, 주거, 음식, 문화, 병원 이용 등)를 다양한 카테고리로 분류하여 제공합니다.
- 중요성: 외국인 근로자들이 초기 정착 시 겪는 정보 부족의 어려움을 해소하고, 생활 편의성을 증진시킵니다.
- 작동 방식: 관리자가 신뢰할 수 있는 출처의 정보를 취합하여 콘텐츠를 생성하고, 주제별/지역별 필터링 기능을 통해 사용자가 원하는 정보를 쉽게 찾을 수 있도록 합니다. 사용자 생성 콘텐츠(팁, 후기)도 추가될 수 있습니다.

2. 한국 서비스 이용 가이드
- 기능 설명: 은행 계좌 개설, 휴대폰 개통, 공과금 납부, 온라인 쇼핑몰 이용 등 한국의 주요 서비스 이용 방법 및 절차를 상세히 안내합니다.
- 중요성: 언어 및 문화적 차이로 인해 서비스 이용에 어려움을 느끼는 외국인 근로자들의 불편을 줄여줍니다.
- 작동 방식: 단계별 가이드, 스크린샷, 필요 서류 목록, 자주 묻는 질문(FAQ) 등을 포함한 콘텐츠를 제공합니다. 필요시 관련 기관/기업의 공식 안내 링크를 첨부합니다.

3. 외국인 근로자 대상 정부 정보 제공
- 기능 설명: 비자, 체류, 고용허가제, 산업안전, 법률 지원, 한국어 교육 등 외국인 근로자에게 필요한 정부 부처 및 공공기관의 공식 정보를 한 곳에 모아 제공합니다.
- 중요성: 흩어져 있고 찾기 어려운 정부 정보를 쉽게 접근하도록 하여 외국인 근로자들이 자신의 권익을 보호하고 의무를 이행하는 데 도움을 줍니다.
- 작동 방식: 정부 부처 웹사이트, 공공기관 자료 등을 주기적으로 모니터링하여 최신 정보를 업데이트하고, 다국어 번역(필요시 요약 번역)과 함께 명확하게 전달합니다. 중요 공지사항은 푸시 알림 등으로 알릴 수 있습니다.

4. 구인·구직 정보 공유
- 기능 설명: 외국인 근로자 채용을 희망하는 구인 정보와 일자리를 찾는 외국인 근로자들의 구직 정보를 게시하고 검색할 수 있는 기능을 제공합니다.
- 중요성: 외국인 근로자들의 취업 및 이직 활동을 지원하고, 구인 기업에게는 적합한 인재를 찾을 기회를 제공합니다.
- 작동 방식: 기업회원/개인회원 구분하여 게시글 작성 권한을 부여하고, 업종, 직무, 지역, 근무 조건 등으로 상세 검색 기능을 제공합니다. 이력서 및 포트폴리오 업로드 기능을 지원할 수 있습니다.

5. 사용자 커뮤니티 (정보 공유 및 Q&A)
- 기능 설명: 사용자들이 자유롭게 질문하고 답변하며, 생활 팁, 경험담 등 다양한 정보를 공유할 수 있는 게시판 또는 포럼 형태의 커뮤니티 기능을 제공합니다.
- 중요성: 사용자 간의 상호 도움을 통해 정보의 폭을 넓히고, 정서적 유대감을 형성하여 한국 생활 적응에 긍정적인 영향을 줍니다.
- 작동 방식: 주제별 게시판 운영, 익명/실명 글쓰기 옵션 제공, 댓글 및 추천 기능, 사용자 신고 및 관리자 필터링 기능을 통해 건전한 커뮤니티 환경을 조성합니다.

# 사용자 경험
사용자 페르소나:
- 아흐메드 (20대 후반, 제조업 종사, 한국 생활 6개월 차): 한국어가 아직 서툴러 은행 업무나 공공기관 방문 시 어려움을 겪음. 비슷한 처지의 동료들과 정보를 나누고 싶어 함. 주말에 할 수 있는 활동이나 저렴한 맛집 정보에 관심이 많음.
- 마리아 (30대 초반, 농업 종사, 한국 생활 3년 차): 기본적인 한국 생활에는 익숙하지만, 비자 연장이나 법률 문제 등 전문적인 정보가 필요할 때가 있음. 새로 온 동료들에게 자신의 경험을 나눠주고 도움을 주고 싶어 함.
- 김철수 사장 (40대 중반, 중소기업 운영): 외국인 근로자 고용 경험은 있으나, 채용 절차나 지원 정책에 대해 항상 최신 정보를 얻고 싶어 함. 성실한 외국인 근로자를 채용하기 위한 구인 정보 게시를 원함.
- 이하나 (20대 초반, 다문화센터 직원): 담당 지역 외국인 근로자들에게 유용한 정보를 한 곳에서 쉽게 찾아 안내해주고 싶어 함. 정부 지원 프로그램이나 문화 행사 정보를 얻고자 함.

주요 사용자 흐름:
- 신규 외국인 근로자의 정보 검색 흐름:
1. 회원 가입 및 로그인 (소셜 로그인 옵션 제공)
2. 메인 화면에서 카테고리별 정보 탐색 (생활 정보, 서비스 이용 가이드 등)
3. 키워드 검색을 통해 원하는 정보 검색 (예: "핸드폰 개통 방법")
4. 검색 결과 확인 및 상세 정보 조회
5. 유용한 정보 북마크 또는 저장

- 질문 게시 및 답변 확인 흐름:
1. 커뮤니티 섹션 접속
2. 질문 게시판 선택
3. 글쓰기 버튼 클릭 후 질문 내용 작성 (필요시 사진/파일 첨부)
4. 게시글 등록
5. 다른 사용자들의 답변 알림 수신 (또는 주기적 확인)
6. 답변 확인 및 추가 질문/감사 표시

- 구인 정보 등록 흐름 (기업 회원):
1. 기업 회원으로 로그인
2. 구인 정보 메뉴 선택
3. 구인 공고 등록 버튼 클릭
4. 채용 분야, 직무 내용, 자격 요건, 급여, 근무 조건 등 상세 정보 입력
5. 공고 미리보기 및 게시

- 구직 정보 열람 및 지원 흐름 (외국인 근로자):
1. 구직 정보 메뉴 선택
2. 지역, 업종 등 조건 설정 후 검색
3. 관심 있는 구인 공고 클릭하여 상세 내용 확인
4. (지원 기능 구현 시) 온라인 지원 또는 연락처 확인 후 직접 연락

UI/UX 고려사항:
- 다국어 지원: 초기에는 한국어 및 주요 외국인 근로자 사용 언어(예: 한국어, 영어 등) 지원 후 점진적 확대. 사용자가 쉽게 언어 설정을 변경할 수 있어야 함.
- 직관적인 네비게이션: 정보 구조를 명확히 하고, 사용자가 원하는 정보에 최소한의 클릭으로 도달할 수 있도록 설계. 아이콘과 명확한 레이블 사용.
- 모바일 최적화: 대부분의 외국인 근로자들이 스마트폰을 주로 사용할 것을 고려하여 반응형 웹 또는 모바일 앱 우선 개발.
- 가독성 높은 디자인: 명확한 폰트, 적절한 글자 크기 및 줄 간격 사용. 문화적 배경을 고려한 색상 및 이미지 사용.
- 쉬운 정보 입력: 게시글 작성, 댓글 입력 등을 위한 인터페이스는 최대한 단순하고 사용하기 쉽게 구현.
- 접근성: 웹 접근성 표준을 준수하여 정보 취약 계층도 쉽게 이용할 수 있도록 고려.
- 푸시 알림: 중요한 정부 공지, 답변 알림, 관심 채용 정보 알림 등 사용자에게 유용한 정보를 시의적절하게 전달.

</context>

<PRD>
# task-manager ai로 task 생성시 한글로 작성한다
# task, sub-task가 끝날때마다 관련 작업내용을 가지고 git commit을 한다.

# 기술 아키텍처
시스템 구성요소:
- 프론트엔드 (Frontend):
1. 프레임워크/UI: **Next.js 15 (App Router)**를 사용하여 사용자 인터페이스(UI)를 구축합니다. 서버 컴포넌트(RSC) 및 클라이언트 컴포넌트를 활용하여 최적의 렌더링 전략을 구사합니다. UI 요소는 React와 Shadcn UI를 통해 구현되며, 스타일링은 **Tailwind CSS (v4)**를 사용합니다.
2. 상태 관리 및 데이터 페칭: 클라이언트 사이드 데이터 페칭 및 캐싱, 상태 동기화를 위해 SWR을 활용합니다.
3. 리치 텍스트 에디터: 사용자 콘텐츠 작성을 위해 Tiptap editor를 도입합니다.
4. 패키지 관리: pnpm을 사용하여 프로젝트 의존성을 관리합니다.

- 백엔드 (Backend Logic - Next.js Server Actions & Supabase):
1. 서버 로직 처리: Next.js 15의 Server Action을 적극 활용하여 폼 제출, 데이터베이스 CRUD (Create, Read, Update, Delete) 작업 등 백엔드 로직을 처리합니다. 이를 통해 API 엔드포인트를 별도로 구축하는 과정을 최소화합니다.
2. 데이터베이스 연동 및 ORM: Prisma를 ORM(Object-Relational Mapper)으로 사용하여 Supabase PostgreSQL 데이터베이스와 상호작용합니다. Prisma는 스키마 관리, 마이그레이션, 타입 세이프(type-safe)한 데이터베이스 쿼리를 지원합니다.
3. 인증: Auth.js (v5) 라이브러리를 사용하여 사용자 인증(소셜 로그인, 이메일/비밀번호 기반 인증 등) 시스템을 구축합니다.
4. 데이터 유효성 검사: Zod를 사용하여 Server Action으로 들어오는 데이터 및 기타 필요한 데이터의 스키마와 유효성을 검사합니다.

- 데이터베이스 (Database):
1. 주요 데이터베이스: Supabase 플랫폼에서 제공하는 PostgreSQL을 사용합니다. Supabase는 데이터베이스 외에도 실시간 기능, 스토리지, 인증 백엔드 등을 함께 제공하여 개발 편의성을 높입니다.

- 개발 및 코드 품질 도구 (Development & Code Quality):
1. Linter/Formatter: 코드 스타일 일관성 유지 및 오류 방지를 위해 ESLint와 Prettier를 사용합니다.

- 배포 및 인프라 (Deployment & Infrastructure):
1. 호스팅 및 배포: Vercel 플랫폼을 사용하여 Next.js 애플리케이션을 배포하고 호스팅합니다. Vercel은 CI/CD, 서버리스 함수(Server Actions 실행 환경), 글로벌 엣지 네트워크를 지원합니다.
2. CDN (Content Delivery Network): Cloudflare를 CDN으로 활용하여 전 세계 사용자에게 정적 에셋(이미지, CSS, JS 파일 등)을 빠르고 안정적으로 제공하며, 보안 및 성능 최적화 기능을 활용합니다. (Vercel 자체 CDN과 함께 또는 전면에 구성 가능)

데이터 모델 (주요 모델 예시):

- Account: Authjs Account 테이블
- Session: Authjs Session 테이블
- VerificationToken: Authjs VerificationToken 테이블
- Authenticator: Authjs Authenticator 테이블
- User: Authjs User 테이블
- Post: 게시글 - 정보, Q&A, 커뮤니티 글 포함
- PostComment: 댓글
- PostLike: 좋아요 actor 정보
- JobPosting: 구인 정보
- JobApplication: 구직 지원 - MVP 이후
- Category: 정보 분류
- Bookmark: 북마크
- News: 뉴스
- Country: 국가 정보


API 및 통합:
- 소셜 로그인 API: Google, Kakao 등 OAuth 2.0 기반 API 연동
- 정부 공공데이터 API (가능하다면): 외국인 지원 정책, 고용 정보 등 관련 공공데이터 API 연동을 통해 정보의 자동 업데이트 및 신뢰성 확보 (예: 고용노동부, 법무부, 지자체 제공 정보)
- 지도 API (선택 사항): 구인 정보 내 근무지 표시, 주변 편의시설 안내 등에 활용 (예: Kakao Maps API, Naver Maps API, Google Maps API)
- 번역 API (선택 사항): 사용자 생성 콘텐츠 또는 외부 정보의 실시간 번역 지원 (예: Google Translate API, Papago API)

인프라 요구사항:
- 호스팅 및 배포 플랫폼 (Hosting & Deployment Platform):
1. Vercel: Next.js 애플리케이션의 빌드, 배포 및 호스팅을 위해 Vercel을 사용합니다. Vercel은 자동 CI/CD 파이프라인, 서버리스 함수 실행 환경(Next.js Server Actions 지원), 글로벌 엣지 네트워크를 통한 빠른 배포를 제공합니다.

- 데이터베이스 및 백엔드 서비스 (Database & Backend Services):
1. Supabase: 핵심 백엔드 인프라로 Supabase를 활용합니다. Supabase는 관리형 PostgreSQL 데이터베이스, 사용자 인증 백엔드 기능 (Auth.js와 연동), 실시간 데이터 동기화 기능 등을 제공하여 백엔드 개발 및 운영 부담을 줄입니다.

- 파일 스토리지 (File Storage):
1. Supabase Storage: 사용자 프로필 이미지, 게시물 내 이미지/동영상, 첨부 파일 등 정적 파일 저장을 위해 Supabase Storage를 사용합니다. 이를 통해 확장 가능하고 안전한 파일 관리 시스템을 구축합니다.

- CDN, 도메인 관리 및 보안 (CDN, Domain Management & Security):
1. Cloudflare:
1.1 CDN: 정적 에셋(이미지, CSS, JavaScript 파일 등)의 글로벌 분산 캐싱 및 전송 속도 향상을 위해 Cloudflare CDN을 활용합니다.
1.2 도메인 및 DNS: 서비스의 주 도메인 등록 및 DNS 관리는 Cloudflare를 통해 이루어집니다.
1.3 SSL/TLS 인증: Cloudflare에서 제공하는 SSL/TLS 인증서를 통해 모든 통신을 HTTPS로 암호화하여 보안을 강화합니다.
1.4 보안 기능: 웹 방화벽(WAF), DDoS 공격 방어 등 Cloudflare의 보안 기능을 활용하여 애플리케이션을 보호합니다.

- 애플리케이션 모니터링 및 오류 추적 (Application Monitoring & Error Tracking):
1. Sentry: 애플리케이션에서 발생하는 프론트엔드 및 백엔드 오류를 실시간으로 추적하고 분석하기 위해 Sentry를 도입합니다. 이를 통해 문제 발생 시 신속하게 원인을 파악하고 해결하여 서비스 안정성을 높입니다.

# 개발 로드맵
MVP (Minimum Viable Product) 요구사항:
1. 사용자 관리:
- 기본 회원 가입 (이메일) 및 로그인 기능
- 소셜 로그인 (1개 이상, 예: 구글)
- 기본적인 사용자 프로필 (닉네임, 국적 - 선택)

2. 핵심 정보 제공 (관리자 등록 콘텐츠 위주):
- 생활 정보 (카테고리별, 예: 교통, 주거, 병원) - 최소 3개 카테고리, 각 카테고리별 5개 이상 콘텐츠
- 한국 서비스 이용 가이드 (예: 은행 계좌 개설, 휴대폰 개통) - 최소 2개 가이드
- 외국인 근로자 대상 정부 정보 (예: 비자 기본 정보, 고용허가제 기본 안내) - 최소 2개 주제
- 콘텐츠 검색 기능 (제목, 내용 기반)

3. 기본 커뮤니티 기능 (커뮤니티 게시판):
- 게시글 작성, 조회, 수정, 삭제 (작성자 본인)
- 댓글 작성, 조회, 수정, 삭제 (작성자 본인)
- 익명 글쓰기 옵션 (선택)

4. 기본 구인·구직 정보 게시판:
- 구인 정보 게시 (기업 회원 구분 없이 초기에는 일반 회원도 게시 가능하도록 단순화 또는 관리자 등록 방식)
- 구직 희망 글 게시
- 게시글 목록 및 상세 조회

5. UI/UX:
- 한국어 기본 지원, 주요 외국어 1개 (예: 영어) 지원 (정적 콘텐츠 위주 번역)
- 반응형 웹 디자인 (모바일 우선 고려)
- 직관적인 메뉴 구조 및 정보 분류

향후 개선 사항 (Phase 2, 3 등):
1. 사용자 기능 강화:
- 다국어 지원 확대 (더 많은 언어 추가, 사용자 생성 콘텐츠 번역 지원 검토)
- 고급 사용자 프로필 (경력, 자기소개 등)
- 사용자 간 쪽지(DM) 기능
- 정보 북마크 및 알림 설정 기능
- 나의 활동 내역 (내가 쓴 글, 댓글 등)

2. 정보 제공 확대 및 고도화:
- 다양한 카테고리의 생활 정보 콘텐츠 확충 (지역별 정보, 문화/여가 정보 등)
- 정부 공공데이터 API 연동을 통한 자동 정보 업데이트
- 사용자 생성 콘텐츠(팁, 후기) 활성화 및 우수 콘텐츠 선정/보상
- 지도 연동 서비스 (주변 편의시설, 구인처 위치 등)

3. 커뮤니티 기능 심화:
- 주제별 전문 게시판 추가 (예: 법률 상담, 비자 문의, 각국 커뮤니티)
- 그룹/소모임 기능
- 온라인 이벤트 (웨비나, 정보 공유회 등) 개최 기능
- 사용자 평판/등급 시스템 (선택적)

4. 구인·구직 기능 전문화:
- 기업 회원 전용 기능 (구인 공고 관리, 지원자 관리)
- 이력서/포트폴리오 등록 및 관리 기능 (구직자)
- 맞춤형 채용 정보 추천
- 온라인 지원 시스템

5. 기술 고도화:
- 네이티브 모바일 앱 개발 (iOS, Android)
- 고급 검색 필터 및 추천 알고리즘 도입
- 푸시 알림 시스템 고도화 (개인화 알림)
- 데이터 분석 기반 서비스 개선

5. 부가 서비스:
- 전문가 연계 서비스 (변호사, 노무사, 행정사 등 - 유료/무료 상담 연계)
- 한국어 학습 콘텐츠 연동 또는 자체 제공
- 온라인 중고 거래 장터 (외국인 근로자 간)

# 논리적 의존성 체인
1. 기반 구축 (백엔드 선행):
- 사용자 인증 시스템: 회원 가입, 로그인, 기본적인 사용자 데이터 모델 정의 및 API 개발 (보안 최우선). 이것이 없으면 어떤 사용자별 기능도 불가능.
- 콘텐츠 데이터 모델 정의: 생활 정보, 정부 정보, Q&A 게시글, 댓글 등 핵심 콘텐츠의 데이터베이스 스키마 설계 및 기본 CRUD API 개발.
- 관리자 페이지 기초: 최소한의 콘텐츠(생활 정보, 정부 정보)를 시스템에 입력하고 관리할 수 있는 아주 기본적인 관리자 도구 개발. 이것이 있어야 MVP에서 보여줄 정보가 존재 가능.

2. 핵심 사용성 - 프론트엔드 중심 (사용 가능한 프론트엔드 빠른 구현):
- 정보 조회 UI: 관리자가 입력한 생활 정보, 정부 정보를 사용자가 카테고리별로 보고, 검색하고, 상세 내용을 읽을 수 있는 화면 개발. (가장 먼저 사용자에게 가치를 줄 수 있는 부분)
- Q&A 게시판 UI: 사용자가 질문을 올리고, 다른 사용자가 답변을 달 수 있는 최소한의 게시판 인터페이스 개발. (커뮤니티의 첫걸음)
- 기본적인 사이트 레이아웃 및 네비게이션: 사용자가 사이트를 둘러보고 주요 기능에 접근할 수 있는 틀 마련.

3. 기본 구성 요소 및 초기 상호작용 (핵심 기능 구체화 및 상호작용):
- 구인·구직 게시판 기본 기능: 사용자가 구인 글 또는 구직 글을 올리고 목록에서 볼 수 있는 기능. (MVP의 또 다른 주요 축)
- 사용자 프로필 기초: 사용자가 자신의 닉네임 정도는 변경할 수 있는 최소한의 프로필.
- 다국어 지원 (1개 외국어): 주요 페이지 및 핵심 정보에 대한 외국어 지원 적용. (초기 타겟 사용자 고려)

4. MVP 완성도 향상 및 개선 준비:
- 반응형 웹 디자인 최종 점검: 다양한 모바일 기기에서의 사용성 확인 및 개선.
- 소셜 로그인 연동: 사용자 가입 편의성 증대.
- 콘텐츠 추가 및 분류 세분화: MVP 범위 내에서 제공할 정보 콘텐츠 확충 및 카테고리 정리.
- 피드백 수집 채널 마련: 사용자의 의견을 받을 수 있는 간단한 방법 (예: 문의하기 페이지).

5. 원칙:
- 각 단계는 독립적으로 테스트 가능하고 사용자에게 가치를 제공할 수 있어야 한다.
- 프론트엔드 개발과 백엔드 개발은 API 정의를 통해 병행될 수 있는 부분을 최대한 활용한다.
- 처음부터 모든 기능을 완벽하게 만들기보다는, 핵심 기능을 빠르게 출시하고 사용자 피드백을 통해 점진적으로 개선한다.

# 위험 요소 및 완화 전략
기술적 도전:
- 다국어 지원의 복잡성: 다양한 언어의 UI/UX, 콘텐츠 번역 및 관리, LTR/RTL 레이아웃 지원 등.
    - 완화 전략: MVP에서는 주요 언어 1~2개에 집중하고, 점진적으로 확장. 번역 품질 관리를 위한 프로세스 마련 (예: 전문 번역가 활용 또는 커뮤니티 번역 검토). 초기에는 정적 콘텐츠 위주로 다국어 지원.
- 데이터베이스 설계 및 확장성: 사용자 증가 및 콘텐츠 다양화에 따른 DB 성능 저하 및 구조 변경의 어려움.
    - 완화 전략: 초기 설계 단계부터 확장성을 고려한 데이터 모델링 (정규화, 인덱싱 최적화). 필요시 NoSQL 혼용 검토. 클라우드 기반의 관리형 DB 서비스 활용으로 유연성 확보.
- 보안 취약점: 개인정보 유출, 악의적인 게시물, SQL 인젝션 등.
    - 완화 전략: 시큐어 코딩 원칙 준수. 입력값 검증, 암호화(비밀번호, 개인정보), 접근 제어 철저. 정기적인 보안 취약점 점검 및 패치. HTTPS 적용 필수.

MVP 구축 관련 도전:
- 너무 많은 기능을 MVP에 포함하려는 유혹: 개발 기간 지연 및 초기 사용성 저하.
    - 완화 전략: 핵심 가치(외국인 근로자의 정보 접근성 향상 및 초기 적응 지원)에 집중. 사용자 페르소나와 핵심 사용자 플로우를 기반으로 MVP 기능 우선순위 명확히 설정. "있으면 좋은 기능"은 과감히 후순위로.
- 초기 사용자의 니즈를 정확히 파악하지 못할 위험: 실제 사용되지 않는 기능 개발.
    - 완화 전략: 개발 전 잠재 사용자 그룹 인터뷰 또는 설문조사 진행. MVP 출시 후 적극적인 사용자 피드백 수집 및 분석을 통해 빠르게 개선 방향 설정.

자원 제약:
- 개발 인력 부족 또는 예산 제한: 기능 구현 지연 또는 품질 저하.
    - 완화 전략: MVP 범위를 현실적으로 조정. 단계별 개발 계획을 통해 핵심 기능부터 순차적으로 오픈. 오픈소스 활용 및 클라우드 서비스 적극 활용으로 초기 인프라 비용 절감. 필요시 외부 개발 리소스 활용 또는 투자 유치 고려.
- 콘텐츠 제작 및 관리 인력 부족: 정보의 질 저하 및 업데이트 지연.
    - 완화 전략: 초기에는 핵심 정보 위주로 관리자가 직접 생성. 사용자 생성 콘텐츠(UGC) 활성화 유도 (예: Q&A, 팁 공유). 장기적으로 콘텐츠 파트너십 또는 커뮤니티 기여자 보상책 마련. 정부/공공기관의 공개된 정보 링크 및 RSS 피드 활용 검토.

# 부록
- 연구 결과: (향후 시장 조사, 경쟁사 분석, 사용자 설문 결과 등 첨부)
- 기술 명세: (더 상세한 API 명세, 시스템 아키텍처 다이어그램 등 첨부)
</PRD>
