import { prisma } from '@/lib/prisma';
import { withValidation } from '../utils/validation';
import { createSuccessResponse, createNotFoundErrorResponse, createErrorResponse } from '../utils/response';
import { ActionResponse } from '../utils/types';
import { withRoleAndData, withRole, RESOURCES, ACTIONS } from '../utils/permission';
import { z } from 'zod';

// 게시글 목록 조회 필터 스키마
const getPostsFilterSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  categoryId: z.string().optional(),
  userId: z.string().optional(),
  type: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'viewCount', 'likeCount', 'commentCount']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

type GetPostsFilterInput = z.infer<typeof getPostsFilterSchema>;

// 게시글 상태 업데이트 스키마
const updatePostStatusSchema = z.object({
  postId: z.string().min(1, '게시글 ID는 필수입니다.'),
  deletedAt: z.date().nullable(),
});

type UpdatePostStatusInput = z.infer<typeof updatePostStatusSchema>;

/**
 * 게시글 목록 조회 액션 (관리자용)
 */
export const getPosts = withValidation(
  getPostsFilterSchema,
  withRoleAndData(
    'MODERATOR',
    async (adminId: string, filter: GetPostsFilterInput): Promise<ActionResponse> => {
      try {
        const { page, limit, categoryId, userId, type, search, sortBy, sortOrder } = filter;
        const skip = (page - 1) * limit;

        // 검색 및 필터 조건 구성
        const where: any = {};
        
        // 카테고리 필터
        if (categoryId) {
          where.categoryId = categoryId;
        }
        
        // 사용자 필터
        if (userId) {
          where.userId = userId;
        }
        
        // 게시글 유형 필터
        if (type) {
          where.type = type;
        }

        // 검색어가 있는 경우
        if (search) {
          where.OR = [
            { content: { contains: search, mode: 'insensitive' } },
            { user: { name: { contains: search, mode: 'insensitive' } } },
            { user: { displayName: { contains: search, mode: 'insensitive' } } },
          ];
        }

        // 게시글 목록 조회
        const [posts, totalCount] = await Promise.all([
          prisma.post.findMany({
            where,
            select: {
              id: true,
              content: true,
              contentHtml: true,
              type: true,
              country: true,
              commentCount: true,
              likeCount: true,
              viewCount: true,
              createdAt: true,
              updatedAt: true,
              deletedAt: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
              _count: {
                select: {
                  postComments: true,
                  postLikes: true,
                },
              },
            },
            orderBy: { [sortBy]: sortOrder },
            skip,
            take: limit,
          }),
          prisma.post.count({ where }),
        ]);

        // 페이지네이션 정보
        const totalPages = Math.ceil(totalCount / limit);

        return createSuccessResponse({
          data: posts,
          pagination: {
            page,
            limit,
            totalCount,
            totalPages,
          },
        });
      } catch (error) {
        console.error('게시글 목록 조회 중 오류 발생:', error);
        throw error;
      }
    }
  )
);

/**
 * 게시글 상세 정보 조회 액션
 */
export const getPostDetail = withRoleAndData(
  'MODERATOR',
  async (adminId: string, postId: string): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 확인
      const post = await prisma.post.findUnique({
        where: { id: postId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
              email: true,
            },
          },
          category: true,
          postComments: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          postLikes: {
            take: 10,
          },
        },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      return createSuccessResponse(post);
    } catch (error) {
      console.error('게시글 상세 정보 조회 중 오류 발생:', error);
      throw error;
    }
  }
);

/**
 * 게시글 상태 업데이트 액션 (삭제/복원)
 */
export const updatePostStatus = withValidation(
  updatePostStatusSchema,
  withRoleAndData(
    'MODERATOR',
    async (adminId: string, data: UpdatePostStatusInput): Promise<ActionResponse> => {
      try {
        const { postId, deletedAt } = data;

        // 게시글 존재 여부 확인
        const post = await prisma.post.findUnique({
          where: { id: postId },
        });

        if (!post) {
          return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
        }

        // 상태 업데이트
        const updatedPost = await prisma.post.update({
          where: { id: postId },
          data: {
            deletedAt,
          },
          select: {
            id: true,
            content: true,
            deletedAt: true,
            createdAt: true,
            updatedAt: true,
          },
        });

        return createSuccessResponse(updatedPost);
      } catch (error) {
        console.error('게시글 상태 업데이트 중 오류 발생:', error);
        throw error;
      }
    }
  )
);
