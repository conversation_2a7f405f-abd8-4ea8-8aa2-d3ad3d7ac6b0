import { z } from "zod";
import { UserRole } from "@prisma/client";

/**
 * 관리자 관련 Zod 스키마 정의
 */

// 사용자 역할 업데이트 스키마
export const updateUserRoleSchema = z.object({
  userId: z.string().min(1, "사용자 ID는 필수입니다."),
  role: z.enum(["USER", "MODERATOR", "ADMIN", "SUPER_ADMIN"] as const, {
    errorMap: () => ({ message: "유효한 역할이 아닙니다." }),
  }),
});

export type UpdateUserRoleInput = z.infer<typeof updateUserRoleSchema>;

// 사용자 권한 부여 스키마
export const grantPermissionSchema = z.object({
  userId: z.string().min(1, "사용자 ID는 필수입니다."),
  resource: z.string().min(1, "리소스는 필수입니다."),
  action: z.string().min(1, "액션은 필수입니다."),
  conditions: z.record(z.any()).optional(),
  description: z.string().optional(),
});

export type GrantPermissionInput = z.infer<typeof grantPermissionSchema>;

// 사용자 권한 회수 스키마
export const revokePermissionSchema = z.object({
  userId: z.string().min(1, "사용자 ID는 필수입니다."),
  resource: z.string().min(1, "리소스는 필수입니다."),
  action: z.string().min(1, "액션은 필수입니다."),
});

export type RevokePermissionInput = z.infer<typeof revokePermissionSchema>;

// 관리자 목록 조회 필터 스키마
export const getAdminsFilterSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  role: z.enum(["MODERATOR", "ADMIN", "SUPER_ADMIN"] as const).optional(),
  search: z.string().optional(),
});

export type GetAdminsFilterInput = z.infer<typeof getAdminsFilterSchema>;

// 사용자 목록 조회 필터 스키마
export const getUsersFilterSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  role: z
    .enum(["USER", "MODERATOR", "ADMIN", "SUPER_ADMIN"] as const)
    .optional(),
  search: z.string().optional(),
  status: z.enum(["active", "inactive", "all"] as const).default("active"),
  sortBy: z
    .enum(["createdAt", "name", "email", "lastLoginAt"] as const)
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"] as const).default("desc"),
});

export type GetUsersFilterInput = z.infer<typeof getUsersFilterSchema>;

// 사용자 상태 업데이트 스키마
export const updateUserStatusSchema = z.object({
  userId: z.string().min(1, "사용자 ID는 필수입니다."),
  isActive: z.boolean(),
});

export type UpdateUserStatusInput = z.infer<typeof updateUserStatusSchema>;

// 사용자 권한 목록 조회 필터 스키마
export const getUserPermissionsFilterSchema = z.object({
  userId: z.string().min(1, "사용자 ID는 필수입니다."),
  resource: z.string().optional(),
  action: z.string().optional(),
});

export type GetUserPermissionsFilterInput = z.infer<
  typeof getUserPermissionsFilterSchema
>;
