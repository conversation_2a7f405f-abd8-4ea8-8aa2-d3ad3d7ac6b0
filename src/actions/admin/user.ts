import { prisma } from "@/lib/prisma";
import { withValidation } from "../utils/validation";
import {
  createSuccessResponse,
  createNotFoundErrorResponse,
  createErrorResponse,
} from "../utils/response";
import { ActionResponse } from "../utils/types";
import {
  withRoleAndData,
  withRole,
  RESOURCES,
  ACTIONS,
} from "../utils/permission";
import {
  updateUserRoleSchema,
  UpdateUserRoleInput,
  getAdminsFilterSchema,
  GetAdminsFilterInput,
  getUserPermissionsFilterSchema,
  GetUserPermissionsFilterInput,
} from "./schemas";

/**
 * 관리자 목록 조회 액션
 */
export const getAdmins = withValidation(
  getAdminsFilterSchema,
  withRoleAndData(
    "ADMIN",
    async (
      userId: string,
      filter: GetAdminsFilterInput
    ): Promise<ActionResponse> => {
      try {
        const { page, limit, role, search } = filter;
        const skip = (page - 1) * limit;

        // 검색 및 필터 조건 구성
        const where: any = {
          role: {
            in: role ? [role] : ["MODERATOR", "ADMIN", "SUPER_ADMIN"],
          },
        };

        // 검색어가 있는 경우
        if (search) {
          where.OR = [
            { name: { contains: search, mode: "insensitive" } },
            { email: { contains: search, mode: "insensitive" } },
            { displayName: { contains: search, mode: "insensitive" } },
          ];
        }

        // 관리자 목록 조회
        const [admins, totalCount] = await Promise.all([
          prisma.user.findMany({
            where,
            select: {
              id: true,
              name: true,
              email: true,
              displayName: true,
              image: true,
              role: true,
              isAdmin: true,
              createdAt: true,
              lastLoginAt: true,
              _count: {
                select: {
                  permissions: true,
                },
              },
            },
            orderBy: { createdAt: "desc" },
            skip,
            take: limit,
          }),
          prisma.user.count({ where }),
        ]);

        // 페이지네이션 정보
        const totalPages = Math.ceil(totalCount / limit);

        return createSuccessResponse({
          data: admins,
          pagination: {
            page,
            limit,
            totalCount,
            totalPages,
          },
        });
      } catch (error) {
        console.error("관리자 목록 조회 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 사용자 역할 업데이트 액션
 */
export const updateUserRole = withValidation(
  updateUserRoleSchema,
  withRoleAndData(
    "SUPER_ADMIN",
    async (
      adminId: string,
      data: UpdateUserRoleInput
    ): Promise<ActionResponse> => {
      try {
        const { userId, role } = data;

        // 사용자 존재 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });

        if (!user) {
          return createNotFoundErrorResponse("사용자를 찾을 수 없습니다.");
        }

        // 자기 자신의 역할은 변경할 수 없음
        if (userId === adminId) {
          return createErrorResponse(
            "자신의 역할은 변경할 수 없습니다.",
            "FORBIDDEN"
          );
        }

        // 역할 업데이트
        const updatedUser = await prisma.user.update({
          where: { id: userId },
          data: {
            role,
            isAdmin: role !== "USER", // USER가 아닌 경우 isAdmin = true
          },
          select: {
            id: true,
            name: true,
            email: true,
            displayName: true,
            role: true,
            isAdmin: true,
          },
        });

        return createSuccessResponse(updatedUser);
      } catch (error) {
        console.error("사용자 역할 업데이트 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 사용자 권한 목록 조회 액션
 */
export const getUserPermissions = withValidation(
  getUserPermissionsFilterSchema,
  withRoleAndData(
    "ADMIN",
    async (
      adminId: string,
      filter: GetUserPermissionsFilterInput
    ): Promise<ActionResponse> => {
      try {
        const { userId, resource, action } = filter;

        // 사용자 존재 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            name: true,
            email: true,
            displayName: true,
            role: true,
          },
        });

        if (!user) {
          return createNotFoundErrorResponse("사용자를 찾을 수 없습니다.");
        }

        // 권한 조회 조건 구성
        const where: any = { userId };
        if (resource) where.resource = resource;
        if (action) where.action = action;

        // 권한 목록 조회
        const permissions = await prisma.userPermission.findMany({
          where,
          orderBy: [{ resource: "asc" }, { action: "asc" }],
        });

        return createSuccessResponse({
          user,
          permissions,
        });
      } catch (error) {
        console.error("사용자 권한 목록 조회 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 사용자 목록 조회 액션 (관리자용)
 */
export const getUsers = withValidation(
  getUsersFilterSchema,
  withRoleAndData(
    "MODERATOR",
    async (
      adminId: string,
      filter: GetUsersFilterInput
    ): Promise<ActionResponse> => {
      try {
        const { page, limit, role, search, status, sortBy, sortOrder } = filter;
        const skip = (page - 1) * limit;

        // 검색 및 필터 조건 구성
        const where: any = {};

        // 역할 필터
        if (role) {
          where.role = role;
        }

        // 상태 필터
        if (status !== "all") {
          where.isActive = status === "active";
        }

        // 검색어가 있는 경우
        if (search) {
          where.OR = [
            { name: { contains: search, mode: "insensitive" } },
            { email: { contains: search, mode: "insensitive" } },
            { displayName: { contains: search, mode: "insensitive" } },
          ];
        }

        // 사용자 목록 조회
        const [users, totalCount] = await Promise.all([
          prisma.user.findMany({
            where,
            select: {
              id: true,
              name: true,
              email: true,
              displayName: true,
              image: true,
              role: true,
              isAdmin: true,
              createdAt: true,
              lastLoginAt: true,
              step: true,
              postCount: true,
              postCommentCount: true,
              likeCount: true,
              _count: {
                select: {
                  posts: true,
                  postComments: true,
                  permissions: true,
                },
              },
            },
            orderBy: { [sortBy]: sortOrder },
            skip,
            take: limit,
          }),
          prisma.user.count({ where }),
        ]);

        // 페이지네이션 정보
        const totalPages = Math.ceil(totalCount / limit);

        return createSuccessResponse({
          data: users,
          pagination: {
            page,
            limit,
            totalCount,
            totalPages,
          },
        });
      } catch (error) {
        console.error("사용자 목록 조회 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 사용자 상태 업데이트 액션
 */
export const updateUserStatus = withValidation(
  updateUserStatusSchema,
  withRoleAndData(
    "ADMIN",
    async (
      adminId: string,
      data: UpdateUserStatusInput
    ): Promise<ActionResponse> => {
      try {
        const { userId, isActive } = data;

        // 사용자 존재 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });

        if (!user) {
          return createNotFoundErrorResponse("사용자를 찾을 수 없습니다.");
        }

        // 자기 자신의 상태는 변경할 수 없음
        if (userId === adminId) {
          return createErrorResponse(
            "자신의 상태는 변경할 수 없습니다.",
            "FORBIDDEN"
          );
        }

        // 최고 관리자의 상태는 변경할 수 없음
        if (user.role === "SUPER_ADMIN") {
          return createErrorResponse(
            "최고 관리자의 상태는 변경할 수 없습니다.",
            "FORBIDDEN"
          );
        }

        // 상태 업데이트
        const updatedUser = await prisma.user.update({
          where: { id: userId },
          data: {
            isActive,
          },
          select: {
            id: true,
            name: true,
            email: true,
            displayName: true,
            role: true,
            isAdmin: true,
            isActive: true,
          },
        });

        return createSuccessResponse(updatedUser);
      } catch (error) {
        console.error("사용자 상태 업데이트 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 사용자 상세 정보 조회 액션
 */
export const getUserDetail = withRoleAndData(
  "MODERATOR",
  async (adminId: string, userId: string): Promise<ActionResponse> => {
    try {
      // 사용자 존재 여부 확인
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          displayName: true,
          image: true,
          bio: true,
          role: true,
          isAdmin: true,
          isActive: true,
          step: true,
          postCount: true,
          postCommentCount: true,
          likeCount: true,
          createdAt: true,
          updatedAt: true,
          lastLoginAt: true,
          emailVerified: true,
          _count: {
            select: {
              posts: true,
              postComments: true,
              permissions: true,
              bookmarks: true,
            },
          },
        },
      });

      if (!user) {
        return createNotFoundErrorResponse("사용자를 찾을 수 없습니다.");
      }

      return createSuccessResponse(user);
    } catch (error) {
      console.error("사용자 상세 정보 조회 중 오류 발생:", error);
      throw error;
    }
  }
);
