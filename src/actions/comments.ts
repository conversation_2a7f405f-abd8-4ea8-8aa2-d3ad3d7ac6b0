'use server';

import { prisma } from '@/lib/prisma';
import { CommentWithPostInfo } from '@/types';

/**
 * 특정 사용자가 작성한 댓글 목록을 가져옵니다.
 * @param userId 사용자 ID
 * @param options 페이징 옵션 (선택 사항)
 * @returns 댓글 목록 (CommentWithPostInfo 타입)
 */
export async function getUserComments(
  userId: string,
  options?: { take?: number; skip?: number }
): Promise<CommentWithPostInfo[]> {
  try {
    const comments = await prisma.postComment.findMany({
      where: {
        userId: userId,
        deletedAt: null, // 삭제되지 않은 댓글만
      },
      include: {
        post: {
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: options?.take,
      skip: options?.skip,
    });
    return comments;
  } catch (error) {
    console.error(`Error fetching user comments for userId: ${userId}`, error);
    throw new Error('사용자의 댓글을 가져오는 데 실패했습니다.');
  }
}
