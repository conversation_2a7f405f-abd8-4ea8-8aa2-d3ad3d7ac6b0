'use server';

import { actionHandlerWithAuth } from '@/lib/actions/core';
import { getServerTranslations } from '@/lib/i18n';
import { prisma } from '@/lib/prisma';
import { Session } from 'next-auth';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

// Define input schema for toggleLike - 번역을 위한 함수로 변경
async function createToggleLikeSchema() {
  const t = await getServerTranslations('comments');
  return z.object({
    postId: z.string().min(1, t('postIdRequired')),
  });
}

// Define input schemas for comments - 번역을 위한 함수로 변경
async function createCommentSchemas() {
  const t = await getServerTranslations('comments');
  return {
    CreateCommentSchema: z.object({
      postId: z.string().min(1, t('postIdRequired')),
      contentText: z.string().trim().min(1, t('contentRequired')),
    }),
    CommentIdSchema: z.object({
      id: z.string().min(1, t('commentIdRequired')),
    }),
  };
}

/**
 * 게시물 좋아요 토글 함수 (Modified to use dynamic schema)
 */
export const toggleLike = actionHandlerWithAuth(
  async (session: Session, input: { postId: string }) => {
    const userId = session.user.id!;
    // 스키마 검증
    const schema = await createToggleLikeSchema();
    const { postId } = schema.parse(input);

    const result = await prisma.$transaction(async (tx) => {
      const existingLike = await tx.postLike.findUnique({
        where: {
          userId_postId: { userId, postId },
        },
      });

      let updatedPost;
      let liked = false;

      if (existingLike) {
        await tx.postLike.delete({
          where: {
            userId_postId: { userId, postId },
          },
        });
        updatedPost = await tx.post.update({
          where: { id: postId },
          data: {
            likeCount: {
              decrement: 1,
            },
          },
          select: { likeCount: true },
        });
        liked = false;
      } else {
        await tx.postLike.create({
          data: {
            userId,
            postId,
          },
        });
        updatedPost = await tx.post.update({
          where: { id: postId },
          data: {
            likeCount: {
              increment: 1,
            },
          },
          select: { likeCount: true },
        });
        liked = true;
      }
      return { newLikesCount: updatedPost.likeCount, liked: liked };
    });

    revalidatePath('/community');
    revalidatePath(`/community/post/${postId}`);

    return result;
  }
);

/**
 * Add a comment to a post (Modified to use dynamic schema)
 */
export const addComment = actionHandlerWithAuth(
  async (
    session: Session,
    input: {
      postId: string;
      contentText: string;
    }
  ) => {
    const userId = session.user.id!;
    // 스키마 검증
    const { CreateCommentSchema } = await createCommentSchemas();
    const { postId, contentText } = CreateCommentSchema.parse(input);

    // Use Prisma transaction to create comment and update post comment count atomically
    const [postComment] = await prisma.$transaction([
      prisma.postComment.create({
        data: {
          postId,
          userId: userId, // Use user ID from the verified session
          content: contentText,
        },
      }),
      prisma.post.update({
        where: { id: postId },
        data: {
          commentCount: {
            increment: 1,
          },
        },
      }),
      // 사용자의 댓글 카운트 증가
      prisma.user.update({
        where: { id: userId },
        data: {
          postCommentCount: {
            increment: 1,
          },
        },
      }),
    ]);

    revalidatePath(`/community/post/${postId}`);
    return { commentId: postComment.id };
  }
);

/**
 * Delete a comment (Soft delete) (Modified to use dynamic schema)
 */
export const deleteComment = actionHandlerWithAuth(
  async (session: Session, input: { id: string }) => {
    const userId = session.user.id!;
    // 스키마 검증
    const { CommentIdSchema } = await createCommentSchemas();
    const { id: commentId } = CommentIdSchema.parse(input);
    const t = await getServerTranslations('comments');

    // Find the comment first to check ownership, existence, and get postId
    const comment = await prisma.postComment.findUnique({
      where: { id: commentId },
      select: { userId: true, postId: true, deletedAt: true },
    });

    if (!comment) {
      throw new Error(t('notFound'));
    }

    if (comment.deletedAt) {
      throw new Error(t('alreadyDeleted'));
    }

    if (comment.userId !== userId) {
      throw new Error(t('noPermission'));
    }

    // Perform soft delete and decrement counts in a transaction
    await prisma.$transaction(async (tx) => {
      // Soft delete the comment
      await tx.postComment.update({
        where: { id: commentId },
        data: {
          deletedAt: new Date(),
        },
      });

      // Decrement post's comment count
      await tx.post.update({
        where: { id: comment.postId },
        data: {
          commentCount: { decrement: 1 },
        },
      });

      // Decrement user's comment count
      await tx.user.update({
        where: { id: userId },
        data: {
          postCommentCount: { decrement: 1 },
        },
      });
    });

    // Revalidate the post page to reflect the changes
    revalidatePath(`/community/post/${comment.postId}`);

    return { success: true, message: t('deleteSuccess') };
  }
);
