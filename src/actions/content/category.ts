"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
} from "../utils";
import { createCategorySchema, updateCategorySchema } from "./schemas";

// 타입 정의
type CreateCategoryInput = z.infer<typeof createCategorySchema>;
type UpdateCategoryInput = z.infer<typeof updateCategorySchema>;

/**
 * 카테고리 생성 액션 (관리자 전용)
 */
export const createCategory = withValidation(
  createCategorySchema,
  withAuthAndData(
    async (
      userId: string,
      data: CreateCategoryInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "카테고리를 생성할 권한이 없습니다."
          );
        }

        // 부모 카테고리 존재 여부 확인 (하위 카테고리인 경우)
        if (data.parentId) {
          const parentCategory = await prisma.category.findUnique({
            where: { id: data.parentId },
          });

          if (!parentCategory) {
            return createNotFoundErrorResponse(
              "부모 카테고리를 찾을 수 없습니다."
            );
          }
        }

        // 카테고리 생성
        const category = await prisma.category.create({
          data: {
            name: data.name,
            description: data.description,
            parentId: data.parentId,
            slug: data.slug || data.name.toLowerCase().replace(/\s+/g, "-"),
            order: data.order || 0,
            isActive: data.isActive !== undefined ? data.isActive : true,
            icon: data.icon,
            imageUrl: data.imageUrl,
            translations: data.translations,
          },
        });

        return createSuccessResponse(category);
      } catch (error) {
        console.error("카테고리 생성 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 카테고리 수정 액션 (관리자 전용)
 */
export const updateCategory = withValidation(
  updateCategorySchema,
  withAuthAndData(
    async (
      userId: string,
      data: UpdateCategoryInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "카테고리를 수정할 권한이 없습니다."
          );
        }

        // 카테고리 존재 여부 확인
        const category = await prisma.category.findUnique({
          where: { id: data.id },
        });

        if (!category) {
          return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
        }

        // 부모 카테고리 존재 여부 확인 (하위 카테고리로 변경하는 경우)
        if (data.parentId) {
          const parentCategory = await prisma.category.findUnique({
            where: { id: data.parentId },
          });

          if (!parentCategory) {
            return createNotFoundErrorResponse(
              "부모 카테고리를 찾을 수 없습니다."
            );
          }

          // 자기 자신을 부모로 설정하는 것 방지
          if (data.parentId === data.id) {
            return createForbiddenErrorResponse(
              "자기 자신을 부모 카테고리로 설정할 수 없습니다."
            );
          }
        }

        // 카테고리 수정
        const updatedCategory = await prisma.category.update({
          where: { id: data.id },
          data: {
            name: data.name,
            description: data.description,
            parentId: data.parentId,
            slug: data.slug,
            order: data.order,
            isActive: data.isActive,
            icon: data.icon,
            imageUrl: data.imageUrl,
            translations: data.translations,
          },
        });

        return createSuccessResponse(updatedCategory);
      } catch (error) {
        console.error("카테고리 수정 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 카테고리 삭제 액션 (관리자 전용)
 */
export const deleteCategory = withAuthAndData(
  async (userId: string, id: string): Promise<ActionResponse> => {
    try {
      // 관리자 권한 확인
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { isAdmin: true },
      });

      if (!user?.isAdmin) {
        return createForbiddenErrorResponse(
          "카테고리를 삭제할 권한이 없습니다."
        );
      }

      // 카테고리 존재 여부 확인
      const category = await prisma.category.findUnique({
        where: { id },
        include: {
          children: true,
          posts: true,
        },
      });

      if (!category) {
        return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
      }

      // 하위 카테고리가 있는 경우 삭제 불가
      if (category.children.length > 0) {
        return createForbiddenErrorResponse(
          "하위 카테고리가 있는 카테고리는 삭제할 수 없습니다."
        );
      }

      // 게시글이 있는 경우 삭제 불가
      if (category.posts.length > 0) {
        return createForbiddenErrorResponse(
          "게시글이 있는 카테고리는 삭제할 수 없습니다."
        );
      }

      // 카테고리 삭제
      await prisma.category.delete({
        where: { id },
      });

      return createSuccessResponse({
        message: "카테고리가 성공적으로 삭제되었습니다.",
      });
    } catch (error) {
      console.error("카테고리 삭제 중 오류 발생:", error);
      throw error;
    }
  }
);

/**
 * 카테고리 목록 조회 액션
 */
export const getCategories = async (params?: {
  parentId?: string;
  includeInactive?: boolean;
  locale?: string;
  flat?: boolean;
}): Promise<ActionResponse> => {
  try {
    const {
      parentId,
      includeInactive = false,
      locale = "ko",
      flat = false,
    } = params || {};

    // 카테고리 조회 조건
    const where = {
      ...(parentId ? { parentId } : {}),
      ...(includeInactive ? {} : { isActive: true }),
    };

    // 모든 카테고리 조회
    const categories = await prisma.category.findMany({
      where,
      orderBy: [{ order: "asc" }, { name: "asc" }],
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            posts: true,
            children: true,
          },
        },
      },
    });

    // 번역 데이터 적용
    const localizedCategories = categories.map((category) => {
      let localizedCategory = { ...category };
      if (locale && locale !== "ko" && category.translations) {
        const translations = category.translations as Record<string, any>;
        if (translations[locale]) {
          localizedCategory = {
            ...category,
            name: translations[locale].name || category.name,
            description:
              translations[locale].description || category.description,
          };
        }
      }
      return localizedCategory;
    });

    // 계층 구조로 변환 (flat이 false이고 parentId가 지정되지 않은 경우)
    if (!flat && !parentId) {
      // 카테고리 트리 구성
      const rootCategories = localizedCategories.filter(
        (category) => !category.parentId
      );
      const categoryMap = new Map(
        localizedCategories.map((category) => [
          category.id,
          { ...category, children: [] },
        ])
      );

      localizedCategories.forEach((category) => {
        if (category.parentId) {
          const parent = categoryMap.get(category.parentId);
          if (parent) {
            parent.children.push(categoryMap.get(category.id));
          }
        }
      });

      return createSuccessResponse({
        categories: rootCategories.map((category) =>
          categoryMap.get(category.id)
        ),
        flatCategories: localizedCategories,
      });
    }

    return createSuccessResponse(localizedCategories);
  } catch (error) {
    console.error("카테고리 목록 조회 중 오류 발생:", error);
    throw error;
  }
};
