'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
} from '../utils';
import { createCommentSchema, updateCommentSchema, deleteCommentSchema } from './schemas';

// 타입 정의
type CreateCommentInput = z.infer<typeof createCommentSchema>;
type UpdateCommentInput = z.infer<typeof updateCommentSchema>;
type DeleteCommentInput = z.infer<typeof deleteCommentSchema>;

/**
 * 댓글 생성 액션
 */
export const createComment = withValidation(
  createCommentSchema,
  withAuthAndData(async (userId: string, data: CreateCommentInput): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 확인
      const post = await prisma.post.findUnique({
        where: { id: data.postId },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      // 부모 댓글 존재 여부 확인 (대댓글인 경우)
      if (data.parentId) {
        const parentComment = await prisma.postComment.findUnique({
          where: { id: data.parentId },
        });

        if (!parentComment) {
          return createNotFoundErrorResponse('부모 댓글을 찾을 수 없습니다.');
        }

        // 대댓글의 대댓글은 허용하지 않음 (1단계 대댓글만 허용)
        if (parentComment.parentId) {
          return createForbiddenErrorResponse('대댓글에는 댓글을 달 수 없습니다.');
        }
      }

      // 댓글 생성
      const comment = await prisma.postComment.create({
        data: {
          content: data.content,
          authorId: userId,
          postId: data.postId,
          parentId: data.parentId,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
        },
      });

      // 사용자의 댓글 수 증가
      await prisma.user.update({
        where: { id: userId },
        data: {
          postCommentCount: {
            increment: 1,
          },
        },
      });

      return createSuccessResponse(comment);
    } catch (error) {
      console.error('댓글 생성 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 댓글 수정 액션
 */
export const updateComment = withValidation(
  updateCommentSchema,
  withAuthAndData(async (userId: string, data: UpdateCommentInput): Promise<ActionResponse> => {
    try {
      // 댓글 존재 여부 및 작성자 확인
      const comment = await prisma.postComment.findUnique({
        where: { id: data.id },
      });

      if (!comment) {
        return createNotFoundErrorResponse('댓글을 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 수정 가능
      if (comment.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('댓글을 수정할 권한이 없습니다.');
        }
      }

      // 댓글 수정
      const updatedComment = await prisma.postComment.update({
        where: { id: data.id },
        data: {
          content: data.content,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
        },
      });

      return createSuccessResponse(updatedComment);
    } catch (error) {
      console.error('댓글 수정 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 댓글 삭제 액션
 */
export const deleteComment = withValidation(
  deleteCommentSchema,
  withAuthAndData(async (userId: string, data: DeleteCommentInput): Promise<ActionResponse> => {
    try {
      // 댓글 존재 여부 및 작성자 확인
      const comment = await prisma.postComment.findUnique({
        where: { id: data.id },
      });

      if (!comment) {
        return createNotFoundErrorResponse('댓글을 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 삭제 가능
      if (comment.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('댓글을 삭제할 권한이 없습니다.');
        }
      }

      // 댓글 삭제
      await prisma.postComment.delete({
        where: { id: data.id },
      });

      // 사용자의 댓글 수 감소
      await prisma.user.update({
        where: { id: comment.authorId },
        data: {
          postCommentCount: {
            decrement: 1,
          },
        },
      });

      return createSuccessResponse({ message: '댓글이 성공적으로 삭제되었습니다.' });
    } catch (error) {
      console.error('댓글 삭제 중 오류 발생:', error);
      throw error;
    }
  })
);
