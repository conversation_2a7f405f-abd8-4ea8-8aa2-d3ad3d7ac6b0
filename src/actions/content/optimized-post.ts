'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  cacheAction,
  CacheTag,
  getPaginationParams,
  getSortParams,
  createPaginationResult,
  logAction,
  logActionError,
  withTransaction,
} from '../utils';
import { getPostSchema, getPostsSchema } from './schemas';

// 타입 정의
type GetPostInput = z.infer<typeof getPostSchema>;
type GetPostsInput = z.infer<typeof getPostsSchema>;

/**
 * 최적화된 게시글 조회 액션
 * - 캐싱 적용
 * - 로깅 추가
 * - 필요한 필드만 선택적으로 조회
 */
export const getOptimizedPost = withValidation(
  getPostSchema,
  cacheAction(async (data: GetPostInput): Promise<ActionResponse> => {
    try {
      logAction('getOptimizedPost', undefined, { postId: data.id });

      // 게시글 조회 (필요한 필드만 선택적으로 조회)
      const post = await prisma.post.findUnique({
        where: { id: data.id },
        select: {
          id: true,
          title: true,
          content: true,
          published: true,
          createdAt: true,
          updatedAt: true,
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          // 댓글은 별도 API로 분리하여 필요할 때만 로드
          _count: {
            select: {
              likes: true,
              comments: true,
            },
          },
        },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      return createSuccessResponse(post);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logActionError('getOptimizedPost', err, undefined, { postId: data.id });
      throw error;
    }
  }, { tags: [CacheTag.POST], revalidate: 60 })
);

/**
 * 최적화된 게시글 목록 조회 액션
 * - 캐싱 적용
 * - 로깅 추가
 * - 페이지네이션 최적화
 * - 필요한 필드만 선택적으로 조회
 */
export const getOptimizedPosts = withValidation(
  getPostsSchema,
  cacheAction(async (data: GetPostsInput): Promise<ActionResponse> => {
    try {
      logAction('getOptimizedPosts', undefined, data);

      const { page, limit, categoryId, userId, query, sortBy, sortOrder } = data;
      
      // 페이지네이션 파라미터 계산
      const { skip, take } = getPaginationParams({ page, limit });
      
      // 정렬 파라미터 계산
      const orderBy = getSortParams(
        { sortBy, sortOrder },
        'createdAt',
        ['createdAt', 'updatedAt', 'title']
      );

      // 필터 조건 구성
      const where: any = {
        published: true,
      };

      if (categoryId) {
        where.categoryId = categoryId;
      }

      if (userId) {
        where.authorId = userId;
      }

      if (query) {
        where.OR = [
          { title: { contains: query, mode: 'insensitive' } },
          { content: { contains: query, mode: 'insensitive' } },
        ];
      }

      // 게시글 목록 조회와 전체 개수 조회를 병렬로 실행
      const [posts, totalCount] = await Promise.all([
        prisma.post.findMany({
          where,
          orderBy,
          skip,
          take,
          select: {
            id: true,
            title: true,
            content: true,
            published: true,
            createdAt: true,
            updatedAt: true,
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                likes: true,
                comments: true,
              },
            },
          },
        }),
        prisma.post.count({ where }),
      ]);

      // 페이지네이션 결과 생성
      const result = createPaginationResult(posts, totalCount, page || 1, limit || 10);

      return createSuccessResponse(result);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logActionError('getOptimizedPosts', err, undefined, data);
      throw error;
    }
  }, { tags: [CacheTag.POSTS], revalidate: 30 })
);

/**
 * 트랜잭션을 사용한 게시글 생성 액션 예시
 */
export const createPostWithTransaction = async (
  userId: string,
  data: { title: string; content: string; categoryId: string; }
): Promise<ActionResponse> => {
  try {
    logAction('createPostWithTransaction', userId, data);

    // 트랜잭션 내에서 여러 작업 수행
    const result = await withTransaction(async (tx) => {
      // 게시글 생성
      const post = await tx.post.create({
        data: {
          title: data.title,
          content: data.content,
          published: true,
          authorId: userId,
          categoryId: data.categoryId,
        },
      });

      // 사용자의 게시글 수 증가
      await tx.user.update({
        where: { id: userId },
        data: {
          postCount: {
            increment: 1,
          },
        },
      });

      return post;
    });

    return createSuccessResponse(result);
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logActionError('createPostWithTransaction', err, userId, data);
    throw error;
  }
};
