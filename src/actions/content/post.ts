'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
} from '../utils';
import { createPostSchema, updatePostSchema, deletePostSchema, getPostSchema, getPostsSchema } from './schemas';

// 타입 정의
type CreatePostInput = z.infer<typeof createPostSchema>;
type UpdatePostInput = z.infer<typeof updatePostSchema>;
type DeletePostInput = z.infer<typeof deletePostSchema>;
type GetPostInput = z.infer<typeof getPostSchema>;
type GetPostsInput = z.infer<typeof getPostsSchema>;

/**
 * 게시글 생성 액션
 */
export const createPost = withValidation(
  createPostSchema,
  withAuthAndData(async (userId: string, data: CreatePostInput): Promise<ActionResponse> => {
    try {
      // 카테고리 존재 여부 확인
      const category = await prisma.category.findUnique({
        where: { id: data.categoryId },
      });

      if (!category) {
        return createNotFoundErrorResponse('카테고리를 찾을 수 없습니다.');
      }

      // 게시글 생성
      const post = await prisma.post.create({
        data: {
          title: data.title,
          content: data.content,
          published: data.isPublished,
          authorId: userId,
          categoryId: data.categoryId,
          // tags 처리 (실제로는 Tag 모델과 연결 필요)
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
          category: true,
        },
      });

      // 사용자의 게시글 수 증가
      await prisma.user.update({
        where: { id: userId },
        data: {
          postCount: {
            increment: 1,
          },
        },
      });

      return createSuccessResponse(post);
    } catch (error) {
      console.error('게시글 생성 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 게시글 수정 액션
 */
export const updatePost = withValidation(
  updatePostSchema,
  withAuthAndData(async (userId: string, data: UpdatePostInput): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 및 작성자 확인
      const post = await prisma.post.findUnique({
        where: { id: data.id },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 수정 가능
      if (post.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('게시글을 수정할 권한이 없습니다.');
        }
      }

      // 카테고리 변경 시 존재 여부 확인
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          return createNotFoundErrorResponse('카테고리를 찾을 수 없습니다.');
        }
      }

      // 게시글 수정
      const updatedPost = await prisma.post.update({
        where: { id: data.id },
        data: {
          title: data.title,
          content: data.content,
          published: data.isPublished,
          categoryId: data.categoryId,
          // tags 처리 (실제로는 Tag 모델과 연결 필요)
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
          category: true,
        },
      });

      return createSuccessResponse(updatedPost);
    } catch (error) {
      console.error('게시글 수정 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 게시글 삭제 액션
 */
export const deletePost = withValidation(
  deletePostSchema,
  withAuthAndData(async (userId: string, data: DeletePostInput): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 및 작성자 확인
      const post = await prisma.post.findUnique({
        where: { id: data.id },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 삭제 가능
      if (post.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('게시글을 삭제할 권한이 없습니다.');
        }
      }

      // 게시글 삭제
      await prisma.post.delete({
        where: { id: data.id },
      });

      // 사용자의 게시글 수 감소
      await prisma.user.update({
        where: { id: post.authorId },
        data: {
          postCount: {
            decrement: 1,
          },
        },
      });

      return createSuccessResponse({ message: '게시글이 성공적으로 삭제되었습니다.' });
    } catch (error) {
      console.error('게시글 삭제 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 게시글 조회 액션
 */
export const getPost = withValidation(
  getPostSchema,
  async (data: GetPostInput): Promise<ActionResponse> => {
    try {
      // 게시글 조회
      const post = await prisma.post.findUnique({
        where: { id: data.id },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
          category: true,
          comments: {
            where: { parentId: null },
            orderBy: { createdAt: 'desc' },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              replies: {
                include: {
                  author: {
                    select: {
                      id: true,
                      name: true,
                      displayName: true,
                      image: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              likes: true,
              comments: true,
            },
          },
        },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      return createSuccessResponse(post);
    } catch (error) {
      console.error('게시글 조회 중 오류 발생:', error);
      throw error;
    }
  }
);

/**
 * 게시글 목록 조회 액션
 */
export const getPosts = withValidation(
  getPostsSchema,
  async (data: GetPostsInput): Promise<ActionResponse> => {
    try {
      const { page, limit, categoryId, userId, query, sortBy, sortOrder } = data;
      const skip = (page - 1) * limit;

      // 필터 조건 구성
      const where: any = {
        published: true,
      };

      if (categoryId) {
        where.categoryId = categoryId;
      }

      if (userId) {
        where.authorId = userId;
      }

      if (query) {
        where.OR = [
          { title: { contains: query, mode: 'insensitive' } },
          { content: { contains: query, mode: 'insensitive' } },
        ];
      }

      // 정렬 조건 구성
      const orderBy: any = {
        [sortBy]: sortOrder,
      };

      // 게시글 목록 조회
      const [posts, totalCount] = await Promise.all([
        prisma.post.findMany({
          where,
          orderBy,
          skip,
          take: limit,
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            _count: {
              select: {
                likes: true,
                comments: true,
              },
            },
          },
        }),
        prisma.post.count({ where }),
      ]);

      return createSuccessResponse({
        posts,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: skip + posts.length < totalCount,
        },
      });
    } catch (error) {
      console.error('게시글 목록 조회 중 오류 발생:', error);
      throw error;
    }
  }
);
