import { z } from "zod";
import { idSchema, titleSchema, contentSchema } from "../utils/schemas";

/**
 * 콘텐츠 관련 Zod 스키마 정의
 */

// 게시글 생성 스키마
export const createPostSchema = z.object({
  title: titleSchema,
  content: contentSchema,
  categoryId: idSchema,
  tags: z.array(z.string()).optional(),
  isPublished: z.boolean().default(true),
});

// 게시글 수정 스키마
export const updatePostSchema = z.object({
  id: idSchema,
  title: titleSchema.optional(),
  content: contentSchema.optional(),
  categoryId: idSchema.optional(),
  tags: z.array(z.string()).optional(),
  isPublished: z.boolean().optional(),
});

// 게시글 삭제 스키마
export const deletePostSchema = z.object({
  id: idSchema,
});

// 게시글 조회 스키마
export const getPostSchema = z.object({
  id: idSchema,
});

// 게시글 목록 조회 스키마
export const getPostsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(50).default(10),
  categoryId: idSchema.optional(),
  userId: idSchema.optional(),
  query: z.string().optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "title"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// 댓글 생성 스키마
export const createCommentSchema = z.object({
  postId: idSchema,
  content: z
    .string()
    .min(1, "댓글 내용은 필수입니다.")
    .max(1000, "댓글은 최대 1000자까지 가능합니다."),
  parentId: idSchema.optional(),
});

// 댓글 수정 스키마
export const updateCommentSchema = z.object({
  id: idSchema,
  content: z
    .string()
    .min(1, "댓글 내용은 필수입니다.")
    .max(1000, "댓글은 최대 1000자까지 가능합니다."),
});

// 댓글 삭제 스키마
export const deleteCommentSchema = z.object({
  id: idSchema,
});

// 카테고리 생성 스키마
export const createCategorySchema = z.object({
  name: z
    .string()
    .min(1, "카테고리 이름은 필수입니다.")
    .max(255, "카테고리 이름은 최대 255자까지 가능합니다."),
  slug: z
    .string()
    .min(1, "슬러그는 필수입니다.")
    .max(255, "슬러그는 최대 255자까지 가능합니다.")
    .regex(
      /^[a-z0-9-]+$/,
      "슬러그는 소문자, 숫자, 하이픈(-)만 포함할 수 있습니다."
    ),
  description: z
    .string()
    .max(1000, "설명은 최대 1000자까지 가능합니다.")
    .optional(),
  parentId: idSchema.optional(),
  order: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
  icon: z.string().max(255).optional(),
  imageUrl: z.string().url("유효한 URL 형식이 아닙니다.").optional(),
  translations: z
    .record(
      z.string(),
      z.object({
        name: z.string().min(1, "번역된 이름은 필수입니다.").max(255),
        description: z.string().max(1000).optional(),
      })
    )
    .optional(),
});

// 카테고리 수정 스키마
export const updateCategorySchema = z.object({
  id: idSchema,
  name: z
    .string()
    .min(1, "카테고리 이름은 필수입니다.")
    .max(255, "카테고리 이름은 최대 255자까지 가능합니다.")
    .optional(),
  slug: z
    .string()
    .min(1, "슬러그는 필수입니다.")
    .max(255, "슬러그는 최대 255자까지 가능합니다.")
    .regex(
      /^[a-z0-9-]+$/,
      "슬러그는 소문자, 숫자, 하이픈(-)만 포함할 수 있습니다."
    )
    .optional(),
  description: z
    .string()
    .max(1000, "설명은 최대 1000자까지 가능합니다.")
    .optional(),
  parentId: idSchema.optional().nullable(),
  order: z.number().int().min(0).optional(),
  isActive: z.boolean().optional(),
  icon: z.string().max(255).optional().nullable(),
  imageUrl: z.string().url("유효한 URL 형식이 아닙니다.").optional().nullable(),
  translations: z
    .record(
      z.string(),
      z.object({
        name: z.string().min(1, "번역된 이름은 필수입니다.").max(255),
        description: z.string().max(1000).optional(),
      })
    )
    .optional(),
});

// 카테고리 조회 스키마
export const getCategorySchema = z.object({
  id: idSchema,
  locale: z.string().default("ko"),
});

// 카테고리 목록 조회 스키마
export const getCategoriesSchema = z.object({
  parentId: idSchema.optional(),
  includeInactive: z.boolean().default(false),
  locale: z.string().default("ko"),
  flat: z.boolean().default(false),
});

// 카테고리 삭제 스키마
export const deleteCategorySchema = z.object({
  id: idSchema,
});
