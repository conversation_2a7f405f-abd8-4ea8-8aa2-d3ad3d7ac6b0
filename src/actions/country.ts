'use server';

import { prisma } from '@/lib/prisma';
import { Country } from '@prisma/client';

// 전체 옵션을 포함한 Country 타입
export type CountryWithAll =
  | Country
  | {
      id: 0;
      code: '';
      name: '전체';
      displayName: '전체';
      enabled: true;
      createdAt: Date;
      updatedAt: Date;
    };

// 모든 국가 정보 가져오기
export async function getCountries(): Promise<CountryWithAll[]> {
  try {
    const countries = await prisma.country.findMany({
      orderBy: {
        code: 'asc',
      },
    });

    return countries;
  } catch (error) {
    console.error('Error fetching countries:', error);
    throw new Error('Failed to fetch countries');
  }
}

// 활성화된 국가만 가져오기
export async function getEnabledCountries(): Promise<CountryWithAll[]> {
  try {
    const countries = await prisma.country.findMany({
      where: {
        enabled: true,
      },
      orderBy: {
        code: 'asc',
      },
    });

    return countries;
  } catch (error) {
    console.error('Error fetching enabled countries:', error);
    throw new Error('Failed to fetch enabled countries');
  }
}

// 코드로 국가 찾기
export async function getCountryByCode(
  code: string
): Promise<CountryWithAll | null> {
  if (!code) {
    // 코드가 없으면 전체(첫 번째 항목) 반환
    return {
      id: 0,
      code: '',
      name: '전체',
      displayName: '전체',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  const lowerCode = code.toLowerCase();

  try {
    const country = await prisma.country.findUnique({
      where: {
        code: lowerCode,
      },
    });

    return country;
  } catch (error) {
    console.error(`Error fetching country with code ${code}:`, error);
    return null;
  }
}

// 국가 정보 추가 또는 업데이트
export async function upsertCountry(countryData: {
  code: string;
  name: string;
  displayName: string;
  enabled: boolean;
}): Promise<Country> {
  try {
    const { code, name, displayName, enabled } = countryData;
    const lowerCode = code.toLowerCase();

    const country = await prisma.country.upsert({
      where: {
        code: lowerCode,
      },
      update: {
        name,
        displayName,
        enabled,
      },
      create: {
        code: lowerCode,
        name,
        displayName,
        enabled,
      },
    });

    return country;
  } catch (error) {
    console.error('Error upserting country:', error);
    throw new Error('Failed to upsert country');
  }
}

// 국가 정보 삭제
export async function deleteCountry(code: string): Promise<boolean> {
  try {
    if (!code) {
      throw new Error('Country code is required');
    }

    const lowerCode = code.toLowerCase();

    await prisma.country.delete({
      where: {
        code: lowerCode,
      },
    });

    return true;
  } catch (error) {
    console.error(`Error deleting country with code ${code}:`, error);
    return false;
  }
}

// 국가 활성화 상태 토글
export async function toggleCountryEnabled(
  code: string,
  enabled: boolean
): Promise<Country | null> {
  try {
    if (!code) {
      throw new Error('Country code is required');
    }

    const lowerCode = code.toLowerCase();

    const country = await prisma.country.update({
      where: {
        code: lowerCode,
      },
      data: {
        enabled,
      },
    });

    return country;
  } catch (error) {
    console.error(
      `Error toggling country enabled status for code ${code}:`,
      error
    );
    return null;
  }
}
