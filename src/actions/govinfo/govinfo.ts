"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
} from "../utils";
import { logAction, logActionError } from "../utils/logging";
import { cacheAction } from "../utils/cache";
import { CacheTag } from "../utils/cache";
import {
  getPaginationParams,
  getSortParams,
  createPaginationResult,
} from "../utils/db";
import {
  createGovInfoSchema,
  updateGovInfoSchema,
  deleteGovInfoSchema,
  getGovInfoSchema,
  getGovInfosSchema,
  uploadGovInfoImageSchema,
  deleteGovInfoImageSchema,
  CreateGovInfoInput,
  UpdateGovInfoInput,
  DeleteGovInfoInput,
  GetGovInfoInput,
  GetGovInfosInput,
  UploadGovInfoImageInput,
  DeleteGovInfoImageInput,
} from "./schemas";

/**
 * 정부 정보 생성 액션
 */
export const createGovInfo = withValidation(
  createGovInfoSchema,
  withAuthAndData(
    async (
      userId: string,
      data: CreateGovInfoInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "정부 정보를 생성할 권한이 없습니다."
          );
        }

        // 카테고리 존재 여부 확인
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
        }

        // 정부 정보 생성
        const govInfo = await prisma.govInfo.create({
          data: {
            title: data.title,
            slug: data.slug,
            summary: data.summary,
            content: data.content,
            contentHtml: data.contentHtml || "<p></p>",
            status: data.status,
            isImportant: data.isImportant,
            govUrl: data.govUrl,
            govDepartment: data.govDepartment,
            translations: data.translations,
            categoryId: data.categoryId,
            authorId: userId,
            publishedAt: data.status === "published" ? new Date() : undefined,
            infoUpdatedAt: data.infoUpdatedAt
              ? new Date(data.infoUpdatedAt)
              : new Date(),
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        logAction("createGovInfo", { userId, govInfoId: govInfo.id });
        return createSuccessResponse(govInfo);
      } catch (error) {
        logActionError("createGovInfo", error);
        throw error;
      }
    }
  )
);

/**
 * 정부 정보 수정 액션
 */
export const updateGovInfo = withValidation(
  updateGovInfoSchema,
  withAuthAndData(
    async (
      userId: string,
      data: UpdateGovInfoInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "정부 정보를 수정할 권한이 없습니다."
          );
        }

        // 정부 정보 존재 여부 확인
        const existingGovInfo = await prisma.govInfo.findUnique({
          where: { id: data.id },
        });

        if (!existingGovInfo) {
          return createNotFoundErrorResponse("정부 정보를 찾을 수 없습니다.");
        }

        // 카테고리 존재 여부 확인 (변경된 경우)
        if (data.categoryId) {
          const category = await prisma.category.findUnique({
            where: { id: data.categoryId },
          });

          if (!category) {
            return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
          }
        }

        // 정부 정보 수정
        const govInfo = await prisma.govInfo.update({
          where: { id: data.id },
          data: {
            title: data.title,
            slug: data.slug,
            summary: data.summary,
            content: data.content,
            contentHtml: data.contentHtml,
            status: data.status,
            isImportant: data.isImportant,
            govUrl: data.govUrl,
            govDepartment: data.govDepartment,
            translations: data.translations,
            categoryId: data.categoryId,
            publishedAt:
              data.status === "published" && !existingGovInfo.publishedAt
                ? new Date()
                : data.publishedAt
                ? new Date(data.publishedAt)
                : existingGovInfo.publishedAt,
            infoUpdatedAt: data.infoUpdatedAt
              ? new Date(data.infoUpdatedAt)
              : existingGovInfo.infoUpdatedAt || new Date(),
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            images: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        logAction("updateGovInfo", { userId, govInfoId: govInfo.id });
        return createSuccessResponse(govInfo);
      } catch (error) {
        logActionError("updateGovInfo", error);
        throw error;
      }
    }
  )
);

/**
 * 정부 정보 삭제 액션
 */
export const deleteGovInfo = withValidation(
  deleteGovInfoSchema,
  withAuthAndData(
    async (
      userId: string,
      data: DeleteGovInfoInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "정부 정보를 삭제할 권한이 없습니다."
          );
        }

        // 정부 정보 존재 여부 확인
        const govInfo = await prisma.govInfo.findUnique({
          where: { id: data.id },
        });

        if (!govInfo) {
          return createNotFoundErrorResponse("정부 정보를 찾을 수 없습니다.");
        }

        // 정부 정보 삭제
        await prisma.govInfo.delete({
          where: { id: data.id },
        });

        logAction("deleteGovInfo", { userId, govInfoId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteGovInfo", error);
        throw error;
      }
    }
  )
);

/**
 * 정부 정보 조회 액션
 */
export const getGovInfo = withValidation(
  getGovInfoSchema,
  cacheAction(
    async (data: GetGovInfoInput): Promise<ActionResponse> => {
      try {
        const govInfo = await prisma.govInfo.findUnique({
          where: { id: data.id },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            images: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        if (!govInfo) {
          return createNotFoundErrorResponse("정부 정보를 찾을 수 없습니다.");
        }

        return createSuccessResponse(govInfo);
      } catch (error) {
        logActionError("getGovInfo", error);
        throw error;
      }
    },
    { tags: [CacheTag.GOV_INFO], revalidate: 60 }
  )
);

/**
 * 정부 정보 목록 조회 액션
 */
export const getGovInfos = withValidation(
  getGovInfosSchema,
  cacheAction(
    async (data: GetGovInfosInput): Promise<ActionResponse> => {
      try {
        const { page, limit } = getPaginationParams(data);
        const { sortBy, sortOrder } = getSortParams(data);

        // 필터 조건 구성
        const where: any = {};

        if (data.categoryId) {
          where.categoryId = data.categoryId;
        }

        if (data.status) {
          where.status = data.status;
        } else {
          // 기본적으로 published 상태만 표시
          where.status = "published";
        }

        if (data.isImportant !== undefined) {
          where.isImportant = data.isImportant;
        }

        if (data.query) {
          where.OR = [
            { title: { contains: data.query, mode: "insensitive" } },
            { summary: { contains: data.query, mode: "insensitive" } },
            { content: { contains: data.query, mode: "insensitive" } },
            { govDepartment: { contains: data.query, mode: "insensitive" } },
          ];
        }

        // 총 항목 수 조회
        const total = await prisma.govInfo.count({ where });

        // 정부 정보 목록 조회
        const govInfos = await prisma.govInfo.findMany({
          where,
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            images: {
              take: 1,
              orderBy: {
                order: "asc",
              },
            },
          },
          orderBy: {
            [sortBy]: sortOrder,
          },
          skip: (page - 1) * limit,
          take: limit,
        });

        return createSuccessResponse(
          createPaginationResult(govInfos, total, page, limit)
        );
      } catch (error) {
        logActionError("getGovInfos", error);
        throw error;
      }
    },
    {
      tags: [
        CacheTag.GOV_INFOS,
        ...(data.categoryId ? [CacheTag.CATEGORY] : []),
      ],
      revalidate: 60,
    }
  )
);

/**
 * 정부 정보 이미지 업로드 액션
 */
export const uploadGovInfoImage = withValidation(
  uploadGovInfoImageSchema,
  withAuthAndData(
    async (
      userId: string,
      data: UploadGovInfoImageInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "이미지를 업로드할 권한이 없습니다."
          );
        }

        // 정부 정보 존재 여부 확인
        const govInfo = await prisma.govInfo.findUnique({
          where: { id: data.govInfoId },
        });

        if (!govInfo) {
          return createNotFoundErrorResponse("정부 정보를 찾을 수 없습니다.");
        }

        // 이미지 업로드
        const image = await prisma.govInfoImage.create({
          data: {
            url: data.url,
            alt: data.alt,
            caption: data.caption,
            order: data.order,
            govInfoId: data.govInfoId,
          },
        });

        logAction("uploadGovInfoImage", {
          userId,
          imageId: image.id,
          govInfoId: data.govInfoId,
        });
        return createSuccessResponse(image);
      } catch (error) {
        logActionError("uploadGovInfoImage", error);
        throw error;
      }
    }
  )
);

/**
 * 정부 정보 이미지 삭제 액션
 */
export const deleteGovInfoImage = withValidation(
  deleteGovInfoImageSchema,
  withAuthAndData(
    async (
      userId: string,
      data: DeleteGovInfoImageInput
    ): Promise<ActionResponse> => {
      try {
        // 관리자 권한 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse(
            "이미지를 삭제할 권한이 없습니다."
          );
        }

        // 이미지 존재 여부 확인
        const image = await prisma.govInfoImage.findUnique({
          where: { id: data.id },
          include: {
            govInfo: {
              select: {
                id: true,
              },
            },
          },
        });

        if (!image) {
          return createNotFoundErrorResponse("이미지를 찾을 수 없습니다.");
        }

        // 이미지 삭제
        await prisma.govInfoImage.delete({
          where: { id: data.id },
        });

        logAction("deleteGovInfoImage", {
          userId,
          imageId: data.id,
          govInfoId: image.govInfo.id,
        });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteGovInfoImage", error);
        throw error;
      }
    }
  )
);
