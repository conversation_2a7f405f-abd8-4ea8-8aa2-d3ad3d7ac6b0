import { z } from 'zod';
import { idSchema, titleSchema, contentSchema } from '../utils/schemas';

/**
 * 정부 정보 콘텐츠 관련 Zod 스키마 정의
 */

// 정부 정보 생성 스키마
export const createGovInfoSchema = z.object({
  title: titleSchema,
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.'),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: contentSchema,
  contentHtml: z.string().optional(),
  categoryId: idSchema,
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  isImportant: z.boolean().default(false),
  govUrl: z.string().url('유효한 URL 형식이 아닙니다.').optional(),
  govDepartment: z.string().max(255, '정부 부처/기관명은 최대 255자까지 가능합니다.').optional(),
  translations: z.record(z.string(), z.object({
    title: z.string().min(1, '제목은 필수입니다.'),
    summary: z.string().optional(),
    content: z.string().min(1, '내용은 필수입니다.')
  })).optional(),
  publishedAt: z.string().optional(),
  infoUpdatedAt: z.string().optional(),
});

// 정부 정보 수정 스키마
export const updateGovInfoSchema = z.object({
  id: idSchema,
  title: titleSchema.optional(),
  slug: z.string().max(255, '슬러그는 최대 255자까지 가능합니다.').optional(),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: contentSchema.optional(),
  contentHtml: z.string().optional(),
  categoryId: idSchema.optional(),
  status: z.enum(['draft', 'published', 'archived']).optional(),
  isImportant: z.boolean().optional(),
  govUrl: z.string().url('유효한 URL 형식이 아닙니다.').optional().nullable(),
  govDepartment: z.string().max(255, '정부 부처/기관명은 최대 255자까지 가능합니다.').optional().nullable(),
  translations: z.record(z.string(), z.object({
    title: z.string().min(1, '제목은 필수입니다.'),
    summary: z.string().optional(),
    content: z.string().min(1, '내용은 필수입니다.')
  })).optional(),
  publishedAt: z.string().optional().nullable(),
  infoUpdatedAt: z.string().optional().nullable(),
});

// 정부 정보 삭제 스키마
export const deleteGovInfoSchema = z.object({
  id: idSchema,
});

// 정부 정보 조회 스키마
export const getGovInfoSchema = z.object({
  id: idSchema,
});

// 정부 정보 목록 조회 스키마
export const getGovInfosSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  categoryId: z.string().optional(),
  status: z.enum(['draft', 'published', 'archived']).optional(),
  isImportant: z.boolean().optional(),
  query: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// 정부 정보 이미지 업로드 스키마
export const uploadGovInfoImageSchema = z.object({
  govInfoId: idSchema,
  url: z.string().url('유효한 URL 형식이 아닙니다.'),
  alt: z.string().optional(),
  caption: z.string().optional(),
  order: z.number().int().min(0).default(0),
});

// 정부 정보 이미지 삭제 스키마
export const deleteGovInfoImageSchema = z.object({
  id: idSchema,
});

// 타입 정의
export type CreateGovInfoInput = z.infer<typeof createGovInfoSchema>;
export type UpdateGovInfoInput = z.infer<typeof updateGovInfoSchema>;
export type DeleteGovInfoInput = z.infer<typeof deleteGovInfoSchema>;
export type GetGovInfoInput = z.infer<typeof getGovInfoSchema>;
export type GetGovInfosInput = z.infer<typeof getGovInfosSchema>;
export type UploadGovInfoImageInput = z.infer<typeof uploadGovInfoImageSchema>;
export type DeleteGovInfoImageInput = z.infer<typeof deleteGovInfoImageSchema>;
