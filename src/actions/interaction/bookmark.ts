'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAuthAndData,
} from '../utils';
import { toggleBookmarkSchema } from './schemas';

// 타입 정의
type ToggleBookmarkInput = z.infer<typeof toggleBookmarkSchema>;

/**
 * 게시글 북마크 토글 액션
 */
export const toggleBookmark = withValidation(
  toggleBookmarkSchema,
  withAuthAndData(async (userId: string, data: ToggleBookmarkInput): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 확인
      const post = await prisma.post.findUnique({
        where: { id: data.postId },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      // 이미 북마크했는지 확인
      const existingBookmark = await prisma.bookmark.findUnique({
        where: {
          postId_userId: {
            postId: data.postId,
            userId: userId,
          },
        },
      });

      let result;
      
      if (existingBookmark) {
        // 북마크 취소
        await prisma.bookmark.delete({
          where: {
            postId_userId: {
              postId: data.postId,
              userId: userId,
            },
          },
        });

        result = {
          action: 'unbookmarked',
          postId: data.postId,
        };
      } else {
        // 북마크 추가
        await prisma.bookmark.create({
          data: {
            postId: data.postId,
            userId: userId,
          },
        });

        result = {
          action: 'bookmarked',
          postId: data.postId,
        };
      }

      return createSuccessResponse(result);
    } catch (error) {
      console.error('북마크 토글 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 게시글 북마크 상태 확인 액션
 */
export const getBookmarkStatus = withAuthAndData(async (userId: string, postId: string): Promise<ActionResponse> => {
  try {
    // 게시글 존재 여부 확인
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
    }

    // 북마크 상태 확인
    const bookmark = await prisma.bookmark.findUnique({
      where: {
        postId_userId: {
          postId: postId,
          userId: userId,
        },
      },
    });

    return createSuccessResponse({
      isBookmarked: !!bookmark,
      postId: postId,
    });
  } catch (error) {
    console.error('북마크 상태 확인 중 오류 발생:', error);
    throw error;
  }
});

/**
 * 사용자 북마크 목록 조회 액션
 */
export const getUserBookmarks = withAuthAndData(async (
  userId: string,
  page: number = 1,
  limit: number = 10
): Promise<ActionResponse> => {
  try {
    const skip = (page - 1) * limit;

    // 북마크 목록 조회
    const [bookmarks, totalCount] = await Promise.all([
      prisma.bookmark.findMany({
        where: { userId: userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        include: {
          post: {
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              category: true,
              _count: {
                select: {
                  likes: true,
                  comments: true,
                },
              },
            },
          },
        },
      }),
      prisma.bookmark.count({
        where: { userId: userId },
      }),
    ]);

    return createSuccessResponse({
      bookmarks: bookmarks.map(bookmark => ({
        id: bookmark.id,
        createdAt: bookmark.createdAt,
        post: bookmark.post,
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: skip + bookmarks.length < totalCount,
      },
    });
  } catch (error) {
    console.error('북마크 목록 조회 중 오류 발생:', error);
    throw error;
  }
});
