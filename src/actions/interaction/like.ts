'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAuthAndData,
} from '../utils';
import { toggleLikeSchema } from './schemas';

// 타입 정의
type ToggleLikeInput = z.infer<typeof toggleLikeSchema>;

/**
 * 게시글 좋아요 토글 액션
 */
export const toggleLike = withValidation(
  toggleLikeSchema,
  withAuthAndData(async (userId: string, data: ToggleLikeInput): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 확인
      const post = await prisma.post.findUnique({
        where: { id: data.postId },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      // 이미 좋아요를 눌렀는지 확인
      const existingLike = await prisma.postLike.findUnique({
        where: {
          postId_userId: {
            postId: data.postId,
            userId: userId,
          },
        },
      });

      let result;
      
      if (existingLike) {
        // 좋아요 취소
        await prisma.postLike.delete({
          where: {
            postId_userId: {
              postId: data.postId,
              userId: userId,
            },
          },
        });

        // 게시글 작성자의 좋아요 수 감소
        await prisma.user.update({
          where: { id: post.authorId },
          data: {
            likeCount: {
              decrement: 1,
            },
          },
        });

        result = {
          action: 'unliked',
          postId: data.postId,
        };
      } else {
        // 좋아요 추가
        await prisma.postLike.create({
          data: {
            postId: data.postId,
            userId: userId,
          },
        });

        // 게시글 작성자의 좋아요 수 증가
        await prisma.user.update({
          where: { id: post.authorId },
          data: {
            likeCount: {
              increment: 1,
            },
          },
        });

        result = {
          action: 'liked',
          postId: data.postId,
        };
      }

      return createSuccessResponse(result);
    } catch (error) {
      console.error('좋아요 토글 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 게시글 좋아요 상태 확인 액션
 */
export const getLikeStatus = withAuthAndData(async (userId: string, postId: string): Promise<ActionResponse> => {
  try {
    // 게시글 존재 여부 확인
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
    }

    // 좋아요 상태 확인
    const like = await prisma.postLike.findUnique({
      where: {
        postId_userId: {
          postId: postId,
          userId: userId,
        },
      },
    });

    return createSuccessResponse({
      isLiked: !!like,
      postId: postId,
    });
  } catch (error) {
    console.error('좋아요 상태 확인 중 오류 발생:', error);
    throw error;
  }
});

/**
 * 게시글 좋아요 수 조회 액션
 */
export const getLikeCount = async (postId: string): Promise<ActionResponse> => {
  try {
    // 게시글 존재 여부 확인
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
    }

    // 좋아요 수 조회
    const likeCount = await prisma.postLike.count({
      where: { postId: postId },
    });

    return createSuccessResponse({
      count: likeCount,
      postId: postId,
    });
  } catch (error) {
    console.error('좋아요 수 조회 중 오류 발생:', error);
    throw error;
  }
};
