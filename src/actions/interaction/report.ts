'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAuthAndData,
} from '../utils';
import { reportPostSchema, reportCommentSchema, reportUserSchema } from './schemas';

// 타입 정의
type ReportPostInput = z.infer<typeof reportPostSchema>;
type ReportCommentInput = z.infer<typeof reportCommentSchema>;
type ReportUserInput = z.infer<typeof reportUserSchema>;

/**
 * 게시글 신고 액션
 */
export const reportPost = withValidation(
  reportPostSchema,
  withAuthAndData(async (userId: string, data: ReportPostInput): Promise<ActionResponse> => {
    try {
      // 게시글 존재 여부 확인
      const post = await prisma.post.findUnique({
        where: { id: data.postId },
      });

      if (!post) {
        return createNotFoundErrorResponse('게시글을 찾을 수 없습니다.');
      }

      // 자신의 게시글은 신고할 수 없음
      if (post.authorId === userId) {
        return createNotFoundErrorResponse('자신의 게시글은 신고할 수 없습니다.');
      }

      // 이미 신고했는지 확인
      const existingReport = await prisma.report.findFirst({
        where: {
          reporterId: userId,
          postId: data.postId,
        },
      });

      if (existingReport) {
        return createSuccessResponse({
          message: '이미 신고한 게시글입니다.',
          alreadyReported: true,
        });
      }

      // 신고 생성
      await prisma.report.create({
        data: {
          reason: data.reason,
          details: data.details,
          reporterId: userId,
          postId: data.postId,
          targetUserId: post.authorId,
        },
      });

      return createSuccessResponse({
        message: '게시글이 성공적으로 신고되었습니다.',
      });
    } catch (error) {
      console.error('게시글 신고 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 댓글 신고 액션
 */
export const reportComment = withValidation(
  reportCommentSchema,
  withAuthAndData(async (userId: string, data: ReportCommentInput): Promise<ActionResponse> => {
    try {
      // 댓글 존재 여부 확인
      const comment = await prisma.postComment.findUnique({
        where: { id: data.commentId },
      });

      if (!comment) {
        return createNotFoundErrorResponse('댓글을 찾을 수 없습니다.');
      }

      // 자신의 댓글은 신고할 수 없음
      if (comment.authorId === userId) {
        return createNotFoundErrorResponse('자신의 댓글은 신고할 수 없습니다.');
      }

      // 이미 신고했는지 확인
      const existingReport = await prisma.report.findFirst({
        where: {
          reporterId: userId,
          commentId: data.commentId,
        },
      });

      if (existingReport) {
        return createSuccessResponse({
          message: '이미 신고한 댓글입니다.',
          alreadyReported: true,
        });
      }

      // 신고 생성
      await prisma.report.create({
        data: {
          reason: data.reason,
          details: data.details,
          reporterId: userId,
          commentId: data.commentId,
          targetUserId: comment.authorId,
        },
      });

      return createSuccessResponse({
        message: '댓글이 성공적으로 신고되었습니다.',
      });
    } catch (error) {
      console.error('댓글 신고 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 사용자 신고 액션
 */
export const reportUser = withValidation(
  reportUserSchema,
  withAuthAndData(async (userId: string, data: ReportUserInput): Promise<ActionResponse> => {
    try {
      // 사용자 존재 여부 확인
      const targetUser = await prisma.user.findUnique({
        where: { id: data.userId },
      });

      if (!targetUser) {
        return createNotFoundErrorResponse('사용자를 찾을 수 없습니다.');
      }

      // 자기 자신은 신고할 수 없음
      if (data.userId === userId) {
        return createNotFoundErrorResponse('자기 자신은 신고할 수 없습니다.');
      }

      // 이미 신고했는지 확인
      const existingReport = await prisma.report.findFirst({
        where: {
          reporterId: userId,
          targetUserId: data.userId,
          postId: null,
          commentId: null,
        },
      });

      if (existingReport) {
        return createSuccessResponse({
          message: '이미 신고한 사용자입니다.',
          alreadyReported: true,
        });
      }

      // 신고 생성
      await prisma.report.create({
        data: {
          reason: data.reason,
          details: data.details,
          reporterId: userId,
          targetUserId: data.userId,
        },
      });

      return createSuccessResponse({
        message: '사용자가 성공적으로 신고되었습니다.',
      });
    } catch (error) {
      console.error('사용자 신고 중 오류 발생:', error);
      throw error;
    }
  })
);
