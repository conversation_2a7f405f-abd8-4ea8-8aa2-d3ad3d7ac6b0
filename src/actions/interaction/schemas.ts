import { z } from 'zod';
import { idSchema } from '../utils/schemas';

/**
 * 상호작용 관련 Zod 스키마 정의
 */

// 좋아요 추가/제거 스키마
export const toggleLikeSchema = z.object({
  postId: idSchema,
});

// 북마크 추가/제거 스키마
export const toggleBookmarkSchema = z.object({
  postId: idSchema,
});

// 게시글 신고 스키마
export const reportPostSchema = z.object({
  postId: idSchema,
  reason: z.string().min(1, '신고 사유는 필수입니다.').max(500, '신고 사유는 최대 500자까지 가능합니다.'),
  details: z.string().max(1000, '상세 내용은 최대 1000자까지 가능합니다.').optional(),
});

// 댓글 신고 스키마
export const reportCommentSchema = z.object({
  commentId: idSchema,
  reason: z.string().min(1, '신고 사유는 필수입니다.').max(500, '신고 사유는 최대 500자까지 가능합니다.'),
  details: z.string().max(1000, '상세 내용은 최대 1000자까지 가능합니다.').optional(),
});

// 사용자 신고 스키마
export const reportUserSchema = z.object({
  userId: idSchema,
  reason: z.string().min(1, '신고 사유는 필수입니다.').max(500, '신고 사유는 최대 500자까지 가능합니다.'),
  details: z.string().max(1000, '상세 내용은 최대 1000자까지 가능합니다.').optional(),
});

// 알림 설정 스키마
export const updateNotificationSettingsSchema = z.object({
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  commentNotifications: z.boolean(),
  likeNotifications: z.boolean(),
  mentionNotifications: z.boolean(),
  followNotifications: z.boolean(),
});

// 알림 읽음 표시 스키마
export const markNotificationAsReadSchema = z.object({
  notificationId: idSchema,
});

// 모든 알림 읽음 표시 스키마
export const markAllNotificationsAsReadSchema = z.object({});
