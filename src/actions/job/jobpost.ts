"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
  cacheAction,
  CacheTag,
  getPaginationParams,
  getSortParams,
  createPaginationResult,
  logAction,
  logActionError,
} from "../utils";
import {
  createJobPostSchema,
  updateJobPostSchema,
  deleteJobPostSchema,
  getJobPostSchema,
  getJobPostsSchema,
  incrementViewCountSchema,
  CreateJobPostInput,
  UpdateJobPostInput,
  DeleteJobPostInput,
  GetJobPostInput,
  GetJobPostsInput,
  IncrementViewCountInput,
} from "./schemas";

/**
 * 구인·구직 정보 생성 액션
 */
export const createJobPost = withValidation(
  createJobPostSchema,
  withAuthAndData(async (userId: string, data: CreateJobPostInput): Promise<ActionResponse> => {
    try {
      logAction("createJobPost", userId);

      // 카테고리 존재 여부 확인 (카테고리가 제공된 경우)
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
        }
      }

      // 만료일 처리
      let expiresAt = undefined;
      if (data.expiresAt) {
        expiresAt = new Date(data.expiresAt);
      }

      // 구인·구직 정보 생성
      const jobPost = await prisma.jobPost.create({
        data: {
          title: data.title,
          description: data.description,
          type: data.type,
          company: data.company || null,
          jobType: data.jobType || null,
          industry: data.industry || null,
          location: data.location,
          salary: data.salary || null,
          requiredSkills: data.requiredSkills || null,
          contactInfo: data.contactInfo,
          authorId: userId,
          categoryId: data.categoryId || null,
          expiresAt: expiresAt,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
          category: true,
        },
      });

      return createSuccessResponse(jobPost);
    } catch (error) {
      logActionError("createJobPost", error);
      throw error;
    }
  })
);

/**
 * 구인·구직 정보 수정 액션
 */
export const updateJobPost = withValidation(
  updateJobPostSchema,
  withAuthAndData(async (userId: string, data: UpdateJobPostInput): Promise<ActionResponse> => {
    try {
      logAction("updateJobPost", userId, { jobPostId: data.id });

      // 구인·구직 정보 존재 여부 및 작성자 확인
      const jobPost = await prisma.jobPost.findUnique({
        where: { id: data.id },
      });

      if (!jobPost) {
        return createNotFoundErrorResponse("구인·구직 정보를 찾을 수 없습니다.");
      }

      // 작성자 또는 관리자만 수정 가능
      if (jobPost.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse("구인·구직 정보를 수정할 권한이 없습니다.");
        }
      }

      // 카테고리 존재 여부 확인 (카테고리가 제공된 경우)
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
        }
      }

      // 만료일 처리
      let expiresAt = undefined;
      if (data.expiresAt) {
        expiresAt = new Date(data.expiresAt);
      }

      // 구인·구직 정보 수정
      const updatedJobPost = await prisma.jobPost.update({
        where: { id: data.id },
        data: {
          title: data.title,
          description: data.description,
          type: data.type,
          company: data.company,
          jobType: data.jobType,
          industry: data.industry,
          location: data.location,
          salary: data.salary,
          requiredSkills: data.requiredSkills,
          contactInfo: data.contactInfo,
          categoryId: data.categoryId,
          status: data.status,
          expiresAt: expiresAt,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              displayName: true,
              image: true,
            },
          },
          category: true,
        },
      });

      return createSuccessResponse(updatedJobPost);
    } catch (error) {
      logActionError("updateJobPost", error);
      throw error;
    }
  })
);

/**
 * 구인·구직 정보 삭제 액션
 */
export const deleteJobPost = withValidation(
  deleteJobPostSchema,
  withAuthAndData(async (userId: string, data: DeleteJobPostInput): Promise<ActionResponse> => {
    try {
      logAction("deleteJobPost", userId, { jobPostId: data.id });

      // 구인·구직 정보 존재 여부 및 작성자 확인
      const jobPost = await prisma.jobPost.findUnique({
        where: { id: data.id },
      });

      if (!jobPost) {
        return createNotFoundErrorResponse("구인·구직 정보를 찾을 수 없습니다.");
      }

      // 작성자 또는 관리자만 삭제 가능
      if (jobPost.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse("구인·구직 정보를 삭제할 권한이 없습니다.");
        }
      }

      // 구인·구직 정보 삭제
      await prisma.jobPost.delete({
        where: { id: data.id },
      });

      return createSuccessResponse({ id: data.id });
    } catch (error) {
      logActionError("deleteJobPost", error);
      throw error;
    }
  })
);

/**
 * 구인·구직 정보 조회 액션
 */
export const getJobPost = withValidation(
  getJobPostSchema,
  cacheAction(
    async (data: GetJobPostInput): Promise<ActionResponse> => {
      try {
        const jobPost = await prisma.jobPost.findUnique({
          where: { id: data.id },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        if (!jobPost) {
          return createNotFoundErrorResponse("구인·구직 정보를 찾을 수 없습니다.");
        }

        return createSuccessResponse(jobPost);
      } catch (error) {
        logActionError("getJobPost", error);
        throw error;
      }
    },
    { tags: [CacheTag.JOB_POST], revalidate: 60 }
  )
);

/**
 * 구인·구직 정보 목록 조회 액션
 */
export const getJobPosts = withValidation(
  getJobPostsSchema,
  cacheAction(
    async (data: GetJobPostsInput): Promise<ActionResponse> => {
      try {
        const { page, limit } = getPaginationParams(data);
        const { sortBy, sortOrder } = getSortParams(data);

        // 필터 조건 구성
        const where: any = {};

        if (data.type) {
          where.type = data.type;
        }

        if (data.jobType) {
          where.jobType = data.jobType;
        }

        if (data.categoryId) {
          where.categoryId = data.categoryId;
        }

        if (data.location) {
          where.location = { contains: data.location, mode: "insensitive" };
        }

        if (data.industry) {
          where.industry = { contains: data.industry, mode: "insensitive" };
        }

        if (data.query) {
          where.OR = [
            { title: { contains: data.query, mode: "insensitive" } },
            { description: { contains: data.query, mode: "insensitive" } },
            { company: { contains: data.query, mode: "insensitive" } },
          ];
        }

        // 기본적으로 활성 상태인 구인·구직 정보만 표시
        where.status = "active";

        // 만료되지 않은 구인·구직 정보만 표시
        where.OR = [
          { expiresAt: { gt: new Date() } },
          { expiresAt: null },
        ];

        // 총 항목 수 조회
        const total = await prisma.jobPost.count({ where });

        // 구인·구직 정보 목록 조회
        const jobPosts = await prisma.jobPost.findMany({
          where,
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
          orderBy: {
            [sortBy]: sortOrder,
          },
          skip: (page - 1) * limit,
          take: limit,
        });

        return createSuccessResponse(
          createPaginationResult(jobPosts, total, page, limit)
        );
      } catch (error) {
        logActionError("getJobPosts", error);
        throw error;
      }
    },
    {
      tags: [
        CacheTag.JOB_POSTS,
        ...(data.categoryId ? [CacheTag.CATEGORY] : []),
      ],
      revalidate: 60,
    }
  )
);

/**
 * 조회수 증가 액션
 */
export const incrementViewCount = withValidation(
  incrementViewCountSchema,
  async (data: IncrementViewCountInput): Promise<ActionResponse> => {
    try {
      const jobPost = await prisma.jobPost.findUnique({
        where: { id: data.id },
      });

      if (!jobPost) {
        return createNotFoundErrorResponse("구인·구직 정보를 찾을 수 없습니다.");
      }

      await prisma.jobPost.update({
        where: { id: data.id },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });

      return createSuccessResponse({ success: true });
    } catch (error) {
      logActionError("incrementViewCount", error);
      throw error;
    }
  }
);
