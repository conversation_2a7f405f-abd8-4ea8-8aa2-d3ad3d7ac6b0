import { z } from "zod";
import { idSchema, titleSchema, contentSchema } from "../utils/schemas";

/**
 * 구인·구직 정보 관련 Zod 스키마 정의
 */

// 구인·구직 정보 생성 스키마
export const createJobPostSchema = z.object({
  title: titleSchema,
  description: contentSchema,
  type: z.enum(["JOB_OFFER", "JOB_SEEK"]).default("JOB_OFFER"),
  company: z.string().optional(),
  jobType: z.enum(["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"]).optional(),
  industry: z.string().optional(),
  location: z.string().min(1, "지역은 필수입니다."),
  salary: z.string().optional(),
  requiredSkills: z.string().optional(),
  contactInfo: z.string().min(1, "연락처는 필수입니다."),
  categoryId: idSchema.optional(),
  expiresAt: z.string().optional(),
});

// 구인·구직 정보 수정 스키마
export const updateJobPostSchema = z.object({
  id: idSchema,
  title: titleSchema.optional(),
  description: contentSchema.optional(),
  type: z.enum(["JOB_OFFER", "JOB_SEEK"]).optional(),
  company: z.string().optional(),
  jobType: z.enum(["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"]).optional(),
  industry: z.string().optional(),
  location: z.string().optional(),
  salary: z.string().optional(),
  requiredSkills: z.string().optional(),
  contactInfo: z.string().optional(),
  categoryId: idSchema.optional(),
  status: z.enum(["active", "closed"]).optional(),
  expiresAt: z.string().optional(),
});

// 구인·구직 정보 삭제 스키마
export const deleteJobPostSchema = z.object({
  id: idSchema,
});

// 구인·구직 정보 조회 스키마
export const getJobPostSchema = z.object({
  id: idSchema,
});

// 구인·구직 정보 목록 조회 스키마
export const getJobPostsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(50).default(10),
  type: z.enum(["JOB_OFFER", "JOB_SEEK"]).optional(),
  jobType: z.enum(["FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP"]).optional(),
  categoryId: idSchema.optional(),
  location: z.string().optional(),
  industry: z.string().optional(),
  query: z.string().optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "title", "expiresAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// 조회수 증가 스키마
export const incrementViewCountSchema = z.object({
  id: idSchema,
});

// 타입 정의
export type CreateJobPostInput = z.infer<typeof createJobPostSchema>;
export type UpdateJobPostInput = z.infer<typeof updateJobPostSchema>;
export type DeleteJobPostInput = z.infer<typeof deleteJobPostSchema>;
export type GetJobPostInput = z.infer<typeof getJobPostSchema>;
export type GetJobPostsInput = z.infer<typeof getJobPostsSchema>;
export type IncrementViewCountInput = z.infer<typeof incrementViewCountSchema>;
