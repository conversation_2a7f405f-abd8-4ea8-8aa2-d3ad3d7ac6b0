'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
  logAction,
  logActionError,
} from '../utils';
import {
  addLifeInfoImageSchema,
  updateLifeInfoImageSchema,
  deleteLifeInfoImageSchema,
} from './schemas';

// 타입 정의
type AddLifeInfoImageInput = z.infer<typeof addLifeInfoImageSchema>;
type UpdateLifeInfoImageInput = z.infer<typeof updateLifeInfoImageSchema>;
type DeleteLifeInfoImageInput = z.infer<typeof deleteLifeInfoImageSchema>;

/**
 * 생활 정보 이미지 추가 액션
 */
export const addLifeInfoImage = withValidation(
  addLifeInfoImageSchema,
  withAuthAndData(async (userId: string, data: AddLifeInfoImageInput): Promise<ActionResponse> => {
    try {
      // 생활 정보 존재 여부 및 권한 확인
      const lifeInfo = await prisma.lifeInfo.findUnique({
        where: { id: data.lifeInfoId },
      });

      if (!lifeInfo) {
        return createNotFoundErrorResponse('생활 정보를 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 이미지 추가 가능
      if (lifeInfo.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('이미지를 추가할 권한이 없습니다.');
        }
      }

      // 이미지 추가
      const image = await prisma.lifeInfoImage.create({
        data: {
          url: data.url,
          alt: data.alt,
          caption: data.caption,
          order: data.order,
          lifeInfoId: data.lifeInfoId,
        },
      });

      logAction('addLifeInfoImage', { userId, imageId: image.id, lifeInfoId: data.lifeInfoId });
      return createSuccessResponse(image);
    } catch (error) {
      logActionError('addLifeInfoImage', error);
      throw error;
    }
  })
);

/**
 * 생활 정보 이미지 수정 액션
 */
export const updateLifeInfoImage = withValidation(
  updateLifeInfoImageSchema,
  withAuthAndData(async (userId: string, data: UpdateLifeInfoImageInput): Promise<ActionResponse> => {
    try {
      // 이미지 존재 여부 확인
      const image = await prisma.lifeInfoImage.findUnique({
        where: { id: data.id },
        include: {
          lifeInfo: true,
        },
      });

      if (!image) {
        return createNotFoundErrorResponse('이미지를 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 이미지 수정 가능
      if (image.lifeInfo.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('이미지를 수정할 권한이 없습니다.');
        }
      }

      // 이미지 수정
      const updatedImage = await prisma.lifeInfoImage.update({
        where: { id: data.id },
        data: {
          url: data.url,
          alt: data.alt,
          caption: data.caption,
          order: data.order,
        },
      });

      logAction('updateLifeInfoImage', { userId, imageId: updatedImage.id });
      return createSuccessResponse(updatedImage);
    } catch (error) {
      logActionError('updateLifeInfoImage', error);
      throw error;
    }
  })
);

/**
 * 생활 정보 이미지 삭제 액션
 */
export const deleteLifeInfoImage = withValidation(
  deleteLifeInfoImageSchema,
  withAuthAndData(async (userId: string, data: DeleteLifeInfoImageInput): Promise<ActionResponse> => {
    try {
      // 이미지 존재 여부 확인
      const image = await prisma.lifeInfoImage.findUnique({
        where: { id: data.id },
        include: {
          lifeInfo: true,
        },
      });

      if (!image) {
        return createNotFoundErrorResponse('이미지를 찾을 수 없습니다.');
      }

      // 작성자 또는 관리자만 이미지 삭제 가능
      if (image.lifeInfo.authorId !== userId) {
        // 관리자 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { isAdmin: true },
        });

        if (!user?.isAdmin) {
          return createForbiddenErrorResponse('이미지를 삭제할 권한이 없습니다.');
        }
      }

      // 이미지 삭제
      await prisma.lifeInfoImage.delete({
        where: { id: data.id },
      });

      logAction('deleteLifeInfoImage', { userId, imageId: data.id });
      return createSuccessResponse({ id: data.id });
    } catch (error) {
      logActionError('deleteLifeInfoImage', error);
      throw error;
    }
  })
);
