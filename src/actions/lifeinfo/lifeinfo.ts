"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
  withAdminAndData,
  cacheAction,
  CacheTag,
  getPaginationParams,
  getSortParams,
  createPaginationResult,
  logAction,
  logActionError,
} from "../utils";
import {
  createLifeInfoSchema,
  updateLifeInfoSchema,
  deleteLifeInfoSchema,
  getLifeInfoSchema,
  getLifeInfosSchema,
  incrementViewCountSchema,
} from "./schemas";

// 타입 정의
type CreateLifeInfoInput = z.infer<typeof createLifeInfoSchema>;
type UpdateLifeInfoInput = z.infer<typeof updateLifeInfoSchema>;
type DeleteLifeInfoInput = z.infer<typeof deleteLifeInfoSchema>;
type GetLifeInfoInput = z.infer<typeof getLifeInfoSchema>;
type GetLifeInfosInput = z.infer<typeof getLifeInfosSchema>;
type IncrementViewCountInput = z.infer<typeof incrementViewCountSchema>;

/**
 * 생활 정보 생성 액션
 */
export const createLifeInfo = withValidation(
  createLifeInfoSchema,
  withAuthAndData(
    async (
      userId: string,
      data: CreateLifeInfoInput
    ): Promise<ActionResponse> => {
      try {
        // 카테고리 존재 여부 확인
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
        }

        // 슬러그 중복 확인
        const existingSlug = await prisma.lifeInfo.findUnique({
          where: { slug: data.slug },
        });

        if (existingSlug) {
          return createForbiddenErrorResponse("이미 사용 중인 슬러그입니다.");
        }

        // 생활 정보 생성
        const lifeInfo = await prisma.lifeInfo.create({
          data: {
            title: data.title,
            slug: data.slug,
            summary: data.summary,
            content: data.content,
            contentHtml: data.contentHtml || "<p></p>",
            status: data.status,
            featured: data.featured,
            translations: data.translations,
            categoryId: data.categoryId,
            authorId: userId,
            publishedAt: data.status === "published" ? new Date() : undefined,
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        logAction("createLifeInfo", { userId, lifeInfoId: lifeInfo.id });
        return createSuccessResponse(lifeInfo);
      } catch (error) {
        logActionError("createLifeInfo", error);
        throw error;
      }
    }
  )
);

/**
 * 생활 정보 수정 액션
 */
export const updateLifeInfo = withValidation(
  updateLifeInfoSchema,
  withAuthAndData(
    async (
      userId: string,
      data: UpdateLifeInfoInput
    ): Promise<ActionResponse> => {
      try {
        // 생활 정보 존재 여부 및 작성자 확인
        const lifeInfo = await prisma.lifeInfo.findUnique({
          where: { id: data.id },
        });

        if (!lifeInfo) {
          return createNotFoundErrorResponse("생활 정보를 찾을 수 없습니다.");
        }

        // 작성자 또는 관리자만 수정 가능
        if (lifeInfo.authorId !== userId) {
          // 관리자 여부 확인
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isAdmin: true },
          });

          if (!user?.isAdmin) {
            return createForbiddenErrorResponse(
              "생활 정보를 수정할 권한이 없습니다."
            );
          }
        }

        // 슬러그 중복 확인 (변경된 경우에만)
        if (data.slug && data.slug !== lifeInfo.slug) {
          const existingSlug = await prisma.lifeInfo.findUnique({
            where: { slug: data.slug },
          });

          if (existingSlug) {
            return createForbiddenErrorResponse("이미 사용 중인 슬러그입니다.");
          }
        }

        // 카테고리 변경 시 존재 여부 확인
        if (data.categoryId) {
          const category = await prisma.category.findUnique({
            where: { id: data.categoryId },
          });

          if (!category) {
            return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
          }
        }

        // 상태가 published로 변경되었고 publishedAt이 없는 경우 현재 시간 설정
        const publishedAt =
          data.status === "published" && !lifeInfo.publishedAt
            ? new Date()
            : data.publishedAt
            ? new Date(data.publishedAt)
            : undefined;

        // 생활 정보 수정
        const updatedLifeInfo = await prisma.lifeInfo.update({
          where: { id: data.id },
          data: {
            title: data.title,
            slug: data.slug,
            summary: data.summary,
            content: data.content,
            contentHtml: data.contentHtml,
            status: data.status,
            featured: data.featured,
            translations: data.translations,
            categoryId: data.categoryId,
            publishedAt,
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        logAction("updateLifeInfo", { userId, lifeInfoId: updatedLifeInfo.id });
        return createSuccessResponse(updatedLifeInfo);
      } catch (error) {
        logActionError("updateLifeInfo", error);
        throw error;
      }
    }
  )
);

/**
 * 생활 정보 삭제 액션
 */
export const deleteLifeInfo = withValidation(
  deleteLifeInfoSchema,
  withAuthAndData(
    async (
      userId: string,
      data: DeleteLifeInfoInput
    ): Promise<ActionResponse> => {
      try {
        // 생활 정보 존재 여부 및 작성자 확인
        const lifeInfo = await prisma.lifeInfo.findUnique({
          where: { id: data.id },
        });

        if (!lifeInfo) {
          return createNotFoundErrorResponse("생활 정보를 찾을 수 없습니다.");
        }

        // 작성자 또는 관리자만 삭제 가능
        if (lifeInfo.authorId !== userId) {
          // 관리자 여부 확인
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isAdmin: true },
          });

          if (!user?.isAdmin) {
            return createForbiddenErrorResponse(
              "생활 정보를 삭제할 권한이 없습니다."
            );
          }
        }

        // 생활 정보 삭제
        await prisma.lifeInfo.delete({
          where: { id: data.id },
        });

        logAction("deleteLifeInfo", { userId, lifeInfoId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteLifeInfo", error);
        throw error;
      }
    }
  )
);

/**
 * 생활 정보 조회 액션
 */
export const getLifeInfo = withValidation(
  getLifeInfoSchema,
  cacheAction(
    async (data: GetLifeInfoInput): Promise<ActionResponse> => {
      try {
        const lifeInfo = await prisma.lifeInfo.findUnique({
          where: { id: data.id },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            images: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        if (!lifeInfo) {
          return createNotFoundErrorResponse("생활 정보를 찾을 수 없습니다.");
        }

        return createSuccessResponse(lifeInfo);
      } catch (error) {
        logActionError("getLifeInfo", error);
        throw error;
      }
    },
    { tags: [CacheTag.LIFE_INFO], revalidate: 60 }
  )
);

/**
 * 생활 정보 목록 조회 액션
 */
export const getLifeInfos = withValidation(
  getLifeInfosSchema,
  cacheAction(
    async (data: GetLifeInfosInput): Promise<ActionResponse> => {
      try {
        const { page, limit } = getPaginationParams(data);
        const { sortBy, sortOrder } = getSortParams(data);

        // 필터 조건 구성
        const where: any = {};

        if (data.categoryId) {
          where.categoryId = data.categoryId;
        }

        if (data.status) {
          where.status = data.status;
        } else {
          // 기본적으로 published 상태만 표시
          where.status = "published";
        }

        if (data.featured !== undefined) {
          where.featured = data.featured;
        }

        if (data.query) {
          where.OR = [
            { title: { contains: data.query, mode: "insensitive" } },
            { summary: { contains: data.query, mode: "insensitive" } },
            { content: { contains: data.query, mode: "insensitive" } },
          ];
        }

        // 총 항목 수 조회
        const total = await prisma.lifeInfo.count({ where });

        // 생활 정보 목록 조회
        const lifeInfos = await prisma.lifeInfo.findMany({
          where,
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            images: {
              take: 1,
              orderBy: {
                order: "asc",
              },
            },
          },
          orderBy: {
            [sortBy]: sortOrder,
          },
          skip: (page - 1) * limit,
          take: limit,
        });

        return createSuccessResponse(
          createPaginationResult(lifeInfos, total, page, limit)
        );
      } catch (error) {
        logActionError("getLifeInfos", error);
        throw error;
      }
    },
    {
      tags: [
        CacheTag.LIFE_INFOS,
        ...(data.categoryId ? [CacheTag.CATEGORY] : []),
      ],
      revalidate: 60,
    }
  )
);

/**
 * 생활 정보 조회수 증가 액션
 */
export const incrementViewCount = withValidation(
  incrementViewCountSchema,
  async (data: IncrementViewCountInput): Promise<ActionResponse> => {
    try {
      const lifeInfo = await prisma.lifeInfo.findUnique({
        where: { id: data.id },
      });

      if (!lifeInfo) {
        return createNotFoundErrorResponse("생활 정보를 찾을 수 없습니다.");
      }

      const updatedLifeInfo = await prisma.lifeInfo.update({
        where: { id: data.id },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });

      return createSuccessResponse({ viewCount: updatedLifeInfo.viewCount });
    } catch (error) {
      logActionError("incrementViewCount", error);
      throw error;
    }
  }
);
