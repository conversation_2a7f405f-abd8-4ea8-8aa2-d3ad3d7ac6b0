import { z } from 'zod';
import { idSchema, titleSchema, contentSchema } from '../utils/schemas';

/**
 * 생활 정보 콘텐츠 관련 Zod 스키마 정의
 */

// 생활 정보 생성 스키마
export const createLifeInfoSchema = z.object({
  title: titleSchema,
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.'),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: contentSchema,
  contentHtml: z.string().optional(),
  categoryId: idSchema,
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  featured: z.boolean().default(false),
  translations: z.record(z.string(), z.object({
    title: z.string().min(1, '제목은 필수입니다.'),
    summary: z.string().optional(),
    content: z.string().min(1, '내용은 필수입니다.')
  })).optional(),
  publishedAt: z.string().optional(),
});

// 생활 정보 수정 스키마
export const updateLifeInfoSchema = z.object({
  id: idSchema,
  title: titleSchema.optional(),
  slug: z.string().max(255, '슬러그는 최대 255자까지 가능합니다.').optional(),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: contentSchema.optional(),
  contentHtml: z.string().optional(),
  categoryId: idSchema.optional(),
  status: z.enum(['draft', 'published', 'archived']).optional(),
  featured: z.boolean().optional(),
  translations: z.record(z.string(), z.object({
    title: z.string().min(1, '제목은 필수입니다.'),
    summary: z.string().optional(),
    content: z.string().min(1, '내용은 필수입니다.')
  })).optional(),
  publishedAt: z.string().optional(),
});

// 생활 정보 삭제 스키마
export const deleteLifeInfoSchema = z.object({
  id: idSchema,
});

// 생활 정보 조회 스키마
export const getLifeInfoSchema = z.object({
  id: idSchema,
});

// 생활 정보 목록 조회 스키마
export const getLifeInfosSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(50).default(10),
  categoryId: idSchema.optional(),
  status: z.enum(['draft', 'published', 'archived']).optional(),
  featured: z.boolean().optional(),
  query: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'title', 'viewCount']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// 생활 정보 이미지 추가 스키마
export const addLifeInfoImageSchema = z.object({
  lifeInfoId: idSchema,
  url: z.string().url('유효한 URL 형식이 아닙니다.'),
  alt: z.string().optional(),
  caption: z.string().optional(),
  order: z.number().int().min(0).default(0),
});

// 생활 정보 이미지 수정 스키마
export const updateLifeInfoImageSchema = z.object({
  id: idSchema,
  url: z.string().url('유효한 URL 형식이 아닙니다.').optional(),
  alt: z.string().optional(),
  caption: z.string().optional(),
  order: z.number().int().min(0).optional(),
});

// 생활 정보 이미지 삭제 스키마
export const deleteLifeInfoImageSchema = z.object({
  id: idSchema,
});

// 생활 정보 조회수 증가 스키마
export const incrementViewCountSchema = z.object({
  id: idSchema,
});
