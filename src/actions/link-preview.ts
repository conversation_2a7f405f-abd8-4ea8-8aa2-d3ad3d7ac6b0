'use server';

import { PreviewData } from '@/types/preview';

export async function getLinkPreview(url: string): Promise<PreviewData | null> {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/link-preview?url=${encodeURIComponent(url)}`
    );
    const data = await res.json();

    if (
      data === 'null' ||
      data === null ||
      typeof data === 'undefined' ||
      data.error
    ) {
      return null;
    } else {
      return data as PreviewData;
    }
  } catch (error) {
    console.error('Error fetching link preview:', error);
    return null;
  }
}
