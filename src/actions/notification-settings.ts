'use server';

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { db } from '@/lib/db';
import { auth } from '@/lib/auth';
import { UpdateNotificationSettingsParams } from '@/types/notification';

// 알림 설정 업데이트 스키마
const updateNotificationSettingsSchema = z.object({
  enableCommentNotifications: z.boolean().optional(),
  enableReplyNotifications: z.boolean().optional(),
  enableLikeNotifications: z.boolean().optional(),
  enableAnnouncementNotifications: z.boolean().optional(),
  enableMentionNotifications: z.boolean().optional(),
  enableSystemNotifications: z.boolean().optional(),
  enableEmailNotifications: z.boolean().optional(),
});

// 사용자 알림 설정 조회 함수
export async function getUserNotificationSettings() {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }
    
    const userId = session.user.id;
    
    // 사용자의 알림 설정 조회
    let settings = await db.notificationSettings.findUnique({
      where: { userId },
    });
    
    // 설정이 없으면 기본 설정으로 생성
    if (!settings) {
      settings = await db.notificationSettings.create({
        data: {
          userId,
        },
      });
    }
    
    return { success: true, settings };
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    return { success: false, error: 'Failed to fetch notification settings' };
  }
}

// 알림 설정 업데이트 함수
export async function updateNotificationSettings(data: UpdateNotificationSettingsParams) {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }
    
    const userId = session.user.id;
    const validatedData = updateNotificationSettingsSchema.parse(data);
    
    // 사용자의 알림 설정 조회
    const existingSettings = await db.notificationSettings.findUnique({
      where: { userId },
    });
    
    // 설정이 없으면 새로 생성, 있으면 업데이트
    if (!existingSettings) {
      await db.notificationSettings.create({
        data: {
          userId,
          ...validatedData,
        },
      });
    } else {
      await db.notificationSettings.update({
        where: { userId },
        data: validatedData,
      });
    }
    
    revalidatePath('/settings/notifications');
    return { success: true };
  } catch (error) {
    console.error('Error updating notification settings:', error);
    return { success: false, error: 'Failed to update notification settings' };
  }
}
