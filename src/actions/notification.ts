'use server';

import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { db } from '@/lib/db';
import { auth } from '@/lib/auth';
import { NotificationType } from '@/generated/prisma';
import { CreateNotificationParams, UpdateNotificationSettingsParams } from '@/types/notification';

// 알림 생성 스키마
const createNotificationSchema = z.object({
  userId: z.string(),
  senderId: z.string().optional(),
  type: z.nativeEnum(NotificationType),
  title: z.string().optional(),
  content: z.string(),
  relatedEntityId: z.string().optional(),
  relatedEntityType: z.string().optional(),
  link: z.string().optional(),
});

// 알림 생성 함수
export async function createNotification(data: CreateNotificationParams) {
  try {
    const validatedData = createNotificationSchema.parse(data);
    
    // 사용자의 알림 설정 확인
    const settings = await db.notificationSettings.findUnique({
      where: { userId: validatedData.userId },
    });
    
    // 설정이 없으면 기본 설정으로 생성
    if (!settings) {
      await db.notificationSettings.create({
        data: {
          userId: validatedData.userId,
        },
      });
    }
    
    // 알림 유형에 따라 설정 확인
    let shouldSendNotification = true;
    
    if (settings) {
      switch (validatedData.type) {
        case 'COMMENT':
          shouldSendNotification = settings.enableCommentNotifications;
          break;
        case 'REPLY':
          shouldSendNotification = settings.enableReplyNotifications;
          break;
        case 'LIKE':
          shouldSendNotification = settings.enableLikeNotifications;
          break;
        case 'ANNOUNCEMENT':
          shouldSendNotification = settings.enableAnnouncementNotifications;
          break;
        case 'MENTION':
          shouldSendNotification = settings.enableMentionNotifications;
          break;
        case 'SYSTEM':
          shouldSendNotification = settings.enableSystemNotifications;
          break;
      }
    }
    
    // 알림 설정이 활성화된 경우에만 알림 생성
    if (shouldSendNotification) {
      const notification = await db.notification.create({
        data: validatedData,
      });
      
      return { success: true, notification };
    }
    
    return { success: true, notification: null, message: 'Notification disabled by user settings' };
  } catch (error) {
    console.error('Error creating notification:', error);
    return { success: false, error: 'Failed to create notification' };
  }
}

// 사용자별 알림 조회 함수
export async function getNotificationsByUserId(userId: string, limit = 10, skip = 0, includeRead = false) {
  try {
    const where = {
      userId,
      ...(includeRead ? {} : { isRead: false }),
    };
    
    const [notifications, count] = await Promise.all([
      db.notification.findMany({
        where,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              image: true,
              displayName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip,
      }),
      db.notification.count({ where }),
    ]);
    
    return { 
      success: true, 
      notifications, 
      count,
      hasMore: count > skip + limit,
    };
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return { success: false, error: 'Failed to fetch notifications' };
  }
}

// 알림 읽음 표시 함수
export async function markNotificationAsRead(id: string) {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }
    
    const notification = await db.notification.findUnique({
      where: { id },
    });
    
    if (!notification) {
      return { success: false, error: 'Notification not found' };
    }
    
    if (notification.userId !== session.user.id) {
      return { success: false, error: 'Unauthorized' };
    }
    
    await db.notification.update({
      where: { id },
      data: { isRead: true },
    });
    
    revalidatePath('/notifications');
    return { success: true };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return { success: false, error: 'Failed to mark notification as read' };
  }
}

// 모든 알림 읽음 표시 함수
export async function markAllNotificationsAsRead() {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }
    
    await db.notification.updateMany({
      where: { 
        userId: session.user.id,
        isRead: false,
      },
      data: { isRead: true },
    });
    
    revalidatePath('/notifications');
    return { success: true };
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return { success: false, error: 'Failed to mark all notifications as read' };
  }
}

// 알림 삭제 함수
export async function deleteNotification(id: string) {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }
    
    const notification = await db.notification.findUnique({
      where: { id },
    });
    
    if (!notification) {
      return { success: false, error: 'Notification not found' };
    }
    
    if (notification.userId !== session.user.id) {
      return { success: false, error: 'Unauthorized' };
    }
    
    await db.notification.delete({
      where: { id },
    });
    
    revalidatePath('/notifications');
    return { success: true };
  } catch (error) {
    console.error('Error deleting notification:', error);
    return { success: false, error: 'Failed to delete notification' };
  }
}
