'use server';

import { NotificationType } from '@/generated/prisma';
import { createNotification } from '@/actions/notification';
import { db } from '@/lib/db';

/**
 * 댓글 알림 트리거
 * 게시글 작성자에게 새 댓글 알림을 보냅니다.
 */
export async function triggerCommentNotification(
  postId: string,
  commentId: string,
  commentContent: string,
  commentAuthorId: string
) {
  try {
    // 게시글 정보 조회
    const post = await db.post.findUnique({
      where: { id: postId },
      select: {
        id: true,
        userId: true,
        user: {
          select: {
            name: true,
            displayName: true,
          }
        }
      }
    });

    if (!post) return;

    // 자신의 게시글에 댓글을 달았을 경우 알림을 보내지 않음
    if (post.userId === commentAuthorId) return;

    // 댓글 작성자 정보 조회
    const commentAuthor = await db.user.findUnique({
      where: { id: commentAuthorId },
      select: {
        name: true,
        displayName: true,
      }
    });

    if (!commentAuthor) return;

    const authorName = commentAuthor.displayName || commentAuthor.name;
    const truncatedContent = commentContent.length > 50 
      ? `${commentContent.substring(0, 50)}...` 
      : commentContent;

    // 알림 생성
    await createNotification({
      userId: post.userId,
      senderId: commentAuthorId,
      type: NotificationType.COMMENT,
      content: `${authorName}님이 회원님의 게시글에 댓글을 남겼습니다: "${truncatedContent}"`,
      relatedEntityId: commentId,
      relatedEntityType: 'comment',
      link: `/posts/${postId}#comment-${commentId}`
    });
  } catch (error) {
    console.error('댓글 알림 생성 중 오류 발생:', error);
  }
}

/**
 * 답글 알림 트리거
 * 원 댓글 작성자에게 새 답글 알림을 보냅니다.
 */
export async function triggerReplyNotification(
  postId: string,
  parentCommentId: string,
  replyId: string,
  replyContent: string,
  replyAuthorId: string
) {
  try {
    // 원 댓글 정보 조회
    const parentComment = await db.postComment.findUnique({
      where: { id: parentCommentId },
      select: {
        id: true,
        userId: true,
        user: {
          select: {
            name: true,
            displayName: true,
          }
        }
      }
    });

    if (!parentComment) return;

    // 자신의 댓글에 답글을 달았을 경우 알림을 보내지 않음
    if (parentComment.userId === replyAuthorId) return;

    // 답글 작성자 정보 조회
    const replyAuthor = await db.user.findUnique({
      where: { id: replyAuthorId },
      select: {
        name: true,
        displayName: true,
      }
    });

    if (!replyAuthor) return;

    const authorName = replyAuthor.displayName || replyAuthor.name;
    const truncatedContent = replyContent.length > 50 
      ? `${replyContent.substring(0, 50)}...` 
      : replyContent;

    // 알림 생성
    await createNotification({
      userId: parentComment.userId,
      senderId: replyAuthorId,
      type: NotificationType.REPLY,
      content: `${authorName}님이 회원님의 댓글에 답글을 남겼습니다: "${truncatedContent}"`,
      relatedEntityId: replyId,
      relatedEntityType: 'reply',
      link: `/posts/${postId}#comment-${replyId}`
    });
  } catch (error) {
    console.error('답글 알림 생성 중 오류 발생:', error);
  }
}

/**
 * 좋아요 알림 트리거
 * 게시글 작성자에게 새 좋아요 알림을 보냅니다.
 */
export async function triggerLikeNotification(
  postId: string,
  likeAuthorId: string
) {
  try {
    // 게시글 정보 조회
    const post = await db.post.findUnique({
      where: { id: postId },
      select: {
        id: true,
        userId: true,
      }
    });

    if (!post) return;

    // 자신의 게시글에 좋아요를 눌렀을 경우 알림을 보내지 않음
    if (post.userId === likeAuthorId) return;

    // 좋아요 작성자 정보 조회
    const likeAuthor = await db.user.findUnique({
      where: { id: likeAuthorId },
      select: {
        name: true,
        displayName: true,
      }
    });

    if (!likeAuthor) return;

    const authorName = likeAuthor.displayName || likeAuthor.name;

    // 알림 생성
    await createNotification({
      userId: post.userId,
      senderId: likeAuthorId,
      type: NotificationType.LIKE,
      content: `${authorName}님이 회원님의 게시글을 좋아합니다.`,
      relatedEntityId: postId,
      relatedEntityType: 'post',
      link: `/posts/${postId}`
    });
  } catch (error) {
    console.error('좋아요 알림 생성 중 오류 발생:', error);
  }
}

/**
 * 공지사항 알림 트리거
 * 모든 사용자에게 새 공지사항 알림을 보냅니다.
 */
export async function triggerAnnouncementNotification(
  announcementId: string,
  announcementTitle: string,
  authorId: string
) {
  try {
    // 모든 사용자 조회 (관리자 제외)
    const users = await db.user.findMany({
      where: {
        isAdmin: false,
        id: {
          not: authorId // 작성자 제외
        }
      },
      select: {
        id: true,
      }
    });

    // 각 사용자에게 알림 생성
    for (const user of users) {
      await createNotification({
        userId: user.id,
        senderId: authorId,
        type: NotificationType.ANNOUNCEMENT,
        title: '새 공지사항',
        content: announcementTitle,
        relatedEntityId: announcementId,
        relatedEntityType: 'announcement',
        link: `/posts/${announcementId}`
      });
    }
  } catch (error) {
    console.error('공지사항 알림 생성 중 오류 발생:', error);
  }
}
