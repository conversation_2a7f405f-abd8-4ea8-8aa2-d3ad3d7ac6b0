"use server";

import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
  logAction,
  logActionError,
} from "../utils";
import { triggerCommentNotification } from "../notification/triggers";
import {
  createPostCommentSchema,
  updatePostCommentSchema,
  deletePostCommentSchema,
  CreatePostCommentInput,
  UpdatePostCommentInput,
  DeletePostCommentInput,
} from "./schemas";

/**
 * 댓글 생성 액션
 */
export const createPostComment = withValidation(
  createPostCommentSchema,
  withAuthAndData(
    async (
      userId: string,
      data: CreatePostCommentInput
    ): Promise<ActionResponse> => {
      try {
        // 게시글 존재 여부 확인
        const post = await prisma.post.findUnique({
          where: {
            id: data.postId,
            deletedAt: null,
          },
        });

        if (!post) {
          return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
        }

        // 댓글 생성
        const comment = await prisma.postComment.create({
          data: {
            content: data.content,
            userId: userId,
            postId: data.postId,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
          },
        });

        // 게시글의 댓글 수 증가
        await prisma.post.update({
          where: { id: data.postId },
          data: {
            commentCount: {
              increment: 1,
            },
          },
        });

        // 사용자의 댓글 수 증가
        await prisma.user.update({
          where: { id: userId },
          data: {
            postCommentCount: {
              increment: 1,
            },
          },
        });

        // 알림 트리거
        await triggerCommentNotification(
          data.postId,
          comment.id,
          data.content,
          userId
        );

        logAction("createPostComment", {
          userId,
          postId: data.postId,
          commentId: comment.id,
        });
        return createSuccessResponse(comment);
      } catch (error) {
        logActionError("createPostComment", error);
        throw error;
      }
    }
  )
);

/**
 * 댓글 수정 액션
 */
export const updatePostComment = withValidation(
  updatePostCommentSchema,
  withAuthAndData(
    async (
      userId: string,
      data: UpdatePostCommentInput
    ): Promise<ActionResponse> => {
      try {
        // 댓글 존재 여부 및 작성자 확인
        const comment = await prisma.postComment.findUnique({
          where: { id: data.id },
        });

        if (!comment) {
          return createNotFoundErrorResponse("댓글을 찾을 수 없습니다.");
        }

        // 작성자 또는 관리자만 수정 가능
        if (comment.userId !== userId) {
          // 관리자 여부 확인
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isAdmin: true },
          });

          if (!user?.isAdmin) {
            return createForbiddenErrorResponse(
              "댓글을 수정할 권한이 없습니다."
            );
          }
        }

        // 댓글 수정
        const updatedComment = await prisma.postComment.update({
          where: { id: data.id },
          data: {
            content: data.content,
            updatedAt: new Date(),
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
          },
        });

        logAction("updatePostComment", { userId, commentId: data.id });
        return createSuccessResponse(updatedComment);
      } catch (error) {
        logActionError("updatePostComment", error);
        throw error;
      }
    }
  )
);

/**
 * 댓글 삭제 액션
 */
export const deletePostComment = withValidation(
  deletePostCommentSchema,
  withAuthAndData(
    async (
      userId: string,
      data: DeletePostCommentInput
    ): Promise<ActionResponse> => {
      try {
        // 댓글 존재 여부 및 작성자 확인
        const comment = await prisma.postComment.findUnique({
          where: { id: data.id },
        });

        if (!comment) {
          return createNotFoundErrorResponse("댓글을 찾을 수 없습니다.");
        }

        // 작성자 또는 관리자만 삭제 가능
        if (comment.userId !== userId) {
          // 관리자 여부 확인
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isAdmin: true },
          });

          if (!user?.isAdmin) {
            return createForbiddenErrorResponse(
              "댓글을 삭제할 권한이 없습니다."
            );
          }
        }

        // 댓글 삭제 (소프트 삭제)
        await prisma.postComment.update({
          where: { id: data.id },
          data: {
            deletedAt: new Date(),
          },
        });

        // 게시글의 댓글 수 감소
        await prisma.post.update({
          where: { id: comment.postId },
          data: {
            commentCount: {
              decrement: 1,
            },
          },
        });

        // 사용자의 댓글 수 감소
        await prisma.user.update({
          where: { id: comment.userId },
          data: {
            postCommentCount: {
              decrement: 1,
            },
          },
        });

        logAction("deletePostComment", { userId, commentId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deletePostComment", error);
        throw error;
      }
    }
  )
);
