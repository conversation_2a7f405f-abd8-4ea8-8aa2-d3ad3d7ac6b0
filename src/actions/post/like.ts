"use server";

import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAuthAndData,
  logAction,
  logActionError,
} from "../utils";
import { triggerLikeNotification } from "../notification/triggers";
import { togglePostLikeSchema, TogglePostLikeInput } from "./schemas";

/**
 * 게시글 좋아요 토글 액션
 */
export const togglePostLike = withValidation(
  togglePostLikeSchema,
  withAuthAndData(
    async (
      userId: string,
      data: TogglePostLikeInput
    ): Promise<ActionResponse> => {
      try {
        // 게시글 존재 여부 확인
        const post = await prisma.post.findUnique({
          where: {
            id: data.postId,
            deletedAt: null,
          },
        });

        if (!post) {
          return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
        }

        // 이미 좋아요를 눌렀는지 확인
        const existingLike = await prisma.postLike.findUnique({
          where: {
            userId_postId: {
              userId: userId,
              postId: data.postId,
            },
          },
        });

        let result;

        if (existingLike) {
          // 좋아요 취소
          await prisma.postLike.delete({
            where: {
              userId_postId: {
                userId: userId,
                postId: data.postId,
              },
            },
          });

          // 게시글의 좋아요 수 감소
          await prisma.post.update({
            where: { id: data.postId },
            data: {
              likeCount: {
                decrement: 1,
              },
            },
          });

          // 게시글 작성자의 좋아요 수 감소
          if (post.userId !== userId) {
            await prisma.user.update({
              where: { id: post.userId },
              data: {
                likeCount: {
                  decrement: 1,
                },
              },
            });
          }

          result = { liked: false, likeCount: post.likeCount - 1 };
          logAction("unlikePost", { userId, postId: data.postId });
        } else {
          // 좋아요 추가
          await prisma.postLike.create({
            data: {
              userId: userId,
              postId: data.postId,
            },
          });

          // 게시글의 좋아요 수 증가
          await prisma.post.update({
            where: { id: data.postId },
            data: {
              likeCount: {
                increment: 1,
              },
            },
          });

          // 게시글 작성자의 좋아요 수 증가
          if (post.userId !== userId) {
            await prisma.user.update({
              where: { id: post.userId },
              data: {
                likeCount: {
                  increment: 1,
                },
              },
            });
          }

          // 알림 트리거
          await triggerLikeNotification(data.postId, userId);

          result = { liked: true, likeCount: post.likeCount + 1 };
          logAction("likePost", { userId, postId: data.postId });
        }

        return createSuccessResponse(result);
      } catch (error) {
        logActionError("togglePostLike", error);
        throw error;
      }
    }
  )
);

/**
 * 게시글 좋아요 상태 확인 액션
 */
export const getPostLikeStatus = withValidation(
  togglePostLikeSchema,
  withAuthAndData(
    async (
      userId: string,
      data: TogglePostLikeInput
    ): Promise<ActionResponse> => {
      try {
        // 게시글 존재 여부 확인
        const post = await prisma.post.findUnique({
          where: {
            id: data.postId,
            deletedAt: null,
          },
        });

        if (!post) {
          return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
        }

        // 좋아요 상태 확인
        const existingLike = await prisma.postLike.findUnique({
          where: {
            userId_postId: {
              userId: userId,
              postId: data.postId,
            },
          },
        });

        return createSuccessResponse({
          liked: !!existingLike,
          likeCount: post.likeCount,
        });
      } catch (error) {
        logActionError("getPostLikeStatus", error);
        throw error;
      }
    }
  )
);
