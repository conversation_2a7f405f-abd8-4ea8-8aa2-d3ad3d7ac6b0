"use server";

import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
  cacheAction,
  CacheTag,
  getPaginationParams,
  getSortParams,
  createPaginationResult,
  logAction,
  logActionError,
} from "../utils";
import { triggerAnnouncementNotification } from "../notification/triggers";
import {
  createPostSchema,
  updatePostSchema,
  deletePostSchema,
  getPostSchema,
  getPostsSchema,
  incrementViewCountSchema,
  CreatePostInput,
  UpdatePostInput,
  DeletePostInput,
  GetPostInput,
  GetPostsInput,
  IncrementViewCountInput,
} from "./schemas";

/**
 * 게시글 생성 액션
 */
export const createPost = withValidation(
  createPostSchema,
  withAuthAndData(
    async (userId: string, data: CreatePostInput): Promise<ActionResponse> => {
      try {
        // 카테고리 존재 여부 확인 (카테고리가 있는 경우)
        if (data.categoryId) {
          const category = await prisma.category.findUnique({
            where: { id: data.categoryId },
          });

          if (!category) {
            return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
          }
        }

        // 게시글 생성
        const post = await prisma.post.create({
          data: {
            type: data.type,
            country: data.country,
            content: data.content,
            contentHtml: data.contentHtml,
            tags: data.tags,
            categoryId: data.categoryId,
            userId: userId,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        // 사용자의 게시글 수 증가
        await prisma.user.update({
          where: { id: userId },
          data: {
            postCount: {
              increment: 1,
            },
          },
        });

        // 공지사항인 경우 알림 트리거
        if (post.type === "ANNOUNCEMENT") {
          await triggerAnnouncementNotification(
            post.id,
            post.content.substring(0, 100), // 내용 일부만 사용
            userId
          );
        }

        logAction("createPost", { userId, postId: post.id });
        return createSuccessResponse(post);
      } catch (error) {
        logActionError("createPost", error);
        throw error;
      }
    }
  )
);

/**
 * 게시글 수정 액션
 */
export const updatePost = withValidation(
  updatePostSchema,
  withAuthAndData(
    async (userId: string, data: UpdatePostInput): Promise<ActionResponse> => {
      try {
        // 게시글 존재 여부 및 작성자 확인
        const post = await prisma.post.findUnique({
          where: { id: data.id },
        });

        if (!post) {
          return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
        }

        // 작성자 또는 관리자만 수정 가능
        if (post.userId !== userId) {
          // 관리자 여부 확인
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isAdmin: true },
          });

          if (!user?.isAdmin) {
            return createForbiddenErrorResponse(
              "게시글을 수정할 권한이 없습니다."
            );
          }
        }

        // 카테고리 존재 여부 확인 (카테고리가 있는 경우)
        if (data.categoryId) {
          const category = await prisma.category.findUnique({
            where: { id: data.categoryId },
          });

          if (!category) {
            return createNotFoundErrorResponse("카테고리를 찾을 수 없습니다.");
          }
        }

        // 게시글 수정
        const updatedPost = await prisma.post.update({
          where: { id: data.id },
          data: {
            type: data.type,
            content: data.content,
            contentHtml: data.contentHtml,
            tags: data.tags,
            categoryId: data.categoryId,
            updatedAt: new Date(),
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        logAction("updatePost", { userId, postId: data.id });
        return createSuccessResponse(updatedPost);
      } catch (error) {
        logActionError("updatePost", error);
        throw error;
      }
    }
  )
);

/**
 * 게시글 삭제 액션
 */
export const deletePost = withValidation(
  deletePostSchema,
  withAuthAndData(
    async (userId: string, data: DeletePostInput): Promise<ActionResponse> => {
      try {
        // 게시글 존재 여부 및 작성자 확인
        const post = await prisma.post.findUnique({
          where: { id: data.id },
        });

        if (!post) {
          return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
        }

        // 작성자 또는 관리자만 삭제 가능
        if (post.userId !== userId) {
          // 관리자 여부 확인
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isAdmin: true },
          });

          if (!user?.isAdmin) {
            return createForbiddenErrorResponse(
              "게시글을 삭제할 권한이 없습니다."
            );
          }
        }

        // 게시글 삭제 (소프트 삭제)
        await prisma.post.update({
          where: { id: data.id },
          data: {
            deletedAt: new Date(),
          },
        });

        // 사용자의 게시글 수 감소
        await prisma.user.update({
          where: { id: post.userId },
          data: {
            postCount: {
              decrement: 1,
            },
          },
        });

        logAction("deletePost", { userId, postId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deletePost", error);
        throw error;
      }
    }
  )
);

/**
 * 게시글 조회 액션
 */
export const getPost = withValidation(
  getPostSchema,
  cacheAction(
    async (data: GetPostInput): Promise<ActionResponse> => {
      try {
        const post = await prisma.post.findUnique({
          where: {
            id: data.id,
            deletedAt: null,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            postComments: {
              where: { deletedAt: null },
              orderBy: { createdAt: "desc" },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    displayName: true,
                    image: true,
                  },
                },
              },
            },
            _count: {
              select: {
                postLikes: true,
                postComments: true,
              },
            },
          },
        });

        if (!post) {
          return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
        }

        return createSuccessResponse(post);
      } catch (error) {
        logActionError("getPost", error);
        throw error;
      }
    },
    { tags: [CacheTag.POST], revalidate: 60 }
  )
);

/**
 * 게시글 목록 조회 액션
 */
export const getPosts = withValidation(
  getPostsSchema,
  cacheAction(
    async (data: GetPostsInput): Promise<ActionResponse> => {
      try {
        const {
          page,
          limit,
          categoryId,
          type,
          userId,
          query,
          sortBy,
          sortOrder,
        } = data;
        const { skip, take } = getPaginationParams(page, limit);
        const orderBy = getSortParams(sortBy, sortOrder);

        // 필터 조건 구성
        const where: any = {
          deletedAt: null,
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (type) {
          where.type = type;
        }

        if (userId) {
          where.userId = userId;
        }

        if (query) {
          where.OR = [{ content: { contains: query, mode: "insensitive" } }];
        }

        // 게시글 목록 조회
        const [posts, totalCount] = await Promise.all([
          prisma.post.findMany({
            where,
            orderBy,
            skip,
            take,
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              category: true,
              _count: {
                select: {
                  postLikes: true,
                  postComments: true,
                },
              },
            },
          }),
          prisma.post.count({ where }),
        ]);

        return createSuccessResponse(
          createPaginationResult(posts, totalCount, page, limit)
        );
      } catch (error) {
        logActionError("getPosts", error);
        throw error;
      }
    },
    { tags: [CacheTag.POST], revalidate: 60 }
  )
);

/**
 * 게시글 조회수 증가 액션
 */
export const incrementViewCount = withValidation(
  incrementViewCountSchema,
  async (data: IncrementViewCountInput): Promise<ActionResponse> => {
    try {
      const post = await prisma.post.findUnique({
        where: {
          id: data.id,
          deletedAt: null,
        },
      });

      if (!post) {
        return createNotFoundErrorResponse("게시글을 찾을 수 없습니다.");
      }

      const updatedPost = await prisma.post.update({
        where: { id: data.id },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });

      return createSuccessResponse({ viewCount: updatedPost.viewCount });
    } catch (error) {
      logActionError("incrementViewCount", error);
      throw error;
    }
  }
);
