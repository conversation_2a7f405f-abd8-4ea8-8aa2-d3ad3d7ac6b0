import { z } from 'zod';

// 공통 스키마
const idSchema = z.string().min(1, '아이디는 필수입니다.');
const contentSchema = z.string().min(1, '내용은 필수입니다.');

// 게시글 생성 스키마
export const createPostSchema = z.object({
  type: z.enum(['NORMAL', 'QUESTION', 'NOTICE']).default('NORMAL'),
  country: z.string().default('kr'),
  content: contentSchema,
  contentHtml: z.string().default('<p></p>'),
  tags: z.array(z.string()).default([]),
  categoryId: z.string().optional(),
});

// 게시글 수정 스키마
export const updatePostSchema = z.object({
  id: idSchema,
  type: z.enum(['NORMAL', 'QUESTION', 'NOTICE']).optional(),
  content: contentSchema.optional(),
  contentHtml: z.string().optional(),
  tags: z.array(z.string()).optional(),
  categoryId: z.string().optional(),
});

// 게시글 삭제 스키마
export const deletePostSchema = z.object({
  id: idSchema,
});

// 게시글 조회 스키마
export const getPostSchema = z.object({
  id: idSchema,
});

// 게시글 목록 조회 스키마
export const getPostsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  categoryId: z.string().optional(),
  type: z.enum(['NORMAL', 'QUESTION', 'NOTICE']).optional(),
  userId: z.string().optional(),
  query: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// 게시글 조회수 증가 스키마
export const incrementViewCountSchema = z.object({
  id: idSchema,
});

// 댓글 생성 스키마
export const createPostCommentSchema = z.object({
  postId: idSchema,
  content: contentSchema,
});

// 댓글 수정 스키마
export const updatePostCommentSchema = z.object({
  id: z.number().int().positive(),
  content: contentSchema,
});

// 댓글 삭제 스키마
export const deletePostCommentSchema = z.object({
  id: z.number().int().positive(),
});

// 좋아요 토글 스키마
export const togglePostLikeSchema = z.object({
  postId: idSchema,
});

// 타입 정의
export type CreatePostInput = z.infer<typeof createPostSchema>;
export type UpdatePostInput = z.infer<typeof updatePostSchema>;
export type DeletePostInput = z.infer<typeof deletePostSchema>;
export type GetPostInput = z.infer<typeof getPostSchema>;
export type GetPostsInput = z.infer<typeof getPostsSchema>;
export type IncrementViewCountInput = z.infer<typeof incrementViewCountSchema>;
export type CreatePostCommentInput = z.infer<typeof createPostCommentSchema>;
export type UpdatePostCommentInput = z.infer<typeof updatePostCommentSchema>;
export type DeletePostCommentInput = z.infer<typeof deletePostCommentSchema>;
export type TogglePostLikeInput = z.infer<typeof togglePostLikeSchema>;
