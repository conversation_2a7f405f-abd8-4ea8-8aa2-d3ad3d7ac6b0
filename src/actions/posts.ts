'use server';

import { action<PERSON><PERSON><PERSON>, actionHandlerWithAuth } from '@/lib/actions/core';
import { sendDiscordMessage } from '@/lib/discord';
import { getServerTranslations } from '@/lib/i18n';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@/lib/prisma/generated/client';
import { PostWithAuthorAndCounts } from '@/types';
import { Envs } from '@/types/env';
import { Session } from 'next-auth';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

// --- Zod Schemas for Input Validation ---

const IdSchema = z.object({
  id: z.string().min(1, 'ID는 필수입니다.'),
});

// 커서 기반 페이지네이션 스키마 추가
const CursorPaginationSchema = z.object({
  cursor: z.string().optional(),
  limit: z.coerce.number().int().positive().optional().default(10), // 기본 10개
  country: z.string().optional(),
});

const PreviewDataSchema = z
  .object({
    url: z.string().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    image: z.string().optional(),
    site_name: z.string().optional(),
    type: z.string().optional(),
  })
  .nullable()
  .optional();

// Zod 스키마를 생성하는 함수로 변경
async function createPostSchema() {
  const t = await getServerTranslations('posts');
  return z.object({
    contentText: z.string().trim().min(1, t('required')),
    contentHtml: z.string(),
    tags: z.array(z.string()).optional().default([]),
    preview: PreviewDataSchema.nullable().optional(),
    country: z.string().optional(),
  });
}

// --- Type Definition for Post Data (based on Prisma Select) ---
// This helps ensure the return type of the action functions is correct.
const PostSelect = {
  id: true,
  content: true,
  contentHtml: true,
  preview: true,
  createdAt: true,
  likeCount: true,
  commentCount: true,
  country: true, // PostSelect에 country 추가
  viewCount: true,
  user: {
    select: { id: true, name: true, displayName: true, image: true },
  },
  postTags: {
    include: { tag: true },
  },
} satisfies Prisma.PostSelect;

// PostData 타입 export 추가
export type PostData = Prisma.PostGetPayload<{ select: typeof PostSelect }>;

// --- Refactored Server Actions ---

/**
 * Get multiple posts (Refactored with actionHandler)
 */
export const getPosts = actionHandler(
  async (limit: number = 10): Promise<PostData[]> => {
    // 최신순 정렬, 삭제되지 않은 게시글만
    const posts = await prisma.post.findMany({
      where: { deletedAt: null },
      orderBy: { createdAt: 'desc' },
      select: PostSelect,
      take: limit,
    });
    return posts;
  }
  // No schema needed for getPosts
);

/**
 * Get multiple posts with cursor pagination (New)
 */
export const getInfinitePosts = actionHandler(
  async (input: z.infer<typeof CursorPaginationSchema>) => {
    const { cursor, limit, country } = input;
    // country가 빈 문자열이거나 undefined면 전체 조회, 그렇지 않으면 해당 country만 조회
    const whereClause = {
      deletedAt: null,
      ...(country && country !== '' ? { country: country.toLowerCase() } : {}),
    };

    const posts = await prisma.post.findMany({
      take: limit + 1, // 다음 커서 존재 여부 확인 위해 1개 더 가져옴
      skip: cursor ? 1 : 0, // cursor 있으면 해당 cursor 건너뜀
      cursor: cursor ? { id: cursor } : undefined,
      where: whereClause,
      orderBy: { createdAt: 'desc' }, // 커서 페이지네이션에는 일관된 순서 중요
      select: PostSelect,
    });

    let nextCursor: typeof cursor | null = null;
    if (posts.length > limit) {
      nextCursor = posts.pop()!.id; // 마지막 요소 제거하고 id를 다음 커서로 사용
    }

    return {
      posts,
      nextCursor,
    };
  },
  { schema: CursorPaginationSchema }
);

/**
 * Get a single post by ID without incrementing views (Refactored with actionHandler)
 */
export const getPostById = actionHandler(
  async (input: z.infer<typeof IdSchema>): Promise<PostData | null> => {
    const { id } = input;
    const post = await prisma.post.findUnique({
      where: { id, deletedAt: null },
      select: PostSelect,
    });
    return post;
  },
  { schema: IdSchema }
);

/**
 * Increment view count and get post data (Refactored with actionHandler)
 */
export const viewPost = actionHandler(
  async (input: z.infer<typeof IdSchema>): Promise<PostData | null> => {
    const { id } = input;

    // Using transaction for atomicity (optional but good practice)
    const post = await prisma.$transaction(async (tx) => {
      // 먼저 게시글이 삭제되지 않았는지 확인
      const existingPost = await tx.post.findUnique({
        where: { id, deletedAt: null },
        select: { id: true },
      });

      if (!existingPost) {
        return null;
      }

      // 조회수 증가
      await tx.post.update({
        where: { id },
        data: {
          viewCount: { increment: 1 },
        },
      });

      // 업데이트된 게시글 정보 반환
      return tx.post.findUnique({
        where: { id },
        select: PostSelect,
      });
    });

    return post; // Return the fetched data
  },
  { schema: IdSchema }
);

/**
 * Create a new post (Modified to use dynamic schema)
 */
export const createPost = actionHandlerWithAuth(
  async (
    session: Session,
    input: {
      contentText: string;
      contentHtml: string;
      tags?: string[];
      preview?: any;
      country?: string;
    }
  ) => {
    const userId = session.user.id!;
    // 스키마 검증
    const schema = await createPostSchema();
    const validatedInput = schema.parse(input);

    const { contentText, contentHtml, tags, preview, country } = validatedInput;

    const t = await getServerTranslations('posts');

    // Use transaction for creating post, tags, and updating user count
    const post = await prisma.$transaction(async (tx) => {
      console.log('createPost preview', preview);
      const newPost = await tx.post.create({
        data: {
          userId: userId,
          content: contentText,
          contentHtml: contentHtml,
          preview:
            preview === null ? undefined : (preview as Prisma.InputJsonValue),
          country: country ? country.toLowerCase() : 'kr',
        },
      });

      // Create tag associations if tags are provided
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          // Remove leading '#' from the tag name
          const cleanedTagName = tagName.replace(/^#/, '');

          // Process only if the cleaned tag name is not empty
          if (cleanedTagName.length > 0) {
            const tag = await tx.tag.upsert({
              where: { name: cleanedTagName },
              update: {
                useCount: { increment: 1 },
              },
              create: {
                name: cleanedTagName,
                useCount: 1,
              },
            });
            await tx.postTag.create({
              data: {
                postId: newPost.id,
                tagId: tag.id,
              },
            });
          }
        }
      }

      // Increment user's post count
      await tx.user.update({
        where: { id: userId },
        data: {
          postCount: { increment: 1 },
        },
      });

      return newPost;
    });

    // Revalidate paths after successful transaction
    revalidatePath('/community');

    // Send Discord notification
    const processedContentText = contentText.replace(/\n\n+/g, '\n');
    const messageContent =
      processedContentText.length > 200
        ? `${processedContentText.substring(0, 200)}...`
        : processedContentText;
    await sendDiscordMessage('', {
      title: t('title'),
      description: messageContent,
      url: `${Envs.publicBaseUrl}/community/feed/${post.id}`,
      color: 0xfbffe4,
    });
    return {
      postId: post.id,
    };
  }
);

/**
 * Delete a post (Soft delete) (New)
 */
export const deletePost = actionHandlerWithAuth(
  async (session: Session, input: z.infer<typeof IdSchema>) => {
    const { id: postId } = input;
    const userId = session.user.id!;
    const t = await getServerTranslations('posts');

    // Find the post first to check ownership and existence
    const post = await prisma.post.findUnique({
      where: { id: postId },
      select: { userId: true, deletedAt: true },
    });

    if (!post) {
      throw new Error(t('notFound'));
    }

    if (post.deletedAt) {
      throw new Error(t('alreadyDeleted'));
    }

    if (post.userId !== userId) {
      throw new Error(t('noPermission'));
    }

    // Perform soft delete
    await prisma.post.update({
      where: { id: postId },
      data: {
        deletedAt: new Date(),
        user: {
          update: {
            postCount: { decrement: 1 },
          },
        },
      },
    });

    // Revalidate relevant paths
    revalidatePath('/community');
    revalidatePath(`/community/post/${postId}`);

    return { success: true, message: t('deleteSuccess') };
  },
  { schema: IdSchema }
);

/**
 * 특정 사용자가 작성한 게시글 목록을 가져옵니다.
 * @param userId 사용자 ID
 * @param options 페이징 옵션 (선택 사항)
 * @returns 게시글 목록 (PostWithAuthorAndCounts 타입)
 */
export async function getUserPosts(
  userId: string,
  options?: { take?: number; skip?: number }
): Promise<PostWithAuthorAndCounts[]> {
  try {
    const posts = await prisma.post.findMany({
      where: {
        userId: userId,
        deletedAt: null, // Ensure only non-deleted posts are fetched
      },
      select: {
        // Explicitly select fields to match PostWithAuthorAndCounts
        id: true,
        content: true,
        contentHtml: true,
        preview: true,
        createdAt: true,
        updatedAt: true,
        likeCount: true,
        commentCount: true,
        viewCount: true,
        deletedAt: true,
        userId: true,

        user: {
          select: {
            id: true,
            name: true,
            displayName: true,
            image: true,
          },
        },
        _count: {
          select: {
            postComments: true,
            postLikes: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: options?.take,
      skip: options?.skip,
    });
    // If PostWithAuthorAndCounts expects stage/shareCount and they are not in the schema,
    // this cast might hide errors or lead to runtime issues if the type expects them to be present.
    // The ideal solution is to align Prisma schema and the TS type.
    return posts as unknown as PostWithAuthorAndCounts[];
  } catch (error) {
    console.error('Error fetching user posts:', error);
    const t = await getServerTranslations('posts');
    throw new Error(t('fetchUserPostsError'));
  }
}

// --- Original Functions (Commented out or removed) ---
/*
interface CreatePostParams { ... }
export async function getPosts() { ... }
export async function getPostById(id: string) { ... }
export async function viewPost(id: string) { ... }
interface CreatePostResult { ... }
export async function createPost(params: CreatePostParams): Promise<CreatePostResult> { ... }
*/
