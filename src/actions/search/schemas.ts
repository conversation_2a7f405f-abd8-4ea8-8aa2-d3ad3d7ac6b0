import { z } from "zod";

// 검색 결과 타입 정의
export enum SearchResultType {
  LIFE_INFO = "LIFE_INFO",
  SERVICE_GUIDE = "SERVICE_GUIDE",
  GOV_INFO = "GOV_INFO",
  JOB_POST = "JOB_POST",
  POST = "POST",
}

// 통합 검색 스키마
export const searchSchema = z.object({
  query: z.string().min(1, "검색어를 입력해주세요."),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(50).default(10),
  types: z
    .array(z.nativeEnum(SearchResultType))
    .optional(),
  categoryId: z.string().optional(),
  sortBy: z
    .enum(["relevance", "createdAt", "viewCount", "title"])
    .default("relevance"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// 카테고리별 검색 스키마
export const categorySearchSchema = z.object({
  query: z.string().min(1, "검색어를 입력해주세요."),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(50).default(10),
  type: z.nativeEnum(SearchResultType),
  categoryId: z.string().optional(),
  sortBy: z
    .enum(["relevance", "createdAt", "viewCount", "title"])
    .default("relevance"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// 타입 정의
export type SearchInput = z.infer<typeof searchSchema>;
export type CategorySearchInput = z.infer<typeof categorySearchSchema>;

// 검색 결과 아이템 타입
export interface SearchResultItem {
  id: string;
  title: string;
  summary?: string;
  content?: string;
  contentHtml?: string;
  slug?: string;
  type: SearchResultType;
  categoryId?: string;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  author?: {
    id: string;
    name: string;
    displayName?: string;
    image?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  viewCount: number;
  url: string;
  imageUrl?: string;
}
