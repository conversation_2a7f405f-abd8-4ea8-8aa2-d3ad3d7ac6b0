"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  withValidation,
  cacheAction,
  CacheTag,
  createPaginationResult,
  logActionError,
} from "../utils";
import {
  searchSchema,
  categorySearchSchema,
  SearchInput,
  CategorySearchInput,
  SearchResultType,
  SearchResultItem,
} from "./schemas";

/**
 * 통합 검색 액션
 */
export const search = withValidation(
  searchSchema,
  cacheAction(
    async (data: SearchInput): Promise<ActionResponse> => {
      try {
        const { query, page, limit, types, categoryId, sortBy, sortOrder } = data;

        // 검색할 타입 결정
        const searchTypes = types || Object.values(SearchResultType);

        // 검색 결과 저장 배열
        let results: SearchResultItem[] = [];
        let totalCount = 0;

        // 생활 정보 검색
        if (searchTypes.includes(SearchResultType.LIFE_INFO)) {
          const lifeInfos = await prisma.lifeInfo.findMany({
            where: {
              status: "published",
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { summary: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
              author: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              images: {
                take: 1,
                orderBy: {
                  order: "asc",
                },
              },
            },
            skip: (page - 1) * limit,
            take: limit,
          });

          const lifeInfoCount = await prisma.lifeInfo.count({
            where: {
              status: "published",
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { summary: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
          });

          totalCount += lifeInfoCount;

          // 검색 결과 변환
          const lifeInfoResults = lifeInfos.map((item): SearchResultItem => ({
            id: item.id,
            title: item.title,
            summary: item.summary || undefined,
            content: item.content,
            contentHtml: item.contentHtml,
            slug: item.slug,
            type: SearchResultType.LIFE_INFO,
            categoryId: item.categoryId,
            category: item.category,
            author: item.author,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            viewCount: item.viewCount,
            url: `/life-info/${item.slug}`,
            imageUrl: item.images?.[0]?.url,
          }));

          results = [...results, ...lifeInfoResults];
        }

        // 서비스 가이드 검색
        if (searchTypes.includes(SearchResultType.SERVICE_GUIDE)) {
          const serviceGuides = await prisma.serviceGuide.findMany({
            where: {
              status: "published",
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { summary: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
              author: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              steps: {
                take: 1,
                orderBy: {
                  order: "asc",
                },
                include: {
                  images: {
                    take: 1,
                    orderBy: {
                      order: "asc",
                    },
                  },
                },
              },
            },
            skip: (page - 1) * limit,
            take: limit,
          });

          const serviceGuideCount = await prisma.serviceGuide.count({
            where: {
              status: "published",
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { summary: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
          });

          totalCount += serviceGuideCount;

          // 검색 결과 변환
          const serviceGuideResults = serviceGuides.map((item): SearchResultItem => ({
            id: item.id,
            title: item.title,
            summary: item.summary || undefined,
            content: item.content,
            contentHtml: item.contentHtml,
            slug: item.slug,
            type: SearchResultType.SERVICE_GUIDE,
            categoryId: item.categoryId,
            category: item.category,
            author: item.author,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            viewCount: item.viewCount,
            url: `/service-guide/${item.slug}`,
            imageUrl: item.steps?.[0]?.images?.[0]?.url,
          }));

          results = [...results, ...serviceGuideResults];
        }

        // 정부 정보 검색
        if (searchTypes.includes(SearchResultType.GOV_INFO)) {
          const govInfos = await prisma.govInfo.findMany({
            where: {
              status: "published",
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { summary: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
              author: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              images: {
                take: 1,
                orderBy: {
                  order: "asc",
                },
              },
            },
            skip: (page - 1) * limit,
            take: limit,
          });

          const govInfoCount = await prisma.govInfo.count({
            where: {
              status: "published",
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { summary: { contains: query, mode: "insensitive" } },
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
          });

          totalCount += govInfoCount;

          // 검색 결과 변환
          const govInfoResults = govInfos.map((item): SearchResultItem => ({
            id: item.id,
            title: item.title,
            summary: item.summary || undefined,
            content: item.content,
            contentHtml: item.contentHtml,
            slug: item.slug,
            type: SearchResultType.GOV_INFO,
            categoryId: item.categoryId,
            category: item.category,
            author: item.author,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            viewCount: item.viewCount,
            url: `/gov-info/${item.slug}`,
            imageUrl: item.images?.[0]?.url,
          }));

          results = [...results, ...govInfoResults];
        }

        // 구인·구직 정보 검색
        if (searchTypes.includes(SearchResultType.JOB_POST)) {
          const jobPosts = await prisma.jobPost.findMany({
            where: {
              status: "active",
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { description: { contains: query, mode: "insensitive" } },
                { company: { contains: query, mode: "insensitive" } },
                { location: { contains: query, mode: "insensitive" } },
              ],
            },
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
              author: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
            },
            skip: (page - 1) * limit,
            take: limit,
          });

          const jobPostCount = await prisma.jobPost.count({
            where: {
              status: "active",
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { title: { contains: query, mode: "insensitive" } },
                { description: { contains: query, mode: "insensitive" } },
                { company: { contains: query, mode: "insensitive" } },
                { location: { contains: query, mode: "insensitive" } },
              ],
            },
          });

          totalCount += jobPostCount;

          // 검색 결과 변환
          const jobPostResults = jobPosts.map((item): SearchResultItem => ({
            id: item.id,
            title: item.title,
            summary: item.description,
            type: SearchResultType.JOB_POST,
            categoryId: item.categoryId || undefined,
            category: item.category || undefined,
            author: item.author,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            viewCount: item.viewCount,
            url: `/jobs/${item.id}`,
          }));

          results = [...results, ...jobPostResults];
        }

        // 게시글 검색
        if (searchTypes.includes(SearchResultType.POST)) {
          const posts = await prisma.post.findMany({
            where: {
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                  image: true,
                },
              },
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
            skip: (page - 1) * limit,
            take: limit,
          });

          const postCount = await prisma.post.count({
            where: {
              deletedAt: null,
              ...(categoryId ? { categoryId } : {}),
              OR: [
                { content: { contains: query, mode: "insensitive" } },
              ],
            },
          });

          totalCount += postCount;

          // 검색 결과 변환
          const postResults = posts.map((item): SearchResultItem => ({
            id: item.id,
            title: `게시글 #${item.id}`,
            content: item.content,
            contentHtml: item.contentHtml,
            type: SearchResultType.POST,
            categoryId: item.categoryId || undefined,
            category: item.category || undefined,
            author: item.user ? {
              id: item.user.id,
              name: item.user.name,
              displayName: item.user.displayName || undefined,
              image: item.user.image || undefined,
            } : undefined,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            viewCount: item.viewCount,
            url: `/community/${item.id}`,
          }));

          results = [...results, ...postResults];
        }

        // 정렬
        if (sortBy === "relevance") {
          // 관련성 점수 계산 로직 (간단한 구현)
          results = results.sort((a, b) => {
            const scoreA = calculateRelevanceScore(a, query);
            const scoreB = calculateRelevanceScore(b, query);
            return sortOrder === "desc" ? scoreB - scoreA : scoreA - scoreB;
          });
        } else {
          // 다른 필드로 정렬
          results = results.sort((a, b) => {
            const valueA = a[sortBy as keyof SearchResultItem];
            const valueB = b[sortBy as keyof SearchResultItem];
            
            if (typeof valueA === "string" && typeof valueB === "string") {
              return sortOrder === "desc" 
                ? valueB.localeCompare(valueA) 
                : valueA.localeCompare(valueB);
            }
            
            if (valueA instanceof Date && valueB instanceof Date) {
              return sortOrder === "desc" 
                ? valueB.getTime() - valueA.getTime() 
                : valueA.getTime() - valueB.getTime();
            }
            
            if (typeof valueA === "number" && typeof valueB === "number") {
              return sortOrder === "desc" ? valueB - valueA : valueA - valueB;
            }
            
            return 0;
          });
        }

        // 페이지네이션 적용
        const paginatedResults = results.slice(0, limit);

        return createSuccessResponse(
          createPaginationResult(paginatedResults, totalCount, page, limit)
        );
      } catch (error) {
        logActionError("search", error);
        throw error;
      }
    },
    {
      tags: [CacheTag.SEARCH],
      revalidate: 60,
    }
  )
);

/**
 * 카테고리별 검색 액션
 */
export const categorySearch = withValidation(
  categorySearchSchema,
  cacheAction(
    async (data: CategorySearchInput): Promise<ActionResponse> => {
      try {
        const { query, page, limit, type, categoryId, sortBy, sortOrder } = data;

        // 타입별 검색 로직 호출
        switch (type) {
          case SearchResultType.LIFE_INFO:
            return searchLifeInfos(query, page, limit, categoryId, sortBy, sortOrder);
          case SearchResultType.SERVICE_GUIDE:
            return searchServiceGuides(query, page, limit, categoryId, sortBy, sortOrder);
          case SearchResultType.GOV_INFO:
            return searchGovInfos(query, page, limit, categoryId, sortBy, sortOrder);
          case SearchResultType.JOB_POST:
            return searchJobPosts(query, page, limit, categoryId, sortBy, sortOrder);
          case SearchResultType.POST:
            return searchPosts(query, page, limit, categoryId, sortBy, sortOrder);
          default:
            throw new Error("지원하지 않는 검색 타입입니다.");
        }
      } catch (error) {
        logActionError("categorySearch", error);
        throw error;
      }
    },
    {
      tags: [CacheTag.SEARCH, CacheTag.CATEGORY],
      revalidate: 60,
    }
  )
);

// 관련성 점수 계산 함수
function calculateRelevanceScore(item: SearchResultItem, query: string): number {
  let score = 0;
  const lowerQuery = query.toLowerCase();

  // 제목에 검색어가 포함되면 높은 점수
  if (item.title && item.title.toLowerCase().includes(lowerQuery)) {
    score += 10;
    // 제목이 검색어로 시작하면 추가 점수
    if (item.title.toLowerCase().startsWith(lowerQuery)) {
      score += 5;
    }
  }

  // 요약에 검색어가 포함되면 중간 점수
  if (item.summary && item.summary.toLowerCase().includes(lowerQuery)) {
    score += 5;
  }

  // 내용에 검색어가 포함되면 낮은 점수
  if (item.content && item.content.toLowerCase().includes(lowerQuery)) {
    score += 3;
  }

  // 최신 콘텐츠에 가중치 부여
  const now = new Date();
  const createdAt = item.createdAt;
  const daysDiff = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24));
  
  // 30일 이내 콘텐츠에 추가 점수 (최대 2점)
  if (daysDiff <= 30) {
    score += 2 * (1 - daysDiff / 30);
  }

  // 조회수가 높은 콘텐츠에 가중치 부여 (최대 2점)
  if (item.viewCount > 0) {
    // 조회수 로그 스케일로 변환하여 0~2점 사이 점수 부여
    score += Math.min(2, Math.log10(item.viewCount) / 2);
  }

  return score;
}

// 생활 정보 검색 함수
async function searchLifeInfos(
  query: string,
  page: number,
  limit: number,
  categoryId?: string,
  sortBy: string = "relevance",
  sortOrder: string = "desc"
): Promise<ActionResponse> {
  // 생략 - 실제 구현은 search 함수의 해당 부분과 유사
  return createSuccessResponse({ data: [], pagination: { page, limit, totalCount: 0, totalPages: 0 } });
}

// 서비스 가이드 검색 함수
async function searchServiceGuides(
  query: string,
  page: number,
  limit: number,
  categoryId?: string,
  sortBy: string = "relevance",
  sortOrder: string = "desc"
): Promise<ActionResponse> {
  // 생략 - 실제 구현은 search 함수의 해당 부분과 유사
  return createSuccessResponse({ data: [], pagination: { page, limit, totalCount: 0, totalPages: 0 } });
}

// 정부 정보 검색 함수
async function searchGovInfos(
  query: string,
  page: number,
  limit: number,
  categoryId?: string,
  sortBy: string = "relevance",
  sortOrder: string = "desc"
): Promise<ActionResponse> {
  // 생략 - 실제 구현은 search 함수의 해당 부분과 유사
  return createSuccessResponse({ data: [], pagination: { page, limit, totalCount: 0, totalPages: 0 } });
}

// 구인·구직 정보 검색 함수
async function searchJobPosts(
  query: string,
  page: number,
  limit: number,
  categoryId?: string,
  sortBy: string = "relevance",
  sortOrder: string = "desc"
): Promise<ActionResponse> {
  // 생략 - 실제 구현은 search 함수의 해당 부분과 유사
  return createSuccessResponse({ data: [], pagination: { page, limit, totalCount: 0, totalPages: 0 } });
}

// 게시글 검색 함수
async function searchPosts(
  query: string,
  page: number,
  limit: number,
  categoryId?: string,
  sortBy: string = "relevance",
  sortOrder: string = "desc"
): Promise<ActionResponse> {
  // 생략 - 실제 구현은 search 함수의 해당 부분과 유사
  return createSuccessResponse({ data: [], pagination: { page, limit, totalCount: 0, totalPages: 0 } });
}
