"use server";

import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAdminAndData,
  logAction,
  logActionError,
} from "../utils";
import {
  createServiceGuideDocumentSchema,
  updateServiceGuideDocumentSchema,
  deleteServiceGuideDocumentSchema,
  CreateServiceGuideDocumentInput,
  UpdateServiceGuideDocumentInput,
  DeleteServiceGuideDocumentInput,
} from "./schemas";

/**
 * 서비스 가이드 문서 생성 액션
 */
export const createServiceGuideDocument = withValidation(
  createServiceGuideDocumentSchema,
  withAdminAndData(
    async (data: CreateServiceGuideDocumentInput, { userId }): Promise<ActionResponse> => {
      try {
        // 서비스 가이드 존재 확인
        const serviceGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.serviceGuideId },
        });

        if (!serviceGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        // 문서 순서 결정 (기본값: 마지막 순서 + 1)
        if (data.order === 0) {
          const lastDocument = await prisma.serviceGuideDocument.findFirst({
            where: { serviceGuideId: data.serviceGuideId },
            orderBy: { order: "desc" },
          });

          if (lastDocument) {
            data.order = lastDocument.order + 1;
          }
        }

        // 문서 생성
        const document = await prisma.serviceGuideDocument.create({
          data: {
            name: data.name,
            description: data.description,
            required: data.required,
            order: data.order,
            serviceGuideId: data.serviceGuideId,
          },
        });

        logAction("createServiceGuideDocument", { userId, documentId: document.id });
        return createSuccessResponse(document);
      } catch (error) {
        logActionError("createServiceGuideDocument", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 문서 수정 액션
 */
export const updateServiceGuideDocument = withValidation(
  updateServiceGuideDocumentSchema,
  withAdminAndData(
    async (data: UpdateServiceGuideDocumentInput, { userId }): Promise<ActionResponse> => {
      try {
        // 문서 존재 확인
        const document = await prisma.serviceGuideDocument.findUnique({
          where: { id: data.id },
        });

        if (!document) {
          return createNotFoundErrorResponse("문서를 찾을 수 없습니다.");
        }

        // 문서 수정
        const updatedDocument = await prisma.serviceGuideDocument.update({
          where: { id: data.id },
          data: {
            name: data.name,
            description: data.description,
            required: data.required,
            order: data.order,
          },
        });

        logAction("updateServiceGuideDocument", { userId, documentId: updatedDocument.id });
        return createSuccessResponse(updatedDocument);
      } catch (error) {
        logActionError("updateServiceGuideDocument", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 문서 삭제 액션
 */
export const deleteServiceGuideDocument = withValidation(
  deleteServiceGuideDocumentSchema,
  withAdminAndData(
    async (data: DeleteServiceGuideDocumentInput, { userId }): Promise<ActionResponse> => {
      try {
        // 문서 존재 확인
        const document = await prisma.serviceGuideDocument.findUnique({
          where: { id: data.id },
        });

        if (!document) {
          return createNotFoundErrorResponse("문서를 찾을 수 없습니다.");
        }

        // 문서 삭제
        await prisma.serviceGuideDocument.delete({
          where: { id: data.id },
        });

        // 남은 문서들의 순서 재정렬
        const remainingDocuments = await prisma.serviceGuideDocument.findMany({
          where: { serviceGuideId: document.serviceGuideId },
          orderBy: { order: "asc" },
        });

        for (let i = 0; i < remainingDocuments.length; i++) {
          await prisma.serviceGuideDocument.update({
            where: { id: remainingDocuments[i].id },
            data: { order: i },
          });
        }

        logAction("deleteServiceGuideDocument", { userId, documentId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteServiceGuideDocument", error);
        throw error;
      }
    }
  )
);
