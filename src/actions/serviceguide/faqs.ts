"use server";

import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAdminAndData,
  logAction,
  logActionError,
} from "../utils";
import {
  createServiceGuideFaqSchema,
  updateServiceGuideFaqSchema,
  deleteServiceGuideFaqSchema,
  CreateServiceGuideFaqInput,
  UpdateServiceGuideFaqInput,
  DeleteServiceGuideFaqInput,
} from "./schemas";

/**
 * 서비스 가이드 FAQ 생성 액션
 */
export const createServiceGuideFaq = withValidation(
  createServiceGuideFaqSchema,
  withAdminAndData(
    async (data: CreateServiceGuideFaqInput, { userId }): Promise<ActionResponse> => {
      try {
        // 서비스 가이드 존재 확인
        const serviceGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.serviceGuideId },
        });

        if (!serviceGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        // FAQ 순서 결정 (기본값: 마지막 순서 + 1)
        if (data.order === 0) {
          const lastFaq = await prisma.serviceGuideFaq.findFirst({
            where: { serviceGuideId: data.serviceGuideId },
            orderBy: { order: "desc" },
          });

          if (lastFaq) {
            data.order = lastFaq.order + 1;
          }
        }

        // FAQ 생성
        const faq = await prisma.serviceGuideFaq.create({
          data: {
            question: data.question,
            answer: data.answer,
            order: data.order,
            serviceGuideId: data.serviceGuideId,
          },
        });

        logAction("createServiceGuideFaq", { userId, faqId: faq.id });
        return createSuccessResponse(faq);
      } catch (error) {
        logActionError("createServiceGuideFaq", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 FAQ 수정 액션
 */
export const updateServiceGuideFaq = withValidation(
  updateServiceGuideFaqSchema,
  withAdminAndData(
    async (data: UpdateServiceGuideFaqInput, { userId }): Promise<ActionResponse> => {
      try {
        // FAQ 존재 확인
        const faq = await prisma.serviceGuideFaq.findUnique({
          where: { id: data.id },
        });

        if (!faq) {
          return createNotFoundErrorResponse("FAQ를 찾을 수 없습니다.");
        }

        // FAQ 수정
        const updatedFaq = await prisma.serviceGuideFaq.update({
          where: { id: data.id },
          data: {
            question: data.question,
            answer: data.answer,
            order: data.order,
          },
        });

        logAction("updateServiceGuideFaq", { userId, faqId: updatedFaq.id });
        return createSuccessResponse(updatedFaq);
      } catch (error) {
        logActionError("updateServiceGuideFaq", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 FAQ 삭제 액션
 */
export const deleteServiceGuideFaq = withValidation(
  deleteServiceGuideFaqSchema,
  withAdminAndData(
    async (data: DeleteServiceGuideFaqInput, { userId }): Promise<ActionResponse> => {
      try {
        // FAQ 존재 확인
        const faq = await prisma.serviceGuideFaq.findUnique({
          where: { id: data.id },
        });

        if (!faq) {
          return createNotFoundErrorResponse("FAQ를 찾을 수 없습니다.");
        }

        // FAQ 삭제
        await prisma.serviceGuideFaq.delete({
          where: { id: data.id },
        });

        // 남은 FAQ들의 순서 재정렬
        const remainingFaqs = await prisma.serviceGuideFaq.findMany({
          where: { serviceGuideId: faq.serviceGuideId },
          orderBy: { order: "asc" },
        });

        for (let i = 0; i < remainingFaqs.length; i++) {
          await prisma.serviceGuideFaq.update({
            where: { id: remainingFaqs[i].id },
            data: { order: i },
          });
        }

        logAction("deleteServiceGuideFaq", { userId, faqId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteServiceGuideFaq", error);
        throw error;
      }
    }
  )
);
