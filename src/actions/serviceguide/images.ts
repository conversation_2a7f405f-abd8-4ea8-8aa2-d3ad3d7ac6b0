"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAdminAndData,
  logAction,
  logActionError,
} from "../utils";
import { idSchema } from "../utils/schemas";

// 이미지 추가 스키마
const addStepImageSchema = z.object({
  stepId: idSchema,
  url: z.string().url("유효한 URL을 입력해주세요."),
  alt: z.string().optional(),
  caption: z.string().optional(),
  order: z.number().int().nonnegative().default(0),
});

// 이미지 수정 스키마
const updateStepImageSchema = z.object({
  id: idSchema,
  url: z.string().url("유효한 URL을 입력해주세요.").optional(),
  alt: z.string().optional(),
  caption: z.string().optional(),
  order: z.number().int().nonnegative().optional(),
});

// 이미지 삭제 스키마
const deleteStepImageSchema = z.object({
  id: idSchema,
});

// 타입 정의
type AddStepImageInput = z.infer<typeof addStepImageSchema>;
type UpdateStepImageInput = z.infer<typeof updateStepImageSchema>;
type DeleteStepImageInput = z.infer<typeof deleteStepImageSchema>;

/**
 * 단계 이미지 추가 액션
 */
export const addStepImage = withValidation(
  addStepImageSchema,
  withAdminAndData(
    async (data: AddStepImageInput, { userId }): Promise<ActionResponse> => {
      try {
        // 단계 존재 확인
        const step = await prisma.serviceGuideStep.findUnique({
          where: { id: data.stepId },
        });

        if (!step) {
          return createNotFoundErrorResponse("단계를 찾을 수 없습니다.");
        }

        // 이미지 순서 결정 (기본값: 마지막 순서 + 1)
        if (data.order === 0) {
          const lastImage = await prisma.serviceGuideStepImage.findFirst({
            where: { stepId: data.stepId },
            orderBy: { order: "desc" },
          });

          if (lastImage) {
            data.order = lastImage.order + 1;
          }
        }

        // 이미지 추가
        const image = await prisma.serviceGuideStepImage.create({
          data: {
            url: data.url,
            alt: data.alt,
            caption: data.caption,
            order: data.order,
            stepId: data.stepId,
          },
        });

        logAction("addStepImage", { userId, imageId: image.id });
        return createSuccessResponse(image);
      } catch (error) {
        logActionError("addStepImage", error);
        throw error;
      }
    }
  )
);

/**
 * 단계 이미지 수정 액션
 */
export const updateStepImage = withValidation(
  updateStepImageSchema,
  withAdminAndData(
    async (data: UpdateStepImageInput, { userId }): Promise<ActionResponse> => {
      try {
        // 이미지 존재 확인
        const image = await prisma.serviceGuideStepImage.findUnique({
          where: { id: data.id },
        });

        if (!image) {
          return createNotFoundErrorResponse("이미지를 찾을 수 없습니다.");
        }

        // 이미지 수정
        const updatedImage = await prisma.serviceGuideStepImage.update({
          where: { id: data.id },
          data: {
            url: data.url,
            alt: data.alt,
            caption: data.caption,
            order: data.order,
          },
        });

        logAction("updateStepImage", { userId, imageId: updatedImage.id });
        return createSuccessResponse(updatedImage);
      } catch (error) {
        logActionError("updateStepImage", error);
        throw error;
      }
    }
  )
);

/**
 * 단계 이미지 삭제 액션
 */
export const deleteStepImage = withValidation(
  deleteStepImageSchema,
  withAdminAndData(
    async (data: DeleteStepImageInput, { userId }): Promise<ActionResponse> => {
      try {
        // 이미지 존재 확인
        const image = await prisma.serviceGuideStepImage.findUnique({
          where: { id: data.id },
        });

        if (!image) {
          return createNotFoundErrorResponse("이미지를 찾을 수 없습니다.");
        }

        // 이미지 삭제
        await prisma.serviceGuideStepImage.delete({
          where: { id: data.id },
        });

        // 남은 이미지들의 순서 재정렬
        const remainingImages = await prisma.serviceGuideStepImage.findMany({
          where: { stepId: image.stepId },
          orderBy: { order: "asc" },
        });

        for (let i = 0; i < remainingImages.length; i++) {
          await prisma.serviceGuideStepImage.update({
            where: { id: remainingImages[i].id },
            data: { order: i },
          });
        }

        logAction("deleteStepImage", { userId, imageId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteStepImage", error);
        throw error;
      }
    }
  )
);
