import { z } from 'zod';
import { idSchema, titleSchema, contentSchema } from '../utils/schemas';

/**
 * 서비스 가이드 관련 Zod 스키마 정의
 */

// 서비스 가이드 생성 스키마
export const createServiceGuideSchema = z.object({
  title: titleSchema,
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.'),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: contentSchema,
  contentHtml: z.string().optional(),
  categoryId: idSchema,
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  featured: z.boolean().default(false),
  officialLink: z.string().url('유효한 URL을 입력해주세요.').optional(),
  translations: z.record(z.string(), z.object({
    title: z.string().min(1, '제목은 필수입니다.'),
    summary: z.string().optional(),
    content: z.string().min(1, '내용은 필수입니다.')
  })).optional(),
  publishedAt: z.string().optional(),
});

// 서비스 가이드 수정 스키마
export const updateServiceGuideSchema = z.object({
  id: idSchema,
  title: titleSchema.optional(),
  slug: z.string().max(255, '슬러그는 최대 255자까지 가능합니다.').optional(),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: contentSchema.optional(),
  contentHtml: z.string().optional(),
  categoryId: idSchema.optional(),
  status: z.enum(['draft', 'published', 'archived']).optional(),
  featured: z.boolean().optional(),
  officialLink: z.string().url('유효한 URL을 입력해주세요.').optional().nullable(),
  translations: z.record(z.string(), z.object({
    title: z.string().min(1, '제목은 필수입니다.'),
    summary: z.string().optional(),
    content: z.string().min(1, '내용은 필수입니다.')
  })).optional(),
  publishedAt: z.string().optional(),
});

// 서비스 가이드 삭제 스키마
export const deleteServiceGuideSchema = z.object({
  id: idSchema,
});

// 서비스 가이드 조회 스키마
export const getServiceGuideSchema = z.object({
  id: idSchema,
});

// 서비스 가이드 목록 조회 스키마
export const getServiceGuidesSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().default(10),
  categoryId: z.string().optional(),
  status: z.enum(['draft', 'published', 'archived']).optional(),
  featured: z.boolean().optional(),
  query: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'title', 'viewCount']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// 서비스 가이드 조회수 증가 스키마
export const incrementViewCountSchema = z.object({
  id: idSchema,
});

// 서비스 가이드 단계 생성 스키마
export const createServiceGuideStepSchema = z.object({
  serviceGuideId: idSchema,
  title: titleSchema,
  content: contentSchema,
  contentHtml: z.string().optional(),
  order: z.number().int().nonnegative().default(0),
});

// 서비스 가이드 단계 수정 스키마
export const updateServiceGuideStepSchema = z.object({
  id: idSchema,
  title: titleSchema.optional(),
  content: contentSchema.optional(),
  contentHtml: z.string().optional(),
  order: z.number().int().nonnegative().optional(),
});

// 서비스 가이드 단계 삭제 스키마
export const deleteServiceGuideStepSchema = z.object({
  id: idSchema,
});

// 서비스 가이드 단계 순서 변경 스키마
export const reorderServiceGuideStepsSchema = z.object({
  serviceGuideId: idSchema,
  steps: z.array(z.object({
    id: idSchema,
    order: z.number().int().nonnegative(),
  })),
});

// 서비스 가이드 문서 생성 스키마
export const createServiceGuideDocumentSchema = z.object({
  serviceGuideId: idSchema,
  name: z.string().min(1, '문서 이름은 필수입니다.').max(255, '문서 이름은 최대 255자까지 가능합니다.'),
  description: z.string().optional(),
  required: z.boolean().default(true),
  order: z.number().int().nonnegative().default(0),
});

// 서비스 가이드 문서 수정 스키마
export const updateServiceGuideDocumentSchema = z.object({
  id: idSchema,
  name: z.string().min(1, '문서 이름은 필수입니다.').max(255, '문서 이름은 최대 255자까지 가능합니다.').optional(),
  description: z.string().optional(),
  required: z.boolean().optional(),
  order: z.number().int().nonnegative().optional(),
});

// 서비스 가이드 문서 삭제 스키마
export const deleteServiceGuideDocumentSchema = z.object({
  id: idSchema,
});

// 서비스 가이드 FAQ 생성 스키마
export const createServiceGuideFaqSchema = z.object({
  serviceGuideId: idSchema,
  question: z.string().min(1, '질문은 필수입니다.'),
  answer: z.string().min(1, '답변은 필수입니다.'),
  order: z.number().int().nonnegative().default(0),
});

// 서비스 가이드 FAQ 수정 스키마
export const updateServiceGuideFaqSchema = z.object({
  id: idSchema,
  question: z.string().min(1, '질문은 필수입니다.').optional(),
  answer: z.string().min(1, '답변은 필수입니다.').optional(),
  order: z.number().int().nonnegative().optional(),
});

// 서비스 가이드 FAQ 삭제 스키마
export const deleteServiceGuideFaqSchema = z.object({
  id: idSchema,
});

// 타입 정의
export type CreateServiceGuideInput = z.infer<typeof createServiceGuideSchema>;
export type UpdateServiceGuideInput = z.infer<typeof updateServiceGuideSchema>;
export type DeleteServiceGuideInput = z.infer<typeof deleteServiceGuideSchema>;
export type GetServiceGuideInput = z.infer<typeof getServiceGuideSchema>;
export type GetServiceGuidesInput = z.infer<typeof getServiceGuidesSchema>;
export type IncrementViewCountInput = z.infer<typeof incrementViewCountSchema>;
export type CreateServiceGuideStepInput = z.infer<typeof createServiceGuideStepSchema>;
export type UpdateServiceGuideStepInput = z.infer<typeof updateServiceGuideStepSchema>;
export type DeleteServiceGuideStepInput = z.infer<typeof deleteServiceGuideStepSchema>;
export type ReorderServiceGuideStepsInput = z.infer<typeof reorderServiceGuideStepsSchema>;
export type CreateServiceGuideDocumentInput = z.infer<typeof createServiceGuideDocumentSchema>;
export type UpdateServiceGuideDocumentInput = z.infer<typeof updateServiceGuideDocumentSchema>;
export type DeleteServiceGuideDocumentInput = z.infer<typeof deleteServiceGuideDocumentSchema>;
export type CreateServiceGuideFaqInput = z.infer<typeof createServiceGuideFaqSchema>;
export type UpdateServiceGuideFaqInput = z.infer<typeof updateServiceGuideFaqSchema>;
export type DeleteServiceGuideFaqInput = z.infer<typeof deleteServiceGuideFaqSchema>;
