"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createForbiddenErrorResponse,
  withValidation,
  withAuthAndData,
  withAdminAndData,
  cacheAction,
  CacheTag,
  getPaginationParams,
  getSortParams,
  createPaginationResult,
  logAction,
  logActionError,
} from "../utils";
import {
  createServiceGuideSchema,
  updateServiceGuideSchema,
  deleteServiceGuideSchema,
  getServiceGuideSchema,
  getServiceGuidesSchema,
  incrementViewCountSchema,
  CreateServiceGuideInput,
  UpdateServiceGuideInput,
  DeleteServiceGuideInput,
  GetServiceGuideInput,
  GetServiceGuidesInput,
  IncrementViewCountInput,
} from "./schemas";

/**
 * 서비스 가이드 생성 액션
 */
export const createServiceGuide = withValidation(
  createServiceGuideSchema,
  withAdminAndData(
    async (data: CreateServiceGuideInput, { userId }): Promise<ActionResponse> => {
      try {
        // 슬러그 중복 확인
        const existingGuide = await prisma.serviceGuide.findUnique({
          where: { slug: data.slug },
        });

        if (existingGuide) {
          return {
            success: false,
            error: {
              code: "CONFLICT",
              message: "이미 사용 중인 슬러그입니다.",
            },
          };
        }

        // 서비스 가이드 생성
        const serviceGuide = await prisma.serviceGuide.create({
          data: {
            title: data.title,
            slug: data.slug,
            summary: data.summary,
            content: data.content,
            contentHtml: data.contentHtml || "<p></p>",
            status: data.status,
            featured: data.featured,
            translations: data.translations,
            categoryId: data.categoryId,
            authorId: userId,
            officialLink: data.officialLink,
            publishedAt: data.status === "published" ? new Date() : undefined,
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
          },
        });

        logAction("createServiceGuide", { userId, serviceGuideId: serviceGuide.id });
        return createSuccessResponse(serviceGuide);
      } catch (error) {
        logActionError("createServiceGuide", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 수정 액션
 */
export const updateServiceGuide = withValidation(
  updateServiceGuideSchema,
  withAdminAndData(
    async (data: UpdateServiceGuideInput, { userId }): Promise<ActionResponse> => {
      try {
        // 서비스 가이드 존재 확인
        const existingGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.id },
        });

        if (!existingGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        // 슬러그 중복 확인 (변경된 경우에만)
        if (data.slug && data.slug !== existingGuide.slug) {
          const slugExists = await prisma.serviceGuide.findUnique({
            where: { slug: data.slug },
          });

          if (slugExists) {
            return {
              success: false,
              error: {
                code: "CONFLICT",
                message: "이미 사용 중인 슬러그입니다.",
              },
            };
          }
        }

        // 발행 상태 변경 시 publishedAt 업데이트
        let publishedAt = undefined;
        if (data.status === "published" && existingGuide.status !== "published") {
          publishedAt = new Date();
        } else if (data.publishedAt) {
          publishedAt = new Date(data.publishedAt);
        }

        // 서비스 가이드 수정
        const updatedGuide = await prisma.serviceGuide.update({
          where: { id: data.id },
          data: {
            title: data.title,
            slug: data.slug,
            summary: data.summary,
            content: data.content,
            contentHtml: data.contentHtml,
            status: data.status,
            featured: data.featured,
            translations: data.translations,
            categoryId: data.categoryId,
            officialLink: data.officialLink,
            publishedAt: publishedAt,
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            steps: {
              orderBy: {
                order: "asc",
              },
            },
            documents: {
              orderBy: {
                order: "asc",
              },
            },
            faqs: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        logAction("updateServiceGuide", { userId, serviceGuideId: updatedGuide.id });
        return createSuccessResponse(updatedGuide);
      } catch (error) {
        logActionError("updateServiceGuide", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 삭제 액션
 */
export const deleteServiceGuide = withValidation(
  deleteServiceGuideSchema,
  withAdminAndData(
    async (data: DeleteServiceGuideInput, { userId }): Promise<ActionResponse> => {
      try {
        // 서비스 가이드 존재 확인
        const existingGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.id },
        });

        if (!existingGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        // 서비스 가이드 삭제
        await prisma.serviceGuide.delete({
          where: { id: data.id },
        });

        logAction("deleteServiceGuide", { userId, serviceGuideId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteServiceGuide", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 조회 액션
 */
export const getServiceGuide = withValidation(
  getServiceGuideSchema,
  cacheAction(
    async (data: GetServiceGuideInput): Promise<ActionResponse> => {
      try {
        const serviceGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.id },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            steps: {
              orderBy: {
                order: "asc",
              },
              include: {
                images: {
                  orderBy: {
                    order: "asc",
                  },
                },
              },
            },
            documents: {
              orderBy: {
                order: "asc",
              },
            },
            faqs: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        if (!serviceGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        return createSuccessResponse(serviceGuide);
      } catch (error) {
        logActionError("getServiceGuide", error);
        throw error;
      }
    },
    { tags: [CacheTag.SERVICE_GUIDE], revalidate: 60 }
  )
);

/**
 * 서비스 가이드 목록 조회 액션
 */
export const getServiceGuides = withValidation(
  getServiceGuidesSchema,
  cacheAction(
    async (data: GetServiceGuidesInput): Promise<ActionResponse> => {
      try {
        const { page, limit } = getPaginationParams(data);
        const { sortBy, sortOrder } = getSortParams(data);

        // 필터링 조건 구성
        const where: any = {};

        if (data.categoryId) {
          where.categoryId = data.categoryId;
        }

        if (data.status) {
          where.status = data.status;
        }

        if (data.featured !== undefined) {
          where.featured = data.featured;
        }

        if (data.query) {
          where.OR = [
            { title: { contains: data.query, mode: "insensitive" } },
            { summary: { contains: data.query, mode: "insensitive" } },
            { content: { contains: data.query, mode: "insensitive" } },
          ];
        }

        // 총 항목 수 조회
        const total = await prisma.serviceGuide.count({ where });

        // 서비스 가이드 목록 조회
        const serviceGuides = await prisma.serviceGuide.findMany({
          where,
          include: {
            author: {
              select: {
                id: true,
                name: true,
                displayName: true,
                image: true,
              },
            },
            category: true,
            steps: {
              take: 1,
              orderBy: {
                order: "asc",
              },
              include: {
                images: {
                  take: 1,
                  orderBy: {
                    order: "asc",
                  },
                },
              },
            },
          },
          orderBy: {
            [sortBy]: sortOrder,
          },
          skip: (page - 1) * limit,
          take: limit,
        });

        return createSuccessResponse(
          createPaginationResult(serviceGuides, total, page, limit)
        );
      } catch (error) {
        logActionError("getServiceGuides", error);
        throw error;
      }
    },
    {
      tags: [
        CacheTag.SERVICE_GUIDES,
        ...(data.categoryId ? [CacheTag.CATEGORY] : []),
      ],
      revalidate: 60,
    }
  )
);

/**
 * 서비스 가이드 조회수 증가 액션
 */
export const incrementViewCount = withValidation(
  incrementViewCountSchema,
  async (data: IncrementViewCountInput): Promise<ActionResponse> => {
    try {
      const serviceGuide = await prisma.serviceGuide.findUnique({
        where: { id: data.id },
      });

      if (!serviceGuide) {
        return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
      }

      const updatedGuide = await prisma.serviceGuide.update({
        where: { id: data.id },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });

      return createSuccessResponse({ viewCount: updatedGuide.viewCount });
    } catch (error) {
      logActionError("incrementViewCount", error);
      throw error;
    }
  }
);
