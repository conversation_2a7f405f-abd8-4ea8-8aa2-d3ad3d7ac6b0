"use server";

import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAdminAndData,
  logAction,
  logActionError,
} from "../utils";
import {
  createServiceGuideStepSchema,
  updateServiceGuideStepSchema,
  deleteServiceGuideStepSchema,
  reorderServiceGuideStepsSchema,
  CreateServiceGuideStepInput,
  UpdateServiceGuideStepInput,
  DeleteServiceGuideStepInput,
  ReorderServiceGuideStepsInput,
} from "./schemas";

/**
 * 서비스 가이드 단계 생성 액션
 */
export const createServiceGuideStep = withValidation(
  createServiceGuideStepSchema,
  withAdminAndData(
    async (data: CreateServiceGuideStepInput, { userId }): Promise<ActionResponse> => {
      try {
        // 서비스 가이드 존재 확인
        const serviceGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.serviceGuideId },
        });

        if (!serviceGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        // 단계 순서 결정 (기본값: 마지막 순서 + 1)
        if (data.order === 0) {
          const lastStep = await prisma.serviceGuideStep.findFirst({
            where: { serviceGuideId: data.serviceGuideId },
            orderBy: { order: "desc" },
          });

          if (lastStep) {
            data.order = lastStep.order + 1;
          }
        }

        // 단계 생성
        const step = await prisma.serviceGuideStep.create({
          data: {
            title: data.title,
            content: data.content,
            contentHtml: data.contentHtml || "<p></p>",
            order: data.order,
            serviceGuideId: data.serviceGuideId,
          },
        });

        logAction("createServiceGuideStep", { userId, stepId: step.id });
        return createSuccessResponse(step);
      } catch (error) {
        logActionError("createServiceGuideStep", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 단계 수정 액션
 */
export const updateServiceGuideStep = withValidation(
  updateServiceGuideStepSchema,
  withAdminAndData(
    async (data: UpdateServiceGuideStepInput, { userId }): Promise<ActionResponse> => {
      try {
        // 단계 존재 확인
        const step = await prisma.serviceGuideStep.findUnique({
          where: { id: data.id },
        });

        if (!step) {
          return createNotFoundErrorResponse("단계를 찾을 수 없습니다.");
        }

        // 단계 수정
        const updatedStep = await prisma.serviceGuideStep.update({
          where: { id: data.id },
          data: {
            title: data.title,
            content: data.content,
            contentHtml: data.contentHtml,
            order: data.order,
          },
          include: {
            images: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        logAction("updateServiceGuideStep", { userId, stepId: updatedStep.id });
        return createSuccessResponse(updatedStep);
      } catch (error) {
        logActionError("updateServiceGuideStep", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 단계 삭제 액션
 */
export const deleteServiceGuideStep = withValidation(
  deleteServiceGuideStepSchema,
  withAdminAndData(
    async (data: DeleteServiceGuideStepInput, { userId }): Promise<ActionResponse> => {
      try {
        // 단계 존재 확인
        const step = await prisma.serviceGuideStep.findUnique({
          where: { id: data.id },
        });

        if (!step) {
          return createNotFoundErrorResponse("단계를 찾을 수 없습니다.");
        }

        // 단계 삭제
        await prisma.serviceGuideStep.delete({
          where: { id: data.id },
        });

        // 남은 단계들의 순서 재정렬
        const remainingSteps = await prisma.serviceGuideStep.findMany({
          where: { serviceGuideId: step.serviceGuideId },
          orderBy: { order: "asc" },
        });

        for (let i = 0; i < remainingSteps.length; i++) {
          await prisma.serviceGuideStep.update({
            where: { id: remainingSteps[i].id },
            data: { order: i },
          });
        }

        logAction("deleteServiceGuideStep", { userId, stepId: data.id });
        return createSuccessResponse({ id: data.id });
      } catch (error) {
        logActionError("deleteServiceGuideStep", error);
        throw error;
      }
    }
  )
);

/**
 * 서비스 가이드 단계 순서 변경 액션
 */
export const reorderServiceGuideSteps = withValidation(
  reorderServiceGuideStepsSchema,
  withAdminAndData(
    async (data: ReorderServiceGuideStepsInput, { userId }): Promise<ActionResponse> => {
      try {
        // 서비스 가이드 존재 확인
        const serviceGuide = await prisma.serviceGuide.findUnique({
          where: { id: data.serviceGuideId },
        });

        if (!serviceGuide) {
          return createNotFoundErrorResponse("서비스 가이드를 찾을 수 없습니다.");
        }

        // 단계 순서 변경
        const updates = data.steps.map((step) =>
          prisma.serviceGuideStep.update({
            where: { id: step.id },
            data: { order: step.order },
          })
        );

        await prisma.$transaction(updates);

        // 업데이트된 단계 목록 조회
        const updatedSteps = await prisma.serviceGuideStep.findMany({
          where: { serviceGuideId: data.serviceGuideId },
          orderBy: { order: "asc" },
          include: {
            images: {
              orderBy: {
                order: "asc",
              },
            },
          },
        });

        logAction("reorderServiceGuideSteps", { userId, serviceGuideId: data.serviceGuideId });
        return createSuccessResponse(updatedSteps);
      } catch (error) {
        logActionError("reorderServiceGuideSteps", error);
        throw error;
      }
    }
  )
);
