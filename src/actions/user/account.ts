'use server';

import { z } from 'zod';
import { auth, signOut } from '@/auth';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createUnauthorizedErrorResponse,
  createErrorResponse,
  ErrorCode,
  withAuth,
} from '../utils';

/**
 * 계정 삭제 액션
 */
export const deleteAccount = withAuth(async (userId: string): Promise<ActionResponse> => {
  try {
    // 사용자 존재 여부 확인
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return createErrorResponse(
        '사용자를 찾을 수 없습니다.',
        ErrorCode.NOT_FOUND
      );
    }

    // 사용자 관련 데이터 삭제
    // 참고: 실제 구현에서는 외래 키 제약 조건 및 관계를 고려해야 합니다.
    // Prisma의 경우 onDelete: Cascade 설정이 되어 있으면 자동으로 관련 데이터가 삭제됩니다.
    await prisma.user.delete({
      where: { id: userId },
    });

    // 로그아웃 처리
    await signOut();

    return createSuccessResponse({ success: true });
  } catch (error) {
    console.error('계정 삭제 중 오류 발생:', error);
    return createErrorResponse(
      '계정 삭제 중 오류가 발생했습니다.',
      ErrorCode.INTERNAL_SERVER_ERROR
    );
  }
});
