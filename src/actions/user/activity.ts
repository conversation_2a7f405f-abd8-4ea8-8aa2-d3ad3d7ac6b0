'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withAuthAndData,
} from '../utils';

// 활동 타입 정의
export type ActivityType = 'post' | 'comment' | 'like' | 'bookmark';

// 활동 조회 입력 스키마
const getUserActivitySchema = z.object({
  type: z.enum(['post', 'comment', 'like', 'bookmark']),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(50).default(10),
});

// 활동 조회 입력 타입
type GetUserActivityInput = z.infer<typeof getUserActivitySchema>;

/**
 * 사용자 활동 내역 조회 액션
 */
export const getUserActivity = withAuthAndData(
  async (userId: string, data: GetUserActivityInput): Promise<ActionResponse> => {
    try {
      // 사용자 존재 여부 확인
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return createNotFoundErrorResponse('사용자를 찾을 수 없습니다.');
      }

      const { type, page, limit } = data;
      const skip = (page - 1) * limit;

      // 활동 타입에 따라 다른 쿼리 실행
      switch (type) {
        case 'post': {
          // 게시글 조회
          const [posts, totalCount] = await Promise.all([
            prisma.post.findMany({
              where: { 
                userId,
                deletedAt: null,
              },
              orderBy: { createdAt: 'desc' },
              skip,
              take: limit,
              select: {
                id: true,
                content: true,
                contentHtml: true,
                createdAt: true,
                updatedAt: true,
                commentCount: true,
                likeCount: true,
                viewCount: true,
              },
            }),
            prisma.post.count({
              where: { 
                userId,
                deletedAt: null,
              },
            }),
          ]);

          // 결과 포맷팅
          const formattedPosts = posts.map(post => ({
            id: post.id,
            type: 'post' as ActivityType,
            title: post.content.substring(0, 50) + (post.content.length > 50 ? '...' : ''),
            content: post.contentHtml,
            targetId: post.id,
            targetType: 'post',
            createdAt: post.createdAt.toISOString(),
            stats: {
              comments: post.commentCount,
              likes: post.likeCount,
              views: post.viewCount,
            },
          }));

          return createSuccessResponse({
            items: formattedPosts,
            pagination: {
              page,
              limit,
              totalCount,
              totalPages: Math.ceil(totalCount / limit),
              hasMore: skip + posts.length < totalCount,
            },
          });
        }

        case 'comment': {
          // 댓글 조회
          const [comments, totalCount] = await Promise.all([
            prisma.postComment.findMany({
              where: { 
                userId,
                deletedAt: null,
              },
              orderBy: { createdAt: 'desc' },
              skip,
              take: limit,
              include: {
                post: {
                  select: {
                    id: true,
                    content: true,
                  },
                },
              },
            }),
            prisma.postComment.count({
              where: { 
                userId,
                deletedAt: null,
              },
            }),
          ]);

          // 결과 포맷팅
          const formattedComments = comments.map(comment => ({
            id: comment.id.toString(),
            type: 'comment' as ActivityType,
            title: `댓글: ${comment.content.substring(0, 30)}${comment.content.length > 30 ? '...' : ''}`,
            content: comment.content,
            targetId: comment.postId,
            targetType: 'post',
            createdAt: comment.createdAt.toISOString(),
            parentTitle: comment.post.content.substring(0, 30) + (comment.post.content.length > 30 ? '...' : ''),
          }));

          return createSuccessResponse({
            items: formattedComments,
            pagination: {
              page,
              limit,
              totalCount,
              totalPages: Math.ceil(totalCount / limit),
              hasMore: skip + comments.length < totalCount,
            },
          });
        }

        case 'like': {
          // 좋아요 조회
          const [likes, totalCount] = await Promise.all([
            prisma.postLike.findMany({
              where: { userId },
              orderBy: { createdAt: 'desc' },
              skip,
              take: limit,
              include: {
                post: {
                  select: {
                    id: true,
                    content: true,
                  },
                },
              },
            }),
            prisma.postLike.count({
              where: { userId },
            }),
          ]);

          // 결과 포맷팅
          const formattedLikes = likes.map(like => ({
            id: like.id.toString(),
            type: 'like' as ActivityType,
            title: '좋아요',
            targetId: like.postId,
            targetType: 'post',
            createdAt: like.createdAt.toISOString(),
            parentTitle: like.post.content.substring(0, 50) + (like.post.content.length > 50 ? '...' : ''),
          }));

          return createSuccessResponse({
            items: formattedLikes,
            pagination: {
              page,
              limit,
              totalCount,
              totalPages: Math.ceil(totalCount / limit),
              hasMore: skip + likes.length < totalCount,
            },
          });
        }

        case 'bookmark': {
          // 북마크 조회
          const [bookmarks, totalCount] = await Promise.all([
            prisma.bookmark.findMany({
              where: { userId },
              orderBy: { createdAt: 'desc' },
              skip,
              take: limit,
              include: {
                post: {
                  select: {
                    id: true,
                    content: true,
                  },
                },
              },
            }),
            prisma.bookmark.count({
              where: { userId },
            }),
          ]);

          // 결과 포맷팅
          const formattedBookmarks = bookmarks.map(bookmark => ({
            id: bookmark.id,
            type: 'bookmark' as ActivityType,
            title: '북마크',
            targetId: bookmark.postId,
            targetType: 'post',
            createdAt: bookmark.createdAt.toISOString(),
            parentTitle: bookmark.post.content.substring(0, 50) + (bookmark.post.content.length > 50 ? '...' : ''),
          }));

          return createSuccessResponse({
            items: formattedBookmarks,
            pagination: {
              page,
              limit,
              totalCount,
              totalPages: Math.ceil(totalCount / limit),
              hasMore: skip + bookmarks.length < totalCount,
            },
          });
        }

        default:
          return createSuccessResponse({
            items: [],
            pagination: {
              page,
              limit,
              totalCount: 0,
              totalPages: 0,
              hasMore: false,
            },
          });
      }
    } catch (error) {
      console.error('활동 내역 조회 중 오류 발생:', error);
      throw error;
    }
  }
);
