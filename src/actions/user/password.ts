'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createErrorResponse,
  withValidation,
  withAuthAndData,
  ErrorCode,
} from '../utils';
import { changePasswordSchema } from './schemas';

// 비밀번호 변경 타입
type ChangePasswordInput = z.infer<typeof changePasswordSchema>;

/**
 * 비밀번호 변경 액션
 * 
 * 참고: 실제 구현에서는 비밀번호 해싱 및 검증 로직이 필요합니다.
 * 현재는 Auth.js를 사용하고 있으므로 실제 비밀번호 변경 로직은 Auth.js 제공자에 따라 달라질 수 있습니다.
 * 이 예제는 개념적인 구현만 제공합니다.
 */
export const changePassword = withValidation(
  changePasswordSchema,
  withAuthAndData(async (userId: string, data: ChangePasswordInput): Promise<ActionResponse> => {
    try {
      // 사용자 존재 여부 확인
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return createNotFoundErrorResponse('사용자를 찾을 수 없습니다.');
      }

      // 현재 비밀번호 확인 (실제로는 해싱된 비밀번호 비교 로직이 필요)
      // 이 부분은 Auth.js 제공자에 따라 구현이 달라질 수 있음
      const isCurrentPasswordValid = true; // 예시로 항상 true 반환
      
      if (!isCurrentPasswordValid) {
        return createErrorResponse('현재 비밀번호가 일치하지 않습니다.', ErrorCode.VALIDATION_ERROR, 'currentPassword');
      }

      // 새 비밀번호로 업데이트 (실제로는 비밀번호 해싱 후 저장)
      // 이 부분은 Auth.js 제공자에 따라 구현이 달라질 수 있음
      // 예시로만 구현
      /*
      await prisma.user.update({
        where: { id: userId },
        data: {
          password: hashedNewPassword,
        },
      });
      */

      return createSuccessResponse({ message: '비밀번호가 성공적으로 변경되었습니다.' });
    } catch (error) {
      console.error('비밀번호 변경 중 오류 발생:', error);
      throw error;
    }
  })
);
