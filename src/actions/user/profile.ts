"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  withValidation,
  withAuthAndData,
} from "../utils";
import { updateProfileSchema } from "./schemas";

// 프로필 업데이트 타입
type UpdateProfileInput = z.infer<typeof updateProfileSchema>;

/**
 * 사용자 프로필 업데이트 액션
 */
export const updateProfile = withValidation(
  updateProfileSchema,
  withAuthAndData(
    async (
      userId: string,
      data: UpdateProfileInput
    ): Promise<ActionResponse> => {
      try {
        // 사용자 존재 여부 확인
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });

        if (!user) {
          return createNotFoundErrorResponse("사용자를 찾을 수 없습니다.");
        }

        // 프로필 업데이트
        const updatedUser = await prisma.user.update({
          where: { id: userId },
          data: {
            displayName: data.displayName,
            bio: data.bio,
            image: data.image,
          },
          select: {
            id: true,
            name: true,
            displayName: true,
            bio: true,
            image: true,
            email: true,
          },
        });

        return createSuccessResponse(updatedUser);
      } catch (error) {
        console.error("프로필 업데이트 중 오류 발생:", error);
        throw error;
      }
    }
  )
);

/**
 * 사용자 프로필 조회 액션
 */
export const getProfile = withAuthAndData(
  async (userId: string): Promise<ActionResponse> => {
    try {
      // 사용자 프로필 조회
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          displayName: true,
          bio: true,
          image: true,
          email: true,
          step: true,
          postCount: true,
          postCommentCount: true,
          likeCount: true,
          createdAt: true,
        },
      });

      if (!user) {
        return createNotFoundErrorResponse("사용자를 찾을 수 없습니다.");
      }

      return createSuccessResponse(user);
    } catch (error) {
      console.error("프로필 조회 중 오류 발생:", error);
      throw error;
    }
  }
);
