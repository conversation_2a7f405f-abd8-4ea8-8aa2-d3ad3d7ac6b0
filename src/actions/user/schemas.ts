import { z } from 'zod';
import { emailSchema, usernameSchema, passwordSchema } from '../utils/schemas';

/**
 * 사용자 관련 Zod 스키마 정의
 */

// 사용자 프로필 업데이트 스키마
export const updateProfileSchema = z.object({
  displayName: usernameSchema.optional(),
  bio: z.string().max(500, '자기소개는 최대 500자까지 가능합니다.').optional(),
  image: z.string().url('유효한 URL 형식이 아닙니다.').optional(),
});

// 비밀번호 변경 스키마
export const changePasswordSchema = z.object({
  currentPassword: passwordSchema,
  newPassword: passwordSchema,
  confirmPassword: passwordSchema,
}).refine(
  (data) => data.newPassword === data.confirmPassword,
  {
    message: '새 비밀번호와 확인 비밀번호가 일치하지 않습니다.',
    path: ['confirmPassword'],
  }
);

// 사용자 설정 업데이트 스키마
export const updateSettingsSchema = z.object({
  language: z.enum(['ko', 'en']),
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
});

// 사용자 검색 스키마
export const searchUsersSchema = z.object({
  query: z.string().min(1, '검색어는 필수입니다.'),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(50).default(10),
});

// 사용자 등록 스키마
export const registerUserSchema = z.object({
  name: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: passwordSchema,
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: '비밀번호와 확인 비밀번호가 일치하지 않습니다.',
    path: ['confirmPassword'],
  }
);
