'use server';

import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import {
  ActionResponse,
  createSuccessResponse,
  createNotFoundErrorResponse,
  createErrorResponse,
  withValidation,
  withAuthAndData,
  ErrorCode,
} from '../utils';
import { updateSettingsSchema } from './schemas';

// 사용자 설정 업데이트 타입
type UpdateSettingsInput = z.infer<typeof updateSettingsSchema>;

/**
 * 사용자 설정 업데이트 액션
 */
export const updateSettings = withValidation(
  updateSettingsSchema,
  withAuthAndData(async (userId: string, data: UpdateSettingsInput): Promise<ActionResponse> => {
    try {
      // 사용자 존재 여부 확인
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return createNotFoundErrorResponse('사용자를 찾을 수 없습니다.');
      }

      // 사용자 설정 업데이트 (실제로는 User 모델에 설정 필드를 추가하거나 별도의 UserSettings 모델을 만들어야 함)
      // 현재는 예시로만 구현
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          // 실제 필드가 있다고 가정
          // language: data.language,
          // emailNotifications: data.emailNotifications,
          // pushNotifications: data.pushNotifications,
        },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });

      return createSuccessResponse({
        ...updatedUser,
        settings: {
          language: data.language,
          emailNotifications: data.emailNotifications,
          pushNotifications: data.pushNotifications,
        },
      });
    } catch (error) {
      console.error('설정 업데이트 중 오류 발생:', error);
      throw error;
    }
  })
);

/**
 * 사용자 설정 조회 액션
 */
export const getSettings = withAuthAndData(async (userId: string): Promise<ActionResponse> => {
  try {
    // 사용자 존재 여부 확인
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return createNotFoundErrorResponse('사용자를 찾을 수 없습니다.');
    }

    // 실제로는 DB에서 설정을 가져와야 함
    // 현재는 예시로 기본값 반환
    return createSuccessResponse({
      language: 'ko',
      emailNotifications: true,
      pushNotifications: true,
    });
  } catch (error) {
    console.error('설정 조회 중 오류 발생:', error);
    throw error;
  }
});
