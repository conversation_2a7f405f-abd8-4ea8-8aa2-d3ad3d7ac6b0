'use server';

import { z } from 'zod';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { uploadProfileImage } from '@/lib/supabase';
import {
  ActionResponse,
  createSuccessResponse,
  createUnauthorizedErrorResponse,
  createErrorResponse,
  ErrorCode,
} from '../utils';

/**
 * 프로필 이미지 업로드 액션
 * @param formData 이미지 파일이 포함된 FormData
 * @returns 업로드된 이미지 URL
 */
export async function uploadProfileImageAction(
  formData: FormData
): Promise<ActionResponse> {
  try {
    // 인증 확인
    const session = await auth();
    if (!session || !session.user?.id) {
      return createUnauthorizedErrorResponse();
    }

    const userId = session.user.id;

    // 파일 추출
    const file = formData.get('file') as File;
    if (!file) {
      return createErrorResponse(
        '파일이 제공되지 않았습니다.',
        ErrorCode.VALIDATION_ERROR,
        'file'
      );
    }

    // 파일 타입 검증
    if (!file.type.startsWith('image/')) {
      return createErrorResponse(
        '이미지 파일만 업로드 가능합니다.',
        ErrorCode.VALIDATION_ERROR,
        'file'
      );
    }

    // 파일 크기 검증 (2MB 제한)
    if (file.size > 2 * 1024 * 1024) {
      return createErrorResponse(
        '이미지 크기는 최대 2MB까지 가능합니다.',
        ErrorCode.VALIDATION_ERROR,
        'file'
      );
    }

    // 이미지 업로드
    const imageUrl = await uploadProfileImage(file, userId);

    // 사용자 프로필 이미지 업데이트
    await prisma.user.update({
      where: { id: userId },
      data: { image: imageUrl },
    });

    return createSuccessResponse({ imageUrl });
  } catch (error) {
    console.error('프로필 이미지 업로드 중 오류 발생:', error);
    return createErrorResponse(
      '이미지 업로드 중 오류가 발생했습니다.',
      ErrorCode.INTERNAL_SERVER_ERROR
    );
  }
}
