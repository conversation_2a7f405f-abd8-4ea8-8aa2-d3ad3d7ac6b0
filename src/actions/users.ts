'use server';

import { auth } from '@/auth';
import { actionH<PERSON><PERSON>, actionHandlerWithAuth } from '@/lib/actions/core';
import { BadRequestException, ConflictException } from '@/lib/api/types/error';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@/lib/prisma/generated/client';
import { Session } from 'next-auth';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

// --- Zod Schemas for Input Validation ---

// 프로필 업데이트 스키마 추가
const updateUserProfileSchema = z
  .object({
    name: z.string().min(1, '이름은 비워둘 수 없습니다.').optional(),
    displayName: z
      .string()
      .min(1, '표시 이름은 비워둘 수 없습니다.')
      .optional(),
    imageUrl: z.string().url('유효한 URL 형식이 아닙니다.').optional(),
  })
  .refine(
    (data) =>
      data.name !== undefined ||
      data.displayName !== undefined ||
      data.imageUrl !== undefined,
    {
      message: '업데이트할 값이 없습니다',
      path: [],
    }
  );

// --- Server Actions ---

// --- New Server Action for User Profile Update ---

/**
 * 현재 로그인된 사용자의 프로필 정보 (이름, 표시 이름, 이미지 URL)를 업데이트합니다.
 */
export const updateUserProfile = actionHandlerWithAuth(
  async (session: Session, formData: FormData) => {
    const userId = session.user.id!;
    // 2. 입력 데이터 추출 및 기본 검사
    const rawData = {
      name: formData.get('name') || undefined,
      displayName: formData.get('displayName') || undefined,
      imageUrl: formData.get('imageUrl') || undefined,
    };

    // 빈 문자열을 undefined로 변환
    if (rawData.name === '') rawData.name = undefined;
    if (rawData.displayName === '') rawData.displayName = undefined;
    if (rawData.imageUrl === '') rawData.imageUrl = undefined;

    // 3. Zod 유효성 검사
    const validationResult = updateUserProfileSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errorMessage =
        validationResult.error.errors[0]?.message ?? '잘못된 입력 값입니다.';
      // actionHandler를 사용하지 않으므로 직접 에러 처리
      throw new BadRequestException(errorMessage);
    }

    const { name, displayName, imageUrl } = validationResult.data;

    // 업데이트할 값이 하나도 없는 경우 (refine에서 처리하지만 이중 확인)
    if (
      name === undefined &&
      displayName === undefined &&
      imageUrl === undefined
    ) {
      throw new BadRequestException('업데이트할 값이 없습니다');
    }

    // 4. 데이터베이스 업데이트
    try {
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...(name !== undefined && { name }),
          ...(displayName !== undefined && { displayName }),
          ...(imageUrl !== undefined && { image: imageUrl }), // Prisma 스키마 필드명 'image'
        },
        select: {
          id: true,
          name: true,
          displayName: true,
          image: true,
        },
      });

      // 관련 페이지 캐시 무효화 (예: /profile)
      // 실제 사용하는 프로필 페이지 경로로 변경해야 합니다.
      revalidatePath('/profile');
      // 필요하다면 다른 관련 경로도 추가 (예: 사용자 목록 페이지)
      // revalidatePath('/users');

      return { success: true, user: updatedUser };
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002' // Unique constraint violation
      ) {
        const target = (error.meta?.target as string[]) || [];
        if (target.includes('name')) {
          // actionHandler를 사용하지 않으므로 직접 에러 처리
          throw new ConflictException('이미 사용 중인 이름입니다.');
        } else {
          // 다른 유니크 제약 조건 위반 처리
          // actionHandler를 사용하지 않으므로 직접 에러 처리
          throw new BadRequestException('데이터베이스 제약 조건 위반');
        }
      }
      // 기타 예상치 못한 에러 로깅 및 일반 에러 throw
      console.error('Error updating user profile:', error);
      // actionHandler를 사용하지 않으므로 직접 에러 처리
      // 실제 운영 환경에서는 더 구체적인 에러 처리가 필요할 수 있습니다.
      throw new Error('프로필 업데이트 중 오류가 발생했습니다.');
    }
  }
);

/**
 * 사용자의 게시글 수와 댓글 수를 가져오는 함수
 */
export const getUserActivity = actionHandlerWithAuth(
  async (
    session: Session
  ): Promise<{ postCount: number; commentCount: number }> => {
    const userId = session.user.id!;

    // 게시글 수와 댓글 수를 병렬로 조회
    const [postCount, commentCount] = await Promise.all([
      prisma.post.count({
        where: {
          userId,
          deletedAt: null,
        },
      }),
      prisma.postComment.count({
        where: {
          userId,
          deletedAt: null,
        },
      }),
    ]);

    return {
      postCount,
      commentCount,
    };
  }
);

/**
 * 사용자 정보와 함께 게시글 수 및 댓글 수를 가져오는 함수
 */
export const getUserWithActivity = actionHandler(
  async (input?: {
    id?: string;
    name?: string;
  }): Promise<{
    user: {
      id: string;
      name: string | null;
      displayName: string | null;
      email: string | null;
      image: string | null;
      postCount: number;
      postCommentCount: number;
    } | null;
  }> => {
    let userQuery: Prisma.UserWhereUniqueInput;

    // 입력값에 따라 쿼리 조건 설정
    if (input?.name) {
      userQuery = { name: input.name };
    } else if (input?.id) {
      userQuery = { id: input.id };
    } else {
      // ID와 이름 둘 다 없으면 현재 로그인한 사용자 ID 사용
      const session = await auth();
      const userId = session?.user?.id;
      if (!userId) {
        return { user: null }; // 로그인되지 않은 경우
      }
      userQuery = { id: userId };
    }

    // 사용자 정보와 함께 게시글 수와 댓글 수 조회
    const user = await prisma.user.findUnique({
      where: userQuery,
      select: {
        id: true,
        name: true,
        displayName: true,
        email: true,
        image: true,
        postCount: true,
        postCommentCount: true,
      },
    });

    if (!user) {
      return { user: null }; // 사용자를 찾을 수 없는 경우
    }

    return { user };
  }
);
