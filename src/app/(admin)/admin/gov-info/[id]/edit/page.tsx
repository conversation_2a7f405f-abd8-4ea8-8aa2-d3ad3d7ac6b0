import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getGovInfo } from "@/actions/govinfo/govinfo";
import { prisma } from "@/lib/prisma";
import GovInfoForm from "@/components/govinfo/GovInfoForm";

interface EditGovInfoPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({
  params,
}: EditGovInfoPageProps): Promise<Metadata> {
  const { id } = params;

  const result = await getGovInfo({ id });
  if (!result.success) {
    return {
      title: "정보를 찾을 수 없습니다 | 관리자",
      description: "요청하신 정부 정보를 찾을 수 없습니다.",
    };
  }

  const govInfo = result.data;

  return {
    title: `${govInfo.title} 수정 | 관리자`,
    description: "정부 정보 수정 페이지입니다.",
  };
}

export default async function EditGovInfoPage({ params }: EditGovInfoPageProps) {
  const { id } = params;

  // 정부 정보 조회
  const result = await getGovInfo({ id });
  if (!result.success) {
    notFound();
  }

  const govInfo = result.data;

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: "asc",
    },
  });

  return (
    <div className="container py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">정부 정보 수정</h1>
        <p className="text-muted-foreground">
          정부 정보 콘텐츠를 수정합니다.
        </p>
      </div>

      <GovInfoForm
        categories={categories}
        initialData={govInfo}
        isEdit={true}
      />
    </div>
  );
}
