import { Suspense } from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { getGovInfo } from "@/actions/govinfo/govinfo";
import { formatDate } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Edit, ExternalLink } from "lucide-react";
import ContentRenderer from "@/components/shared/ContentRenderer";
import ImageGallery from "@/components/govinfo/ImageGallery";

interface AdminGovInfoDetailPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({
  params,
}: AdminGovInfoDetailPageProps): Promise<Metadata> {
  const { id } = params;

  const result = await getGovInfo({ id });
  if (!result.success) {
    return {
      title: "정보를 찾을 수 없습니다 | 관리자",
      description: "요청하신 정부 정보를 찾을 수 없습니다.",
    };
  }

  const govInfo = result.data;

  return {
    title: `${govInfo.title} | 관리자`,
    description: "정부 정보 상세 관리 페이지입니다.",
  };
}

export default async function AdminGovInfoDetailPage({
  params,
}: AdminGovInfoDetailPageProps) {
  const { id } = params;

  // 정부 정보 조회
  const result = await getGovInfo({ id });
  if (!result.success) {
    notFound();
  }

  const govInfo = result.data;

  // 상태에 따른 배지 색상 설정
  const statusBadgeVariant = 
    govInfo.status === "published" 
      ? "success" as const
      : govInfo.status === "draft"
      ? "secondary" as const
      : "outline" as const;

  // 상태 텍스트 변환
  const statusText = 
    govInfo.status === "published"
      ? "발행됨"
      : govInfo.status === "draft"
      ? "임시저장"
      : "보관됨";

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">{govInfo.title}</h1>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant={statusBadgeVariant}>{statusText}</Badge>
            {govInfo.isImportant && (
              <Badge variant="destructive">중요 공지사항</Badge>
            )}
            <span className="text-sm text-muted-foreground">
              카테고리: {govInfo.category.name}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/gov-info/${govInfo.slug}`} target="_blank">
              <ExternalLink className="mr-2 h-4 w-4" />
              공개 페이지 보기
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/admin/gov-info/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              수정
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">작성자</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-medium">{govInfo.author.displayName || govInfo.author.name}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">작성일</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-medium">{formatDate(govInfo.createdAt)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">조회수</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-medium">{govInfo.viewCount}회</div>
          </CardContent>
        </Card>
      </div>

      {govInfo.govUrl && (
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">정부/공공기관 링크</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="font-medium">{govInfo.govDepartment || "정부 사이트"}</span>
              <Button variant="outline" size="sm" asChild>
                <a href={govInfo.govUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  바로가기
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {govInfo.infoUpdatedAt && (
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">정보 업데이트 날짜</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-medium">{formatDate(govInfo.infoUpdatedAt)}</div>
          </CardContent>
        </Card>
      )}

      <div className="mb-6">
        <Card>
          <CardHeader>
            <CardTitle>정부 정보 콘텐츠</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content">
              <TabsList>
                <TabsTrigger value="content">내용</TabsTrigger>
                <TabsTrigger value="images">이미지 ({govInfo.images.length})</TabsTrigger>
              </TabsList>
              <TabsContent value="content" className="mt-4">
                <ContentRenderer content={govInfo.contentHtml} />
              </TabsContent>
              <TabsContent value="images" className="mt-4">
                <Suspense fallback={<Skeleton className="h-96 w-full" />}>
                  <ImageGallery
                    govInfoId={govInfo.id}
                    images={govInfo.images}
                    onImagesChange={() => {
                      // 클라이언트 컴포넌트에서 서버 액션 호출 후 페이지 새로고침을 위한 콜백
                    }}
                  />
                </Suspense>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
