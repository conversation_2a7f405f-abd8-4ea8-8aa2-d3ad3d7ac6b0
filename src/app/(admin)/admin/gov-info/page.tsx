import { Metada<PERSON> } from "next";
import Link from "next/link";
import { getGovInfos } from "@/actions/govinfo/govinfo";
import { prisma } from "@/lib/prisma";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PlusCircle } from "lucide-react";
import GovInfoTable from "@/components/govinfo/GovInfoTable";

export const metadata: Metadata = {
  title: "정부 정보 관리 | 관리자",
  description: "정부 정보 콘텐츠를 관리합니다.",
};

interface AdminGovInfoPageProps {
  searchParams: {
    status?: string;
    page?: string;
    categoryId?: string;
  };
}

export default async function AdminGovInfoPage({
  searchParams,
}: AdminGovInfoPageProps) {
  // 페이지 파라미터 처리
  const status = searchParams.status as "draft" | "published" | "archived" | undefined;
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const categoryId = searchParams.categoryId;

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    orderBy: {
      order: "asc",
    },
  });

  // 정부 정보 목록 조회
  const draftResult = status === "draft" || !status
    ? await getGovInfos({
        page: status === "draft" ? page : 1,
        limit: 10,
        status: "draft",
        categoryId: status === "draft" ? categoryId : undefined,
      })
    : { success: true, data: { data: [], pagination: { totalPages: 1 } } };

  const publishedResult = status === "published"
    ? await getGovInfos({
        page,
        limit: 10,
        status: "published",
        categoryId,
      })
    : { success: true, data: { data: [], pagination: { totalPages: 1 } } };

  const archivedResult = status === "archived"
    ? await getGovInfos({
        page,
        limit: 10,
        status: "archived",
        categoryId,
      })
    : { success: true, data: { data: [], pagination: { totalPages: 1 } } };

  // 데이터 추출
  const draftGovInfos = draftResult.success ? draftResult.data.data : [];
  const publishedGovInfos = publishedResult.success ? publishedResult.data.data : [];
  const archivedGovInfos = archivedResult.success ? archivedResult.data.data : [];

  const draftPagination = draftResult.success ? draftResult.data.pagination : { totalPages: 1 };
  const publishedPagination = publishedResult.success ? publishedResult.data.pagination : { totalPages: 1 };
  const archivedPagination = archivedResult.success ? archivedResult.data.pagination : { totalPages: 1 };

  // 각 상태별 정부 정보 수 조회
  const draftCount = await prisma.govInfo.count({ where: { status: "draft" } });
  const publishedCount = await prisma.govInfo.count({ where: { status: "published" } });
  const archivedCount = await prisma.govInfo.count({ where: { status: "archived" } });

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">정부 정보 관리</h1>
          <p className="text-muted-foreground">
            정부 정보 콘텐츠를 생성하고 관리합니다.
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/gov-info/create">
            <PlusCircle className="mr-2 h-4 w-4" />
            정부 정보 생성
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              임시저장
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{draftCount}개</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              발행됨
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{publishedCount}개</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              보관됨
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{archivedCount}개</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={status || "draft"} className="space-y-4">
        <TabsList>
          <TabsTrigger value="draft" asChild>
            <Link href="/admin/gov-info?status=draft">임시저장 ({draftCount})</Link>
          </TabsTrigger>
          <TabsTrigger value="published" asChild>
            <Link href="/admin/gov-info?status=published">발행됨 ({publishedCount})</Link>
          </TabsTrigger>
          <TabsTrigger value="archived" asChild>
            <Link href="/admin/gov-info?status=archived">보관됨 ({archivedCount})</Link>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="draft" className="space-y-4">
          <GovInfoTable
            govInfos={draftGovInfos}
            categories={categories}
            pagination={draftPagination}
            currentPage={status === "draft" ? page : 1}
            status="draft"
            categoryId={status === "draft" ? categoryId : undefined}
          />
        </TabsContent>
        <TabsContent value="published" className="space-y-4">
          <GovInfoTable
            govInfos={publishedGovInfos}
            categories={categories}
            pagination={publishedPagination}
            currentPage={page}
            status="published"
            categoryId={categoryId}
          />
        </TabsContent>
        <TabsContent value="archived" className="space-y-4">
          <GovInfoTable
            govInfos={archivedGovInfos}
            categories={categories}
            pagination={archivedPagination}
            currentPage={page}
            status="archived"
            categoryId={categoryId}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
