import { Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { prisma } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import ContentRenderer from '@/components/editor/ContentRenderer';
import ImageGallery from '@/components/lifeinfo/ImageGallery';
import { formatDate } from '@/lib/utils';
import { Pencil, Eye, ArrowLeft } from 'lucide-react';

export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const { id } = params;

  // 생활 정보 조회
  const lifeInfo = await prisma.lifeInfo.findUnique({
    where: {
      id,
    },
    select: {
      title: true,
    },
  });

  if (!lifeInfo) {
    return {
      title: '찾을 수 없음 | 관리자',
      description: '요청하신 페이지를 찾을 수 없습니다.',
    };
  }

  return {
    title: `${lifeInfo.title} | 관리자`,
    description: '생활 정보 콘텐츠 관리',
  };
}

interface AdminLifeInfoDetailPageProps {
  params: {
    id: string;
  };
}

export default async function AdminLifeInfoDetailPage({
  params,
}: AdminLifeInfoDetailPageProps) {
  const { id } = params;

  // 생활 정보 조회
  const lifeInfo = await prisma.lifeInfo.findUnique({
    where: {
      id,
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          displayName: true,
          image: true,
        },
      },
      category: true,
      images: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!lifeInfo) {
    notFound();
  }

  // 상태별 배지 색상
  const statusColors: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'secondary',
  };

  // 상태 텍스트
  const statusText = {
    draft: '초안',
    published: '발행됨',
    archived: '보관됨',
  };

  return (
    <div className="container py-8">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/admin/life-info">
            <ArrowLeft className="mr-2 h-4 w-4" />
            목록으로
          </Link>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/admin/life-info/edit/${id}`}>
            <Pencil className="mr-2 h-4 w-4" />
            수정
          </Link>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/life-info/${lifeInfo.slug}`} target="_blank">
            <Eye className="mr-2 h-4 w-4" />
            공개 페이지 보기
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl">{lifeInfo.title}</CardTitle>
                <Badge variant={statusColors[lifeInfo.status] as any}>
                  {statusText[lifeInfo.status as keyof typeof statusText]}
                </Badge>
              </div>
              {lifeInfo.summary && (
                <p className="text-muted-foreground">{lifeInfo.summary}</p>
              )}
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="content">
                <TabsList>
                  <TabsTrigger value="content">내용</TabsTrigger>
                  <TabsTrigger value="images">이미지 ({lifeInfo.images.length})</TabsTrigger>
                </TabsList>
                <TabsContent value="content" className="mt-4">
                  <ContentRenderer content={lifeInfo.contentHtml} />
                </TabsContent>
                <TabsContent value="images" className="mt-4">
                  <Suspense fallback={<Skeleton className="h-96 w-full" />}>
                    <ImageGallery
                      lifeInfoId={lifeInfo.id}
                      images={lifeInfo.images}
                      onImagesChange={() => {
                        // 클라이언트 컴포넌트에서 서버 액션 호출 후 페이지 새로고침을 위한 콜백
                      }}
                    />
                  </Suspense>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>정보</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">카테고리</h3>
                <p>{lifeInfo.category.name}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">슬러그</h3>
                <p className="text-sm font-mono">{lifeInfo.slug}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">작성자</h3>
                <div className="flex items-center gap-2 mt-1">
                  {lifeInfo.author.image && (
                    <Image
                      src={lifeInfo.author.image}
                      alt={lifeInfo.author.name}
                      width={24}
                      height={24}
                      className="rounded-full"
                    />
                  )}
                  <span>
                    {lifeInfo.author.displayName || lifeInfo.author.name}
                  </span>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium">조회수</h3>
                <p>{lifeInfo.viewCount}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">주요 콘텐츠</h3>
                <p>{lifeInfo.featured ? '예' : '아니오'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">생성일</h3>
                <p>{formatDate(lifeInfo.createdAt)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">수정일</h3>
                <p>{formatDate(lifeInfo.updatedAt)}</p>
              </div>
              {lifeInfo.publishedAt && (
                <div>
                  <h3 className="text-sm font-medium">발행일</h3>
                  <p>{formatDate(lifeInfo.publishedAt)}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
