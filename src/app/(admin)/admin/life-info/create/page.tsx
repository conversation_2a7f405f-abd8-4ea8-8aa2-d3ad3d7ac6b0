import { Metadata } from 'next';
import { prisma } from '@/lib/prisma';
import LifeInfoForm from '@/components/lifeinfo/LifeInfoForm';

export const metadata: Metadata = {
  title: '생활 정보 생성 | 관리자',
  description: '새로운 생활 정보 콘텐츠를 생성합니다.',
};

export default async function CreateLifeInfoPage() {
  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });

  return (
    <div className="container py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">생활 정보 생성</h1>
        <p className="text-muted-foreground">
          새로운 생활 정보 콘텐츠를 생성합니다.
        </p>
      </div>

      <LifeInfoForm categories={categories} />
    </div>
  );
}
