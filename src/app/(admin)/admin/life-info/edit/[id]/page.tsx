import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import LifeInfoForm from '@/components/lifeinfo/LifeInfoForm';

export const metadata: Metadata = {
  title: '생활 정보 수정 | 관리자',
  description: '생활 정보 콘텐츠를 수정합니다.',
};

interface EditLifeInfoPageProps {
  params: {
    id: string;
  };
}

export default async function EditLifeInfoPage({
  params,
}: EditLifeInfoPageProps) {
  const { id } = params;

  // 생활 정보 조회
  const lifeInfo = await prisma.lifeInfo.findUnique({
    where: {
      id,
    },
  });

  if (!lifeInfo) {
    notFound();
  }

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });

  return (
    <div className="container py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">생활 정보 수정</h1>
        <p className="text-muted-foreground">
          생활 정보 콘텐츠를 수정합니다.
        </p>
      </div>

      <LifeInfoForm
        categories={categories}
        initialData={lifeInfo}
        isEdit={true}
      />
    </div>
  );
}
