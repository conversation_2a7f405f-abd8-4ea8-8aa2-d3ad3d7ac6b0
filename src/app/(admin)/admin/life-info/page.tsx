import { Suspense } from 'react';
import Link from 'next/link';
import { Metadata } from 'next';
import { getLifeInfos } from '@/actions/lifeinfo/lifeinfo';
import { prisma } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/lib/utils';
import { Plus } from 'lucide-react';

export const metadata: Metadata = {
  title: '생활 정보 관리 | 관리자',
  description: '생활 정보 콘텐츠를 관리합니다.',
};

interface AdminLifeInfoPageProps {
  searchParams: {
    page?: string;
    status?: string;
  };
}

export default async function AdminLifeInfoPage({
  searchParams,
}: AdminLifeInfoPageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const status = searchParams.status as 'draft' | 'published' | 'archived' | undefined;

  // 생활 정보 목록 조회
  const result = await getLifeInfos({
    page,
    limit: 10,
    status,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const lifeInfos = result.success ? result.data.data : [];
  const pagination = result.success ? result.data.pagination : { totalPages: 1 };

  // 상태별 배지 색상
  const statusColors: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'secondary',
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">생활 정보 관리</h1>
          <p className="text-muted-foreground">
            생활 정보 콘텐츠를 생성, 수정, 삭제합니다.
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/life-info/create">
            <Plus className="mr-2 h-4 w-4" />
            새 생활 정보
          </Link>
        </Button>
      </div>

      <div className="mb-6 flex gap-2">
        <Button
          variant={!status ? 'default' : 'outline'}
          size="sm"
          asChild
        >
          <Link href="/admin/life-info">전체</Link>
        </Button>
        <Button
          variant={status === 'draft' ? 'default' : 'outline'}
          size="sm"
          asChild
        >
          <Link href="/admin/life-info?status=draft">초안</Link>
        </Button>
        <Button
          variant={status === 'published' ? 'default' : 'outline'}
          size="sm"
          asChild
        >
          <Link href="/admin/life-info?status=published">발행됨</Link>
        </Button>
        <Button
          variant={status === 'archived' ? 'default' : 'outline'}
          size="sm"
          asChild
        >
          <Link href="/admin/life-info?status=archived">보관됨</Link>
        </Button>
      </div>

      <Suspense
        fallback={
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-96 w-full" />
          </div>
        }
      >
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>제목</TableHead>
                <TableHead>카테고리</TableHead>
                <TableHead>상태</TableHead>
                <TableHead>조회수</TableHead>
                <TableHead>작성일</TableHead>
                <TableHead>작업</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {lifeInfos.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    등록된 생활 정보가 없습니다.
                  </TableCell>
                </TableRow>
              ) : (
                lifeInfos.map((lifeInfo) => (
                  <TableRow key={lifeInfo.id}>
                    <TableCell className="font-medium">
                      <Link
                        href={`/admin/life-info/${lifeInfo.id}`}
                        className="hover:underline"
                      >
                        {lifeInfo.title}
                      </Link>
                      {lifeInfo.featured && (
                        <Badge variant="outline" className="ml-2">
                          주요
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>{lifeInfo.category.name}</TableCell>
                    <TableCell>
                      <Badge variant={statusColors[lifeInfo.status] as any}>
                        {lifeInfo.status === 'draft'
                          ? '초안'
                          : lifeInfo.status === 'published'
                          ? '발행됨'
                          : '보관됨'}
                      </Badge>
                    </TableCell>
                    <TableCell>{lifeInfo.viewCount}</TableCell>
                    <TableCell>
                      {formatDate(lifeInfo.createdAt, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/admin/life-info/edit/${lifeInfo.id}`}>
                            수정
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/life-info/${lifeInfo.slug}`} target="_blank">
                            보기
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {pagination.totalPages > 1 && (
          <Pagination className="mt-6">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href={`/admin/life-info?page=${page > 1 ? page - 1 : 1}${
                    status ? `&status=${status}` : ''
                  }`}
                  aria-disabled={page <= 1}
                  tabIndex={page <= 1 ? -1 : undefined}
                  className={page <= 1 ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>
              
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(
                (pageNum) => (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      href={`/admin/life-info?page=${pageNum}${
                        status ? `&status=${status}` : ''
                      }`}
                      isActive={pageNum === page}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                )
              )}
              
              <PaginationItem>
                <PaginationNext
                  href={`/admin/life-info?page=${
                    page < pagination.totalPages ? page + 1 : pagination.totalPages
                  }${status ? `&status=${status}` : ''}`}
                  aria-disabled={page >= pagination.totalPages}
                  tabIndex={page >= pagination.totalPages ? -1 : undefined}
                  className={
                    page >= pagination.totalPages
                      ? 'pointer-events-none opacity-50'
                      : ''
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </Suspense>
    </div>
  );
}
