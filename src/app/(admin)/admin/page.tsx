import { Metadata } from "next";
import Link from "next/link";
import Image from "next/image";
import { prisma } from "@/lib/prisma";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  LayoutDashboard,
  FileText,
  Users,
  MessageSquare,
  Briefcase,
  BookOpen,
  Tag,
  Settings,
} from "lucide-react";
import { formatDate } from "@/lib/utils";

export const metadata: Metadata = {
  title: "관리자 대시보드",
  description: "사이트 관리를 위한 관리자 대시보드입니다.",
};

export default async function AdminDashboardPage() {
  // 통계 데이터 조회
  const [
    userCount,
    postCount,
    commentCount,
    lifeInfoCount,
    serviceGuideCount,
    govInfoCount,
    jobPostCount,
    categoryCount,
  ] = await Promise.all([
    prisma.user.count(),
    prisma.post.count(),
    prisma.postComment.count(),
    prisma.lifeInfo.count(),
    prisma.serviceGuide.count(),
    prisma.govInfo.count(),
    prisma.jobPost.count(),
    prisma.category.count(),
  ]);

  // 최근 가입한 사용자 조회
  const recentUsers = await prisma.user.findMany({
    select: {
      id: true,
      name: true,
      email: true,
      displayName: true,
      image: true,
      createdAt: true,
    },
    orderBy: { createdAt: "desc" },
    take: 5,
  });

  // 최근 게시글 조회
  const recentPosts = await prisma.post.findMany({
    select: {
      id: true,
      content: true,
      createdAt: true,
      user: {
        select: {
          name: true,
          displayName: true,
        },
      },
    },
    orderBy: { createdAt: "desc" },
    take: 5,
  });

  // 대시보드 카드 데이터
  const dashboardCards = [
    {
      title: "사용자",
      value: userCount,
      description: "등록된 사용자 수",
      icon: <Users className="h-5 w-5" />,
      href: "/admin/users",
    },
    {
      title: "게시글",
      value: postCount,
      description: "등록된 게시글 수",
      icon: <FileText className="h-5 w-5" />,
      href: "/admin/posts",
    },
    {
      title: "댓글",
      value: commentCount,
      description: "등록된 댓글 수",
      icon: <MessageSquare className="h-5 w-5" />,
      href: "/admin/comments",
    },
    {
      title: "생활 정보",
      value: lifeInfoCount,
      description: "등록된 생활 정보 수",
      icon: <BookOpen className="h-5 w-5" />,
      href: "/admin/life-info",
    },
    {
      title: "서비스 가이드",
      value: serviceGuideCount,
      description: "등록된 서비스 가이드 수",
      icon: <FileText className="h-5 w-5" />,
      href: "/admin/service-guide",
    },
    {
      title: "정부 정보",
      value: govInfoCount,
      description: "등록된 정부 정보 수",
      icon: <FileText className="h-5 w-5" />,
      href: "/admin/gov-info",
    },
    {
      title: "구인구직",
      value: jobPostCount,
      description: "등록된 구인구직 정보 수",
      icon: <Briefcase className="h-5 w-5" />,
      href: "/admin/jobs",
    },
    {
      title: "카테고리",
      value: categoryCount,
      description: "등록된 카테고리 수",
      icon: <Tag className="h-5 w-5" />,
      href: "/admin/categories",
    },
  ];

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">관리자 대시보드</h1>
          <p className="text-muted-foreground">
            사이트 통계 및 관리 기능에 접근할 수 있습니다.
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/" target="_blank">
            사이트로 이동
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {dashboardCards.map((card) => (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              {card.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {card.description}
              </p>
              <Button variant="ghost" size="sm" className="mt-4 w-full" asChild>
                <Link href={card.href}>관리하기</Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>최근 가입한 사용자</CardTitle>
            <CardDescription>
              최근에 가입한 5명의 사용자 목록입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentUsers.length === 0 ? (
              <p className="text-muted-foreground">
                최근 가입한 사용자가 없습니다.
              </p>
            ) : (
              <div className="space-y-4">
                {recentUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                        {user.image ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={user.image}
                              alt={user.name || ""}
                              className="object-cover"
                              fill
                              sizes="40px"
                            />
                          </div>
                        ) : (
                          <Users className="h-5 w-5" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {user.displayName || user.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatDate(user.createdAt)}
                    </div>
                  </div>
                ))}
              </div>
            )}
            <Button variant="ghost" size="sm" className="mt-4 w-full" asChild>
              <Link href="/admin/users">모든 사용자 보기</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>최근 게시글</CardTitle>
            <CardDescription>
              최근에 작성된 5개의 게시글 목록입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentPosts.length === 0 ? (
              <p className="text-muted-foreground">
                최근 작성된 게시글이 없습니다.
              </p>
            ) : (
              <div className="space-y-4">
                {recentPosts.map((post) => (
                  <div
                    key={post.id}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <p className="font-medium line-clamp-1">
                        {post.content.substring(0, 50)}
                        {post.content.length > 50 ? "..." : ""}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        작성자: {post.user.displayName || post.user.name}
                      </p>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatDate(post.createdAt)}
                    </div>
                  </div>
                ))}
              </div>
            )}
            <Button variant="ghost" size="sm" className="mt-4 w-full" asChild>
              <Link href="/admin/posts">모든 게시글 보기</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
