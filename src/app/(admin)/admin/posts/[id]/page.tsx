import { Metadata } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getPostDetail } from '@/actions/admin/post';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDate } from '@/lib/utils';
import PostStatusForm from '@/components/admin/post/post-status-form';
import ContentRenderer from '@/components/content/content-renderer';

export const metadata: Metadata = {
  title: '게시글 상세 정보 | 관리자',
  description: '게시글의 상세 정보를 관리합니다.',
};

export default async function PostDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const postId = params.id;

  // 게시글 상세 정보 조회
  const result = await getPostDetail(postId);

  if (!result.success) {
    notFound();
  }

  const post = result.data;

  // 게시글 유형 표시
  const postTypeLabels: Record<string, string> = {
    general: '일반',
    question: '질문',
    review: '후기',
    news: '소식',
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">게시글 상세 정보</h1>
          <p className="text-muted-foreground">
            게시글의 상세 정보를 관리합니다.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/posts">
              목록으로 돌아가기
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>게시글 정보</CardTitle>
            <CardDescription>
              게시글의 기본 정보입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">작성자</p>
                <div className="flex items-center mt-1">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage src={post.user.image || ''} alt={post.user.name || ''} />
                    <AvatarFallback>
                      {post.user.name?.substring(0, 2) || 'UN'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{post.user.displayName || post.user.name}</p>
                    <p className="text-xs text-muted-foreground">{post.user.email}</p>
                  </div>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">카테고리</p>
                <p>{post.category?.name || '-'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">유형</p>
                <Badge variant="outline">
                  {postTypeLabels[post.type as keyof typeof postTypeLabels] || '일반'}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">작성일</p>
                <p>{formatDate(post.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">수정일</p>
                <p>{post.updatedAt ? formatDate(post.updatedAt) : '-'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">상태</p>
                <Badge variant={post.deletedAt ? 'destructive' : 'success'}>
                  {post.deletedAt ? '삭제됨' : '활성'}
                </Badge>
              </div>
              {post.deletedAt && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">삭제일</p>
                  <p>{formatDate(post.deletedAt)}</p>
                </div>
              )}
              <div>
                <p className="text-sm font-medium text-muted-foreground">조회수</p>
                <p>{post.viewCount}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">좋아요</p>
                <p>{post.likeCount}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">댓글</p>
                <p>{post.commentCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>게시글 관리</CardTitle>
            <CardDescription>
              게시글의 내용과 상태를 관리합니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content">
              <TabsList className="mb-4">
                <TabsTrigger value="content">내용</TabsTrigger>
                <TabsTrigger value="comments">댓글 ({post.postComments.length})</TabsTrigger>
                <TabsTrigger value="status">상태 관리</TabsTrigger>
              </TabsList>

              <TabsContent value="content">
                <Card>
                  <CardContent className="pt-6">
                    {post.contentHtml ? (
                      <ContentRenderer content={post.contentHtml} />
                    ) : (
                      <p className="whitespace-pre-wrap">{post.content}</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="comments">
                <div className="space-y-4">
                  {post.postComments.length === 0 ? (
                    <p className="text-muted-foreground">댓글이 없습니다.</p>
                  ) : (
                    post.postComments.map((comment: any) => (
                      <Card key={comment.id}>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Avatar className="h-6 w-6 mr-2">
                                <AvatarImage src={comment.user.image || ''} alt={comment.user.name || ''} />
                                <AvatarFallback>
                                  {comment.user.name?.substring(0, 2) || 'UN'}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="text-sm font-medium">{comment.user.displayName || comment.user.name}</p>
                              </div>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {formatDate(comment.createdAt)}
                            </p>
                          </div>
                        </CardHeader>
                        <CardContent className="py-2">
                          <p className="text-sm">{comment.content}</p>
                        </CardContent>
                        <CardFooter className="pt-0 pb-2">
                          <div className="flex items-center text-xs text-muted-foreground">
                            <span className="mr-4">좋아요 {comment.likeCount}</span>
                            <Badge variant={comment.deletedAt ? 'destructive' : 'outline'} className="text-xs">
                              {comment.deletedAt ? '삭제됨' : '활성'}
                            </Badge>
                          </div>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </div>
              </TabsContent>

              <TabsContent value="status">
                <PostStatusForm postId={post.id} isDeleted={!!post.deletedAt} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
