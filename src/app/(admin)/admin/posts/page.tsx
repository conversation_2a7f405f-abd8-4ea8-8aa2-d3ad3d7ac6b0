import { Metadata } from 'next';
import Link from 'next/link';
import { getPosts } from '@/actions/admin/post';
import { prisma } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatDate } from '@/lib/utils';

export const metadata: Metadata = {
  title: '게시글 관리 | 관리자',
  description: '커뮤니티 게시글을 관리합니다.',
};

interface AdminPostsPageProps {
  searchParams: {
    page?: string;
    categoryId?: string;
    type?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default async function AdminPostsPage({
  searchParams,
}: AdminPostsPageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const categoryId = searchParams.categoryId;
  const type = searchParams.type;
  const search = searchParams.search;
  const sortBy = (searchParams.sortBy || 'createdAt') as 'createdAt' | 'updatedAt' | 'viewCount' | 'likeCount' | 'commentCount';
  const sortOrder = (searchParams.sortOrder || 'desc') as 'asc' | 'desc';

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });

  // 게시글 목록 조회
  const result = await getPosts({
    page,
    limit: 10,
    categoryId,
    type,
    search,
    sortBy,
    sortOrder,
  });

  const posts = result.success ? result.data.data : [];
  const pagination = result.success ? result.data.pagination : { totalPages: 1 };

  // 게시글 유형 목록
  const postTypes = [
    { value: 'general', label: '일반' },
    { value: 'question', label: '질문' },
    { value: 'review', label: '후기' },
    { value: 'news', label: '소식' },
  ];

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">게시글 관리</h1>
          <p className="text-muted-foreground">
            커뮤니티 게시글을 관리합니다.
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>게시글 목록</CardTitle>
          <CardDescription>
            커뮤니티에 등록된 게시글 목록입니다. 카테고리, 유형별로 필터링할 수 있습니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* 검색 및 필터 */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <form action="/admin/posts" method="get">
                <Input
                  name="search"
                  placeholder="게시글 내용, 작성자 검색..."
                  defaultValue={search || ''}
                  className="w-full"
                />
                <input type="hidden" name="categoryId" value={categoryId || ''} />
                <input type="hidden" name="type" value={type || ''} />
                <input type="hidden" name="sortBy" value={sortBy || 'createdAt'} />
                <input type="hidden" name="sortOrder" value={sortOrder || 'desc'} />
                <Button type="submit" className="sr-only">검색</Button>
              </form>
            </div>
            <div className="flex gap-2">
              <Select
                defaultValue={categoryId || ''}
                onValueChange={(value) => {
                  const url = new URL(window.location.href);
                  if (value) {
                    url.searchParams.set('categoryId', value);
                  } else {
                    url.searchParams.delete('categoryId');
                  }
                  window.location.href = url.toString();
                }}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="카테고리" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">모든 카테고리</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                defaultValue={type || ''}
                onValueChange={(value) => {
                  const url = new URL(window.location.href);
                  if (value) {
                    url.searchParams.set('type', value);
                  } else {
                    url.searchParams.delete('type');
                  }
                  window.location.href = url.toString();
                }}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="유형" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">모든 유형</SelectItem>
                  {postTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                defaultValue={`${sortBy}-${sortOrder}`}
                onValueChange={(value) => {
                  const [newSortBy, newSortOrder] = value.split('-');
                  const url = new URL(window.location.href);
                  url.searchParams.set('sortBy', newSortBy);
                  url.searchParams.set('sortOrder', newSortOrder);
                  window.location.href = url.toString();
                }}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="정렬" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt-desc">최신순</SelectItem>
                  <SelectItem value="createdAt-asc">오래된순</SelectItem>
                  <SelectItem value="viewCount-desc">조회수 높은순</SelectItem>
                  <SelectItem value="likeCount-desc">좋아요 많은순</SelectItem>
                  <SelectItem value="commentCount-desc">댓글 많은순</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {posts.length === 0 ? (
            <div className="py-10 text-center">
              <p className="text-muted-foreground">등록된 게시글이 없습니다.</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>내용</TableHead>
                    <TableHead>카테고리</TableHead>
                    <TableHead>유형</TableHead>
                    <TableHead>작성자</TableHead>
                    <TableHead>작성일</TableHead>
                    <TableHead>조회수</TableHead>
                    <TableHead>좋아요</TableHead>
                    <TableHead>댓글</TableHead>
                    <TableHead>상태</TableHead>
                    <TableHead className="text-right">작업</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {posts.map((post: any) => (
                    <TableRow key={post.id}>
                      <TableCell className="font-medium max-w-[200px] truncate">
                        {post.content}
                      </TableCell>
                      <TableCell>{post.category?.name || '-'}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {post.type === 'general' && '일반'}
                          {post.type === 'question' && '질문'}
                          {post.type === 'review' && '후기'}
                          {post.type === 'news' && '소식'}
                          {!post.type && '일반'}
                        </Badge>
                      </TableCell>
                      <TableCell>{post.user.displayName || post.user.name}</TableCell>
                      <TableCell>{formatDate(post.createdAt)}</TableCell>
                      <TableCell>{post.viewCount}</TableCell>
                      <TableCell>{post.likeCount}</TableCell>
                      <TableCell>{post.commentCount}</TableCell>
                      <TableCell>
                        <Badge variant={post.deletedAt ? 'destructive' : 'success'}>
                          {post.deletedAt ? '삭제됨' : '활성'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/posts/${post.id}`}>
                            상세
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* 페이지네이션 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((p) => (
                  <Button
                    key={p}
                    variant={p === page ? 'default' : 'outline'}
                    size="sm"
                    asChild
                  >
                    <Link
                      href={`/admin/posts?page=${p}${
                        categoryId ? `&categoryId=${categoryId}` : ''
                      }${type ? `&type=${type}` : ''}${
                        search ? `&search=${search}` : ''
                      }${sortBy ? `&sortBy=${sortBy}` : ''}${
                        sortOrder ? `&sortOrder=${sortOrder}` : ''
                      }`}
                    >
                      {p}
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
