import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Pencil, Trash, Eye } from 'lucide-react';
import ContentRenderer from '@/components/editor/ContentRenderer';
import StepManager from '@/components/serviceguide/StepManager';
import DocumentManager from '@/components/serviceguide/DocumentManager';
import FaqManager from '@/components/serviceguide/FaqManager';
import { formatDate } from '@/lib/utils';

export const metadata: Metadata = {
  title: '서비스 가이드 상세 | 관리자',
  description: '서비스 이용 가이드 상세 정보 및 관리 페이지입니다.',
};

interface AdminServiceGuideDetailPageProps {
  params: {
    id: string;
  };
}

export default async function AdminServiceGuideDetailPage({
  params,
}: AdminServiceGuideDetailPageProps) {
  const { id } = params;

  // 서비스 가이드 조회
  const serviceGuide = await prisma.serviceGuide.findUnique({
    where: {
      id,
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          displayName: true,
          image: true,
        },
      },
      category: true,
      steps: {
        orderBy: {
          order: 'asc',
        },
        include: {
          images: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      },
      documents: {
        orderBy: {
          order: 'asc',
        },
      },
      faqs: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!serviceGuide) {
    notFound();
  }

  // 상태별 배지 색상
  const statusColors: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'secondary',
  };

  // 상태 텍스트
  const statusText = {
    draft: '초안',
    published: '발행됨',
    archived: '보관됨',
  };

  // 현재 사용자 정보
  const session = await auth();
  const currentUser = session?.user || null;

  return (
    <div className="container py-8">
      <div className="flex justify-between items-start mb-6">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Badge variant={statusColors[serviceGuide.status] as any}>
              {statusText[serviceGuide.status as keyof typeof statusText]}
            </Badge>
            {serviceGuide.featured && (
              <Badge variant="outline" className="bg-yellow-100 dark:bg-yellow-900">추천</Badge>
            )}
            <Badge variant="outline">{serviceGuide.category.name}</Badge>
          </div>
          <h1 className="text-2xl font-bold">{serviceGuide.title}</h1>
          <div className="text-sm text-muted-foreground mt-1">
            <span>작성자: {serviceGuide.author.displayName || serviceGuide.author.name}</span>
            <span className="mx-2">•</span>
            <span>작성일: {formatDate(serviceGuide.createdAt)}</span>
            <span className="mx-2">•</span>
            <span>조회수: {serviceGuide.viewCount}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/service-guide/${serviceGuide.slug}`} target="_blank">
              <Eye className="mr-2 h-4 w-4" />
              미리보기
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/admin/service-guide/edit/${serviceGuide.id}`}>
              <Pencil className="mr-2 h-4 w-4" />
              수정
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">개요</TabsTrigger>
          <TabsTrigger value="steps">단계 관리</TabsTrigger>
          <TabsTrigger value="documents">서류 관리</TabsTrigger>
          <TabsTrigger value="faqs">FAQ 관리</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>서비스 가이드 정보</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">슬러그</h3>
                  <p className="text-sm text-muted-foreground">{serviceGuide.slug}</p>
                </div>
                
                {serviceGuide.summary && (
                  <div>
                    <h3 className="text-sm font-medium">요약</h3>
                    <p className="text-sm text-muted-foreground">{serviceGuide.summary}</p>
                  </div>
                )}
                
                {serviceGuide.officialLink && (
                  <div>
                    <h3 className="text-sm font-medium">공식 링크</h3>
                    <a 
                      href={serviceGuide.officialLink} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline"
                    >
                      {serviceGuide.officialLink}
                    </a>
                  </div>
                )}
                
                <Separator />
                
                <div>
                  <h3 className="text-sm font-medium mb-2">내용</h3>
                  <ContentRenderer content={serviceGuide.contentHtml} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="steps">
          <StepManager
            serviceGuideId={serviceGuide.id}
            steps={serviceGuide.steps}
          />
        </TabsContent>

        <TabsContent value="documents">
          <DocumentManager
            serviceGuideId={serviceGuide.id}
            documents={serviceGuide.documents}
          />
        </TabsContent>

        <TabsContent value="faqs">
          <FaqManager
            serviceGuideId={serviceGuide.id}
            faqs={serviceGuide.faqs}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
