import { Metadata } from 'next';
import { prisma } from '@/lib/prisma';
import ServiceGuideForm from '@/components/serviceguide/ServiceGuideForm';

export const metadata: Metadata = {
  title: '서비스 가이드 생성 | 관리자',
  description: '새로운 서비스 이용 가이드를 생성합니다.',
};

export default async function CreateServiceGuidePage() {
  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });

  return (
    <div className="container py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">서비스 가이드 생성</h1>
        <p className="text-muted-foreground">
          새로운 서비스 이용 가이드를 생성합니다.
        </p>
      </div>

      <ServiceGuideForm categories={categories} />
    </div>
  );
}
