import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import ServiceGuideForm from '@/components/serviceguide/ServiceGuideForm';

export const metadata: Metadata = {
  title: '서비스 가이드 수정 | 관리자',
  description: '서비스 이용 가이드를 수정합니다.',
};

interface EditServiceGuidePageProps {
  params: {
    id: string;
  };
}

export default async function EditServiceGuidePage({
  params,
}: EditServiceGuidePageProps) {
  const { id } = params;

  // 서비스 가이드 조회
  const serviceGuide = await prisma.serviceGuide.findUnique({
    where: {
      id,
    },
  });

  if (!serviceGuide) {
    notFound();
  }

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });

  return (
    <div className="container py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">서비스 가이드 수정</h1>
        <p className="text-muted-foreground">
          서비스 이용 가이드를 수정합니다.
        </p>
      </div>

      <ServiceGuideForm
        categories={categories}
        initialData={serviceGuide}
        isEdit={true}
      />
    </div>
  );
}
