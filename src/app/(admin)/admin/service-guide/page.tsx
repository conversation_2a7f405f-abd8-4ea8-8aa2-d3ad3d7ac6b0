import { Metadata } from 'next';
import Link from 'next/link';
import { getServiceGuides } from '@/actions/serviceguide';
import { prisma } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Plus } from 'lucide-react';
import { formatDate } from '@/lib/utils';

export const metadata: Metadata = {
  title: '서비스 가이드 관리 | 관리자',
  description: '서비스 이용 가이드 관리 페이지입니다.',
};

interface AdminServiceGuidePageProps {
  searchParams: {
    page?: string;
    status?: string;
  };
}

export default async function AdminServiceGuidePage({
  searchParams,
}: AdminServiceGuidePageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const status = searchParams.status as 'draft' | 'published' | 'archived' | undefined;

  // 서비스 가이드 목록 조회
  const result = await getServiceGuides({
    page,
    limit: 10,
    status,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const serviceGuides = result.success ? result.data.data : [];
  const pagination = result.success
    ? result.data.pagination
    : { totalPages: 1 };

  // 상태별 배지 색상
  const statusColors: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'secondary',
  };

  // 상태 텍스트
  const statusText = {
    draft: '초안',
    published: '발행됨',
    archived: '보관됨',
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">서비스 가이드 관리</h1>
          <p className="text-muted-foreground">
            서비스 이용 가이드를 생성하고 관리합니다.
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/service-guide/create">
            <Plus className="mr-2 h-4 w-4" /> 가이드 생성
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>서비스 가이드 목록</CardTitle>
          <CardDescription>
            모든 서비스 가이드 목록입니다. 상태별로 필터링할 수 있습니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex gap-2">
            <Button
              variant={!status ? 'default' : 'outline'}
              size="sm"
              asChild
            >
              <Link href="/admin/service-guide">전체</Link>
            </Button>
            <Button
              variant={status === 'draft' ? 'default' : 'outline'}
              size="sm"
              asChild
            >
              <Link href="/admin/service-guide?status=draft">초안</Link>
            </Button>
            <Button
              variant={status === 'published' ? 'default' : 'outline'}
              size="sm"
              asChild
            >
              <Link href="/admin/service-guide?status=published">발행됨</Link>
            </Button>
            <Button
              variant={status === 'archived' ? 'default' : 'outline'}
              size="sm"
              asChild
            >
              <Link href="/admin/service-guide?status=archived">보관됨</Link>
            </Button>
          </div>

          {serviceGuides.length === 0 ? (
            <div className="py-10 text-center">
              <p className="text-muted-foreground">등록된 서비스 가이드가 없습니다.</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>제목</TableHead>
                    <TableHead>카테고리</TableHead>
                    <TableHead>상태</TableHead>
                    <TableHead>작성자</TableHead>
                    <TableHead>작성일</TableHead>
                    <TableHead>조회수</TableHead>
                    <TableHead className="text-right">작업</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {serviceGuides.map((guide: any) => (
                    <TableRow key={guide.id}>
                      <TableCell className="font-medium">
                        <Link
                          href={`/admin/service-guide/${guide.id}`}
                          className="hover:underline"
                        >
                          {guide.title}
                        </Link>
                        {guide.featured && (
                          <Badge variant="outline" className="ml-2 bg-yellow-100 dark:bg-yellow-900">
                            추천
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{guide.category.name}</TableCell>
                      <TableCell>
                        <Badge variant={statusColors[guide.status] as any}>
                          {statusText[guide.status as keyof typeof statusText]}
                        </Badge>
                      </TableCell>
                      <TableCell>{guide.author.displayName || guide.author.name}</TableCell>
                      <TableCell>{formatDate(guide.createdAt)}</TableCell>
                      <TableCell>{guide.viewCount}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/service-guide/${guide.id}`}>
                            상세
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/service-guide/edit/${guide.id}`}>
                            수정
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* 페이지네이션 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((p) => (
                  <Button
                    key={p}
                    variant={p === page ? 'default' : 'outline'}
                    size="sm"
                    asChild
                  >
                    <Link
                      href={`/admin/service-guide?page=${p}${
                        status ? `&status=${status}` : ''
                      }`}
                    >
                      {p}
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
