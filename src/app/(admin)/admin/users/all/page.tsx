import { Metadata } from 'next';
import Link from 'next/link';
import { getUsers } from '@/actions/admin/user';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatDate } from '@/lib/utils';
import { UserRole } from '@prisma/client';

export const metadata: Metadata = {
  title: '전체 사용자 관리 | 관리자',
  description: '모든 사용자 계정을 관리합니다.',
};

interface AllUsersPageProps {
  searchParams: {
    page?: string;
    role?: string;
    status?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

// 역할별 배지 색상
const roleBadgeColors: Record<string, string> = {
  USER: 'default',
  MODERATOR: 'secondary',
  ADMIN: 'primary',
  SUPER_ADMIN: 'destructive',
};

// 역할 한글 표시
const roleLabels: Record<string, string> = {
  USER: '일반 사용자',
  MODERATOR: '중재자',
  ADMIN: '관리자',
  SUPER_ADMIN: '최고 관리자',
};

// 상태별 배지 색상
const statusBadgeColors: Record<string, string> = {
  active: 'success',
  inactive: 'destructive',
};

// 상태 한글 표시
const statusLabels: Record<string, string> = {
  active: '활성',
  inactive: '비활성',
};

export default async function AllUsersPage({
  searchParams,
}: AllUsersPageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const role = searchParams.role as UserRole | undefined;
  const status = searchParams.status as 'active' | 'inactive' | 'all' || 'active';
  const search = searchParams.search;
  const sortBy = (searchParams.sortBy || 'createdAt') as 'createdAt' | 'name' | 'email' | 'lastLoginAt';
  const sortOrder = (searchParams.sortOrder || 'desc') as 'asc' | 'desc';

  // 사용자 목록 조회
  const result = await getUsers({
    page,
    limit: 10,
    role,
    status,
    search,
    sortBy,
    sortOrder,
  });

  const users = result.success ? result.data.data : [];
  const pagination = result.success ? result.data.pagination : { totalPages: 1 };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">전체 사용자 관리</h1>
          <p className="text-muted-foreground">
            모든 사용자 계정을 관리합니다.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/users">
              관리자 목록
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>사용자 목록</CardTitle>
          <CardDescription>
            시스템의 모든 사용자 계정 목록입니다. 역할, 상태별로 필터링할 수 있습니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* 검색 및 필터 */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <form action="/admin/users/all" method="get">
                <Input
                  name="search"
                  placeholder="이름, 이메일 검색..."
                  defaultValue={search || ''}
                  className="w-full"
                />
                <input type="hidden" name="role" value={role || ''} />
                <input type="hidden" name="status" value={status || 'active'} />
                <input type="hidden" name="sortBy" value={sortBy || 'createdAt'} />
                <input type="hidden" name="sortOrder" value={sortOrder || 'desc'} />
                <Button type="submit" className="sr-only">검색</Button>
              </form>
            </div>
            <div className="flex gap-2">
              <Select
                defaultValue={status}
                onValueChange={(value) => {
                  const url = new URL(window.location.href);
                  url.searchParams.set('status', value);
                  window.location.href = url.toString();
                }}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="상태" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">모든 상태</SelectItem>
                  <SelectItem value="active">활성</SelectItem>
                  <SelectItem value="inactive">비활성</SelectItem>
                </SelectContent>
              </Select>
              <Select
                defaultValue={role || ''}
                onValueChange={(value) => {
                  const url = new URL(window.location.href);
                  if (value) {
                    url.searchParams.set('role', value);
                  } else {
                    url.searchParams.delete('role');
                  }
                  window.location.href = url.toString();
                }}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="역할" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">모든 역할</SelectItem>
                  <SelectItem value="USER">일반 사용자</SelectItem>
                  <SelectItem value="MODERATOR">중재자</SelectItem>
                  <SelectItem value="ADMIN">관리자</SelectItem>
                  <SelectItem value="SUPER_ADMIN">최고 관리자</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {users.length === 0 ? (
            <div className="py-10 text-center">
              <p className="text-muted-foreground">등록된 사용자가 없습니다.</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>이름</TableHead>
                    <TableHead>이메일</TableHead>
                    <TableHead>역할</TableHead>
                    <TableHead>상태</TableHead>
                    <TableHead>게시글</TableHead>
                    <TableHead>댓글</TableHead>
                    <TableHead>가입일</TableHead>
                    <TableHead className="text-right">작업</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user: any) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        {user.displayName || user.name}
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge variant={roleBadgeColors[user.role] as any}>
                          {roleLabels[user.role]}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.isActive ? 'success' : 'destructive'}>
                          {user.isActive ? '활성' : '비활성'}
                        </Badge>
                      </TableCell>
                      <TableCell>{user._count.posts}</TableCell>
                      <TableCell>{user._count.postComments}</TableCell>
                      <TableCell>{formatDate(user.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/users/detail/${user.id}`}>
                            상세
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/users/${user.id}/permissions`}>
                            권한
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* 페이지네이션 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((p) => (
                  <Button
                    key={p}
                    variant={p === page ? 'default' : 'outline'}
                    size="sm"
                    asChild
                  >
                    <Link
                      href={`/admin/users/all?page=${p}${
                        role ? `&role=${role}` : ''
                      }${status ? `&status=${status}` : ''}${
                        search ? `&search=${search}` : ''
                      }${sortBy ? `&sortBy=${sortBy}` : ''}${
                        sortOrder ? `&sortOrder=${sortOrder}` : ''
                      }`}
                    >
                      {p}
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
