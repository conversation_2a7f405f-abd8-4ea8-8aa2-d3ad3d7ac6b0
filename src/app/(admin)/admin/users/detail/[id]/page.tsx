import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getUserDetail } from '@/actions/admin/user';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDate } from '@/lib/utils';
import UserStatusForm from '@/components/admin/user/user-status-form';
import UserRoleForm from '@/components/admin/user/user-role-form';

export const metadata: Metadata = {
  title: '사용자 상세 정보 | 관리자',
  description: '사용자의 상세 정보를 관리합니다.',
};

// 역할별 배지 색상
const roleBadgeColors: Record<string, string> = {
  USER: 'default',
  MODERATOR: 'secondary',
  ADMIN: 'primary',
  SUPER_ADMIN: 'destructive',
};

// 역할 한글 표시
const roleLabels: Record<string, string> = {
  USER: '일반 사용자',
  MODERATOR: '중재자',
  ADMIN: '관리자',
  SUPER_ADMIN: '최고 관리자',
};

export default async function UserDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const userId = params.id;

  // 사용자 상세 정보 조회
  const result = await getUserDetail(userId);

  if (!result.success) {
    notFound();
  }

  const user = result.data;

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">사용자 상세 정보</h1>
          <p className="text-muted-foreground">
            사용자의 상세 정보를 관리합니다.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/users/all">
              사용자 목록으로 돌아가기
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/admin/users/${userId}/permissions`}>
              권한 관리
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>사용자 프로필</CardTitle>
            <CardDescription>
              사용자의 기본 정보입니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center mb-6">
              <Avatar className="w-24 h-24 mb-4">
                <AvatarImage src={user.image || ''} alt={user.name || ''} />
                <AvatarFallback className="text-lg">
                  {user.name?.substring(0, 2) || 'UN'}
                </AvatarFallback>
              </Avatar>
              <h3 className="text-xl font-bold">{user.displayName || user.name}</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <div className="flex gap-2 mt-2">
                <Badge variant={roleBadgeColors[user.role] as any}>
                  {roleLabels[user.role]}
                </Badge>
                <Badge variant={user.isActive ? 'success' : 'destructive'}>
                  {user.isActive ? '활성' : '비활성'}
                </Badge>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">가입일</p>
                <p>{formatDate(user.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">마지막 로그인</p>
                <p>{user.lastLoginAt ? formatDate(user.lastLoginAt) : '로그인 기록 없음'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">이메일 인증</p>
                <p>{user.emailVerified ? formatDate(user.emailVerified) : '인증되지 않음'}</p>
              </div>
              {user.bio && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">자기소개</p>
                  <p className="text-sm">{user.bio}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>사용자 관리</CardTitle>
            <CardDescription>
              사용자의 상태와 역할을 관리합니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="activity">
              <TabsList className="mb-4">
                <TabsTrigger value="activity">활동 정보</TabsTrigger>
                <TabsTrigger value="status">상태 관리</TabsTrigger>
                <TabsTrigger value="role">역할 관리</TabsTrigger>
              </TabsList>

              <TabsContent value="activity">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">게시글</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{user._count.posts}</div>
                      <p className="text-xs text-muted-foreground mt-1">작성한 게시글 수</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">댓글</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{user._count.postComments}</div>
                      <p className="text-xs text-muted-foreground mt-1">작성한 댓글 수</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">북마크</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{user._count.bookmarks}</div>
                      <p className="text-xs text-muted-foreground mt-1">저장한 북마크 수</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">권한</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{user._count.permissions}</div>
                      <p className="text-xs text-muted-foreground mt-1">부여된 권한 수</p>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4">활동 통계</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium">게시글 수</p>
                      <p className="text-2xl font-bold">{user.postCount}</p>
                    </div>
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium">댓글 수</p>
                      <p className="text-2xl font-bold">{user.postCommentCount}</p>
                    </div>
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium">좋아요 수</p>
                      <p className="text-2xl font-bold">{user.likeCount}</p>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="status">
                <UserStatusForm userId={userId} isActive={user.isActive} />
              </TabsContent>

              <TabsContent value="role">
                <UserRoleForm userId={userId} currentRole={user.role} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
