import { Metada<PERSON> } from "next";
import Link from "next/link";
import { getAdmins } from "@/actions/admin/user";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";
import { UserRole } from "@prisma/client";

export const metadata: Metadata = {
  title: "사용자 관리 | 관리자",
  description: "사용자 계정 및 권한을 관리합니다.",
};

interface AdminUsersPageProps {
  searchParams: {
    page?: string;
    role?: string;
    search?: string;
  };
}

// 역할별 배지 색상
const roleBadgeColors: Record<string, string> = {
  USER: "default",
  MODERATOR: "secondary",
  ADMIN: "primary",
  SUPER_ADMIN: "destructive",
};

// 역할 한글 표시
const roleLabels: Record<string, string> = {
  USER: "일반 사용자",
  MODERATOR: "중재자",
  ADMIN: "관리자",
  SUPER_ADMIN: "최고 관리자",
};

export default async function AdminUsersPage({
  searchParams,
}: AdminUsersPageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const role = searchParams.role as UserRole | undefined;
  const search = searchParams.search;

  // 관리자 목록 조회
  const result = await getAdmins({
    page,
    limit: 10,
    role,
    search,
  });

  const admins = result.success ? result.data.data : [];
  const pagination = result.success
    ? result.data.pagination
    : { totalPages: 1 };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">사용자 관리</h1>
          <p className="text-muted-foreground">
            사용자 계정 및 권한을 관리합니다.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/users/all">전체 사용자 목록</Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>관리자 목록</CardTitle>
          <CardDescription>
            시스템의 모든 관리자 계정 목록입니다. 역할별로 필터링할 수 있습니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex gap-2">
            <Button variant={!role ? "default" : "outline"} size="sm" asChild>
              <Link href="/admin/users">전체</Link>
            </Button>
            <Button
              variant={role === "MODERATOR" ? "default" : "outline"}
              size="sm"
              asChild
            >
              <Link href="/admin/users?role=MODERATOR">중재자</Link>
            </Button>
            <Button
              variant={role === "ADMIN" ? "default" : "outline"}
              size="sm"
              asChild
            >
              <Link href="/admin/users?role=ADMIN">관리자</Link>
            </Button>
            <Button
              variant={role === "SUPER_ADMIN" ? "default" : "outline"}
              size="sm"
              asChild
            >
              <Link href="/admin/users?role=SUPER_ADMIN">최고 관리자</Link>
            </Button>
          </div>

          {admins.length === 0 ? (
            <div className="py-10 text-center">
              <p className="text-muted-foreground">등록된 관리자가 없습니다.</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>이름</TableHead>
                    <TableHead>이메일</TableHead>
                    <TableHead>역할</TableHead>
                    <TableHead>권한 수</TableHead>
                    <TableHead>가입일</TableHead>
                    <TableHead>마지막 로그인</TableHead>
                    <TableHead className="text-right">작업</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {admins.map((admin: any) => (
                    <TableRow key={admin.id}>
                      <TableCell className="font-medium">
                        {admin.displayName || admin.name}
                      </TableCell>
                      <TableCell>{admin.email}</TableCell>
                      <TableCell>
                        <Badge variant={roleBadgeColors[admin.role] as any}>
                          {roleLabels[admin.role]}
                        </Badge>
                      </TableCell>
                      <TableCell>{admin._count.permissions}</TableCell>
                      <TableCell>{formatDate(admin.createdAt)}</TableCell>
                      <TableCell>
                        {admin.lastLoginAt
                          ? formatDate(admin.lastLoginAt)
                          : "-"}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/users/${admin.id}`}>상세</Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/users/${admin.id}/permissions`}>
                            권한
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* 페이지네이션 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                {Array.from(
                  { length: pagination.totalPages },
                  (_, i) => i + 1
                ).map((p) => (
                  <Button
                    key={p}
                    variant={p === page ? "default" : "outline"}
                    size="sm"
                    asChild
                  >
                    <Link
                      href={`/admin/users?page=${p}${
                        role ? `&role=${role}` : ""
                      }${search ? `&search=${search}` : ""}`}
                    >
                      {p}
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
