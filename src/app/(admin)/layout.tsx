"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import {
  LayoutDashboard,
  FileText,
  BookOpen,
  Users,
  Settings,
  MessageSquare,
  Briefcase,
  Tag,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  LogOut,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ThemeToggle } from "@/components/theme-toggle";
import { Separator } from "@/components/ui/separator";

type SidebarItem = {
  href: string;
  label: string;
  icon: React.ReactNode;
  children?: SidebarItem[];
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // 로딩 중이 아니고 인증되지 않았거나 관리자 권한이 없는 경우 리디렉션
  useEffect(() => {
    if (status !== "loading") {
      if (!session) {
        redirect("/login");
      } else if (
        !session.user.isAdmin &&
        session.user.role !== "ADMIN" &&
        session.user.role !== "SUPER_ADMIN" &&
        session.user.role !== "MODERATOR"
      ) {
        redirect("/");
      }
    }
  }, [session, status]);

  // 사이드바 접기/펼치기
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // 모바일에서 사이드바 열기/닫기
  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  // 관리자 메뉴 아이템 (역할별 접근 권한 포함)
  const adminMenuItems: (SidebarItem & { minRole?: string })[] = [
    {
      href: "/admin",
      label: "대시보드",
      icon: <LayoutDashboard className="h-5 w-5" />,
      minRole: "MODERATOR", // 중재자 이상
    },
    {
      href: "/admin/posts",
      label: "게시글 관리",
      icon: <FileText className="h-5 w-5" />,
      minRole: "MODERATOR", // 중재자 이상
    },
    {
      href: "/admin/life-info",
      label: "생활 정보 관리",
      icon: <BookOpen className="h-5 w-5" />,
      minRole: "ADMIN", // 관리자 이상
    },
    {
      href: "/admin/service-guide",
      label: "서비스 가이드 관리",
      icon: <FileText className="h-5 w-5" />,
      minRole: "ADMIN", // 관리자 이상
    },
    {
      href: "/admin/gov-info",
      label: "정부 정보 관리",
      icon: <FileText className="h-5 w-5" />,
      minRole: "ADMIN", // 관리자 이상
    },
    {
      href: "/admin/users",
      label: "사용자 관리",
      icon: <Users className="h-5 w-5" />,
      minRole: "ADMIN", // 관리자 이상
    },
    {
      href: "/admin/comments",
      label: "댓글 관리",
      icon: <MessageSquare className="h-5 w-5" />,
      minRole: "MODERATOR", // 중재자 이상
    },
    {
      href: "/admin/jobs",
      label: "구인구직 관리",
      icon: <Briefcase className="h-5 w-5" />,
      minRole: "MODERATOR", // 중재자 이상
    },
    {
      href: "/admin/categories",
      label: "카테고리 관리",
      icon: <Tag className="h-5 w-5" />,
    },
    {
      href: "/admin/settings",
      label: "설정",
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  // 로딩 중인 경우 로딩 표시
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 사용자 역할 레벨 확인 함수
  const hasRequiredRole = (minRole?: string): boolean => {
    if (!minRole || !session?.user?.role) return true;

    const roleLevels: Record<string, number> = {
      USER: 1,
      MODERATOR: 2,
      ADMIN: 3,
      SUPER_ADMIN: 4,
    };

    const userRoleLevel = roleLevels[session.user.role] || 1;
    const requiredRoleLevel = roleLevels[minRole] || 1;

    return userRoleLevel >= requiredRoleLevel;
  };

  // 메뉴 아이템 렌더링 함수
  const renderMenuItem = (item: SidebarItem & { minRole?: string }) => {
    // 필요한 역할 레벨이 없는 경우 메뉴 아이템을 표시하지 않음
    if (item.minRole && !hasRequiredRole(item.minRole)) {
      return null;
    }

    const isActive =
      pathname === item.href || pathname.startsWith(`${item.href}/`);

    return (
      <Link
        key={item.href}
        href={item.href}
        className={`flex items-center py-2 px-3 rounded-md text-sm ${
          isActive
            ? "bg-primary/10 text-primary font-medium"
            : "text-foreground/80 hover:bg-accent hover:text-accent-foreground"
        }`}
      >
        <span className="mr-3">{item.icon}</span>
        {!isCollapsed && <span>{item.label}</span>}
      </Link>
    );
  };

  // 모바일 토글 버튼
  const MobileToggle = () => (
    <Button
      variant="ghost"
      size="icon"
      className="md:hidden fixed top-4 left-4 z-50"
      onClick={toggleMobileSidebar}
      aria-label={isMobileOpen ? "Close sidebar" : "Open sidebar"}
    >
      {isMobileOpen ? <X size={20} /> : <Menu size={20} />}
    </Button>
  );

  // 사이드바 컨텐츠
  const SidebarContent = () => (
    <div className="h-full flex flex-col">
      {/* 사이드바 헤더 */}
      <div className="p-4 flex items-center justify-between border-b">
        <div className="flex items-center">
          {!isCollapsed && (
            <Link href="/admin" className="text-xl font-bold">
              관리자
            </Link>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="hidden md:flex"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </Button>
      </div>

      {/* 사용자 정보 */}
      {!isCollapsed && (
        <div className="p-4 border-b">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarImage
                src={session?.user?.image || ""}
                alt={session?.user?.name || "관리자"}
              />
              <AvatarFallback>
                {session?.user?.name?.substring(0, 2) || "AD"}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">
                {session?.user?.name || "관리자"}
              </p>
              <p className="text-xs text-muted-foreground">
                {session?.user?.role === "SUPER_ADMIN" && "최고 관리자"}
                {session?.user?.role === "ADMIN" && "관리자"}
                {session?.user?.role === "MODERATOR" && "중재자"}
                {!session?.user?.role && "관리자"}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 사이드바 메뉴 */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="space-y-1 px-2">
          {adminMenuItems.map(renderMenuItem)}
        </nav>
      </div>

      {/* 사이드바 푸터 */}
      <div className="p-4 border-t">
        <div className="flex items-center justify-between">
          <ThemeToggle />
          {!isCollapsed && (
            <Button variant="ghost" size="sm" asChild>
              <Link href="/" target="_blank">
                사이트로 이동
              </Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex min-h-screen bg-background">
      <MobileToggle />

      {/* 데스크탑 사이드바 */}
      <aside
        className={`hidden md:block h-screen bg-background border-r transition-all duration-300 ${
          isCollapsed ? "w-16" : "w-64"
        } fixed top-0 left-0 z-40`}
      >
        <SidebarContent />
      </aside>

      {/* 모바일 사이드바 오버레이 */}
      {isMobileOpen && (
        <div
          className="md:hidden fixed inset-0 bg-background/80 backdrop-blur-sm z-40"
          onClick={toggleMobileSidebar}
        />
      )}

      {/* 모바일 사이드바 */}
      <aside
        className={`md:hidden fixed top-0 left-0 h-screen bg-background border-r w-64 z-50 transform transition-transform duration-300 ${
          isMobileOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <SidebarContent />
      </aside>

      {/* 메인 콘텐츠 */}
      <main
        id="content"
        className={`flex-1 ${isCollapsed ? "md:ml-16" : "md:ml-64"}`}
      >
        {children}
      </main>
    </div>
  );
}
