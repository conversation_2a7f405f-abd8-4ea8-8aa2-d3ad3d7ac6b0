import SignIn from "@/components/sign-in";
import Image from "next/image";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { ThemeToggle } from "@/components/theme-toggle";
import LanguageSwitcher from "@/components/language-switcher";

export default function LoginPage() {
  const t = useTranslations("Auth");
  
  return (
    <div className="flex flex-col items-center justify-center w-full max-w-md p-8 space-y-8">
      <div className="flex w-full justify-between items-center mb-4">
        <Link href="/" className="text-2xl font-bold">
          Sodamm
        </Link>
        <div className="flex items-center space-x-2">
          <ThemeToggle />
          <LanguageSwitcher />
        </div>
      </div>
      
      <div className="w-full text-center space-y-2">
        <h1 className="text-2xl font-bold">{t("login")}</h1>
        <p className="text-sm text-muted-foreground">
          {t("noAccount")} <Link href="/signup" className="text-primary hover:underline">{t("createAccount")}</Link>
        </p>
      </div>
      
      <div className="w-full">
        <SignIn />
      </div>
    </div>
  );
}
