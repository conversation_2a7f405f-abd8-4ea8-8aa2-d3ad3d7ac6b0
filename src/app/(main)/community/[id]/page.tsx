import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getPost, incrementViewCount } from '@/actions/post';
import PostDetail from '@/components/post/PostDetail';
import PostComments from '@/components/post/PostComments';
import { Separator } from '@/components/ui/separator';

interface PostPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: PostPageProps): Promise<Metadata> {
  const result = await getPost({ id: params.id });
  
  if (!result.success) {
    return {
      title: '게시글을 찾을 수 없습니다 | SODAMM',
    };
  }
  
  const post = result.data;
  
  // 게시글 내용에서 HTML 태그 제거하여 설명 생성
  const description = post.content
    .replace(/<[^>]*>/g, '')
    .slice(0, 150)
    .trim();
  
  return {
    title: `${post.type === 'QUESTION' ? '[질문] ' : post.type === 'NOTICE' ? '[공지] ' : ''}커뮤니티 | SODAMM`,
    description,
  };
}

export default async function PostPage({ params }: PostPageProps) {
  // 게시글 조회
  const result = await getPost({ id: params.id });
  
  if (!result.success) {
    notFound();
  }
  
  const post = result.data;
  
  // 조회수 증가 (서버 컴포넌트에서 직접 호출)
  await incrementViewCount({ id: params.id });
  
  return (
    <div className="container py-6 space-y-6">
      <PostDetail post={post} />
      
      <Separator />
      
      <PostComments postId={params.id} initialComments={post.postComments} />
    </div>
  );
}
