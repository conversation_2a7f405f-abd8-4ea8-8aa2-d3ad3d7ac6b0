import { Metadata } from 'next';
import { redirect, notFound } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { getCategories } from '@/actions/category/category';
import { getPost } from '@/actions/post';
import PostForm from '@/components/post/PostForm';

export const metadata: Metadata = {
  title: '게시글 수정 | SODAMM',
  description: '커뮤니티 게시글을 수정합니다.',
};

interface EditPostPageProps {
  params: {
    id: string;
  };
}

export default async function EditPostPage({ params }: EditPostPageProps) {
  // 로그인 확인
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    redirect(`/api/auth/signin?callbackUrl=/community/edit/${params.id}`);
  }
  
  // 게시글 조회
  const postResult = await getPost({ id: params.id });
  
  if (!postResult.success) {
    notFound();
  }
  
  const post = postResult.data;
  
  // 작성자 또는 관리자만 수정 가능
  if (post.userId !== session.user.id && !session.user.isAdmin) {
    redirect('/community');
  }
  
  // 카테고리 목록 조회
  const categoriesResult = await getCategories({
    isActive: true,
  });
  
  const categories = categoriesResult.success ? categoriesResult.data.items : [];
  
  return (
    <div className="container py-6 space-y-6">
      <h1 className="text-3xl font-bold">게시글 수정</h1>
      <PostForm 
        categories={categories} 
        initialData={post} 
        isEdit={true} 
      />
    </div>
  );
}
