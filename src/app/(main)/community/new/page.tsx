import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { getCategories } from '@/actions/category/category';
import PostForm from '@/components/post/PostForm';

export const metadata: Metadata = {
  title: '게시글 작성 | SODAMM',
  description: '커뮤니티 게시글을 작성합니다.',
};

export default async function NewPostPage() {
  // 로그인 확인
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    redirect('/api/auth/signin?callbackUrl=/community/new');
  }
  
  // 카테고리 목록 조회
  const categoriesResult = await getCategories({
    isActive: true,
  });
  
  const categories = categoriesResult.success ? categoriesResult.data.items : [];
  
  return (
    <div className="container py-6 space-y-6">
      <h1 className="text-3xl font-bold">게시글 작성</h1>
      <PostForm categories={categories} />
    </div>
  );
}
