import { Suspense } from 'react';
import { Metadata } from 'next';
import { getPosts } from '@/actions/post';
import { getCategories } from '@/actions/category/category';
import PostList from '@/components/post/PostList';
import PostListSkeleton from '@/components/post/PostListSkeleton';
import CategoryFilter from '@/components/category/CategoryFilter';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '커뮤니티 | SODAMM',
  description: '외국인 근로자를 위한 커뮤니티 게시판입니다. 질문, 정보 공유, 소통을 할 수 있습니다.',
};

interface CommunityPageProps {
  searchParams: {
    page?: string;
    categoryId?: string;
    type?: string;
    query?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default async function CommunityPage({ searchParams }: CommunityPageProps) {
  // 카테고리 목록 조회
  const categoriesResult = await getCategories({
    parentId: null,
    isActive: true,
  });

  const categories = categoriesResult.success ? categoriesResult.data.items : [];

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">커뮤니티</h1>
        <Link href="/community/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            글쓰기
          </Button>
        </Link>
      </div>
      
      <Separator />
      
      <CategoryFilter 
        categories={categories} 
        selectedCategoryId={searchParams.categoryId}
        baseUrl="/community"
      />
      
      <Suspense fallback={<PostListSkeleton />}>
        <PostList searchParams={searchParams} />
      </Suspense>
    </div>
  );
}
