'use client';

import { useState } from 'react';
import { SWRExample } from '@/components/examples/swr-example';
import { InfiniteScrollExample } from '@/components/examples/infinite-scroll-example';
import { OptimisticUpdateExample } from '@/components/examples/optimistic-update-example';
import { ErrorLoadingExample } from '@/components/examples/error-loading-example';
import { RevalidationExample } from '@/components/examples/revalidation-example';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * SWR 예제 페이지
 * 
 * 이 페이지는 SWR의 다양한 기능을 보여주는 예제 컴포넌트들을 포함합니다.
 */
export default function SWRExamplesPage() {
  const [activeTab, setActiveTab] = useState('basic');
  
  return (
    <div className="container py-8 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">SWR 예제</h1>
        <p className="text-muted-foreground">
          SWR을 사용한 데이터 페칭 및 캐싱 전략 예제입니다.
        </p>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic">기본 사용법</TabsTrigger>
          <TabsTrigger value="infinite">무한 스크롤</TabsTrigger>
          <TabsTrigger value="optimistic">낙관적 업데이트</TabsTrigger>
          <TabsTrigger value="error">에러 처리</TabsTrigger>
          <TabsTrigger value="revalidation">재검증 전략</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-4">
          <div className="rounded-md bg-muted p-4 text-sm">
            <p>SWR의 기본 사용법을 보여주는 예제입니다.</p>
            <p>데이터 페칭, 로딩 상태, 에러 처리 등의 기본 기능을 확인할 수 있습니다.</p>
          </div>
          <SWRExample />
        </TabsContent>
        
        <TabsContent value="infinite" className="space-y-4">
          <div className="rounded-md bg-muted p-4 text-sm">
            <p>SWR Infinite를 사용한 무한 스크롤 구현 예제입니다.</p>
            <p>스크롤을 내리면 자동으로 더 많은 데이터를 로드합니다.</p>
          </div>
          <InfiniteScrollExample />
        </TabsContent>
        
        <TabsContent value="optimistic" className="space-y-4">
          <div className="rounded-md bg-muted p-4 text-sm">
            <p>SWR의 mutate 기능을 활용한 낙관적 UI 업데이트 예제입니다.</p>
            <p>사용자 액션 후 API 응답을 기다리지 않고 UI를 즉시 업데이트합니다.</p>
          </div>
          <OptimisticUpdateExample />
        </TabsContent>
        
        <TabsContent value="error" className="space-y-4">
          <div className="rounded-md bg-muted p-4 text-sm">
            <p>SWR의 에러 처리 및 로딩 상태 관리 기능을 보여주는 예제입니다.</p>
            <p>네트워크 상태 감지, 에러 메시지 표시, 로딩 스켈레톤 등을 확인할 수 있습니다.</p>
          </div>
          <ErrorLoadingExample />
        </TabsContent>
        
        <TabsContent value="revalidation" className="space-y-4">
          <div className="rounded-md bg-muted p-4 text-sm">
            <p>SWR의 다양한 데이터 재검증 전략을 보여주는 예제입니다.</p>
            <p>포커스 시 재검증, 주기적 재검증, 조건부 재검증, 이벤트 기반 재검증 등을 확인할 수 있습니다.</p>
          </div>
          <RevalidationExample />
        </TabsContent>
      </Tabs>
    </div>
  );
}
