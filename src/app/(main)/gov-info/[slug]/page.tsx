import { Metadata } from "next";
import { notFound } from "next/navigation";
import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import ContentRenderer from "@/components/shared/ContentRenderer";
import ImageGallery from "@/components/shared/ImageGallery";
import RelatedItems from "@/components/shared/RelatedItems";
import { ExternalLink } from "lucide-react";

interface GovInfoDetailPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({
  params,
}: GovInfoDetailPageProps): Promise<Metadata> {
  const { slug } = params;

  const govInfo = await prisma.govInfo.findUnique({
    where: {
      slug,
      status: "published",
    },
    select: {
      title: true,
      summary: true,
    },
  });

  if (!govInfo) {
    return {
      title: "정보를 찾을 수 없습니다 | 소담",
      description: "요청하신 정부 정보를 찾을 수 없습니다.",
    };
  }

  return {
    title: `${govInfo.title} | 소담`,
    description: govInfo.summary || "외국인 근로자를 위한 정부 정보입니다.",
  };
}

export default async function GovInfoDetailPage({
  params,
}: GovInfoDetailPageProps) {
  const { slug } = params;
  const session = await auth();

  // 정부 정보 조회
  const govInfo = await prisma.govInfo.findUnique({
    where: {
      slug,
      ...(session?.user?.isAdmin ? {} : { status: "published" }),
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          displayName: true,
          image: true,
        },
      },
      category: true,
      images: {
        orderBy: {
          order: "asc",
        },
      },
    },
  });

  if (!govInfo) {
    notFound();
  }

  // 조회수 증가 (비동기로 처리)
  prisma.govInfo.update({
    where: { id: govInfo.id },
    data: { viewCount: { increment: 1 } },
  }).catch((error) => {
    console.error("조회수 증가 중 오류 발생:", error);
  });

  // 관련 정부 정보 조회
  const relatedGovInfos = await prisma.govInfo.findMany({
    where: {
      categoryId: govInfo.categoryId,
      status: "published",
      id: {
        not: govInfo.id,
      },
    },
    take: 3,
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      title: true,
      slug: true,
      summary: true,
      isImportant: true,
    },
  });

  return (
    <div className="container py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="outline">{govInfo.category.name}</Badge>
            {govInfo.isImportant && (
              <Badge variant="destructive">중요 공지사항</Badge>
            )}
          </div>
          <h1 className="text-3xl font-bold mb-2">{govInfo.title}</h1>
          {govInfo.summary && (
            <p className="text-muted-foreground mb-4">{govInfo.summary}</p>
          )}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>
                작성일: {formatDate(govInfo.createdAt)}
              </span>
              {govInfo.infoUpdatedAt && (
                <span>
                  정보 업데이트: {formatDate(govInfo.infoUpdatedAt)}
                </span>
              )}
              <span>조회수: {govInfo.viewCount + 1}</span>
            </div>
            {session?.user?.isAdmin && (
              <a
                href={`/admin/gov-info/${govInfo.id}`}
                className="text-primary hover:underline"
              >
                관리자 편집
              </a>
            )}
          </div>
        </div>

        <Separator className="my-6" />

        {govInfo.govUrl && (
          <div className="mb-6">
            <Button
              variant="outline"
              className="gap-2"
              asChild
            >
              <a href={govInfo.govUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink size={16} />
                {govInfo.govDepartment || "정부 사이트"} 바로가기
              </a>
            </Button>
          </div>
        )}

        <Card className="mb-8">
          <CardContent className="pt-6">
            <ContentRenderer content={govInfo.contentHtml} />
          </CardContent>
        </Card>

        {govInfo.images.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">관련 이미지</h2>
            <ImageGallery images={govInfo.images} />
          </div>
        )}

        {relatedGovInfos.length > 0 && (
          <div className="mt-12">
            <h2 className="text-xl font-semibold mb-4">관련 정부 정보</h2>
            <RelatedItems
              items={relatedGovInfos.map((info) => ({
                id: info.id,
                title: info.title,
                description: info.summary || "",
                href: `/gov-info/${info.slug}`,
                badge: info.isImportant ? "중요" : undefined,
              }))}
            />
          </div>
        )}
      </div>
    </div>
  );
}
