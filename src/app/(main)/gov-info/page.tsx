import { Suspense } from "react";
import { Metadata } from "next";
import { getGovInfos } from "@/actions/govinfo/govinfo";
import { prisma } from "@/lib/prisma";
import GovInfoList from "@/components/govinfo/GovInfoList";
import SearchForm from "@/components/govinfo/SearchForm";
import { Skeleton } from "@/components/ui/skeleton";

export const metadata: Metadata = {
  title: "정부 정보 | 소담",
  description: "외국인 근로자를 위한 정부 정보 및 공공기관 정보를 제공합니다.",
};

interface GovInfoPageProps {
  searchParams: {
    page?: string;
    categoryId?: string;
    query?: string;
    isImportant?: string;
  };
}

export default async function GovInfoPage({
  searchParams,
}: GovInfoPageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const categoryId = searchParams.categoryId;
  const query = searchParams.query;
  const isImportant = searchParams.isImportant === "true" ? true : undefined;

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: "asc",
    },
  });

  // 정부 정보 목록 조회
  const result = await getGovInfos({
    page,
    limit: 9,
    categoryId,
    status: "published",
    isImportant,
    query,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  const govInfos = result.success ? result.data.data : [];
  const pagination = result.success
    ? result.data.pagination
    : { totalPages: 1 };

  // 중요 공지사항 조회
  const importantInfos = isImportant ? [] : await prisma.govInfo.findMany({
    where: {
      status: "published",
      isImportant: true,
    },
    take: 3,
    orderBy: {
      createdAt: "desc",
    },
    include: {
      category: true,
    },
  });

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">정부 정보</h1>
        <p className="text-muted-foreground mb-4">
          외국인 근로자를 위한 정부 정보 및 공공기관 정보를 제공합니다.
        </p>
        <SearchForm className="max-w-2xl" />
      </div>

      {!isImportant && importantInfos.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">중요 공지사항</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {importantInfos.map((info) => (
              <div
                key={info.id}
                className="bg-primary/10 border border-primary/20 rounded-lg p-4"
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                    중요
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {info.category.name}
                  </span>
                </div>
                <h3 className="font-medium mb-1">
                  <a
                    href={`/gov-info/${info.slug}`}
                    className="hover:underline"
                  >
                    {info.title}
                  </a>
                </h3>
                {info.summary && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {info.summary}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <Suspense
        fallback={
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="h-48 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ))}
          </div>
        }
      >
        <GovInfoList
          govInfos={govInfos}
          categories={categories}
          totalPages={pagination.totalPages}
          currentPage={page}
          isImportant={isImportant}
        />
      </Suspense>
    </div>
  );
}
