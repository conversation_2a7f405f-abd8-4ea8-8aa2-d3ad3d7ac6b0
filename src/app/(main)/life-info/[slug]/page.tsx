import { Suspense } from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import LifeInfoDetail from '@/components/lifeinfo/LifeInfoDetail';
import { Skeleton } from '@/components/ui/skeleton';

interface LifeInfoDetailPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({
  params,
}: LifeInfoDetailPageProps): Promise<Metadata> {
  const { slug } = params;

  // 생활 정보 조회
  const lifeInfo = await prisma.lifeInfo.findUnique({
    where: {
      slug,
      status: 'published',
    },
    select: {
      title: true,
      summary: true,
      images: {
        take: 1,
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!lifeInfo) {
    return {
      title: '찾을 수 없음 | 소담',
      description: '요청하신 페이지를 찾을 수 없습니다.',
    };
  }

  return {
    title: `${lifeInfo.title} | 소담`,
    description: lifeInfo.summary || '한국 생활 정보',
    openGraph: lifeInfo.images && lifeInfo.images.length > 0
      ? {
          images: [
            {
              url: lifeInfo.images[0].url,
              width: 1200,
              height: 630,
              alt: lifeInfo.images[0].alt || lifeInfo.title,
            },
          ],
        }
      : undefined,
  };
}

export default async function LifeInfoDetailPage({
  params,
}: LifeInfoDetailPageProps) {
  const { slug } = params;
  const session = await auth();

  // 생활 정보 조회
  const lifeInfo = await prisma.lifeInfo.findUnique({
    where: {
      slug,
      ...(session?.user?.isAdmin ? {} : { status: 'published' }),
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          displayName: true,
          image: true,
        },
      },
      category: true,
      images: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!lifeInfo) {
    notFound();
  }

  // 관련 생활 정보 조회
  const relatedLifeInfos = await prisma.lifeInfo.findMany({
    where: {
      categoryId: lifeInfo.categoryId,
      status: 'published',
      id: {
        not: lifeInfo.id,
      },
    },
    take: 3,
    orderBy: {
      createdAt: 'desc',
    },
    select: {
      id: true,
      title: true,
      slug: true,
      summary: true,
    },
  });

  // 현재 사용자 정보
  const currentUser = session?.user || null;

  return (
    <div className="container py-8">
      <Suspense
        fallback={
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-96 w-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        }
      >
        <LifeInfoDetail
          lifeInfo={lifeInfo}
          relatedLifeInfos={relatedLifeInfos}
          currentUser={currentUser}
        />
      </Suspense>
    </div>
  );
}
