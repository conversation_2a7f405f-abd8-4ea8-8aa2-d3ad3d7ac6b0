import { Suspense } from "react";
import { Metadata } from "next";
import { getLifeInfos } from "@/actions/lifeinfo/lifeinfo";
import { prisma } from "@/lib/prisma";
import LifeInfoList from "@/components/lifeinfo/LifeInfoList";
import SearchForm from "@/components/lifeinfo/SearchForm";
import { Skeleton } from "@/components/ui/skeleton";

export const metadata: Metadata = {
  title: "생활 정보 | 소담",
  description: "한국 생활에 필요한 다양한 정보를 제공합니다.",
};

interface LifeInfoPageProps {
  searchParams: {
    page?: string;
    categoryId?: string;
    query?: string;
  };
}

export default async function LifeInfoPage({
  searchParams,
}: LifeInfoPageProps) {
  // 페이지 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const categoryId = searchParams.categoryId;
  const query = searchParams.query;

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: "asc",
    },
  });

  // 생활 정보 목록 조회
  const result = await getLifeInfos({
    page,
    limit: 9,
    categoryId,
    status: "published",
    query,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  const lifeInfos = result.success ? result.data.data : [];
  const pagination = result.success
    ? result.data.pagination
    : { totalPages: 1 };

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">생활 정보</h1>
        <p className="text-muted-foreground mb-4">
          한국 생활에 필요한 다양한 정보를 제공합니다.
        </p>
        <SearchForm className="max-w-2xl" />
      </div>

      <Suspense
        fallback={
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="h-48 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ))}
          </div>
        }
      >
        <LifeInfoList
          lifeInfos={lifeInfos}
          categories={categories}
          totalPages={pagination.totalPages}
          currentPage={page}
        />
      </Suspense>
    </div>
  );
}
