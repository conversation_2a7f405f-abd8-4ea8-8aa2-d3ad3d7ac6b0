import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import type { Metadata } from "next";
import { constructMetadata } from "@/lib/metadata";
import { MultipleStructuredData } from "@/components/structured-data";
import { OptimizedImage } from "@/components/ui/optimized-image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Building,
  Briefcase,
  Home,
  Bus,
  Stethoscope,
  GraduationCap,
  Bank,
  Phone,
  FileText,
} from "lucide-react";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("HomePage");

  return constructMetadata({
    title: t("metaTitle"),
    description: t("metaDescription"),
    image: "/images/home-og-image.jpg",
  });
}

export default function MainPage() {
  const t = useTranslations("HomePage");

  // 구조화된 데이터 생성
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Sodamm",
    url: process.env.NEXT_PUBLIC_APP_URL || "https://sodamm.com",
    potentialAction: {
      "@type": "SearchAction",
      target: `${
        process.env.NEXT_PUBLIC_APP_URL || "https://sodamm.com"
      }/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
    inLanguage: ["ko-KR", "en-US"],
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Sodamm",
    url: process.env.NEXT_PUBLIC_APP_URL || "https://sodamm.com",
    logo: `${process.env.NEXT_PUBLIC_APP_URL || "https://sodamm.com"}/logo.png`,
    sameAs: [
      "https://facebook.com/sodamm",
      "https://twitter.com/sodamm",
      "https://instagram.com/sodamm",
    ],
    description: "외국인 근로자를 위한 한국 생활 정보 및 구인구직 플랫폼",
  };

  return (
    <div className="space-y-12">
      <MultipleStructuredData dataArray={[websiteSchema, organizationSchema]} />

      {/* 히어로 섹션 */}
      <section className="relative py-16 px-4 rounded-xl overflow-hidden bg-gradient-to-r from-primary/10 to-primary/5">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            {t("title")}
          </h1>
          <p className="text-xl text-muted-foreground mb-8">{t("subtitle")}</p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button size="lg" asChild>
              <Link href="/info">{t("getStarted")}</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/about">{t("learnMore")}</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* 주요 카테고리 바로가기 */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">주요 카테고리</h2>
          <Link
            href="/info"
            className="text-primary flex items-center hover:underline"
          >
            모든 카테고리 보기 <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <CategoryCard
            href="/info/housing"
            title="주거 정보"
            icon={<Home className="h-6 w-6" />}
          />
          <CategoryCard
            href="/info/transportation"
            title="교통 정보"
            icon={<Bus className="h-6 w-6" />}
          />
          <CategoryCard
            href="/info/healthcare"
            title="의료 정보"
            icon={<Stethoscope className="h-6 w-6" />}
          />
          <CategoryCard
            href="/info/education"
            title="교육 정보"
            icon={<GraduationCap className="h-6 w-6" />}
          />
          <CategoryCard
            href="/guide/banking"
            title="은행 서비스"
            icon={<Bank className="h-6 w-6" />}
          />
          <CategoryCard
            href="/guide/mobile"
            title="통신 서비스"
            icon={<Phone className="h-6 w-6" />}
          />
          <CategoryCard
            href="/government/visa"
            title="비자 정보"
            icon={<FileText className="h-6 w-6" />}
          />
          <CategoryCard
            href="/jobs/listings"
            title="구인 정보"
            icon={<Briefcase className="h-6 w-6" />}
          />
        </div>
      </section>

      {/* 최신 정보 및 공지사항 섹션 */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">최신 정보 및 공지사항</h2>
          <Link
            href="/news"
            className="text-primary flex items-center hover:underline"
          >
            더보기 <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <NewsCard
            title="2024년 외국인 등록증 발급 절차 변경 안내"
            date="2024.06.15"
            category="정부 정보"
            href="/news/1"
          />
          <NewsCard
            title="여름 휴가철 대중교통 이용 안내"
            date="2024.06.10"
            category="교통 정보"
            href="/news/2"
          />
          <NewsCard
            title="외국인 대상 한국어 무료 강좌 안내"
            date="2024.06.05"
            category="교육 정보"
            href="/news/3"
          />
          <NewsCard
            title="여름철 건강관리 팁"
            date="2024.06.01"
            category="의료 정보"
            href="/news/4"
          />
        </div>
      </section>

      {/* 인기 게시글 섹션 */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">인기 게시글</h2>
          <Link
            href="/community"
            className="text-primary flex items-center hover:underline"
          >
            커뮤니티 가기 <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <PostCard
            title="한국에서 집 구하기 꿀팁"
            author="김소담"
            date="2024.06.14"
            commentCount={24}
            likeCount={56}
            href="/community/post/1"
          />
          <PostCard
            title="외국인이 알아야 할 한국 문화 에티켓"
            author="John Smith"
            date="2024.06.12"
            commentCount={18}
            likeCount={42}
            href="/community/post/2"
          />
          <PostCard
            title="서울 근교 당일치기 여행 추천"
            author="이여행"
            date="2024.06.10"
            commentCount={15}
            likeCount={38}
            href="/community/post/3"
          />
        </div>
      </section>

      {/* 구인 정보 하이라이트 */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">최신 구인 정보</h2>
          <Link
            href="/jobs/listings"
            className="text-primary flex items-center hover:underline"
          >
            모든 구인 정보 <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <JobCard
            title="영어 원어민 교사 모집"
            company="서울 국제 학교"
            location="서울 강남구"
            salary="4,000,000원 ~ 5,000,000원"
            type="정규직"
            href="/jobs/post/1"
            image="/images/jobs/teacher.jpg"
          />
          <JobCard
            title="소프트웨어 개발자 (React/Node.js)"
            company="테크 스타트업"
            location="서울 강남구"
            salary="협의 후 결정"
            type="정규직"
            href="/jobs/post/2"
            image="/images/jobs/developer.jpg"
          />
          <JobCard
            title="호텔 프론트 데스크 직원 모집"
            company="그랜드 호텔"
            location="서울 중구"
            salary="시급 12,000원"
            type="파트타임"
            href="/jobs/post/3"
            image="/images/jobs/hotel.jpg"
          />
          <JobCard
            title="마케팅 매니저 (외국어 가능자 우대)"
            company="글로벌 기업"
            location="경기도 성남시"
            salary="5,000,000원 ~ 6,000,000원"
            type="정규직"
            href="/jobs/post/4"
            image="/images/jobs/marketing.jpg"
          />
        </div>
      </section>
    </div>
  );
}

// 카테고리 카드 컴포넌트
function CategoryCard({
  href,
  title,
  icon,
}: {
  href: string;
  title: string;
  icon: React.ReactNode;
}) {
  return (
    <Link href={href}>
      <Card className="h-full hover:border-primary transition-colors">
        <CardContent className="flex flex-col items-center justify-center p-6">
          <div className="mb-3 text-primary">{icon}</div>
          <h3 className="font-medium text-center">{title}</h3>
        </CardContent>
      </Card>
    </Link>
  );
}

// 뉴스 카드 컴포넌트
function NewsCard({
  title,
  date,
  category,
  href,
}: {
  title: string;
  date: string;
  category: string;
  href: string;
}) {
  return (
    <Link href={href}>
      <Card className="h-full hover:border-primary transition-colors">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <Badge variant="outline">{category}</Badge>
            <span className="text-sm text-muted-foreground">{date}</span>
          </div>
        </CardHeader>
        <CardContent>
          <h3 className="font-medium">{title}</h3>
        </CardContent>
      </Card>
    </Link>
  );
}

// 게시글 카드 컴포넌트
function PostCard({
  title,
  author,
  date,
  commentCount,
  likeCount,
  href,
}: {
  title: string;
  author: string;
  date: string;
  commentCount: number;
  likeCount: number;
  href: string;
}) {
  return (
    <Link href={href}>
      <Card className="h-full hover:border-primary transition-colors">
        <CardHeader className="pb-2">
          <h3 className="font-medium">{title}</h3>
        </CardHeader>
        <CardContent className="pb-2">
          <p className="text-sm text-muted-foreground">작성자: {author}</p>
        </CardContent>
        <CardFooter className="pt-0 flex justify-between text-sm text-muted-foreground">
          <span>{date}</span>
          <div className="flex space-x-3">
            <span>댓글 {commentCount}</span>
            <span>좋아요 {likeCount}</span>
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}

// 구인 정보 카드 컴포넌트
function JobCard({
  title,
  company,
  location,
  salary,
  type,
  href,
  image,
}: {
  title: string;
  company: string;
  location: string;
  salary: string;
  type: string;
  href: string;
  image?: string;
}) {
  return (
    <Link href={href}>
      <Card className="h-full hover:border-primary transition-colors overflow-hidden">
        {image && (
          <div className="relative h-32 w-full">
            <OptimizedImage
              src={
                image ||
                `/images/jobs/${company.toLowerCase().replace(/\s+/g, "-")}.jpg`
              }
              alt={`${company}의 ${title} 채용 정보`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
            />
          </div>
        )}
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <h3 className="font-medium">{title}</h3>
            <Badge variant={type === "정규직" ? "default" : "secondary"}>
              {type}
            </Badge>
          </div>
          <CardDescription>{company}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2 pb-2">
          <div className="flex items-center">
            <Building className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm">{location}</span>
          </div>
          <div className="flex items-center">
            <span className="text-sm font-medium">{salary}</span>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
