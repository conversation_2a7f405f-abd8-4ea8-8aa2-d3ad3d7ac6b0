import { Suspense } from "react";
import { Metadata } from "next";
import { search, SearchResultType } from "@/actions/search";
import { prisma } from "@/lib/prisma";
import SearchResultList from "@/components/search/SearchResultList";
import SearchForm from "@/components/search/SearchForm";
import { Skeleton } from "@/components/ui/skeleton";

export const metadata: Metadata = {
  title: "검색 | 소담",
  description: "한국 생활에 필요한 다양한 정보를 검색합니다.",
};

interface SearchPageProps {
  searchParams: {
    query?: string;
    page?: string;
    categoryId?: string;
    types?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const query = searchParams.query || "";
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const categoryId = searchParams.categoryId;
  const types = searchParams.types
    ? (searchParams.types.split(",") as SearchResultType[])
    : undefined;
  const sortBy = (searchParams.sortBy as any) || "relevance";
  const sortOrder = (searchParams.sortOrder as any) || "desc";

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: "asc",
    },
  });

  // 검색 결과 조회
  const result = await search({
    query: query || " ", // 빈 검색어일 경우 공백 문자 전달
    page,
    limit: 9,
    types,
    categoryId,
    sortBy,
    sortOrder,
  });

  const searchResults = result.success ? result.data.data : [];
  const pagination = result.success
    ? result.data.pagination
    : { totalPages: 1, totalCount: 0 };

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">검색</h1>
        <SearchForm className="max-w-2xl" />
      </div>

      {query ? (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">
            "{query}" 검색 결과 ({pagination.totalCount}건)
          </h2>
        </div>
      ) : (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">
            전체 콘텐츠 ({pagination.totalCount}건)
          </h2>
        </div>
      )}

      <Suspense
        fallback={
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="h-48 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ))}
          </div>
        }
      >
        <SearchResultList
          results={searchResults}
          query={query}
          totalPages={pagination.totalPages}
          currentPage={page}
          totalCount={pagination.totalCount}
          categories={categories}
        />
      </Suspense>
    </div>
  );
}
