import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { Skeleton } from "@/components/ui/skeleton";
import ServiceGuideDetail from "@/components/serviceguide/ServiceGuideDetail";

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  const { slug } = params;

  // 서비스 가이드 조회
  const serviceGuide = await prisma.serviceGuide.findUnique({
    where: {
      slug,
      status: "published",
    },
    select: {
      title: true,
      summary: true,
    },
  });

  if (!serviceGuide) {
    return {
      title: "서비스 가이드를 찾을 수 없습니다 | 소담",
      description: "요청하신 서비스 가이드를 찾을 수 없습니다.",
    };
  }

  return {
    title: `${serviceGuide.title} | 소담`,
    description: serviceGuide.summary || "한국 생활에 필요한 서비스 이용 가이드입니다.",
  };
}

interface ServiceGuideDetailPageProps {
  params: {
    slug: string;
  };
}

export default async function ServiceGuideDetailPage({
  params,
}: ServiceGuideDetailPageProps) {
  const { slug } = params;
  const session = await auth();

  // 서비스 가이드 조회
  const serviceGuide = await prisma.serviceGuide.findUnique({
    where: {
      slug,
      ...(session?.user?.isAdmin ? {} : { status: "published" }),
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          displayName: true,
          image: true,
        },
      },
      category: true,
      steps: {
        orderBy: {
          order: "asc",
        },
        include: {
          images: {
            orderBy: {
              order: "asc",
            },
          },
        },
      },
      documents: {
        orderBy: {
          order: "asc",
        },
      },
      faqs: {
        orderBy: {
          order: "asc",
        },
      },
    },
  });

  if (!serviceGuide) {
    notFound();
  }

  // 관련 서비스 가이드 조회
  const relatedGuides = await prisma.serviceGuide.findMany({
    where: {
      categoryId: serviceGuide.categoryId,
      status: "published",
      id: {
        not: serviceGuide.id,
      },
    },
    take: 3,
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      title: true,
      slug: true,
      summary: true,
    },
  });

  // 현재 사용자 정보
  const currentUser = session?.user || null;

  return (
    <div className="container py-8">
      <Suspense
        fallback={
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-96 w-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        }
      >
        <ServiceGuideDetail
          serviceGuide={serviceGuide}
          relatedGuides={relatedGuides}
          currentUser={currentUser}
        />
      </Suspense>
    </div>
  );
}
