'use client';

import { ModalLoading } from '@/components/ui/loading';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, lazy, useEffect } from 'react';

// 동적으로 LoginForm 컴포넌트 로드
const LoginForm = lazy(() => import('@/components/auth/login-form'));

export default function LoginModalPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';
  const error = searchParams.get('error');

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handleBackgroundClick = () => {
    router.back();
  };

  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div className="animate-in fade-in fixed inset-0 z-50 flex items-center justify-center duration-300">
      <div
        className="animate-in fade-in absolute inset-0 bg-black/30 backdrop-blur-[4px] duration-300"
        onClick={handleBackgroundClick}
      />
      <div
        className="animate-in fade-in slide-in-from-bottom-4 z-10 p-4 duration-500"
        onClick={handleCardClick}
      >
        <Suspense fallback={<ModalLoading />}>
          <LoginForm
            callbackUrl={callbackUrl}
            error={error}
          />
        </Suspense>
      </div>
    </div>
  );
}
