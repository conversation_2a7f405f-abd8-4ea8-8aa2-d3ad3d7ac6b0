'use client';

import { ModalLoading } from '@/components/ui/loading';
import { useRouter } from 'next/navigation';
import { Suspense, lazy, useEffect } from 'react';

// 동적으로 NewPostForm 컴포넌트 로드
const NewPostForm = lazy(
  () => import('@/components/community/feed/new-post-form')
);

export default function PostNewModalPage() {
  const router = useRouter();
  // Add overflow hidden to body when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 p-4 backdrop-blur-md"
      onClick={() => router.back()}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="mx-auto w-full max-w-md rounded-lg shadow-lg"
      >
        <Suspense fallback={<ModalLoading />}>
          <NewPostForm />
        </Suspense>
      </div>
    </div>
  );
}
