import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import BookmarksList from '@/components/bookmarks/bookmarks-list';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Bookmarks');
  
  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
  };
}

export default async function BookmarksPage() {
  const session = await auth();
  
  // 인증되지 않은 사용자는 로그인 페이지로 리디렉션
  if (!session?.user) {
    redirect('/login');
  }
  
  const t = await getTranslations('Bookmarks');
  
  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-3xl font-bold mb-6">{t('pageTitle')}</h1>
      <p className="text-muted-foreground mb-8">
        {t('pageDescription')}
      </p>
      <BookmarksList userId={session.user.id} />
    </div>
  );
}
