import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { auth } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { NotificationSettingsForm } from '@/components/notifications/notification-settings-form';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Settings');
  
  return {
    title: t('notificationSettings'),
    description: t('notificationSettingsDescription'),
  };
}

export default async function NotificationSettingsPage() {
  const session = await auth();
  
  // 인증되지 않은 사용자는 로그인 페이지로 리디렉션
  if (!session?.user) {
    redirect('/login');
  }
  
  const t = await getTranslations('Settings');
  
  return (
    <div className="container max-w-2xl py-8">
      <h1 className="text-3xl font-bold mb-6">{t('notificationSettings')}</h1>
      <p className="text-muted-foreground mb-8">
        {t('notificationSettingsDescription')}
      </p>
      
      <NotificationSettingsForm userId={session.user.id} />
    </div>
  );
}
