'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { NotificationType } from '@/generated/prisma';
import { createNotification } from '@/actions/notification';
import { auth } from '@/lib/auth';

export default function TestNotificationPage() {
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState('');
  const [type, setType] = useState<NotificationType>(NotificationType.SYSTEM);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [link, setLink] = useState('');
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('Notifications');

  // 알림 생성 테스트
  const handleCreateNotification = async () => {
    if (!userId || !content) {
      toast({
        title: '오류',
        description: '사용자 ID와 내용은 필수 입력 항목입니다.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const result = await createNotification({
        userId,
        type,
        title: title || undefined,
        content,
        link: link || undefined,
      });

      if (result.success) {
        toast({
          title: '성공',
          description: '알림이 성공적으로 생성되었습니다.',
        });
        
        // 폼 초기화
        setTitle('');
        setContent('');
        setLink('');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('알림 생성 중 오류 발생:', error);
      toast({
        title: '오류',
        description: '알림 생성 중 오류가 발생했습니다.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container max-w-2xl py-8">
      <h1 className="text-3xl font-bold mb-6">알림 테스트</h1>
      <p className="text-muted-foreground mb-8">
        이 페이지는 알림 시스템을 테스트하기 위한 페이지입니다. 실제 서비스에서는 제거해야 합니다.
      </p>
      
      <Card>
        <CardHeader>
          <CardTitle>알림 생성 테스트</CardTitle>
          <CardDescription>
            테스트용 알림을 생성합니다.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="userId">사용자 ID</Label>
            <Input
              id="userId"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="알림을 받을 사용자 ID"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="type">알림 유형</Label>
            <Select value={type} onValueChange={(value) => setType(value as NotificationType)}>
              <SelectTrigger>
                <SelectValue placeholder="알림 유형 선택" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="COMMENT">댓글</SelectItem>
                <SelectItem value="REPLY">답글</SelectItem>
                <SelectItem value="LIKE">좋아요</SelectItem>
                <SelectItem value="ANNOUNCEMENT">공지사항</SelectItem>
                <SelectItem value="MENTION">멘션</SelectItem>
                <SelectItem value="SYSTEM">시스템</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="title">제목 (선택사항)</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="알림 제목"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="content">내용</Label>
            <Textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="알림 내용"
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="link">링크 (선택사항)</Label>
            <Input
              id="link"
              value={link}
              onChange={(e) => setLink(e.target.value)}
              placeholder="알림 클릭 시 이동할 링크 (예: /posts/123)"
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.back()}>
            뒤로 가기
          </Button>
          <Button onClick={handleCreateNotification} disabled={loading}>
            {loading ? '생성 중...' : '알림 생성'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
