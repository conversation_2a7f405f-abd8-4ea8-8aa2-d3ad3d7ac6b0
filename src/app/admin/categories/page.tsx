import { Metadata } from 'next';
import { getCategories } from '@/actions/content/category';
import CategoryList from '@/components/admin/category/category-list';
import CategoryHeader from '@/components/admin/category/category-header';

export const metadata: Metadata = {
  title: '카테고리 관리',
  description: '카테고리를 생성, 수정, 삭제할 수 있습니다.',
};

export default async function CategoriesPage() {
  // 카테고리 목록 조회
  const response = await getCategories();
  
  return (
    <div className="container py-6 space-y-6">
      <CategoryHeader />
      <CategoryList initialData={response.success ? response.data : null} />
    </div>
  );
}
