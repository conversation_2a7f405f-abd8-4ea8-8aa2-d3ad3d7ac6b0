import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * 특정 댓글 정보를 가져오는 API 라우트
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const commentId = params.id;
    
    // 댓글 정보 조회
    const comment = await prisma.comment.findUnique({
      where: { id: commentId },
      select: {
        id: true,
        content: true,
        authorId: true,
        postId: true,
        parentId: true,
        createdAt: true,
        updatedAt: true,
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        post: {
          select: {
            id: true,
            title: true,
          },
        },
        replies: {
          select: {
            id: true,
            content: true,
            authorId: true,
            createdAt: true,
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
          take: 5,
        },
        _count: {
          select: {
            replies: true,
            likes: true,
          },
        },
      },
    });
    
    if (!comment) {
      return NextResponse.json(
        { error: '댓글을 찾을 수 없습니다.' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(comment);
  } catch (error) {
    console.error('댓글 정보 조회 오류:', error);
    return NextResponse.json(
      { error: '댓글 정보를 가져오는 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
