import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * 댓글 목록을 가져오는 API 라우트
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // 필터 파라미터 추출
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const postId = searchParams.get('postId') || undefined;
    const authorId = searchParams.get('authorId') || undefined;
    const parentId = searchParams.get('parentId');
    const orderBy = searchParams.get('orderBy') || 'createdAt';
    const order = (searchParams.get('order') || 'asc') as 'asc' | 'desc';
    
    // 검색 조건 구성
    const where: any = {};
    
    if (postId) {
      where.postId = postId;
    }
    
    if (authorId) {
      where.authorId = authorId;
    }
    
    // parentId가 null이면 최상위 댓글만 가져오기
    if (parentId === 'null') {
      where.parentId = null;
    } else if (parentId) {
      where.parentId = parentId;
    }
    
    // 총 댓글 수 조회
    const total = await prisma.comment.count({ where });
    
    // 댓글 목록 조회
    const comments = await prisma.comment.findMany({
      where,
      select: {
        id: true,
        content: true,
        authorId: true,
        postId: true,
        parentId: true,
        createdAt: true,
        updatedAt: true,
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        post: {
          select: {
            id: true,
            title: true,
          },
        },
        _count: {
          select: {
            replies: true,
            likes: true,
          },
        },
      },
      orderBy: { [orderBy]: order },
      skip: (page - 1) * limit,
      take: limit,
    });
    
    // 응답 데이터 구성
    const totalPages = Math.ceil(total / limit);
    
    return NextResponse.json({
      comments,
      total,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error('댓글 목록 조회 오류:', error);
    return NextResponse.json(
      { error: '댓글 목록을 가져오는 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
