import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * 특정 구인·구직 정보를 가져오는 API 라우트
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const jobId = params.id;
    
    // 구인·구직 정보 조회
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      select: {
        id: true,
        title: true,
        description: true,
        authorId: true,
        location: true,
        salary: true,
        companyName: true,
        contactEmail: true,
        contactPhone: true,
        jobType: true,
        experienceLevel: true,
        isActive: true,
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        _count: {
          select: {
            applications: true,
            views: true,
          },
        },
      },
    });
    
    if (!job) {
      return NextResponse.json(
        { error: '구인·구직 정보를 찾을 수 없습니다.' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(job);
  } catch (error) {
    console.error('구인·구직 정보 조회 오류:', error);
    return NextResponse.json(
      { error: '구인·구직 정보를 가져오는 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
