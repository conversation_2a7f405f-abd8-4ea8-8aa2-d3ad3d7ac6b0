import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * 구인·구직 정보 목록을 가져오는 API 라우트
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // 필터 파라미터 추출
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const authorId = searchParams.get('authorId') || undefined;
    const location = searchParams.get('location') || undefined;
    const jobType = searchParams.get('jobType') || undefined;
    const experienceLevel = searchParams.get('experienceLevel') || undefined;
    const isActive = searchParams.get('isActive') === 'true' ? true : 
                    searchParams.get('isActive') === 'false' ? false : 
                    undefined;
    const search = searchParams.get('search') || undefined;
    const orderBy = searchParams.get('orderBy') || 'createdAt';
    const order = (searchParams.get('order') || 'desc') as 'asc' | 'desc';
    
    // 검색 조건 구성
    const where: any = {};
    
    if (authorId) {
      where.authorId = authorId;
    }
    
    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }
    
    if (jobType) {
      where.jobType = jobType;
    }
    
    if (experienceLevel) {
      where.experienceLevel = experienceLevel;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { companyName: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // 총 구인·구직 정보 수 조회
    const total = await prisma.job.count({ where });
    
    // 구인·구직 정보 목록 조회
    const jobs = await prisma.job.findMany({
      where,
      select: {
        id: true,
        title: true,
        description: true,
        authorId: true,
        location: true,
        salary: true,
        companyName: true,
        contactEmail: true,
        contactPhone: true,
        jobType: true,
        experienceLevel: true,
        isActive: true,
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        _count: {
          select: {
            applications: true,
            views: true,
          },
        },
      },
      orderBy: { [orderBy]: order },
      skip: (page - 1) * limit,
      take: limit,
    });
    
    // 응답 데이터 구성
    const totalPages = Math.ceil(total / limit);
    
    return NextResponse.json({
      jobs,
      total,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error('구인·구직 정보 목록 조회 오류:', error);
    return NextResponse.json(
      { error: '구인·구직 정보 목록을 가져오는 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
