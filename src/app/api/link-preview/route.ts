import * as cheerio from 'cheerio';
import { NextRequest, NextResponse } from 'next/server';

// Threads URL 체크
const isThreadsUrl = (url: string) => {
  return url.includes('threads.net') || url.includes('threads.com');
};

// Threads URL에서 데이터 추출
const getThreadsData = (url: string) => {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const username = pathParts[1]?.replace('@', '');
    const postId = pathParts[3];

    return {
      title: `${username}'s Thread`,
      description: 'View this thread on Threads',
      image: 'https://threads.net/favicon.ico', // 기본 Threads 아이콘
      url,
      isThreads: true,
      username,
      postId,
    };
  } catch (error) {
    console.error('Error parsing Threads URL:', error);
    return null;
  }
};

export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get('url');
    if (!url) {
      return NextResponse.json(
        { error: 'URL parameter is required' },
        { status: 400 }
      );
    }

    // Threads URL인 경우 별도 처리
    if (isThreadsUrl(url)) {
      const threadsData = getThreadsData(url);
      if (!threadsData) {
        throw new Error('Invalid Threads URL');
      }
      return NextResponse.json(threadsData);
    }

    const response = await fetch(url);
    const html = await response.text();
    const $ = cheerio.load(html);

    const title =
      $('meta[property="og:title"]').attr('content') ||
      $('title').text() ||
      $('meta[name="title"]').attr('content') ||
      '';

    const description =
      $('meta[property="og:description"]').attr('content') ||
      $('meta[name="description"]').attr('content') ||
      '';

    const image =
      $('meta[property="og:image"]').attr('content') ||
      $('meta[name="image"]').attr('content') ||
      '';

    return NextResponse.json({
      title,
      description,
      image,
      url,
      isThreads: false,
    });
  } catch (error) {
    console.error('Link preview error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch link preview' },
      { status: 500 }
    );
  }
}
