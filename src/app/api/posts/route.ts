import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * 게시글 목록을 가져오는 API 라우트
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // 필터 파라미터 추출
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const categoryId = searchParams.get('categoryId') || undefined;
    const authorId = searchParams.get('authorId') || undefined;
    const search = searchParams.get('search') || undefined;
    const published = searchParams.get('published') === 'true' ? true : 
                     searchParams.get('published') === 'false' ? false : 
                     undefined;
    const orderBy = searchParams.get('orderBy') || 'createdAt';
    const order = (searchParams.get('order') || 'desc') as 'asc' | 'desc';
    
    // 검색 조건 구성
    const where: any = {};
    
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    if (authorId) {
      where.authorId = authorId;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (published !== undefined) {
      where.published = published;
    }
    
    // 총 게시글 수 조회
    const total = await prisma.post.count({ where });
    
    // 게시글 목록 조회
    const posts = await prisma.post.findMany({
      where,
      select: {
        id: true,
        title: true,
        content: true,
        authorId: true,
        categoryId: true,
        published: true,
        createdAt: true,
        updatedAt: true,
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            comments: true,
            likes: true,
          },
        },
      },
      orderBy: { [orderBy]: order },
      skip: (page - 1) * limit,
      take: limit,
    });
    
    // 응답 데이터 구성
    const totalPages = Math.ceil(total / limit);
    
    return NextResponse.json({
      posts,
      total,
      page,
      limit,
      totalPages,
    });
  } catch (error) {
    console.error('게시글 목록 조회 오류:', error);
    return NextResponse.json(
      { error: '게시글 목록을 가져오는 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
