'use client';

import LoginForm from '@/components/auth/login-form';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function LoginModalPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';
  const error = searchParams.get('error');

  // Add overflow hidden to body when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const pathname = usePathname();
  const isStandalone = pathname === '/auth/login';

  const handleBackgroundClick = () => {
    if (!isStandalone) router.back();
  };

  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div
      className={
        isStandalone
          ? 'flex min-h-screen items-center justify-center p-4'
          : 'animate-in fade-in fixed inset-0 z-50 flex items-center justify-center duration-300'
      }
    >
      {/* Backdrop with blur (모달로 열릴 때만) */}
      {!isStandalone && (
        <div
          className="animate-in fade-in absolute inset-0 bg-black/30 backdrop-blur-[4px] duration-300"
          onClick={handleBackgroundClick}
        />
      )}

      {/* Login form */}
      <div
        className="animate-in fade-in slide-in-from-bottom-4 z-10 p-4 duration-500"
        onClick={handleCardClick}
      >
        <LoginForm
          callbackUrl={callbackUrl}
          error={error}
        />
      </div>
    </div>
  );
}
