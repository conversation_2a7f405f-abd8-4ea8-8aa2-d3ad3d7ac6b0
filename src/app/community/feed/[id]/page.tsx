import { getPostById, viewPost } from '@/actions/posts';
import BackButton from '@/components/back-button';
import PostCommentForm from '@/components/community/feed/new-post-comment-form';
import PostCommentList from '@/components/community/feed/post-comment-list';
import PostDisplay from '@/components/community/feed/post-display';
import { JsonLd } from '@/components/json-ld';
import type { Metadata } from 'next';

export const dynamic = 'force-dynamic';

interface PostPageProps {
  params: { id: string };
}

export async function generateMetadata({
  params,
}: PostPageProps): Promise<Metadata> {
  const { id } = await params;
  const postResult = await getPostById({ id });

  if (!postResult.success || !postResult.data) {
    return {
      title: '게시글을 찾을 수 없습니다 - SODAMM',
      description: '요청하신 게시글을 찾을 수 없습니다.',
    };
  }

  const post = postResult.data;
  // Use the first line or first 50 characters of content as title
  const postTitle = post.content.split('\n')[0].slice(0, 50);
  const description = post.content.slice(0, 160);

  return {
    title: `${postTitle} - SODAMM`,
    description,
    openGraph: {
      title: postTitle,
      description,
      type: 'article',
      publishedTime: post.createdAt.toISOString(),
      authors: [post.user.name || 'SODAMM User'],
      url: `https://sodamm.com/community/post/${post.id}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: postTitle,
      description,
    },
  };
}

export default async function PostDetailPage({ params }: PostPageProps) {
  const { id } = await params;
  const postResult = await viewPost({ id });

  if (!postResult.success) {
    return (
      <div className="text-muted-foreground py-24 text-center">
        {postResult.message || '게시글을 불러오는 중 오류가 발생했습니다.'}
      </div>
    );
  }
  if (!postResult.data) {
    return (
      <div className="text-muted-foreground py-24 text-center">
        존재하지 않는 게시글입니다.
      </div>
    );
  }

  const post = postResult.data;

  // Generate structured data for the post
  const postStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'DiscussionForumPosting',
    'headline': post.content.split('\n')[0].slice(0, 110), // First line as title, max 110 chars
    'articleBody': post.content,
    'datePublished': post.createdAt.toISOString(),
    'author': {
      '@type': 'Person',
      'name': post.user.name,
    },
    'interactionStatistic': [
      {
        '@type': 'InteractionCounter',
        'interactionType': 'https://schema.org/ViewAction',
        'userInteractionCount': post.viewCount,
      },
      {
        '@type': 'InteractionCounter',
        'interactionType': 'https://schema.org/LikeAction',
        'userInteractionCount': post.likeCount,
      },
      {
        '@type': 'InteractionCounter',
        'interactionType': 'https://schema.org/CommentAction',
        'userInteractionCount': post.commentCount,
      },
    ],
  };

  return (
    <>
      <JsonLd data={postStructuredData} />
      <div className="container mx-auto">
        <BackButton className="mb-4 inline-block text-sm font-bold hover:underline" />
        <div className="space-y-2">
          <PostDisplay post={post} />
          <PostCommentForm postId={post.id} />
          <PostCommentList postId={post.id} />
        </div>
      </div>
    </>
  );
}
