'use client';

import NewPostForm from '@/components/community/feed/new-post-form';
import { useRouter } from 'next/navigation'; // useSearchParams 추가
import { useEffect } from 'react';

export default function PostNewModalPage() {
  const router = useRouter();

  // Add overflow hidden to body when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);
  return (
    <div
      className="bg-opacity-80 fixed inset-0 z-50 flex items-center justify-center bg-black p-4"
      onClick={() => router.back()}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="mx-auto w-full max-w-md rounded-lg shadow-lg"
      >
        <NewPostForm />
      </div>
    </div>
  );
}
