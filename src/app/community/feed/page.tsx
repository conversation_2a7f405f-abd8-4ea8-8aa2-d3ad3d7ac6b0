'use client'; // 클라이언트 컴포넌트로 변경

import PostDisplay from '@/components/community/feed/post-display';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge'; // Badge 컴포넌트 추가
import { useCountries } from '@/hooks/use-countries'; // 국가 정보 훅 import
import { useInfinitePosts } from '@/hooks/use-infinite-posts'; // 커스텀 훅 import
import { useNeedsPostListRefresh, useResetPostListRefresh } from '@/store';
import { AlertTriangle, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation'; // useSearchParams import 추가
import { useEffect, useMemo } from 'react'; // useEffect import 추가

export default function CommunityPage() {
  const router = useRouter();
  // useSearchParams 훅을 사용하여 URL 쿼리 파라미터 가져오기
  const searchParams = useSearchParams();
  const countryParam = searchParams.get('c') || ''; // c 파라미터 값 가져오기, 기본값은 빈 문자열로 설정
  // 빈 문자열이면 undefined로 변환하여 전체 조회가 되도록 함
  const country = countryParam === '' ? undefined : countryParam;

  // useCountries 훅 사용
  const { countries, getCountryByCode } = useCountries();

  // 국가 버튼 클릭 핸들러
  const handleCountryClick = (selectedCountry: string) => {
    // 현재 URL에서 쿼리 파라미터만 변경
    const params = new URLSearchParams(searchParams.toString());

    if (selectedCountry) {
      params.set('c', selectedCountry);
    } else {
      params.delete('c');
    }

    router.push(`/community/feed?${params.toString()}`);
  };

  // 현재 선택된 국가 정보 찾기
  const selectedCountry = useMemo(() => {
    return (
      getCountryByCode(countryParam) ||
      (countries.length > 0
        ? countries[0]
        : { code: '', name: '전체', displayName: '전체' })
    );
  }, [countryParam, getCountryByCode, countries]);

  // useInfinitePosts 훅 사용
  const {
    posts,
    error,
    isLoading,
    isValidating,
    isReachingEnd,
    isEmpty,
    loadMoreRef,
    mutate, // useInfinitePosts에서 mutate 함수 가져오기
  } = useInfinitePosts({ country }); // country 파라미터 전달

  // Zustand 스토어 상태 및 액션 구독 (최적화된 선택자 함수 사용)
  const needsRefresh = useNeedsPostListRefresh();
  const resetRefresh = useResetPostListRefresh();

  // needsRefresh 상태가 true로 변경되면 게시글 목록 재검증
  useEffect(() => {
    if (needsRefresh) {
      // SWR 데이터 재검증 (첫 페이지만 갱신하도록 설정)
      mutate(undefined, { revalidate: true });
      // 플래그 리셋
      resetRefresh();
    }
  }, [needsRefresh, mutate, resetRefresh]);

  // 컴포넌트가 마운트될 때 한 번 데이터 재검증
  useEffect(() => {
    // 컴포넌트 마운트 시 데이터 재검증
    mutate();
  }, [mutate]);

  return (
    <div className="mt-3 space-y-6">
      {/* 국가 선택 Badge - enabled가 true인 것만 표시 */}
      <div className="flex flex-wrap gap-2">
        {countries.length > 0 ? (
          countries
            .filter((item) => item.enabled)
            .map((item) => (
              <Badge
                key={item.code}
                variant={countryParam === item.code ? 'default' : 'outline'}
                className={`cursor-pointer px-3 py-1 text-sm ${
                  countryParam === item.code
                    ? 'bg-primary hover:bg-primary/90'
                    : 'hover:bg-zinc-100 dark:hover:bg-zinc-700'
                }`}
                onClick={() => handleCountryClick(item.code)}
              >
                {item.name} ({item.displayName})
              </Badge>
            ))
        ) : (
          <div className="text-muted-foreground py-2">
            국가 정보를 불러오는 중...
          </div>
        )}
      </div>

      {/* 새글 입력 input - 선택된 국가 정보 표시 */}
      <Link // legacyBehavior와 passHref는 Next.js 13 App Router에서 더 이상 필요하지 않거나 다른 방식으로 처리됩니다.
        // href 객체를 사용하여 쿼리 파라미터를 명시적으로 전달합니다.
        href={{
          pathname: '/community/feed/new',
          query: selectedCountry.code ? { country: selectedCountry.code } : {},
        }}
      >
        <div className="group flex min-h-10 cursor-pointer items-center transition-all duration-150">
          <span className="text-primary text-xl font-black group-hover:underline">
            + {selectedCountry.code !== '' && `(${selectedCountry.name}) `}
            커뮤니티에 새 글을 작성해보세요!
          </span>
        </div>
      </Link>

      {/* 로딩 상태 (초기 로딩) */}
      {isLoading && (
        <div className="flex justify-center py-12">
          <Loader2 className="text-muted-foreground size-8 animate-spin" />
        </div>
      )}

      {/* 에러 처리 */}
      {error && !isLoading && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>오류</AlertTitle>
          <AlertDescription>
            {error.message || '게시글을 불러오는 중 오류가 발생했습니다.'}
          </AlertDescription>
        </Alert>
      )}

      {/* 데이터 표시 */}
      {!isLoading && !error && (
        <>
          {isEmpty ? (
            <div className="text-muted-foreground py-12 text-center">
              아직 게시글이 없습니다. 첫 번째 게시글을 작성해보세요!
            </div>
          ) : (
            <ul className="space-y-2">
              {posts.map((post) => {
                return (
                  <li key={post.id}>
                    <PostDisplay post={post} />
                  </li>
                );
              })}
            </ul>
          )}

          {/* 다음 페이지 로딩 트리거 및 로딩/끝 표시 */}
          <div
            ref={loadMoreRef}
            className="h-10"
          >
            {' '}
            {/* Observer 타겟, ref 이름 변경됨 */}
            {isValidating && (
              <div className="flex justify-center py-4">
                <Loader2 className="text-muted-foreground size-6 animate-spin" />
              </div>
            )}
            {isReachingEnd && !isEmpty && !isValidating && (
              <div className="text-muted-foreground py-4 text-center text-sm">
                더 이상 게시글이 없습니다.
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
