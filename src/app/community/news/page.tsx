'use client';

import { incrementNewsViewCount } from '@/actions/news';
import { Label } from '@/components/ui/label';
import { useInfiniteNews } from '@/hooks/use-infinite-news';

function NewsDisplay({
  news,
  onIncrementView,
}: {
  news: any;
  onIncrementView: (newsId: string) => Promise<void>;
}) {
  const handleClick = async () => {
    await onIncrementView(news.id);
  };

  return (
    <div
      className={`{className} relative flex flex-col rounded-lg bg-zinc-800 p-3`}
    >
      <a
        href={news.link}
        target="_blank"
        rel="noopener noreferrer"
        className="block hover:underline"
        onClick={handleClick}
      >
        <h2
          className="mb-1 line-clamp-2 text-sm font-semibold"
          dangerouslySetInnerHTML={{ __html: news.title }}
        />
      </a>
      <div className="flex items-center space-x-2">
        <span className="text-xs text-zinc-400">
          {news.publishedAt
            ? new Date(news.publishedAt).toLocaleString('ko-KR')
            : ''}
        </span>
        <span className="text-xs text-zinc-400">
          조회수 {news.viewCount || 0}
        </span>
      </div>
    </div>
  );
}

export default function NewsPage() {
  const {
    news,
    error,
    isLoading,
    isReachingEnd,
    isEmpty,
    loadMoreRef,
    mutate: infiniteNewsMutate,
  } = useInfiniteNews();

  const handleIncrementView = async (newsId: string) => {
    // Optimistic update
    infiniteNewsMutate(
      (cachedData: any) => {
        // cachedData 타입을 any로 명시하여 임시 해결
        if (!cachedData || !Array.isArray(cachedData)) return [];
        return cachedData.map((page: any) => {
          // page 타입도 any로 명시
          if (
            page &&
            page.success &&
            page.data &&
            Array.isArray(page.data.news)
          ) {
            return {
              ...page,
              data: {
                ...page.data,
                news: page.data.news.map(
                  (item: {
                    id: string;
                    viewCount?: number;
                    [key: string]: any;
                  }) =>
                    item.id === newsId
                      ? { ...item, viewCount: (item.viewCount || 0) + 1 }
                      : item
                ),
              },
            };
          }
          return page;
        });
      },
      false // revalidate를 false로 설정
    );

    try {
      await incrementNewsViewCount(newsId); // try 블록 안으로 이동
      // Optimistic update가 충분하므로, 성공 시 별도의 revalidate는 생략 가능
      // 필요하다면 infiniteNewsMutate(); 호출하여 전체 재검증
    } catch (error) {
      console.error('Failed to increment view count', error);
      // 오류 발생 시 낙관적 업데이트를 롤백하기 위해 원래 데이터로 revalidate
      infiniteNewsMutate();
    }
  };

  return (
    <div className="container mx-auto p-8">
      <Label className="mb-4 text-xl font-black">Super Fast News</Label>
      {isLoading && news.length === 0 && (
        <div className="text-muted-foreground py-12 text-center">
          로딩 중...
        </div>
      )}
      {error && (
        <div className="py-12 text-center text-red-500">
          뉴스를 불러오는 중 오류가 발생했습니다: {error.message}
        </div>
      )}
      <div className="space-y-4">
        {news.map((newsItem) => (
          <NewsDisplay
            key={newsItem.id}
            news={newsItem}
            onIncrementView={handleIncrementView}
          />
        ))}
      </div>
      {isLoading && news.length > 0 && (
        <div className="text-muted-foreground py-4 text-center">
          더 많은 뉴스 로딩 중...
        </div>
      )}
      {isEmpty && !error && (
        <div className="text-muted-foreground py-12 text-center">
          등록된 뉴스가 없습니다.
        </div>
      )}
      {isReachingEnd && !isEmpty && !error && (
        <div className="text-muted-foreground py-12 text-center">
          모든 뉴스를 불러왔습니다.
        </div>
      )}
      {!isLoading && !isReachingEnd && !error && (
        <div
          ref={loadMoreRef}
          style={{ height: '2rem' }}
        />
      )}
    </div>
  );
}
