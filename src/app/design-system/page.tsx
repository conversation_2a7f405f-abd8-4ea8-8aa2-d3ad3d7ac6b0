'use client';

import { ComponentDoc } from '@/design-system/components/docs';
import {
  ButtonExample,
  CardExample,
  ConfirmDialogExample,
  InputExample,
} from '@/design-system/components/examples';
import { tokens } from '@/design-system/tokens';
import { cn } from '@/design-system/utils';
import { useState } from 'react';

/**
 * 디자인 시스템 문서 페이지
 *
 * 이 페이지는 디자인 시스템의 컴포넌트와 토큰을 보여줍니다.
 */
export default function DesignSystemPage() {
  const [activeSection, setActiveSection] = useState<string>('overview');

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-8 text-4xl font-bold">Sodamm 디자인 시스템</h1>

      <div className="flex flex-col gap-8 md:flex-row">
        {/* 사이드바 네비게이션 */}
        <aside className="shrink-0 md:w-64">
          <nav className="sticky top-24 space-y-1">
            <button
              onClick={() => scrollToSection('overview')}
              className={cn(
                'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                activeSection === 'overview'
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-accent'
              )}
            >
              개요
            </button>
            <div className="pt-2">
              <div className="text-muted-foreground mb-2 px-3 text-xs font-semibold tracking-wider uppercase">
                컴포넌트
              </div>
              <div className="space-y-1">
                <button
                  onClick={() => scrollToSection('button')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'button'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  Button
                </button>
                <button
                  onClick={() => scrollToSection('input')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'input'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  Input
                </button>
                <button
                  onClick={() => scrollToSection('card')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'card'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  Card
                </button>
                <button
                  onClick={() => scrollToSection('confirm-dialog')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'confirm-dialog'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  ConfirmDialog
                </button>
              </div>
            </div>
            <div className="pt-2">
              <div className="text-muted-foreground mb-2 px-3 text-xs font-semibold tracking-wider uppercase">
                디자인 토큰
              </div>
              <div className="space-y-1">
                <button
                  onClick={() => scrollToSection('colors')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'colors'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  색상
                </button>
                <button
                  onClick={() => scrollToSection('typography')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'typography'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  타이포그래피
                </button>
                <button
                  onClick={() => scrollToSection('spacing')}
                  className={cn(
                    'w-full rounded-md px-3 py-2 text-left text-sm font-medium',
                    activeSection === 'spacing'
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent'
                  )}
                >
                  간격
                </button>
              </div>
            </div>
          </nav>
        </aside>

        {/* 메인 콘텐츠 */}
        <main className="flex-1">
          <section
            id="overview"
            className="mb-12"
          >
            <h2 className="mb-4 text-2xl font-semibold">개요</h2>
            <div className="space-y-4 rounded-lg border p-6">
              <p>
                Sodamm 디자인 시스템은 일관된 사용자 경험을 제공하기 위한 재사용
                가능한 컴포넌트, 디자인 토큰, 패턴 및 가이드라인의 모음입니다.
              </p>
              <h3 className="mt-4 text-xl font-medium">디자인 원칙</h3>
              <ul className="list-disc space-y-2 pl-6">
                <li>
                  <strong>일관성:</strong> 모든 컴포넌트는 일관된 디자인 언어를
                  따릅니다.
                </li>
                <li>
                  <strong>재사용성:</strong> 컴포넌트는 다양한 상황에서 재사용할
                  수 있도록 설계되었습니다.
                </li>
                <li>
                  <strong>접근성:</strong> 모든 컴포넌트는 WCAG 접근성
                  가이드라인을 준수합니다.
                </li>
                <li>
                  <strong>반응형:</strong> 컴포넌트는 다양한 화면 크기에
                  적응합니다.
                </li>
                <li>
                  <strong>유지보수성:</strong> 코드는 명확하고 문서화되어 있어
                  유지보수가 용이합니다.
                </li>
              </ul>
            </div>
          </section>

          <section
            id="button"
            className="mb-12"
          >
            <ComponentDoc
              title="Button"
              description="다양한 스타일과 크기를 지원하는 기본 버튼 컴포넌트입니다."
              example={<ButtonExample />}
              code={`import { Button } from '@/design-system/components/atoms/Button';

// 기본 버튼
<Button>기본 버튼</Button>

// 변형
<Button variant="default">기본</Button>
<Button variant="destructive">삭제</Button>
<Button variant="outline">아웃라인</Button>
<Button variant="secondary">보조</Button>
<Button variant="ghost">고스트</Button>
<Button variant="link">링크</Button>

// 크기
<Button size="sm">작게</Button>
<Button size="default">기본</Button>
<Button size="lg">크게</Button>
<Button size="icon"><IconComponent /></Button>

// 비활성화
<Button disabled>비활성화</Button>`}
              props={[
                {
                  name: 'variant',
                  type: "'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'",
                  defaultValue: 'default',
                  description: '버튼의 시각적 스타일을 정의합니다.',
                },
                {
                  name: 'size',
                  type: "'default' | 'sm' | 'lg' | 'icon'",
                  defaultValue: 'default',
                  description: '버튼의 크기를 정의합니다.',
                },
                {
                  name: 'asChild',
                  type: 'boolean',
                  defaultValue: 'false',
                  description:
                    '자식 요소를 버튼의 루트로 사용할지 여부를 정의합니다.',
                },
                {
                  name: 'disabled',
                  type: 'boolean',
                  defaultValue: 'false',
                  description: '버튼의 비활성화 상태를 정의합니다.',
                },
              ]}
            />
          </section>

          <section
            id="input"
            className="mb-12"
          >
            <ComponentDoc
              title="Input"
              description="다양한 입력 필드를 위한 기본 컴포넌트입니다."
              example={<InputExample />}
              code={`import { Input } from '@/design-system/components/atoms/Input';

// 기본 입력 필드
<Input placeholder="이름을 입력하세요" />

// 다양한 타입
<Input type="email" placeholder="이메일" />
<Input type="password" placeholder="비밀번호" />
<Input type="number" placeholder="숫자" />

// 상태
<Input disabled placeholder="비활성화된 입력 필드" />
<Input readOnly value="읽기 전용 입력 필드" />
<Input error={true} placeholder="오류가 있는 입력 필드" />
<Input required placeholder="필수 입력 필드" />`}
              props={[
                {
                  name: 'type',
                  type: "'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'date' | 'time' | 'search'",
                  defaultValue: 'text',
                  description: '입력 필드의 타입을 정의합니다.',
                },
                {
                  name: 'error',
                  type: 'boolean',
                  defaultValue: 'false',
                  description: '에러 상태를 표시할지 여부를 정의합니다.',
                },
                {
                  name: 'disabled',
                  type: 'boolean',
                  defaultValue: 'false',
                  description: '입력 필드의 비활성화 상태를 정의합니다.',
                },
                {
                  name: 'readOnly',
                  type: 'boolean',
                  defaultValue: 'false',
                  description: '입력 필드의 읽기 전용 상태를 정의합니다.',
                },
                {
                  name: 'required',
                  type: 'boolean',
                  defaultValue: 'false',
                  description: '입력 필드의 필수 입력 여부를 정의합니다.',
                },
              ]}
            />
          </section>

          <section
            id="card"
            className="mb-12"
          >
            <ComponentDoc
              title="Card"
              description="콘텐츠를 그룹화하고 표시하기 위한 카드 컴포넌트입니다."
              example={<CardExample />}
              code={`import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  CardAction,
} from '@/design-system/components/molecules/Card';

// 기본 카드
<Card>
  <CardHeader>
    <CardTitle>카드 제목</CardTitle>
    <CardDescription>카드 설명</CardDescription>
  </CardHeader>
  <CardContent>
    <p>카드 내용</p>
  </CardContent>
  <CardFooter>
    <Button>확인</Button>
  </CardFooter>
</Card>

// 변형
<Card variant="outline">...</Card>
<Card variant="elevated">...</Card>

// 액션이 있는 카드
<Card>
  <CardHeader>
    <CardTitle>제목</CardTitle>
    <CardDescription>설명</CardDescription>
    <CardAction>
      <Button variant="ghost" size="icon">...</Button>
    </CardAction>
  </CardHeader>
  <CardContent>...</CardContent>
</Card>`}
              props={[
                {
                  name: 'variant',
                  type: "'default' | 'outline' | 'elevated'",
                  defaultValue: 'default',
                  description: '카드의 시각적 스타일을 정의합니다.',
                },
              ]}
            />
          </section>

          <section
            id="confirm-dialog"
            className="mb-12"
          >
            <ComponentDoc
              title="ConfirmDialog"
              description="사용자에게 작업 확인을 요청하는 다이얼로그를 표시합니다."
              example={<ConfirmDialogExample />}
              code={`import { ConfirmDialog } from '@/design-system/components/molecules/ConfirmDialog';
import { useState } from 'react';

function Example() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        다이얼로그 열기
      </Button>

      <ConfirmDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onConfirm={() => {
          // 확인 작업 수행
          setIsOpen(false);
        }}
        title="작업 확인"
        description="이 작업을 진행하시겠습니까?"
        variant="alert"
      />
    </>
  );
}`}
              props={[
                {
                  name: 'isOpen',
                  type: 'boolean',
                  description: '다이얼로그가 열려있는지 여부를 정의합니다.',
                  required: true,
                },
                {
                  name: 'onClose',
                  type: '() => void',
                  description:
                    '다이얼로그가 닫힐 때 호출되는 함수를 정의합니다.',
                  required: true,
                },
                {
                  name: 'onConfirm',
                  type: '() => void',
                  description:
                    '확인 버튼을 클릭했을 때 호출되는 함수를 정의합니다.',
                  required: true,
                },
                {
                  name: 'title',
                  type: 'string',
                  description: '다이얼로그 제목을 정의합니다.',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'string',
                  description: '다이얼로그 설명을 정의합니다.',
                  required: true,
                },
                {
                  name: 'variant',
                  type: "'alert' | 'custom'",
                  defaultValue: 'alert',
                  description: '다이얼로그 변형을 정의합니다.',
                },
                {
                  name: 'cancelText',
                  type: 'string',
                  defaultValue: '취소',
                  description: '취소 버튼 텍스트를 정의합니다.',
                },
                {
                  name: 'confirmText',
                  type: 'string',
                  defaultValue: '확인',
                  description: '확인 버튼 텍스트를 정의합니다.',
                },
                {
                  name: 'confirmVariant',
                  type: "'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'",
                  defaultValue: 'default',
                  description: '확인 버튼 변형을 정의합니다.',
                },
                {
                  name: 'size',
                  type: "'sm' | 'md' | 'lg' | 'xl' | 'full'",
                  defaultValue: 'md',
                  description: '다이얼로그 크기를 정의합니다.',
                },
              ]}
            />
          </section>

          <section
            id="colors"
            className="mb-12"
          >
            <h2 className="mb-4 text-2xl font-semibold">색상 토큰</h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {Object.entries(tokens.colors).map(([name, value]) => (
                <div
                  key={name}
                  className="overflow-hidden rounded-lg border"
                >
                  <div
                    className="h-16 w-full"
                    style={{ backgroundColor: value.light }}
                  />
                  <div className="p-2">
                    <p className="font-medium">{name}</p>
                    <p className="text-xs text-gray-500">{value.light}</p>
                  </div>
                </div>
              ))}
            </div>
          </section>

          <section
            id="typography"
            className="mb-12"
          >
            <h2 className="mb-4 text-2xl font-semibold">타이포그래피</h2>
            <div className="space-y-4 rounded-lg border p-6">
              {Object.entries(tokens.typography.heading).map(
                ([name, value]) => (
                  <div
                    key={name}
                    className="border-b pb-4"
                  >
                    <p
                      className={cn(
                        'mb-2',
                        name === 'h1' && 'text-4xl font-bold',
                        name === 'h2' && 'text-3xl font-bold',
                        name === 'h3' && 'text-2xl font-semibold',
                        name === 'h4' && 'text-xl font-semibold',
                        name === 'h5' && 'text-lg font-semibold',
                        name === 'h6' && 'text-base font-semibold'
                      )}
                    >
                      {name}: 제목 예시
                    </p>
                    <p className="text-xs text-gray-500">
                      {`${value.fontSize} / ${value.fontWeight} / ${value.lineHeight}`}
                    </p>
                  </div>
                )
              )}

              {Object.entries(tokens.typography.body).map(([name, value]) => (
                <div
                  key={`body-${name}`}
                  className="border-b pb-4"
                >
                  <p
                    className={cn(
                      'mb-2',
                      name === 'default' && 'text-base',
                      name === 'sm' && 'text-sm',
                      name === 'lg' && 'text-lg'
                    )}
                  >
                    Body {name}: 본문 텍스트 예시입니다. 이 텍스트는 본문에
                    사용되는 스타일을 보여줍니다.
                  </p>
                  <p className="text-xs text-gray-500">
                    {`${value.fontSize} / ${value.fontWeight} / ${value.lineHeight}`}
                  </p>
                </div>
              ))}
            </div>
          </section>

          <section
            id="spacing"
            className="mb-12"
          >
            <h2 className="mb-4 text-2xl font-semibold">간격</h2>
            <div className="space-y-4 rounded-lg border p-6">
              {Object.entries(tokens.spacing)
                .filter(
                  ([key]) =>
                    !['button', 'card', 'form', 'section', 'layout'].includes(
                      key
                    )
                )
                .map(([name, value]) => {
                  // 기본 간격 값만 표시 (객체가 아닌 문자열 값)
                  if (typeof value === 'string') {
                    return (
                      <div
                        key={name}
                        className="flex items-center border-b pb-4"
                      >
                        <div
                          className="bg-primary mr-4"
                          style={{ width: value, height: '20px' }}
                        />
                        <div>
                          <p className="font-medium">{name}</p>
                          <p className="text-xs text-gray-500">{value}</p>
                        </div>
                      </div>
                    );
                  }
                  return null;
                })}
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}
