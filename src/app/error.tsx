'use client';

import ErrorBoundary from '@/components/error-boundary';
import { AppError } from '@/lib/errors/base-error';
import { ERROR_CODE } from '@/lib/errors/error-codes';
import { ErrorSeverity, reportError } from '@/lib/errors/error-monitoring';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';

/**
 * 앱 라우터의 에러 페이지
 *
 * 이 컴포넌트는 라우트 세그먼트 내에서 발생하는 에러를 처리합니다.
 * 루트 레이아웃에서 발생하는 에러는 global-error.tsx에서 처리됩니다.
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/error-handling
 */
export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  const pathname = usePathname();

  // 에러 로깅
  useEffect(() => {
    // 에러 모니터링 서비스로 에러 보고
    reportError(error, {
      severity: ErrorSeverity.ERROR,
      context: {
        component: 'AppRouterErrorBoundary',
        pathname,
      },
      tags: {
        type: 'app_router_error',
        location: 'client',
      },
    });
  }, [error, pathname]);

  // AppError로 변환
  const appError =
    error instanceof AppError
      ? error
      : new AppError(
          error.message || '페이지를 표시하는 중 오류가 발생했습니다.',
          ERROR_CODE.UNKNOWN_ERROR,
          { originalError: error },
          false
        );

  return (
    <ErrorBoundary
      error={appError}
      reset={reset}
    />
  );
}
