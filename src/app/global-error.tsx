'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AppError } from '@/lib/errors/base-error';
import { ERROR_CODE } from '@/lib/errors/error-codes';
import { ErrorSeverity, reportError } from '@/lib/errors/error-monitoring';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { useEffect } from 'react';

/**
 * 글로벌 에러 페이지
 *
 * 이 컴포넌트는 루트 레이아웃에서 발생하는 에러를 처리합니다.
 * 일반적인 라우트 세그먼트 에러는 error.tsx에서 처리됩니다.
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/error-handling
 */
export default function GlobalError({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  // 에러 로깅
  useEffect(() => {
    // 에러 모니터링 서비스로 에러 보고
    reportError(error, {
      severity: ErrorSeverity.FATAL, // 루트 레이아웃 에러는 FATAL로 처리
      context: {
        component: 'GlobalErrorBoundary',
      },
      tags: {
        type: 'root_layout_error',
        location: 'client',
      },
    });
  }, [error]);

  // AppError로 변환
  const appError =
    error instanceof AppError
      ? error
      : new AppError(
          error.message || '애플리케이션에 심각한 오류가 발생했습니다.',
          ERROR_CODE.UNKNOWN_ERROR,
          { originalError: error },
          false
        );

  return (
    <html>
      <body>
        <div className="bg-background flex min-h-screen items-center justify-center p-4">
          <Card className="mx-auto max-w-md shadow-lg">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <CardTitle className="text-xl font-semibold">
                심각한 오류가 발생했습니다
              </CardTitle>
              <CardDescription>
                애플리케이션을 로드하는 중 문제가 발생했습니다.
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="bg-muted text-muted-foreground rounded-md p-4 text-sm">
                <p className="font-mono">{appError.message}</p>
              </div>
            </CardContent>

            <CardFooter className="flex justify-center">
              <Button
                onClick={reset}
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-4 w-4" />
                <span>다시 시도</span>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </body>
    </html>
  );
}
