import { Metadata } from "next";
import { notFound } from "next/navigation";
import { auth } from "@/auth";
import { getJobPost } from "@/actions/job/jobpost";
import JobPostDetail from "@/components/job/JobPostDetail";
import { getTranslations } from "next-intl/server";
import { constructMetadata } from "@/lib/metadata";
import {
  generateJobPostingSchema,
  generateOrganizationSchema,
  generateBreadcrumbSchema,
} from "@/lib/schema";
import { MultipleStructuredData } from "@/components/structured-data";

interface JobPostPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({
  params,
}: JobPostPageProps): Promise<Metadata> {
  const t = await getTranslations("JobsPage");
  const result = await getJobPost({ id: params.id });

  if (!result.success) {
    return constructMetadata({
      title: t("notFound.title", {
        fallback: "구인·구직 정보를 찾을 수 없습니다",
      }),
      description: t("notFound.description", {
        fallback: "요청하신 구인·구직 정보를 찾을 수 없습니다.",
      }),
      noIndex: true,
    });
  }

  const jobPost = result.data;
  const plainDescription = jobPost.description
    .substring(0, 160)
    .replace(/<[^>]*>/g, "");

  return constructMetadata({
    title: jobPost.title,
    description: plainDescription,
    image: jobPost.image || "/images/jobs-og-image.jpg",
    openGraph: {
      type: "article",
      publishedTime: jobPost.createdAt,
      modifiedTime: jobPost.updatedAt,
      authors: [jobPost.author.name],
      section: "Jobs",
      tags: [jobPost.jobType, jobPost.location, "job", "career"],
    },
  });
}

export default async function JobPostPage({ params }: JobPostPageProps) {
  // 현재 사용자 세션 가져오기
  const session = await auth();

  // 구인·구직 정보 조회
  const result = await getJobPost({ id: params.id });

  if (!result.success) {
    notFound();
  }

  const jobPost = result.data;

  // 작성자 또는 관리자 여부 확인
  const isAuthor = session?.user?.id === jobPost.author.id;
  const isAdmin = session?.user?.isAdmin === true;

  // 구조화된 데이터 생성
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://sodamm.com";
  const jobUrl = `${baseUrl}/jobs/${jobPost.id}`;

  // 구인 정보 스키마
  const jobPostingSchema = generateJobPostingSchema({
    title: jobPost.title,
    description: jobPost.description.replace(/<[^>]*>/g, ""), // HTML 태그 제거
    url: jobUrl,
    datePosted: jobPost.createdAt,
    validThrough: jobPost.expiresAt,
    employmentType: mapJobTypeToSchemaType(jobPost.jobType),
    hiringOrganization: {
      name: jobPost.companyName || jobPost.author.name,
      url: jobPost.companyUrl || undefined,
    },
    jobLocation: {
      addressLocality: jobPost.location,
      addressCountry: "KR",
    },
    ...(jobPost.salary && {
      baseSalary: {
        currency: "KRW",
        value: jobPost.salary,
        unitText: "MONTH",
      },
    }),
    skills: jobPost.skills,
    experienceRequirements: mapExperienceLevelToText(jobPost.experienceLevel),
  });

  // 조직 스키마
  const organizationSchema = generateOrganizationSchema();

  // 빵 부스러기 스키마
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: "홈", url: baseUrl },
    { name: "구인·구직", url: `${baseUrl}/jobs` },
    { name: jobPost.title, url: jobUrl },
  ]);

  return (
    <div className="container py-8">
      <MultipleStructuredData
        dataArray={[jobPostingSchema, organizationSchema, breadcrumbSchema]}
      />
      <JobPostDetail jobPost={jobPost} isAuthor={isAuthor} isAdmin={isAdmin} />
    </div>
  );
}

/**
 * 구인 정보 유형을 Schema.org 고용 유형으로 변환
 */
function mapJobTypeToSchemaType(jobType: string): string {
  const typeMap: Record<string, string> = {
    FULL_TIME: "FULL_TIME",
    PART_TIME: "PART_TIME",
    CONTRACT: "CONTRACTOR",
    INTERNSHIP: "INTERN",
    OTHER: "OTHER",
  };

  return typeMap[jobType] || "OTHER";
}

/**
 * 경력 수준을 텍스트로 변환
 */
function mapExperienceLevelToText(level?: string): string {
  const levelMap: Record<string, string> = {
    ENTRY: "신입 (경력 없음)",
    JUNIOR: "주니어 (1-3년 경력)",
    MID: "미드레벨 (3-5년 경력)",
    SENIOR: "시니어 (5년 이상 경력)",
    EXECUTIVE: "임원급 (10년 이상 경력)",
  };

  return level ? levelMap[level] || "" : "";
}
