import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '@/auth';
import JobPostForm from '@/components/job/JobPostForm';
import { prisma } from '@/lib/prisma';

export const metadata: Metadata = {
  title: '구인·구직 정보 등록 | SODAMM',
  description: '새로운 구인·구직 정보를 등록합니다.',
};

export default async function CreateJobPostPage() {
  // 현재 사용자 세션 가져오기
  const session = await auth();
  
  // 로그인하지 않은 경우 로그인 페이지로 리다이렉트
  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/jobs/create');
  }
  
  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });
  
  return (
    <div className="container py-8">
      <div className="space-y-4 mb-8">
        <h1 className="text-3xl font-bold">구인·구직 정보 등록</h1>
        <p className="text-muted-foreground">
          새로운 구인·구직 정보를 등록합니다.
        </p>
      </div>
      
      <JobPostForm categories={categories} />
    </div>
  );
}
