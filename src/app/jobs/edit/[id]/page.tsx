import { Metadata } from 'next';
import { redirect, notFound } from 'next/navigation';
import { auth } from '@/auth';
import { getJobPost } from '@/actions/job/jobpost';
import JobPostForm from '@/components/job/JobPostForm';
import { prisma } from '@/lib/prisma';

interface EditJobPostPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: EditJobPostPageProps): Promise<Metadata> {
  const result = await getJobPost({ id: params.id });
  
  if (!result.success) {
    return {
      title: '구인·구직 정보를 찾을 수 없습니다 | SODAMM',
    };
  }
  
  const jobPost = result.data;
  
  return {
    title: `${jobPost.title} 수정 | SODAMM`,
    description: '구인·구직 정보를 수정합니다.',
  };
}

export default async function EditJobPostPage({ params }: EditJobPostPageProps) {
  // 현재 사용자 세션 가져오기
  const session = await auth();
  
  // 로그인하지 않은 경우 로그인 페이지로 리다이렉트
  if (!session?.user) {
    redirect(`/auth/signin?callbackUrl=/jobs/edit/${params.id}`);
  }
  
  // 구인·구직 정보 조회
  const result = await getJobPost({ id: params.id });
  
  if (!result.success) {
    notFound();
  }
  
  const jobPost = result.data;
  
  // 작성자 또는 관리자만 수정 가능
  const isAuthor = session.user.id === jobPost.author.id;
  const isAdmin = session.user.isAdmin === true;
  
  if (!isAuthor && !isAdmin) {
    redirect(`/jobs/${params.id}`);
  }
  
  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: 'asc',
    },
  });
  
  return (
    <div className="container py-8">
      <div className="space-y-4 mb-8">
        <h1 className="text-3xl font-bold">구인·구직 정보 수정</h1>
        <p className="text-muted-foreground">
          구인·구직 정보를 수정합니다.
        </p>
      </div>
      
      <JobPostForm jobPost={jobPost} categories={categories} />
    </div>
  );
}
