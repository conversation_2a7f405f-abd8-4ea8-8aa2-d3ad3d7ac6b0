import { Metadata } from "next";
import { auth } from "@/auth";
import { getJobPosts } from "@/actions/job/jobpost";
import JobPostList from "@/components/job/JobPostList";
import { prisma } from "@/lib/prisma";
import { getTranslations } from "next-intl/server";
import { constructMetadata } from "@/lib/metadata";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("JobsPage");

  return constructMetadata({
    title: t("metaTitle", { fallback: "구인·구직 정보 | Sodamm" }),
    description: t("metaDescription", {
      fallback:
        "한국에서의 구인·구직 정보를 확인하고 등록할 수 있습니다. 외국인 근로자를 위한 다양한 일자리 정보를 제공합니다.",
    }),
    image: "/images/jobs-og-image.jpg",
  });
}

interface JobsPageProps {
  searchParams: {
    page?: string;
    limit?: string;
    type?: string;
    jobType?: string;
    categoryId?: string;
    location?: string;
    query?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default async function JobsPage({ searchParams }: JobsPageProps) {
  // 현재 사용자 세션 가져오기
  const session = await auth();
  const isAuthenticated = !!session?.user;

  // 검색 파라미터 처리
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const limit = searchParams.limit ? parseInt(searchParams.limit) : 12;

  // 구인·구직 정보 목록 조회
  const result = await getJobPosts({
    page,
    limit,
    type: searchParams.type as any,
    jobType: searchParams.jobType as any,
    categoryId: searchParams.categoryId,
    location: searchParams.location,
    query: searchParams.query,
    sortBy: (searchParams.sortBy as any) || "createdAt",
    sortOrder: (searchParams.sortOrder as any) || "desc",
  });

  // 카테고리 목록 조회
  const categories = await prisma.category.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      order: "asc",
    },
  });

  const { data, success } = result;
  const { items: jobPosts, total } = success ? data : { items: [], total: 0 };

  return (
    <div className="container py-8">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">구인·구직 정보</h1>
        <p className="text-muted-foreground">
          한국에서의 구인·구직 정보를 확인하고 등록할 수 있습니다.
        </p>
      </div>

      <div className="mt-8">
        <JobPostList
          jobPosts={jobPosts}
          categories={categories}
          total={total}
          page={page}
          limit={limit}
          isAuthenticated={isAuthenticated}
        />
      </div>
    </div>
  );
}
