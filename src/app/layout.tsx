import type { Metadata, Viewport } from 'next';

// Import from @next/third-parties when ready to implement
// import { GoogleAnalytics } from '@next/third-parties/google';

import MainLayout from '@/components/community/layout/main-layout';
import { pretendard } from '@/lib/fonts';
import { cn } from '@/lib/utils';
import { getLocale, getMessages, getTimeZone } from 'next-intl/server';
import './globals.css';
import { Providers } from './providers';

export const metadata: Metadata = {
  title: 'SODAMM - 소통하는 담벼락',
  description:
    '사람들이 자유롭게 생각을 나누고 소통할 수 있는 현대적인 커뮤니티 플랫폼',
  openGraph: {
    title: 'SODAMM - 소통하는 담벼락',
    description:
      '사람들이 자유롭게 생각을 나누고 소통할 수 있는 현대적인 커뮤니티 플랫폼',
    type: 'website',
    locale: 'ko_KR',
    siteName: 'SODAMM',
    // 오픈 그래프 이미지 최적화
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'SODAMM - 소통하는 담벼락',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SODAMM - 소통하는 담벼락',
    description:
      '사람들이 자유롭게 생각을 나누고 소통할 수 있는 현대적인 커뮤니티 플랫폼',
    // 트위터 이미지 최적화
    images: ['/images/twitter-image.jpg'],
  },
  // 리소스 힌트 추가
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32' },
      { url: '/icon.png', sizes: '192x192' },
    ],
    apple: [{ url: '/apple-icon.png', sizes: '180x180' }],
  },
  // 매니페스트 추가
  manifest: '/manifest.json',
};

export function generateViewport(): Viewport {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: '#ffffff',
    // 이미지 최적화를 위한 뷰포트 설정
    viewportFit: 'cover',
  };
}

export default async function RootLayout({
  children,
  modal,
}: Readonly<{
  children: React.ReactNode;
  modal?: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();
  const timeZone = await getTimeZone();

  return (
    <html
      lang={locale}
      className="h-full"
      suppressHydrationWarning
    >
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0"
        />
      </head>

      <body
        className={cn(
          pretendard.variable,
          'bg-background min-h-screen font-sans antialiased'
        )}
      >
        <Providers
          locale={locale}
          messages={messages}
          timeZone={timeZone}
        >
          <MainLayout>{children}</MainLayout>
          {modal}
        </Providers>
        {/* Uncomment and add your Google Analytics ID when ready to implement */}
        {/* <GoogleAnalytics gaId="GA-MEASUREMENT-ID" /> */}
      </body>
    </html>
  );
}
