'use client';

// import { signOut } from '@/auth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
// import { meApiClient } from '@/lib/api/client/me-api'; // 제거
import { updateUserProfile } from '@/actions/users'; // 서버 액션 import 추가
import { useUser } from '@/hooks/use-user'; // useUser 훅 추가
import { supabase } from '@/lib/supabase/client';
import { toast } from 'sonner'; // toast 추가 (에러/성공 메시지 용)

import BackButton from '@/components/back-button';
import UserPostList from '@/components/community/me/user-post-list';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useRef, useState, useTransition } from 'react'; // useTransition 추가

export default function MePage() {
  const { data: session, status, update: sessionUpdate } = useSession();
  const router = useRouter();
  const [isPending, startTransition] = useTransition(); // 서버 액션 로딩 상태

  // useUser 커스텀 훅을 사용하여 사용자 정보 가져오기
  const { user: userData, isLoading: isLoadingUser } = useUser();

  // SWR을 사용하여 사용자 게시글 가져오기
  // const {
  //   data: userPosts,
  //   error: postsError,
  //   isLoading: isLoadingPosts,
  // } = useSWR<PostWithAuthorAndCounts[]>(
  //   // userData가 있을 때만 SWR 키를 생성하여 요청 시작
  //   userData?.id ? ['user-posts', userData.id] : null,
  //   // fetcher 함수의 파라미터 타입을 명시적으로 지정 [string, string]
  //   async ([, userId]: [string, string]) => getUserPosts(userId, { take: 10 })
  // );

  const [preview, setPreview] = useState<string | null>(
    session?.user?.image || null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploading, setUploading] = useState(false); // Supabase 업로드 상태는 별도 유지

  // 이름, 표시 이름 상태는 유지
  const [editingName, setEditingName] = useState(false);
  const [nameInput, setNameInput] = useState(session?.user?.name || '');
  const [editingDisplayName, setEditingDisplayName] = useState(false);
  const [displayNameInput, setDisplayNameInput] = useState(
    session?.user?.displayName || ''
  );

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  // 프로필 업데이트 처리 함수 (서버 액션 호출)
  const handleProfileUpdate = async (formData: FormData) => {
    startTransition(async () => {
      try {
        const result = await updateUserProfile(formData);
        if (result.success && result.user) {
          // 서버 액션 성공 시 세션 업데이트
          await sessionUpdate?.({
            user: {
              ...session?.user, // 기존 세션 정보 유지
              ...result.user, // 업데이트된 정보 병합 (name, displayName, image)
            },
          });
          // 상태 업데이트 (즉시 반영 위함)
          if (formData.has('name')) setNameInput(result.user.name ?? '');
          if (formData.has('displayName'))
            setDisplayNameInput(result.user.displayName ?? '');
          if (formData.has('imageUrl')) setPreview(result.user.image ?? null);
          toast.success('프로필이 업데이트되었습니다.');

          // 인풋 필드 수정 상태 종료
          if (formData.has('name')) setEditingName(false);
          if (formData.has('displayName')) setEditingDisplayName(false);
        } else {
          // result.success가 false인 경우 (throw 되지 않은 예상된 실패 케이스)
          toast.error('프로필 업데이트에 실패했습니다.');
        }
      } catch (error) {
        // 서버 액션에서 throw된 에러 처리
        console.error('Profile update error:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : '알 수 없는 오류가 발생했습니다.';
        toast.error(`프로필 업데이트 실패: ${errorMessage}`);
        // 실패 시 원래 값으로 복구 (옵션)
        if (formData.has('name')) setNameInput(session?.user?.name || '');
        if (formData.has('displayName'))
          setDisplayNameInput(session?.user?.displayName || '');
      }
    });
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !session?.user) return;
    setUploading(true); // Supabase 업로드 시작
    try {
      const user = session.user;
      const imageBucketName = 'images';
      const ext = file.name.split('.').pop();
      const fileName = `users/avatar/${user.id}_${Date.now()}.${ext}`;

      const { error: uploadError } = await supabase.storage
        .from(imageBucketName)
        .upload(fileName, file, { upsert: true });

      if (uploadError) {
        toast.error(`이미지 업로드 실패: ${uploadError.message}`);
        setUploading(false);
        return;
      }

      const { data: urlData } = supabase.storage
        .from(imageBucketName)
        .getPublicUrl(fileName);

      if (urlData?.publicUrl) {
        // Supabase 업로드 성공 후 서버 액션 호출하여 DB 업데이트
        const formData = new FormData();
        formData.append('imageUrl', urlData.publicUrl);
        await handleProfileUpdate(formData); // 서버 액션 호출
      } else {
        toast.error('이미지 URL을 가져오는데 실패했습니다.');
      }
    } catch (err) {
      console.error('Image upload/update error:', err);
      toast.error('이미지 처리 중 오류 발생');
    } finally {
      setUploading(false); // Supabase 업로드 종료
    }
  };

  if (status === 'loading' || isLoadingUser) {
    return (
      <div className="container mx-auto flex min-h-[40vh] items-center justify-center p-8">
        <span className="text-muted-foreground">로딩 중...</span>
      </div>
    );
  }

  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className="container mx-auto flex min-h-[40vh] flex-col items-center justify-center gap-6 p-8">
        <div className="w-full max-w-md text-center">
          <div className="text-2xl font-bold">로그인이 필요합니다</div>
          <div className="text-muted-foreground mb-4">
            내 정보를 확인하려면 로그인 해주세요.
          </div>
          <Button onClick={() => router.push('/auth/signin?callbackUrl=/me')}>
            로그인 하러 가기
          </Button>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="container mx-auto flex min-h-[40vh] items-center justify-center p-8">
        <span className="text-muted-foreground">
          사용자 정보를 불러올 수 없습니다.
        </span>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-4 flex justify-between">
        <BackButton className="inline-block text-sm hover:underline" />
        <div className="flex justify-end">
          <SignOutButton />
        </div>
      </div>

      <div className="hover:border-primary cursor-pointer rounded-xl border-2 border-zinc-300 bg-zinc-100 p-5 shadow-sm transition-colors hover:shadow-lg dark:border-zinc-700 dark:bg-zinc-800">
        <div className="w-full max-w-md">
          <div className="flex flex-col gap-2">
            <div
              onClick={handleAvatarClick}
              className="relative inline-block cursor-pointer" // relative 추가
            >
              <Avatar className="h-20 w-20">
                <AvatarImage
                  src={preview || userData.image || undefined} // userData 사용
                  alt={nameInput || displayNameInput || 'user'} // 상태 사용
                />
                <AvatarFallback>
                  {nameInput
                    ? nameInput[0]
                    : displayNameInput
                      ? displayNameInput[0]
                      : userData.name
                        ? userData.name[0]
                        : 'U'}
                </AvatarFallback>
                {(uploading || isPending) && ( // 업로드 중 또는 서버 액션 처리 중 표시
                  <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/40 text-xs text-white">
                    {uploading ? '업로드 중...' : '저장 중...'}
                  </div>
                )}
              </Avatar>
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileChange}
                disabled={uploading || isPending} // 처리 중 비활성화
              />
            </div>
            <div className="mt-8">
              {editingDisplayName ? (
                <Input
                  className="w-full bg-transparent text-sm outline-none"
                  value={displayNameInput}
                  autoFocus
                  disabled={isPending} // 액션 처리 중 비활성화
                  onChange={(e) => setDisplayNameInput(e.target.value)}
                  onKeyDown={async (e) => {
                    if (e.key === 'Enter' && !isPending) {
                      if (
                        displayNameInput &&
                        displayNameInput !== session?.user?.displayName
                      ) {
                        const formData = new FormData();
                        formData.append('displayName', displayNameInput);
                        await handleProfileUpdate(formData);
                      } else {
                        // 변경 없으면 그냥 닫기
                        setEditingDisplayName(false);
                      }
                    } else if (e.key === 'Escape') {
                      // Esc 누르면 원래 값으로 복구하고 닫기
                      setDisplayNameInput(session?.user?.displayName || '');
                      setEditingDisplayName(false);
                    }
                  }}
                  onBlur={() => {
                    // 포커스 잃으면 원래 값으로 복구하고 닫기 (옵션)
                    // Enter로만 저장하게 하려면 이 로직 제거
                    // setDisplayNameInput(session?.user?.displayName || '');
                    // setEditingDisplayName(false);
                  }}
                />
              ) : (
                <div
                  className="font-base cursor-pointer truncate text-sm hover:underline"
                  onClick={() => !isPending && setEditingDisplayName(true)} // 처리 중 클릭 방지
                  title="클릭해서 별명 수정"
                >
                  {displayNameInput || userData.displayName || '별명 없음'}
                </div>
              )}
              <div className="text-md overflow-hidden font-black">
                {editingName ? (
                  <Input
                    className="w-full bg-transparent text-lg font-bold outline-none"
                    value={nameInput}
                    autoFocus
                    disabled={isPending} // 액션 처리 중 비활성화
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      const newValue = e.target.value;
                      // 영어 알파벳과 숫자만 허용하는 정규식
                      const regex = new RegExp('^[a-zA-Z0-9]*$');
                      if (regex.test(newValue) || newValue === '') {
                        setNameInput(newValue);
                      }
                    }}
                    onKeyDown={async (e) => {
                      if (e.key === 'Enter' && !isPending) {
                        if (nameInput && nameInput !== session?.user?.name) {
                          const formData = new FormData();
                          formData.append('name', nameInput);
                          await handleProfileUpdate(formData);
                        } else {
                          // 변경 없으면 그냥 닫기
                          setEditingName(false);
                        }
                      } else if (e.key === 'Escape') {
                        // Esc 누르면 원래 값으로 복구하고 닫기
                        setNameInput(session?.user?.name || '');
                        setEditingName(false);
                      }
                    }}
                    onBlur={() => {
                      // 포커스 잃으면 원래 값으로 복구하고 닫기 (옵션)
                      // Enter로만 저장하게 하려면 이 로직 제거
                      // setNameInput(session?.user?.name || '');
                      // setEditingName(false);
                    }}
                  />
                ) : (
                  <div
                    className="cursor-pointer truncate text-sm hover:underline"
                    onClick={() => !isPending && setEditingName(true)} // 처리 중 클릭 방지
                    title="클릭해서 이름 수정"
                  >
                    @{nameInput || userData.name || userData.id}
                  </div>
                )}
              </div>

              <div className="mt-2 flex items-center space-x-4 text-sm text-zinc-600 dark:text-zinc-400">
                <div>
                  <span className="font-medium">{userData.postCount || 0}</span>{' '}
                  Post
                </div>
                <div>
                  <span className="font-medium">
                    {userData.postCommentCount || 0}
                  </span>{' '}
                  댓글
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-3">
        <Tabs
          defaultValue="posts"
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="posts">게시글</TabsTrigger>
            <TabsTrigger value="comments">댓글</TabsTrigger>
          </TabsList>
          <TabsContent value="posts">
            {userData?.id ? (
              <UserPostList userId={userData.id} />
            ) : (
              <p className="text-muted-foreground mt-4">
                사용자 정보를 불러오는 중...
              </p>
            )}
          </TabsContent>
          <TabsContent value="comments">
            <p className="text-muted-foreground">
              작성한 댓글이 여기에 표시됩니다.
            </p>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
