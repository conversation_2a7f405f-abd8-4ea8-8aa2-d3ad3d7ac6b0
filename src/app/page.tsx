'use client';

import { HomeMenu } from '@/components/home/<USER>';
import { IrregularGrid } from '@/components/home/<USER>';
import { menuItems } from '@/constants/menu-items';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <>
      <div className="relative min-h-screen overflow-hidden bg-[#FDFBF6] p-4 text-black">
        {/* 불규칙한 그리드 라인 - 홈 화면 */}
        <IrregularGrid
          variant="home"
          color="black"
          opacity={5}
        />

        {/* 상단 메뉴 버튼 */}
        <div className="absolute top-4 right-4 z-10">
          <button
            className="text-sm font-black uppercase"
            onClick={toggleMenu}
          >
            Menu
          </button>
        </div>

        {/* 데스크톱 레이아웃 */}
        <div className="hidden md:block">
          {/* 좌측 섹션 */}
          <div className="absolute top-36 left-10">
            <div className="mb-32">
              <h2 className="mb-2 text-lg font-black">WHY WE STARTED SODAMM</h2>
              <p className="max-w-[300px] text-sm">
                Sodamm is a social platform created specifically for foreign
                workers in Korea - a place where community feels like home, not
                just another app. It&apos;s a dedicated space where you can find
                essential information about Korean life, share your experiences,
                and build meaningful connections with others on similar
                journeys.
              </p>
            </div>

            <div>
              <h2 className="mb-2 text-lg font-black">OUR MISSION</h2>
              <p className="max-w-[300px] text-sm">
                Our mission is to connect people and build a better future for
                foreign workers in Korea.
              </p>
            </div>
          </div>

          {/* 하단 푸터 */}
          <div className="absolute bottom-5 w-full">
            <div className="flex justify-between p-5">
              <div className="flex items-center">
                {/* 로고 */}
                <div className="pl-3">
                  <Image
                    src="/logo_only_black.svg"
                    alt="logo"
                    width={400}
                    height={400}
                  />
                </div>

                <div className="pl-8">
                  <p className="text-xs">All rights reserved ©sodamm</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="mr-10 flex flex-col space-y-1 text-right">
                  {menuItems.map((item) => (
                    <Link
                      key={item.name}
                      href={item.path}
                      className="text-xs hover:underline"
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>

                <div className="flex flex-col space-y-1 pr-10 text-right">
                  <Link
                    href="https://www.threads.com"
                    className="text-xs hover:underline"
                  >
                    THREADS
                  </Link>
                  <Link
                    href="https://instagram.com"
                    className="text-xs hover:underline"
                  >
                    INSTAGRAM
                  </Link>
                </div>
                <div className="pr-10 text-right">
                  <p className="text-xs"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 모바일 레이아웃 */}
        <div className="flex min-h-screen flex-col p-5 md:hidden">
          {/* 상단 섹션 */}
          <div className="mt-16">
            <div className="mb-8">
              <h2 className="mb-2 text-sm font-bold">OUR STUDIO</h2>
              <p className="text-sm leading-relaxed">
                Our portfolio is expanding. The range of projects, from polished
                and structured to speculative and experimental, testify to the
                way our creative philosophy permeates every area we work in.
              </p>
            </div>

            <div className="mb-8">
              <h2 className="mb-2 text-sm font-bold">CONTACT US</h2>
              <p className="text-sm leading-relaxed">
                We look forward to hearing from you. Reach out to collaborate
                with us.
              </p>
            </div>
          </div>

          {/* 하단 로고 */}
          <div className="mt-auto">
            <div className="mb-4 flex items-center">
              <Image
                src="/logo_only_black.svg"
                alt="logo"
                width={190}
                height={190}
              />
            </div>
            <div className="flex justify-between">
              <p className="text-xs">All rights reserved ©sodamm</p>
              <div className="flex flex-col space-y-1 text-right">
                <Link
                  href="https://www.threads.com"
                  className="text-xs hover:underline"
                >
                  THREADS
                </Link>
                <Link
                  href="https://instagram.com"
                  className="text-xs hover:underline"
                >
                  INSTAGRAM
                </Link>
              </div>
            </div>
            <div className="mt-2 text-right">
              <p className="text-xs"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      {/* 메뉴 컴포넌트 */}
      <HomeMenu
        isOpen={isMenuOpen}
        onClose={toggleMenu}
      />
    </>
  );
}
