import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { constructMetadata } from '@/lib/metadata';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('Profile');
  
  return constructMetadata({
    title: t('metaTitle', { fallback: '내 프로필 | Sodamm' }),
    description: t('metaDescription', { fallback: '프로필 정보를 관리하고 계정 설정을 변경할 수 있습니다.' }),
    noIndex: true, // 개인 프로필 페이지는 검색 엔진에 노출되지 않도록 설정
  });
}
