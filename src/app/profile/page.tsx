'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import ProfileForm from '@/components/profile/profile-form';
import PasswordForm from '@/components/profile/password-form';
import AccountSettings from '@/components/profile/account-settings';
import UserActivity from '@/components/profile/user-activity';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function ProfilePage() {
  const t = useTranslations('Profile');
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState('profile');

  // 로그인하지 않은 사용자는 로그인 페이지로 리디렉션
  if (status === 'unauthenticated') {
    redirect('/login');
  }

  // 로딩 중일 때 표시할 내용
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container max-w-5xl py-10">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">{t('subtitle')}</p>
        </div>
        <Separator />

        <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="profile">{t('profileTab')}</TabsTrigger>
            <TabsTrigger value="password">{t('passwordTab')}</TabsTrigger>
            <TabsTrigger value="activity">{t('activityTab')}</TabsTrigger>
            <TabsTrigger value="account">{t('accountTab')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('profileInfo')}</CardTitle>
                <CardDescription>{t('profileDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ProfileForm user={session?.user} />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="password" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('changePassword')}</CardTitle>
                <CardDescription>{t('passwordDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <PasswordForm />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('yourActivity')}</CardTitle>
                <CardDescription>{t('activityDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <UserActivity userId={session?.user?.id} />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="account" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('accountSettings')}</CardTitle>
                <CardDescription>{t('accountDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <AccountSettings userId={session?.user?.id} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
