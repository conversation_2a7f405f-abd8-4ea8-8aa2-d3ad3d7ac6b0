'use client';

import { StoreProvider } from '@/components/store-provider';
import ThemeProvider from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { setupGlobalErrorHandlers } from '@/lib/errors/error-monitoring';
import { SessionProvider } from 'next-auth/react';
import { NextIntlClientProvider } from 'next-intl';
import { type ReactNode, useEffect } from 'react';

interface ProvidersProps {
  children: ReactNode;
  locale: string;
  timeZone: string;
  messages: any;
}

/**
 * 애플리케이션 전역 프로바이더 컴포넌트
 * 모든 전역 상태 및 컨텍스트를 제공
 */
export function Providers({
  children,
  locale,
  timeZone,
  messages,
}: ProvidersProps) {
  // 전역 에러 핸들러 설정
  useEffect(() => {
    setupGlobalErrorHandlers();
  }, []);

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange
    >
      <NextIntlClientProvider
        locale={locale}
        messages={messages}
        timeZone={timeZone}
      >
        <SessionProvider>
          {/* Zustand 스토어 프로바이더 추가 */}
          <StoreProvider>
            {children}
            <Toaster position="top-right" />
            {/* 개발 도구 추가 */}
          </StoreProvider>
        </SessionProvider>
      </NextIntlClientProvider>
    </ThemeProvider>
  );
}
