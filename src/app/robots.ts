import { MetadataRoute } from 'next';

/**
 * robots.txt 파일 생성
 * 
 * 검색 엔진 크롤러에게 사이트의 어떤 부분을 크롤링해도 되는지 알려주는 파일입니다.
 */
export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com';
  
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/api/',
        '/admin/',
        '/private/',
        '/profile/',
        '/login/',
        '/signup/',
        '/forgot-password/',
        '/reset-password/',
        '/verify-email/',
        '/dashboard/',
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  };
}
