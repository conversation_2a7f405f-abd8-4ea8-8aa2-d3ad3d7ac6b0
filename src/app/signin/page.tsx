'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function SigninRedirectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';
  const error = searchParams.get('error');

  useEffect(() => {
    // 새로운 URL 생성
    const newUrl = new URL('/auth/signin', window.location.origin);

    // 기존 쿼리 파라미터 유지
    if (callbackUrl) {
      newUrl.searchParams.set('callbackUrl', callbackUrl);
    }
    if (error) {
      newUrl.searchParams.set('error', error);
    }

    // 리디렉션
    router.replace(newUrl.toString());
  }, [router, callbackUrl, error]);

  return (
    <div className="flex h-screen items-center justify-center">
      <p className="text-muted-foreground">리디렉션 중...</p>
    </div>
  );
}
