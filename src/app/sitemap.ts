import { prisma } from '@/lib/prisma';
import { MetadataRoute } from 'next';

interface Post {
  id: string;
  updatedAt: Date;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Get all posts for dynamic routes
  const posts = await prisma.post.findMany({
    select: {
      id: true,
      updatedAt: true,
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });

  // Generate post entries
  const postEntries = posts.map((post: Post) => ({
    url: `https://sodamm.com/community/post/${post.id}`,
    lastModified: post.updatedAt,
    changeFrequency: 'daily' as const,
    priority: 0.7,
  }));

  // Static routes
  const routes = [
    {
      url: 'https://sodamm.com',
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: 'https://sodamm.com/community',
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: 'https://sodamm.com/news',
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: 'https://sodamm.com/job',
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: 'https://sodamm.com/terms-of-service',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.3,
    },
    {
      url: 'https://sodamm.com/privacy-policy',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.3,
    },
  ];

  return [...routes, ...postEntries];
}
