"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption } from "@/components/ui/table"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

// 샘플 데이터
const users = [
  {
    id: 1,
    name: "김철수",
    email: "<EMAIL>",
    role: "관리자",
    status: "활성",
    lastLogin: "2023-10-15",
    avatar: "",
  },
  {
    id: 2,
    name: "이영희",
    email: "<EMAIL>",
    role: "사용자",
    status: "활성",
    lastLogin: "2023-10-14",
    avatar: "",
  },
  {
    id: 3,
    name: "박지민",
    email: "<EMAIL>",
    role: "사용자",
    status: "비활성",
    lastLogin: "2023-09-20",
    avatar: "",
  },
  {
    id: 4,
    name: "최민수",
    email: "<EMAIL>",
    role: "편집자",
    status: "활성",
    lastLogin: "2023-10-12",
    avatar: "",
  },
]

export default function DataDisplayPage() {
  const [loading, setLoading] = useState(false)

  const toggleLoading = () => {
    setLoading(!loading)
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">데이터 표시 컴포넌트</h1>

      <div className="space-y-10">
        {/* 카드 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">카드 컴포넌트</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>사용자 통계</CardTitle>
                <CardDescription>지난 30일 동안의 사용자 활동</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">2,345</p>
                <p className="text-sm text-muted-foreground">
                  전월 대비 +15% 증가
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm">
                  자세히 보기
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>게시글 통계</CardTitle>
                <CardDescription>지난 30일 동안의 게시글 활동</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">1,234</p>
                <p className="text-sm text-muted-foreground">
                  전월 대비 +8% 증가
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm">
                  자세히 보기
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>댓글 통계</CardTitle>
                <CardDescription>지난 30일 동안의 댓글 활동</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">5,678</p>
                <p className="text-sm text-muted-foreground">
                  전월 대비 +20% 증가
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm">
                  자세히 보기
                </Button>
              </CardFooter>
            </Card>
          </div>
        </section>

        {/* 테이블 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">테이블 컴포넌트</h2>
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableCaption>사용자 목록</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>사용자</TableHead>
                    <TableHead>이메일</TableHead>
                    <TableHead>역할</TableHead>
                    <TableHead>상태</TableHead>
                    <TableHead>마지막 로그인</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Avatar>
                            <AvatarImage src={user.avatar} alt={user.name} />
                            <AvatarFallback>
                              {user.name.substring(0, 2)}
                            </AvatarFallback>
                          </Avatar>
                          <span>{user.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge variant={user.role === "관리자" ? "default" : "secondary"}>
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.status === "활성" ? "success" : "destructive"}>
                          {user.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{user.lastLogin}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                <TableFooter>
                  <TableRow>
                    <TableCell colSpan={5}>총 {users.length}명의 사용자</TableCell>
                  </TableRow>
                </TableFooter>
              </Table>
            </CardContent>
          </Card>
        </section>

        {/* 아바타 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">아바타 컴포넌트</h2>
          <Card>
            <CardContent className="flex flex-wrap gap-4 p-6">
              <Avatar className="h-16 w-16">
                <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
              <Avatar className="h-12 w-12">
                <AvatarImage src="" alt="@example" />
                <AvatarFallback>EX</AvatarFallback>
              </Avatar>
              <Avatar className="h-10 w-10">
                <AvatarFallback>KR</AvatarFallback>
              </Avatar>
              <Avatar className="h-8 w-8">
                <AvatarFallback>JP</AvatarFallback>
              </Avatar>
            </CardContent>
          </Card>
        </section>

        {/* 배지 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">배지 컴포넌트</h2>
          <Card>
            <CardContent className="flex flex-wrap gap-4 p-6">
              <Badge>기본</Badge>
              <Badge variant="secondary">보조</Badge>
              <Badge variant="outline">외곽선</Badge>
              <Badge variant="destructive">위험</Badge>
              <Badge variant="success">성공</Badge>
              <Badge variant="warning">경고</Badge>
              <Badge variant="info">정보</Badge>
            </CardContent>
          </Card>
        </section>

        {/* 스켈레톤 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">스켈레톤 컴포넌트</h2>
          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between">
                <span>스켈레톤 로딩 상태</span>
                <Button onClick={toggleLoading}>
                  {loading ? "로딩 중지" : "로딩 시작"}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                <>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                  <Skeleton className="h-[125px] w-full rounded-xl" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                </>
              ) : (
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">콘텐츠 제목</h3>
                    <p className="text-muted-foreground">콘텐츠 설명 텍스트</p>
                  </div>
                  <div className="h-[125px] w-full rounded-xl bg-secondary flex items-center justify-center">
                    이미지 또는 차트
                  </div>
                  <div>
                    <p>추가 콘텐츠 내용입니다.</p>
                    <p>더 많은 텍스트가 여기에 표시됩니다.</p>
                    <p>마지막 줄의 텍스트입니다.</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
