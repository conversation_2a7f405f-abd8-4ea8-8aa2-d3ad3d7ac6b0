"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Info, AlertCircle, Check, MoreVertical } from "lucide-react"

export default function FeedbackNavigationPage() {
  const [open, setOpen] = useState(false)

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">피드백 및 네비게이션 컴포넌트</h1>

      <div className="space-y-10">
        {/* 모달 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">모달 컴포넌트</h2>
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="flex flex-wrap gap-4">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>기본 모달 열기</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>모달 제목</DialogTitle>
                      <DialogDescription>
                        이것은 기본 모달 컴포넌트입니다. 다양한 콘텐츠를 표시할 수 있습니다.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <p>모달 내용이 여기에 표시됩니다.</p>
                    </div>
                    <DialogFooter>
                      <Button variant="outline">취소</Button>
                      <Button>확인</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Dialog open={open} onOpenChange={setOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline">제어된 모달 열기</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>제어된 모달</DialogTitle>
                      <DialogDescription>
                        이 모달은 상태로 제어됩니다.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <p>상태를 통해 모달을 프로그래밍 방식으로 제어할 수 있습니다.</p>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setOpen(false)}>
                        취소
                      </Button>
                      <Button onClick={() => {
                        setOpen(false)
                        toast.success("모달에서 작업이 완료되었습니다!")
                      }}>
                        확인
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* 토스트 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">토스트 컴포넌트</h2>
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="flex flex-wrap gap-4">
                <Button
                  onClick={() =>
                    toast.success("성공!", {
                      description: "작업이 성공적으로 완료되었습니다.",
                    })
                  }
                >
                  성공 토스트
                </Button>
                <Button
                  variant="destructive"
                  onClick={() =>
                    toast.error("오류!", {
                      description: "작업 중 오류가 발생했습니다.",
                    })
                  }
                >
                  오류 토스트
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    toast.info("정보", {
                      description: "참고할 정보가 있습니다.",
                    })
                  }
                >
                  정보 토스트
                </Button>
                <Button
                  variant="secondary"
                  onClick={() =>
                    toast.warning("경고", {
                      description: "주의가 필요한 상황입니다.",
                    })
                  }
                >
                  경고 토스트
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* 탭 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">탭 컴포넌트</h2>
          <Card>
            <CardContent className="p-6">
              <Tabs defaultValue="account" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="account">계정</TabsTrigger>
                  <TabsTrigger value="password">비밀번호</TabsTrigger>
                  <TabsTrigger value="settings">설정</TabsTrigger>
                </TabsList>
                <TabsContent value="account" className="p-4 border rounded-md mt-4">
                  <h3 className="text-lg font-medium">계정 설정</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    계정 정보를 관리하고 업데이트할 수 있습니다.
                  </p>
                </TabsContent>
                <TabsContent value="password" className="p-4 border rounded-md mt-4">
                  <h3 className="text-lg font-medium">비밀번호 변경</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    계정 비밀번호를 업데이트할 수 있습니다.
                  </p>
                </TabsContent>
                <TabsContent value="settings" className="p-4 border rounded-md mt-4">
                  <h3 className="text-lg font-medium">일반 설정</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    알림, 테마 및 기타 설정을 관리할 수 있습니다.
                  </p>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </section>

        {/* 아코디언 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">아코디언 컴포넌트</h2>
          <Card>
            <CardContent className="p-6">
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>소담(SODAMM)이란 무엇인가요?</AccordionTrigger>
                  <AccordionContent>
                    소담(SODAMM)은 한국에 거주하는 외국인 근로자들이 한국 생활에 더 쉽고 빠르게 적응할 수 있도록 지원하는 온라인 커뮤니티 플랫폼입니다.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger>어떤 정보를 제공하나요?</AccordionTrigger>
                  <AccordionContent>
                    생활 정보, 한국 공공 서비스 및 편의 서비스 이용 안내, 정부 제공 외국인 근로자 지원 정보, 구인·구직 정보 등을 통합적으로 제공합니다.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger>어떻게 가입할 수 있나요?</AccordionTrigger>
                  <AccordionContent>
                    회원 가입 페이지에서 이메일 또는 소셜 로그인을 통해 간편하게 가입할 수 있습니다. 가입 후 프로필을 설정하고 커뮤니티 활동을 시작할 수 있습니다.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
        </section>

        {/* 드롭다운 메뉴 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">드롭다운 메뉴 컴포넌트</h2>
          <Card>
            <CardContent className="p-6 flex flex-wrap gap-8">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">메뉴 열기</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>내 계정</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>프로필</DropdownMenuItem>
                  <DropdownMenuItem>설정</DropdownMenuItem>
                  <DropdownMenuItem>알림</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>로그아웃</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>편집</DropdownMenuItem>
                  <DropdownMenuItem>복제</DropdownMenuItem>
                  <DropdownMenuItem>공유</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-destructive">삭제</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardContent>
          </Card>
        </section>

        {/* 알림 컴포넌트 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">알림 컴포넌트</h2>
          <Card>
            <CardContent className="p-6 space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>정보</AlertTitle>
                <AlertDescription>
                  일반적인 정보를 제공하는 알림입니다.
                </AlertDescription>
              </Alert>

              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>오류</AlertTitle>
                <AlertDescription>
                  중요한 오류나 경고를 표시하는 알림입니다.
                </AlertDescription>
              </Alert>

              <Alert className="bg-green-50 border-green-500 dark:bg-green-900/20">
                <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                <AlertTitle className="text-green-600 dark:text-green-400">성공</AlertTitle>
                <AlertDescription className="text-green-700 dark:text-green-300">
                  작업이 성공적으로 완료되었음을 알리는 알림입니다.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
