"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"

export default function UITestPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
    agreeTerms: false,
    notificationPreference: "email",
    darkMode: false,
    country: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    toast.success("폼이 제출되었습니다!", {
      description: "입력하신 데이터가 성공적으로 처리되었습니다.",
    })
    console.log(formData)
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">UI 컴포넌트 테스트</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>폼 요소 컴포넌트</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">이름</Label>
              <Input
                id="name"
                placeholder="이름을 입력하세요"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">이메일</Label>
              <Input
                id="email"
                type="email"
                placeholder="이메일을 입력하세요"
                value={formData.email}
                onChange={(e) =>
                  setFormData({ ...formData, email: e.target.value })
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">메시지</Label>
              <Textarea
                id="message"
                placeholder="메시지를 입력하세요"
                value={formData.message}
                onChange={(e) =>
                  setFormData({ ...formData, message: e.target.value })
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">국가</Label>
              <Select
                value={formData.country}
                onValueChange={(value) =>
                  setFormData({ ...formData, country: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="국가를 선택하세요" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kr">대한민국</SelectItem>
                  <SelectItem value="us">미국</SelectItem>
                  <SelectItem value="jp">일본</SelectItem>
                  <SelectItem value="cn">중국</SelectItem>
                  <SelectItem value="vn">베트남</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>알림 설정</Label>
              <RadioGroup
                value={formData.notificationPreference}
                onValueChange={(value) =>
                  setFormData({
                    ...formData,
                    notificationPreference: value,
                  })
                }
                className="flex flex-col space-y-1"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="email" id="r1" />
                  <Label htmlFor="r1">이메일</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="sms" id="r2" />
                  <Label htmlFor="r2">SMS</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="push" id="r3" />
                  <Label htmlFor="r3">푸시 알림</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={formData.agreeTerms}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    agreeTerms: checked as boolean,
                  })
                }
              />
              <Label htmlFor="terms">이용약관에 동의합니다</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="dark-mode"
                checked={formData.darkMode}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, darkMode: checked })
                }
              />
              <Label htmlFor="dark-mode">다크 모드</Label>
            </div>

            <Button type="submit">제출하기</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
