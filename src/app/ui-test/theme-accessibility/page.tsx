"use client"

import { useState } from "react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ThemeToggle } from "@/components/theme-toggle"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Moon, Sun, Monitor } from "lucide-react"

export default function ThemeAccessibilityPage() {
  const { theme, setTheme } = useTheme()
  const [focusVisible, setFocusVisible] = useState(false)

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">테마 및 접근성 테스트</h1>

      <div className="space-y-10">
        {/* 테마 전환 섹션 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">테마 전환</h2>
          <Card>
            <CardHeader>
              <CardTitle>테마 설정</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-wrap gap-4">
                <Button
                  variant={theme === "light" ? "default" : "outline"}
                  onClick={() => setTheme("light")}
                  aria-pressed={theme === "light"}
                >
                  <Sun className="mr-2 h-4 w-4" />
                  라이트 모드
                </Button>
                <Button
                  variant={theme === "dark" ? "default" : "outline"}
                  onClick={() => setTheme("dark")}
                  aria-pressed={theme === "dark"}
                >
                  <Moon className="mr-2 h-4 w-4" />
                  다크 모드
                </Button>
                <Button
                  variant={theme === "system" ? "default" : "outline"}
                  onClick={() => setTheme("system")}
                  aria-pressed={theme === "system"}
                >
                  <Monitor className="mr-2 h-4 w-4" />
                  시스템 설정
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <p>현재 테마:</p>
                <span className="font-semibold">
                  {theme === "light" && "라이트 모드"}
                  {theme === "dark" && "다크 모드"}
                  {theme === "system" && "시스템 설정"}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <p>간편 테마 전환:</p>
                <ThemeToggle />
              </div>
            </CardContent>
          </Card>
        </section>

        {/* 접근성 테스트 섹션 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">접근성 테스트</h2>
          <Card>
            <CardHeader>
              <CardTitle>키보드 접근성</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground mb-4">
                Tab 키를 사용하여 아래 요소들 사이를 이동해보세요. 포커스 표시가 명확하게 보여야 합니다.
              </p>

              <div className="space-y-4">
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="name">이름</Label>
                  <Input
                    type="text"
                    id="name"
                    placeholder="이름을 입력하세요"
                    aria-describedby="name-description"
                  />
                  <p id="name-description" className="text-sm text-muted-foreground">
                    이름을 입력하는 필드입니다.
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox id="terms" aria-describedby="terms-description" />
                  <div className="grid gap-1.5">
                    <Label htmlFor="terms">이용약관에 동의합니다</Label>
                    <p id="terms-description" className="text-sm text-muted-foreground">
                      서비스 이용약관 및 개인정보 처리방침에 동의합니다.
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>알림 설정</Label>
                  <RadioGroup defaultValue="email" aria-label="알림 설정">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="email" id="r1" />
                      <Label htmlFor="r1">이메일</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="sms" id="r2" />
                      <Label htmlFor="r2">SMS</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="push" id="r3" />
                      <Label htmlFor="r3">푸시 알림</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="dark-mode" aria-label="다크 모드 전환" />
                  <Label htmlFor="dark-mode">다크 모드</Label>
                </div>

                <Tabs defaultValue="account" className="w-full">
                  <TabsList aria-label="계정 설정 탭">
                    <TabsTrigger value="account">계정</TabsTrigger>
                    <TabsTrigger value="password">비밀번호</TabsTrigger>
                    <TabsTrigger value="settings">설정</TabsTrigger>
                  </TabsList>
                  <TabsContent value="account" className="p-4 border rounded-md mt-4">
                    <h3 className="text-lg font-medium">계정 설정</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      계정 정보를 관리하고 업데이트할 수 있습니다.
                    </p>
                  </TabsContent>
                  <TabsContent value="password" className="p-4 border rounded-md mt-4">
                    <h3 className="text-lg font-medium">비밀번호 변경</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      계정 비밀번호를 업데이트할 수 있습니다.
                    </p>
                  </TabsContent>
                  <TabsContent value="settings" className="p-4 border rounded-md mt-4">
                    <h3 className="text-lg font-medium">일반 설정</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      알림, 테마 및 기타 설정을 관리할 수 있습니다.
                    </p>
                  </TabsContent>
                </Tabs>

                <div className="flex space-x-2">
                  <Button>기본 버튼</Button>
                  <Button
                    onFocus={() => setFocusVisible(true)}
                    onBlur={() => setFocusVisible(false)}
                  >
                    {focusVisible ? "포커스됨!" : "포커스 테스트"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* 접근성 가이드라인 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">접근성 가이드라인</h2>
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">키보드 접근성</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>모든 상호작용 요소는 키보드로 접근 가능해야 합니다.</li>
                  <li>포커스 표시가 명확해야 합니다.</li>
                  <li>논리적인 탭 순서를 유지해야 합니다.</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">스크린 리더 호환성</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>모든 이미지에 대체 텍스트를 제공해야 합니다.</li>
                  <li>폼 요소에 레이블을 연결해야 합니다.</li>
                  <li>ARIA 속성을 적절히 사용해야 합니다.</li>
                  <li>시맨틱 HTML 요소를 사용해야 합니다.</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">색상 및 대비</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>텍스트와 배경 간의 충분한 대비를 유지해야 합니다.</li>
                  <li>색상만으로 정보를 전달하지 않아야 합니다.</li>
                  <li>다크 모드와 라이트 모드 모두에서 가독성을 유지해야 합니다.</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
