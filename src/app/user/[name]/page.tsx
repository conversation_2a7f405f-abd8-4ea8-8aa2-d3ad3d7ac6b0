'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useUser } from '@/hooks/use-user';
import { useParams } from 'next/navigation';

export default function UserProfilePage() {
  const params = useParams();
  const rawName = params.name as string;
  const decodedRawName = decodeURIComponent(rawName);
  const name = decodedRawName.startsWith('@')
    ? decodedRawName.substring(1)
    : decodedRawName;
  console.log(name);

  // useUser 훅을 사용하여 특정 사용자의 정보 가져오기
  const { user, isLoading, error } = useUser({ name });

  if (isLoading) {
    return (
      <div className="container mx-auto flex min-h-[40vh] items-center justify-center p-8">
        <span className="text-muted-foreground">로딩 중...</span>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="container mx-auto flex min-h-[40vh] items-center justify-center p-8">
        <span className="text-muted-foreground">
          사용자 정보를 불러올 수 없습니다.
        </span>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8">
      {/* 프로필 카드 스타일 div */}
      <div className="rounded-xl border-2 border-zinc-300 bg-zinc-100 p-5 shadow-sm dark:border-zinc-700 dark:bg-zinc-800">
        <div className="w-full max-w-md">
          <div className="flex flex-col gap-2">
            {/* 아바타 (me/page.tsx 와 동일한 크기) */}
            <div className="relative inline-block">
              <Avatar className="h-20 w-20">
                <AvatarImage
                  src={user.image || undefined}
                  alt={user.name || user.displayName || 'user'}
                />
                <AvatarFallback>
                  {user.name
                    ? user.name[0]
                    : user.displayName
                      ? user.displayName[0]
                      : 'U'}{' '}
                  {/* me/page.tsx fallback 참조 */}
                </AvatarFallback>
              </Avatar>
            </div>

            {/* 이름 및 활동 정보 (me/page.tsx 레이아웃 참조) */}
            <div className="mt-8">
              {/* 표시 이름 */}
              <div className="truncate text-sm">
                {user.displayName || '별명 없음'}
              </div>
              {/* 사용자 이름 (@name) */}
              <div className="text-md overflow-hidden font-black">
                @{user.name || '이름 없음'}
              </div>

              {/* 사용자 활동 정보 */}
              <div className="mt-2 flex items-center space-x-4 text-sm text-zinc-600 dark:text-zinc-400">
                <div>
                  <span className="font-medium">{user.postCount || 0}</span>{' '}
                  게시글
                </div>
                <div>
                  <span className="font-medium">
                    {user.postCommentCount || 0}
                  </span>{' '}
                  댓글
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
