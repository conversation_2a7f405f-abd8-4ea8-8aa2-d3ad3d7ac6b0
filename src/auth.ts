import NextAuth from 'next-auth';
import Google from 'next-auth/providers/google';

import { PrismaAdapter } from '@auth/prisma-adapter';

import { sendDiscordMessage } from './lib/discord';
import { prisma } from './lib/prisma';
import { Envs } from './types/env';

export const { handlers, signIn, signOut, auth } = NextAuth({
  secret: Envs.authSecret,
  adapter: PrismaAdapter(prisma as any),
  trustHost: true,
  providers: [
    Google({
      clientId: Envs.authGoogleId,
      clientSecret: Envs.authGoogleSecret,
    }),
  ],
  events: {
    createUser: async ({ user }) => {
      // 여기서 첫 가입 시 필요한 초기화 작업 수행
      await prisma.user.update({
        where: { id: user.id },
        data: {
          name: `user-${user.id}`,
          displayName: user.name,
          bio: `hello ${user.name}`,
          isAdmin: false,
          isTester: false,
        },
      });
      await sendDiscordMessage('', {
        title: '새로운 사용자 가입',
        description: `${user.name} (${user.email})`,
      });
    },
  },
  pages: {
    signIn: '/auth/login',
    signOut: '/auth/signout',
  },
  session: {
    strategy: 'jwt',
    maxAge: 60 * 60 * 24 * 30, // 30일 (1달)
  },
  callbacks: {
    // session 파라미터 제거, trigger 파라미터 유지 (만료 확인 등 다른 로직 위해)
    async jwt({ token, user, account, trigger }) {
      // 사용자가 소셜 로그인(예: 구글) 등으로 처음 로그인할 때,
      // jwt 콜백이 호출되고, 이때 user와 account가 값이 있음.
      if (user) {
        // user가 있을 때만 user의 값을 사용해서 토큰을 만듦
        return {
          ...token,
          id: user.id,
          name: user.name,
          displayName: user.displayName,
          isAdmin: user.isAdmin || false,
          isTester: user.isTester || false,
          accessToken: account?.access_token,
          expiresAt: token?.expiresAt,
        };
      }

      // 토큰이 만료되었는지 확인
      const now = Math.floor(Date.now() / 1000);
      if (token.expiresAt && now >= Number(token.expiresAt)) {
        // AuthJS는 자동으로 세션을 갱신하므로 여기서는 만료 상태만 표시
        return {
          ...token,
          error: 'TokenExpired',
        };
      }

      // sessionUpdate 관련 로직 제거 (session 콜백에서 처리)

      return token;
    },

    // 클라이언트에서 useSession()이나 getServerSession() 등으로 세션 정보를 요청할 때마다 호출됩니다.
    // JWT 토큰 정보를 기반으로 DB에서 최신 사용자 정보를 조회하여 세션 객체에 반영합니다.
    async session({ session, token }) {
      if (token.id && session.user) {
        // 타입 가드 함수를 사용하여 타입 안전성 확보
        const tokenId = typeof token.id === 'string' ? token.id : '';

        const dbUser = await prisma.user.findUnique({
          where: { id: tokenId },
          select: {
            id: true,
            name: true,
            email: true, // email은 기본 session.user에 있으므로 유지
            displayName: true,
            image: true,
            isAdmin: true,
            isTester: true,
          },
        });

        if (dbUser) {
          // DB에서 가져온 최신 정보로 session.user 업데이트
          session.user.id = dbUser.id;
          session.user.name = dbUser.name;
          session.user.displayName = dbUser.displayName;
          session.user.image = dbUser.image;
          // 커스텀 필드 추가
          session.user.isAdmin = dbUser.isAdmin;
          session.user.isTester = dbUser.isTester;
        } else {
          // DB 사용자를 찾지 못한 경우, 세션을 유효하지 않게 처리할 수 있음 (선택 사항)
          // 예: return null; 또는 session.user = null;
          // 여기서는 일단 기존 토큰 기반 정보 유지
          session.user.id = typeof token.id === 'string' ? token.id : '';
          session.user.name =
            typeof token.name === 'string' ? token.name : null;
          session.user.displayName =
            typeof token.displayName === 'string' ? token.displayName : null;
          session.user.isAdmin =
            typeof token.isAdmin === 'boolean' ? token.isAdmin : false;
          session.user.isTester =
            typeof token.isTester === 'boolean' ? token.isTester : false;
        }
      } else if (session.user) {
        // 토큰 ID가 없지만 session.user가 있는 경우
        // 기존 토큰 기반 정보라도 채워넣기 시도
        session.user.id = typeof token.id === 'string' ? token.id : '';
        session.user.name = typeof token.name === 'string' ? token.name : null;
        session.user.displayName =
          typeof token.displayName === 'string' ? token.displayName : null;
        session.user.isAdmin =
          typeof token.isAdmin === 'boolean' ? token.isAdmin : false;
        session.user.isTester =
          typeof token.isTester === 'boolean' ? token.isTester : false;
      }
      return session;
    },
  },
});
