'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { createCategory, updateCategory, getCategories } from '@/actions/content/category';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Loader2 } from 'lucide-react';

// 카테고리 폼 스키마
const categoryFormSchema = z.object({
  name: z.string().min(1, '카테고리 이름은 필수입니다.').max(255, '카테고리 이름은 최대 255자까지 가능합니다.'),
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.')
    .regex(/^[a-z0-9-]+$/, '슬러그는 소문자, 숫자, 하이픈(-)만 포함할 수 있습니다.'),
  description: z.string().max(1000, '설명은 최대 1000자까지 가능합니다.').optional(),
  parentId: z.string().optional(),
  order: z.coerce.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
  icon: z.string().max(255).optional(),
  imageUrl: z.string().url('유효한 URL 형식이 아닙니다.').optional().or(z.literal('')),
});

type CategoryFormValues = z.infer<typeof categoryFormSchema>;

interface CategoryFormProps {
  category?: any;
  onSuccess?: () => void;
}

export default function CategoryForm({ category, onSuccess }: CategoryFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [parentCategories, setParentCategories] = useState<any[]>([]);
  const isEditMode = !!category;

  // 폼 초기화
  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: category?.name || '',
      slug: category?.slug || '',
      description: category?.description || '',
      parentId: category?.parentId || undefined,
      order: category?.order || 0,
      isActive: category?.isActive !== undefined ? category?.isActive : true,
      icon: category?.icon || '',
      imageUrl: category?.imageUrl || '',
    },
  });

  // 부모 카테고리 목록 로드
  useEffect(() => {
    const loadParentCategories = async () => {
      try {
        const response = await getCategories();
        if (response.success && response.data) {
          // 자기 자신을 부모로 선택하지 못하도록 필터링
          const filteredCategories = response.data.flatCategories.filter(
            (cat: any) => !isEditMode || cat.id !== category.id
          );
          setParentCategories(filteredCategories);
        }
      } catch (error) {
        console.error('부모 카테고리 로드 중 오류 발생:', error);
        toast.error('부모 카테고리 목록을 불러오는 중 오류가 발생했습니다.');
      }
    };

    loadParentCategories();
  }, [category, isEditMode]);

  // 이름 변경 시 슬러그 자동 생성
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    form.setValue('name', name);
    
    // 슬러그가 비어있거나 사용자가 수정하지 않은 경우에만 자동 생성
    if (!form.getValues('slug') || form.getValues('slug') === form.getValues('name').toLowerCase().replace(/\s+/g, '-')) {
      const slug = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      form.setValue('slug', slug);
    }
  };

  // 폼 제출 핸들러
  const onSubmit = async (data: CategoryFormValues) => {
    setIsLoading(true);
    try {
      const action = isEditMode
        ? updateCategory({ ...data, id: category.id })
        : createCategory(data);
      
      const response = await action;
      
      if (response.success) {
        toast.success(
          isEditMode
            ? '카테고리가 성공적으로 수정되었습니다.'
            : '카테고리가 성공적으로 생성되었습니다.'
        );
        if (onSuccess) onSuccess();
      } else {
        toast.error(response.error?.message || '카테고리 저장 중 오류가 발생했습니다.');
      }
    } catch (error) {
      console.error('카테고리 저장 중 오류 발생:', error);
      toast.error('카테고리 저장 중 오류가 발생했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* 이름 */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>이름</FormLabel>
              <FormControl>
                <Input
                  placeholder="카테고리 이름"
                  {...field}
                  onChange={handleNameChange}
                />
              </FormControl>
              <FormDescription>
                카테고리의 표시 이름입니다.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 슬러그 */}
        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>슬러그</FormLabel>
              <FormControl>
                <Input
                  placeholder="category-slug"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                URL에 사용될 고유 식별자입니다. 소문자, 숫자, 하이픈(-)만 사용 가능합니다.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 설명 */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>설명</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="카테고리에 대한 설명을 입력하세요."
                  className="resize-none"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                카테고리에 대한 간략한 설명입니다.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 부모 카테고리 */}
        <FormField
          control={form.control}
          name="parentId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>부모 카테고리</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="부모 카테고리 선택" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="">없음 (최상위 카테고리)</SelectItem>
                  {parentCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                이 카테고리의 상위 카테고리입니다. 없으면 최상위 카테고리가 됩니다.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 순서 */}
        <FormField
          control={form.control}
          name="order"
          render={({ field }) => (
            <FormItem>
              <FormLabel>순서</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min={0}
                  {...field}
                />
              </FormControl>
              <FormDescription>
                카테고리 표시 순서입니다. 낮은 숫자가 먼저 표시됩니다.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 활성화 여부 */}
        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">활성화</FormLabel>
                <FormDescription>
                  카테고리를 활성화하면 사용자에게 표시됩니다.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* 아이콘 */}
        <FormField
          control={form.control}
          name="icon"
          render={({ field }) => (
            <FormItem>
              <FormLabel>아이콘</FormLabel>
              <FormControl>
                <Input
                  placeholder="lucide-icon-name"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                Lucide 아이콘 이름을 입력하세요. (예: home, settings, user)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 이미지 URL */}
        <FormField
          control={form.control}
          name="imageUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>이미지 URL</FormLabel>
              <FormControl>
                <Input
                  placeholder="https://example.com/image.jpg"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                카테고리 이미지의 URL을 입력하세요.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 제출 버튼 */}
        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              저장 중...
            </>
          ) : (
            isEditMode ? '카테고리 수정' : '카테고리 생성'
          )}
        </Button>
      </form>
    </Form>
  );
}
