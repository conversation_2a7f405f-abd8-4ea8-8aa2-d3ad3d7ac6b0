'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import CategoryForm from './category-form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

export default function CategoryHeader() {
  const [open, setOpen] = useState(false);

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">카테고리 관리</h1>
        <p className="text-muted-foreground">
          카테고리를 생성, 수정, 삭제할 수 있습니다.
        </p>
      </div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            카테고리 추가
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>카테고리 추가</DialogTitle>
            <DialogDescription>
              새로운 카테고리를 추가합니다. 모든 필드를 입력한 후 저장 버튼을 클릭하세요.
            </DialogDescription>
          </DialogHeader>
          <CategoryForm onSuccess={() => setOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
}
