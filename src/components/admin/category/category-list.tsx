'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { getCategories, deleteCategory } from '@/actions/content/category';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Edit, MoreHorizontal, Trash2, Plus, ChevronRight, ChevronDown } from 'lucide-react';
import CategoryForm from './category-form';

interface CategoryListProps {
  initialData: any;
}

export default function CategoryList({ initialData }: CategoryListProps) {
  const [categories, setCategories] = useState<any[]>([]);
  const [flatCategories, setFlatCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editCategory, setEditCategory] = useState<any>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<any>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // 초기 데이터 설정
  useEffect(() => {
    if (initialData) {
      setCategories(initialData.categories || []);
      setFlatCategories(initialData.flatCategories || []);
    }
  }, [initialData]);

  // 카테고리 목록 새로고침
  const refreshCategories = async () => {
    setIsLoading(true);
    try {
      const response = await getCategories();
      if (response.success && response.data) {
        setCategories(response.data.categories || []);
        setFlatCategories(response.data.flatCategories || []);
      } else {
        toast.error('카테고리 목록을 불러오는 중 오류가 발생했습니다.');
      }
    } catch (error) {
      console.error('카테고리 목록 로드 중 오류 발생:', error);
      toast.error('카테고리 목록을 불러오는 중 오류가 발생했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  // 카테고리 삭제 핸들러
  const handleDeleteCategory = async () => {
    if (!categoryToDelete) return;
    
    try {
      const response = await deleteCategory(categoryToDelete.id);
      if (response.success) {
        toast.success('카테고리가 성공적으로 삭제되었습니다.');
        refreshCategories();
      } else {
        toast.error(response.error?.message || '카테고리 삭제 중 오류가 발생했습니다.');
      }
    } catch (error) {
      console.error('카테고리 삭제 중 오류 발생:', error);
      toast.error('카테고리 삭제 중 오류가 발생했습니다.');
    } finally {
      setDeleteDialogOpen(false);
      setCategoryToDelete(null);
    }
  };

  // 카테고리 수정 핸들러
  const handleEditCategory = (category: any) => {
    setEditCategory(category);
    setEditDialogOpen(true);
  };

  // 카테고리 확장/축소 토글
  const toggleCategoryExpand = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // 재귀적으로 카테고리 트리 렌더링
  const renderCategoryTree = (categoryList: any[], level = 0) => {
    return categoryList.map((category) => (
      <>
        <TableRow key={category.id} className={level > 0 ? 'bg-muted/30' : ''}>
          <TableCell className="font-medium">
            <div className="flex items-center" style={{ paddingLeft: `${level * 1.5}rem` }}>
              {category.children && category.children.length > 0 ? (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 mr-1"
                  onClick={() => toggleCategoryExpand(category.id)}
                >
                  {expandedCategories[category.id] ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              ) : (
                <div className="w-7"></div>
              )}
              {category.name}
            </div>
          </TableCell>
          <TableCell>{category.slug}</TableCell>
          <TableCell>{category._count?.posts || 0}</TableCell>
          <TableCell>{category.order}</TableCell>
          <TableCell>
            {category.isActive ? (
              <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
                활성
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50">
                비활성
              </Badge>
            )}
          </TableCell>
          <TableCell>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">메뉴 열기</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>작업</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleEditCategory(category)}>
                  <Edit className="mr-2 h-4 w-4" />
                  수정
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setCategoryToDelete(category);
                    setDeleteDialogOpen(true);
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  삭제
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
        {category.children && category.children.length > 0 && expandedCategories[category.id] && (
          renderCategoryTree(category.children, level + 1)
        )}
      </>
    ));
  };

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>이름</TableHead>
              <TableHead>슬러그</TableHead>
              <TableHead>게시글 수</TableHead>
              <TableHead>순서</TableHead>
              <TableHead>상태</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center h-24">
                  {isLoading ? '카테고리 로딩 중...' : '카테고리가 없습니다.'}
                </TableCell>
              </TableRow>
            ) : (
              renderCategoryTree(categories)
            )}
          </TableBody>
        </Table>
      </div>

      {/* 카테고리 수정 다이얼로그 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>카테고리 수정</DialogTitle>
            <DialogDescription>
              카테고리 정보를 수정합니다. 모든 필드를 입력한 후 저장 버튼을 클릭하세요.
            </DialogDescription>
          </DialogHeader>
          {editCategory && (
            <CategoryForm
              category={editCategory}
              onSuccess={() => {
                setEditDialogOpen(false);
                refreshCategories();
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* 카테고리 삭제 확인 다이얼로그 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>카테고리 삭제</DialogTitle>
            <DialogDescription>
              정말로 이 카테고리를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
              {categoryToDelete && categoryToDelete._count?.children > 0 && (
                <p className="text-destructive mt-2">
                  이 카테고리에는 하위 카테고리가 있어 삭제할 수 없습니다.
                </p>
              )}
              {categoryToDelete && categoryToDelete._count?.posts > 0 && (
                <p className="text-destructive mt-2">
                  이 카테고리에는 게시글이 있어 삭제할 수 없습니다.
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false);
                setCategoryToDelete(null);
              }}
            >
              취소
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCategory}
              disabled={
                !categoryToDelete ||
                categoryToDelete._count?.children > 0 ||
                categoryToDelete._count?.posts > 0
              }
            >
              삭제
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
