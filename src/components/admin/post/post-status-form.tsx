'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { updatePostStatus } from '@/actions/admin/post';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface PostStatusFormProps {
  postId: string;
  isDeleted: boolean;
}

export default function PostStatusForm({
  postId,
  isDeleted,
}: PostStatusFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 게시글 상태 업데이트 처리
  const handleUpdateStatus = async () => {
    try {
      setIsSubmitting(true);
      
      const result = await updatePostStatus({
        postId,
        deletedAt: isDeleted ? null : new Date(),
      });

      if (result.success) {
        toast({
          title: '상태 업데이트 성공',
          description: `게시글이 ${isDeleted ? '복원' : '삭제'}되었습니다.`,
        });
        router.refresh();
      } else {
        toast({
          title: '상태 업데이트 실패',
          description: result.message || '상태를 업데이트하는 중 오류가 발생했습니다.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('상태 업데이트 중 오류 발생:', error);
      toast({
        title: '상태 업데이트 실패',
        description: '상태를 업데이트하는 중 오류가 발생했습니다.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>주의</AlertTitle>
        <AlertDescription>
          {isDeleted
            ? '게시글을 복원하면 사용자가 다시 볼 수 있게 됩니다.'
            : '게시글을 삭제하면 사용자가 볼 수 없게 됩니다. 이 작업은 되돌릴 수 있습니다.'}
        </AlertDescription>
      </Alert>

      <div className="flex items-center gap-2">
        <Button
          variant={isDeleted ? 'default' : 'destructive'}
          onClick={handleUpdateStatus}
          disabled={isSubmitting}
        >
          {isSubmitting
            ? '처리 중...'
            : isDeleted
            ? '게시글 복원'
            : '게시글 삭제'}
        </Button>
      </div>
    </div>
  );
}
