'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { updateUserStatus } from '@/actions/admin/user';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

// 사용자 상태 폼 스키마
const statusFormSchema = z.object({
  isActive: z.boolean(),
});

type StatusFormValues = z.infer<typeof statusFormSchema>;

interface UserStatusFormProps {
  userId: string;
  isActive: boolean;
}

export default function UserStatusForm({
  userId,
  isActive,
}: UserStatusFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 폼 설정
  const form = useForm<StatusFormValues>({
    resolver: zodResolver(statusFormSchema),
    defaultValues: {
      isActive,
    },
  });

  // 상태 업데이트 처리
  const onSubmit = async (data: StatusFormValues) => {
    // 상태가 변경되지 않은 경우 처리하지 않음
    if (data.isActive === isActive) {
      toast({
        title: '변경 사항 없음',
        description: '사용자 상태가 변경되지 않았습니다.',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      
      const result = await updateUserStatus({
        userId,
        isActive: data.isActive,
      });

      if (result.success) {
        toast({
          title: '상태 업데이트 성공',
          description: `사용자 상태가 ${data.isActive ? '활성' : '비활성'}으로 변경되었습니다.`,
        });
        router.refresh();
      } else {
        toast({
          title: '상태 업데이트 실패',
          description: result.message || '상태를 업데이트하는 중 오류가 발생했습니다.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('상태 업데이트 중 오류 발생:', error);
      toast({
        title: '상태 업데이트 실패',
        description: '상태를 업데이트하는 중 오류가 발생했습니다.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>주의</AlertTitle>
        <AlertDescription>
          사용자 상태를 비활성화하면 해당 사용자는 로그인할 수 없게 됩니다.
          이 작업은 되돌릴 수 있습니다.
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">사용자 상태</FormLabel>
                  <FormDescription>
                    사용자 계정의 활성 상태를 설정합니다.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex items-center gap-2">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? '처리 중...' : '상태 변경'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => form.reset({ isActive })}
              disabled={isSubmitting}
            >
              초기화
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
