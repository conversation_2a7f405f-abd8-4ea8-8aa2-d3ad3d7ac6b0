'use client';

import { GoogleAnalytics } from './google-analytics';
import { NaverAnalytics } from './naver-analytics';

/**
 * 분석 도구 통합 컴포넌트
 * 
 * 여러 분석 도구를 한 번에 통합하는 컴포넌트입니다.
 * 환경 변수를 통해 각 분석 도구의 ID를 설정할 수 있습니다.
 */
export function AnalyticsProvider() {
  // 환경 변수에서 분석 도구 ID 가져오기
  const googleAnalyticsId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
  const naverVerificationId = process.env.NEXT_PUBLIC_NAVER_SITE_VERIFICATION;
  
  return (
    <>
      {/* Google Analytics */}
      <GoogleAnalytics measurementId={googleAnalyticsId || ''} />
      
      {/* 네이버 웹마스터 도구 */}
      <NaverAnalytics verificationId={naverVerificationId || ''} />
    </>
  );
}
