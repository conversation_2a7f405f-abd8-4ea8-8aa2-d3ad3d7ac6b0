'use client';

import Script from 'next/script';

interface NaverAnalyticsProps {
  /**
   * 네이버 웹마스터 도구 인증 ID
   */
  verificationId: string;
}

/**
 * 네이버 웹마스터 도구 통합 컴포넌트
 * 
 * 네이버 웹마스터 도구를 Next.js 애플리케이션에 통합합니다.
 * 네이버 검색 엔진 최적화(SEO)를 위한 사이트 인증 및 분석을 지원합니다.
 */
export function NaverAnalytics({ verificationId }: NaverAnalyticsProps) {
  // 인증 ID가 없으면 아무것도 렌더링하지 않음
  if (!verificationId) return null;
  
  return (
    <>
      {/* 네이버 웹마스터 도구 인증 스크립트 */}
      <Script
        id="naver-webmaster"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${verificationId}');
          `,
        }}
      />
    </>
  );
}
