'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User } from 'next-auth';

interface AppAvatarProps {
  user: User | null | undefined;
}

export function AppAvatar({ user }: AppAvatarProps) {
  return (
    <Avatar>
      <AvatarImage
        src={user?.image ?? ''}
        alt={user?.name ?? ''}
        className="size-7 rounded-full"
      />
      <AvatarFallback className="size-7 rounded-full">
        {user?.name?.[0] ?? '?'}
      </AvatarFallback>
    </Avatar>
  );
}
