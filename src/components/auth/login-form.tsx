'use client';

import { signInWithGoogle } from '@/actions/auth';
import { GoogleSignInButton } from '@/design-system/components/molecules/auth';
import Link from 'next/link';

interface LoginFormProps {
  callbackUrl: string;
  error?: string | null;
}

export default function LoginForm({ callbackUrl, error }: LoginFormProps) {
  return (
    <div className="max-w-md min-w-sm rounded-lg bg-zinc-800 p-5">
      <div className="text-3xl font-black">SO:DAMM</div>
      <div className="text-sm">Sign up below to unlock the full experience</div>
      <div className="space-y-6">
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-500 dark:bg-red-900/20 dark:text-red-300">
            {error === 'SessionRequired'
              ? '세션이 만료되었습니다. 다시 로그인해 주세요.'
              : '로그인 중 오류가 발생했습니다.'}
          </div>
        )}

        <div className="mt-10 space-y-4">
          <div className="text-sm">
            By Continuing, you agree to our{' '}
            <Link
              href="/privacy-policy"
              className="hover:text-primary font-bold underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </Link>
            {' and '}
            <Link
              href="/terms-of-service"
              className="hover:text-primary font-bold underline"
            >
              Terms of Service
            </Link>
          </div>
          <form
            action={signInWithGoogle}
            className="space-y-4"
          >
            <input
              type="hidden"
              name="callbackUrl"
              value={'/community'}
            />
            <GoogleSignInButton />
          </form>
        </div>
      </div>
    </div>
  );
}
