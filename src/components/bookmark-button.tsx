'use client';

import { useState, useEffect } from 'react';
import { Bookmark } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useBookmarkToggle } from '@/hooks/use-bookmarks';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

type BookmarkButtonProps = {
  postId: string;
  initialIsBookmarked: boolean;
  showText?: boolean;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
};

/**
 * 북마크 버튼 컴포넌트
 * 
 * 게시글을 북마크하거나 북마크 취소할 수 있는 버튼 컴포넌트입니다.
 * 낙관적 UI 업데이트를 사용하여 즉각적인 피드백을 제공합니다.
 */
export default function BookmarkButton({
  postId,
  initialIsBookmarked,
  showText = true,
  variant = 'ghost',
  size = 'sm',
  className,
}: BookmarkButtonProps) {
  // 북마크 토글 훅 사용
  const {
    isBookmarked,
    isPending,
    error,
    toggleBookmark,
  } = useBookmarkToggle(postId, initialIsBookmarked);

  // 에러 상태 관리
  const [hasError, setHasError] = useState(false);

  // 에러 상태 업데이트
  useEffect(() => {
    if (error) {
      setHasError(true);
      const timer = setTimeout(() => setHasError(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // 북마크 토글 핸들러
  const handleToggleBookmark = async () => {
    try {
      const success = await toggleBookmark();
      
      if (success) {
        // 성공 메시지 표시
        toast.success(
          isBookmarked ? '북마크가 취소되었습니다.' : '북마크에 추가되었습니다.',
          {
            id: `bookmark-${postId}`,
            duration: 2000,
          }
        );
      }
    } catch (error) {
      console.error('북마크 토글 중 오류 발생:', error);
      toast.error('북마크 처리 중 오류가 발생했습니다.', {
        id: `bookmark-error-${postId}`,
      });
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleToggleBookmark}
      disabled={isPending}
      className={cn(
        isBookmarked ? 'text-primary' : '',
        hasError ? 'text-destructive' : '',
        className
      )}
      aria-label={isBookmarked ? '북마크 취소' : '북마크 추가'}
    >
      <Bookmark
        className={cn(
          'h-4 w-4',
          isBookmarked ? 'fill-current' : 'fill-none'
        )}
      />
      {showText && <span>{isBookmarked ? '저장됨' : '저장'}</span>}
      {hasError && (
        <span className="text-xs text-destructive ml-1">오류</span>
      )}
    </Button>
  );
}
