'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { format } from 'date-fns';
import { ko } from 'date-fns/locale';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toggleBookmark } from '@/actions/interaction/bookmark';
import { toast } from 'sonner';
import { MessageSquare, ThumbsUp, Bookmark, Trash, Clock, User } from 'lucide-react';

interface BookmarkCardProps {
  bookmark: {
    id: string;
    createdAt: string;
    post: {
      id: string;
      content: string;
      contentHtml: string;
      createdAt: string;
      author: {
        id: string;
        name: string;
        displayName: string | null;
        image: string | null;
      };
      category: {
        id: string;
        name: string;
      } | null;
      _count: {
        likes: number;
        comments: number;
      };
    };
  };
  onRemove: () => void;
}

export function BookmarkCard({ bookmark, onRemove }: BookmarkCardProps) {
  const t = useTranslations('Bookmarks');
  const [isRemoving, setIsRemoving] = useState(false);
  
  // 북마크 제거 핸들러
  const handleRemoveBookmark = async () => {
    try {
      setIsRemoving(true);
      
      const response = await toggleBookmark({ postId: bookmark.post.id });
      
      if (response.success) {
        toast.success(t('bookmarkRemoved'));
        onRemove();
      } else {
        toast.error(response.error?.message || t('errorRemoving'));
      }
    } catch (error) {
      console.error('북마크 제거 중 오류 발생:', error);
      toast.error(t('errorRemoving'));
    } finally {
      setIsRemoving(false);
    }
  };
  
  // 게시글 내용 요약 (최대 150자)
  const contentSummary = bookmark.post.content.length > 150
    ? `${bookmark.post.content.substring(0, 150)}...`
    : bookmark.post.content;
  
  // 작성자 표시 이름
  const authorName = bookmark.post.author.displayName || bookmark.post.author.name;
  
  return (
    <Card className="mb-4 hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <Link href={`/posts/${bookmark.post.id}`} className="hover:underline">
            <CardTitle className="text-lg font-medium line-clamp-1">
              {contentSummary}
            </CardTitle>
          </Link>
          
          {bookmark.post.category && (
            <Badge variant="outline" className="ml-2">
              {bookmark.post.category.name}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pb-2">
        <div className="text-sm text-muted-foreground mb-3 line-clamp-2"
          dangerouslySetInnerHTML={{ __html: bookmark.post.contentHtml }}
        />
        
        <div className="flex items-center text-sm text-muted-foreground">
          <div className="flex items-center mr-4">
            <Avatar className="h-5 w-5 mr-1">
              <AvatarImage src={bookmark.post.author.image || undefined} alt={authorName} />
              <AvatarFallback>{authorName.substring(0, 2)}</AvatarFallback>
            </Avatar>
            <Link href={`/profile/${bookmark.post.author.name}`} className="hover:underline">
              {authorName}
            </Link>
          </div>
          
          <div className="flex items-center mr-4">
            <Clock className="h-3 w-3 mr-1" />
            <span>
              {format(new Date(bookmark.post.createdAt), 'yyyy.MM.dd', { locale: ko })}
            </span>
          </div>
          
          <div className="flex items-center mr-4">
            <ThumbsUp className="h-3 w-3 mr-1" />
            <span>{bookmark.post._count.likes}</span>
          </div>
          
          <div className="flex items-center">
            <MessageSquare className="h-3 w-3 mr-1" />
            <span>{bookmark.post._count.comments}</span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-2 flex justify-between">
        <Button
          variant="outline"
          size="sm"
          asChild
        >
          <Link href={`/posts/${bookmark.post.id}`}>
            {t('viewPost')}
          </Link>
        </Button>
        
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="text-destructive"
            >
              <Trash className="h-4 w-4 mr-1" />
              {t('remove')}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t('removeBookmarkTitle')}</AlertDialogTitle>
              <AlertDialogDescription>
                {t('removeBookmarkDescription')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleRemoveBookmark}
                disabled={isRemoving}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isRemoving ? t('removing') : t('remove')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardFooter>
    </Card>
  );
}
