'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useBookmarks } from '@/hooks/use-bookmarks';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BookmarkCard } from './bookmark-card';
import { Search, Filter, RefreshCw } from 'lucide-react';
import { useDebounce } from '@/hooks/use-debounce';
import { EmptyState } from '@/components/ui/empty-state';

interface BookmarksListProps {
  userId: string;
}

export default function BookmarksList({ userId }: BookmarksListProps) {
  const t = useTranslations('Bookmarks');
  
  // 필터 상태
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryId, setCategoryId] = useState<string>('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  
  // 디바운스된 검색어
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  
  // 북마크 목록 가져오기
  const {
    bookmarks,
    pagination,
    isLoading,
    isLoadingMore,
    error,
    isEmpty,
    loadMore,
    mutate,
  } = useBookmarks({
    page,
    limit,
    categoryId: categoryId || undefined,
    query: debouncedSearchQuery || undefined,
  });
  
  // 검색어 변경 시 페이지 초기화
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchQuery, categoryId]);
  
  // 검색어 입력 핸들러
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // 카테고리 변경 핸들러
  const handleCategoryChange = (value: string) => {
    setCategoryId(value);
  };
  
  // 새로고침 핸들러
  const handleRefresh = () => {
    mutate();
  };
  
  // 로딩 스켈레톤 렌더링
  const renderSkeletons = () => {
    return Array(3)
      .fill(0)
      .map((_, index) => (
        <Card key={index} className="mb-4">
          <CardHeader className="pb-2">
            <Skeleton className="h-5 w-3/4" />
          </CardHeader>
          <CardContent className="pb-2">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-5/6 mb-2" />
            <Skeleton className="h-4 w-4/6" />
          </CardContent>
          <CardFooter className="pt-2">
            <Skeleton className="h-8 w-24 mr-2" />
            <Skeleton className="h-8 w-24" />
          </CardFooter>
        </Card>
      ));
  };
  
  return (
    <div className="space-y-6">
      {/* 필터 및 검색 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-8"
          />
        </div>
        <Select value={categoryId} onValueChange={handleCategoryChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder={t('allCategories')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">{t('allCategories')}</SelectItem>
            {/* 카테고리 목록은 API에서 가져와야 함 */}
            <SelectItem value="category1">카테고리 1</SelectItem>
            <SelectItem value="category2">카테고리 2</SelectItem>
            <SelectItem value="category3">카테고리 3</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon" onClick={handleRefresh} aria-label={t('refresh')}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
      
      {/* 북마크 목록 */}
      <div className="space-y-4">
        {isLoading ? (
          renderSkeletons()
        ) : isEmpty ? (
          <EmptyState
            icon={<Bookmark className="h-12 w-12" />}
            title={t('noBookmarksTitle')}
            description={t('noBookmarksDescription')}
          />
        ) : (
          <>
            {bookmarks.map((bookmark) => (
              <BookmarkCard
                key={bookmark.id}
                bookmark={bookmark}
                onRemove={() => mutate()}
              />
            ))}
            
            {/* 더 불러오기 버튼 */}
            {pagination && pagination.hasMore && (
              <div className="flex justify-center mt-6">
                <Button
                  variant="outline"
                  onClick={loadMore}
                  disabled={isLoadingMore}
                >
                  {isLoadingMore ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      {t('loading')}
                    </>
                  ) : (
                    t('loadMore')
                  )}
                </Button>
              </div>
            )}
          </>
        )}
        
        {error && (
          <div className="p-4 text-center text-destructive">
            <p>{t('errorLoading')}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="mt-2"
            >
              {t('tryAgain')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
