'use client';

import { useRouter } from 'next/navigation';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  order: number;
  isActive: boolean;
  icon?: string | null;
  imageUrl?: string | null;
  translations?: any;
  parentId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface CategoryFilterProps {
  categories: Category[];
  selectedCategoryId?: string;
  baseUrl: string;
}

export default function CategoryFilter({
  categories,
  selectedCategoryId,
  baseUrl,
}: CategoryFilterProps) {
  const router = useRouter();

  const handleCategoryChange = (categoryId: string | null) => {
    const url = new URL(window.location.href);
    
    // 페이지 초기화
    url.searchParams.delete('page');
    
    // 카테고리 설정 또는 제거
    if (categoryId) {
      url.searchParams.set('categoryId', categoryId);
    } else {
      url.searchParams.delete('categoryId');
    }
    
    router.push(url.toString());
  };

  return (
    <ScrollArea className="w-full whitespace-nowrap">
      <div className="flex space-x-2 p-1">
        <Button
          variant={!selectedCategoryId ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleCategoryChange(null)}
        >
          전체
        </Button>
        
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategoryId === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleCategoryChange(category.id)}
          >
            {category.name}
          </Button>
        ))}
      </div>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  );
}
