import { User } from 'next-auth';
import { AppAvatar } from '../app-avatar';

interface AppSidebarUserProps {
  user: User | null | undefined;
}

export function AppSidebarUser({ user }: AppSidebarUserProps) {
  return (
    <div className="flex items-center gap-3">
      <AppAvatar user={user} />
      <div className="grid flex-1 text-left text-sm leading-tight">
        <span className="truncate font-semibold">{user?.displayName}</span>
        {/* <span className="text-muted-foreground truncate text-xs">
          {user?.name}
        </span> */}
      </div>
    </div>
  );
}
