'use client';

import { PostData } from '@/actions/posts'; // PostData 타입 경로 확인 필요
import { useInfinitePosts } from '@/hooks/use-infinite-posts';
import Link from 'next/link';
import { memo } from 'react';

/**
 * 피드 섹션 컴포넌트
 * 
 * 최신 피드 목록을 표시합니다.
 */
function FeedSection() {
  const { posts, isLoading, error, isEmpty } = useInfinitePosts({
    pageSize: 3,
  });

  return (
    <section className="w-full max-w-3xl">
      <Link
        href="/community/feed"
        className="text-lg font-bold hover:underline"
      >
        최신 Feed
      </Link>
      {isLoading && <p>Feed를 불러오는 중입니다...</p>}
      {error && <p>Feed를 불러오는데 실패했습니다: {error.message}</p>}
      {!isLoading && !error && posts && posts.length > 0 && (
        <div className="flex flex-col space-y-2">
          {posts.map((post: PostData) => (
            <div
              key={post.id}
              className="flex justify-between border-b border-gray-200 pb-1"
            >
              <Link
                href={`/community/feed/${post.id}`}
                className="text-sm hover:underline"
              >
                {post.content || (post as any).body || '내용이 없습니다.'}
              </Link>
              <p className="text-xs text-gray-500">
                view: {post.viewCount || 0}
              </p>
            </div>
          ))}
        </div>
      )}
      {!isLoading && (!posts || posts.length === 0) && !error && (
        <p className="text-sm text-gray-500">등록된 피드가 없습니다.</p>
      )}
    </section>
  );
}

/**
 * 피드 섹션 컴포넌트를 메모이제이션하여 성능 최적화
 * 
 * 컴포넌트가 불필요하게 리렌더링되지 않도록 합니다.
 */
export default memo(FeedSection);
