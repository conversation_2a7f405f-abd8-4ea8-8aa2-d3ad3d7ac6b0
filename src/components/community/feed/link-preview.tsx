'use client';

import { PreviewData } from '@/types/preview';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Card, CardContent } from '../../ui/card';
import { Skeleton } from '../../ui/skeleton';
import { ThreadsEmbed } from '../../ui/threads-embed';

interface LinkPreviewProps {
  url: string;
  previewData?: PreviewData | null;
}

export function LinkPreview({
  url,
  previewData: initialPreviewData,
}: LinkPreviewProps) {
  const [previewData, setPreviewData] = useState<PreviewData | null>(
    initialPreviewData || null
  );
  const [loading, setLoading] = useState(!initialPreviewData);
  const [error, setError] = useState<string | null>(null);

  // Threads URL 체크
  const isThreadsUrl = (url: string) => {
    return url.includes('threads.net') || url.includes('threads.com');
  };

  useEffect(() => {
    const fetchPreviewData = async () => {
      // Threads URL인 경우 미리보기 데이터를 가져오지 않음
      if (isThreadsUrl(url)) {
        setLoading(false);
        return;
      }

      // 이미 previewData가 있으면 API 호출 생략
      if (initialPreviewData) {
        setPreviewData(initialPreviewData);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await fetch(
          `/api/link-preview?url=${encodeURIComponent(url)}`
        );
        if (!response.ok) throw new Error('미리보기를 불러올 수 없습니다.');

        const data = await response.json();
        setPreviewData(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : '미리보기를 불러올 수 없습니다.'
        );
      } finally {
        setLoading(false);
      }
    };

    if (url && !initialPreviewData) {
      fetchPreviewData();
    }
  }, [url, initialPreviewData]);

  // Threads 임베드 표시
  if (isThreadsUrl(url)) {
    return (
      <Card className="w-full overflow-hidden">
        <CardContent className="p-0">
          <ThreadsEmbed
            url={url}
            className="w-full"
          />
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className="w-full max-w-[600px]">
        <CardContent className="p-4">
          <div className="flex gap-4">
            <Skeleton className="h-24 w-24" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !previewData) {
    return null;
  }

  return (
    <Card className="w-full max-w-[600px] cursor-pointer overflow-hidden hover:bg-zinc-50 dark:hover:bg-zinc-800/50">
      <CardContent className="p-4">
        <div className="flex gap-4">
          {previewData.image && (
            <div className="relative h-24 w-24 flex-shrink-0 overflow-hidden rounded-lg">
              <Image
                src={previewData.image}
                alt={previewData.title}
                fill
                className="object-cover"
              />
            </div>
          )}
          <div className="flex-1 space-y-1">
            <h3 className="line-clamp-1 text-sm font-semibold">
              {previewData.title}
            </h3>
            <p className="truncate text-xs text-zinc-500 dark:text-zinc-500">
              {url}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
