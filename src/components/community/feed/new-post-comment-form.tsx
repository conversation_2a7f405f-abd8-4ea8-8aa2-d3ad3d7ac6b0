'use client';

import {
  TiptapEditor,
  useEditorState,
} from '@/components/editor/tiptap-editor';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { useSWRConfig } from 'swr';

import { createPostComment } from '@/actions/community';

interface NewPostCommentFormProps {
  postId: string;
  className?: string;
}

export default function NewPostCommentForm({
  postId,
  className = '',
}: NewPostCommentFormProps) {
  const { data: _session, status } = useSession();
  const { mutate } = useSWRConfig();
  const router = useRouter();
  const {
    contentText,
    setContentText,
    handleChangeText,
    contentHtml,
    setContentHtml,
    handleChangeHtml,
  } = useEditorState();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await createPostComment({
        postId,
        contentText,
      });
      if (result.success) {
        toast.success('댓글이 등록되었습니다.');
        setContentText('');
        setContentHtml('');

        // SWR 캐시 갱신하여 댓글 목록을 즉시 업데이트
        mutate(['comments', postId]);
      } else {
        toast.error(result.message || '댓글 등록 중 오류가 발생했습니다.');
      }
    } catch (_err) {
      console.error(_err);
      toast.error('댓글 등록 중 오류가 발생했습니다.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={cn('space-y-4 rounded-lg bg-zinc-800 p-2', className)}
    >
      <TiptapEditor
        id="comment-editor"
        content={contentHtml}
        onChangeText={handleChangeText}
        onChangeHtml={handleChangeHtml}
        placeholder="댓글을 작성하세요..."
        className="comment-content focus-visible:ring-ring min-h-[40px] rounded-md border-zinc-300 bg-transparent focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none dark:border-zinc-700"
      />
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isSubmitting || !contentText.trim()}
        >
          {isSubmitting ? '작성 중...' : '댓글 작성'}
        </Button>
      </div>
    </form>
  );
}
