'use client'; // S<PERSON> hook requires this to be a client component

import { getCommentsByPostId } from '@/actions/community';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'; // Import Alert for error state
import { Skeleton } from '@/components/ui/skeleton'; // Import Skeleton for loading state
import { Terminal } from 'lucide-react';
import useSWR from 'swr';
import PostCommentDisplay from './post-comment-display';

// Define the comment data type to match what PostCommentDisplay expects
// and the data structure returned by getCommentsByPostId
type CommentData = {
  id: string;
  content: string;
  createdAt: Date;
  // Include properties expected by PostCommentDisplay
  postId: string;
  userId: string;
  updatedAt: Date;
  deletedAt: Date | null;
  user: {
    id: string;
    name: string;
    image: string | null;
  };
  // Include other properties if necessary
};

interface PostCommentListProps {
  postId: string;
  className?: string;
}

// Define the fetcher function for SWR
const fetchComments = async ([, postId]: [string, string]): Promise<
  CommentData[] | null
> => {
  const result = await getCommentsByPostId({ postId });
  if (!result.success) {
    // Throw an error with the message from the action result
    throw new Error(
      result.message || '댓글을 불러오는 중 오류가 발생했습니다.'
    );
  }
  // Return the data on success
  return result.data;
};

export default function PostCommentList({
  postId,
  className = '',
}: PostCommentListProps) {
  // Use SWR to fetch comments. The key includes the postId to ensure uniqueness.
  const {
    data: commentsData,
    error,
    isLoading,
  } = useSWR(
    ['comments', postId], // SWR key - using an array is a common pattern
    fetchComments
  );

  // Loading state
  if (isLoading) {
    return (
      <div className={`mt-8 border-t pt-2 ${className}`}>
        <Skeleton className="mb-4 h-6 w-24" />
        <div className="space-y-3">
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert
        variant="destructive"
        className={`mt-2 ${className}`}
      >
        <Terminal className="h-4 w-4" />
        <AlertTitle>오류 발생</AlertTitle>
        <AlertDescription>
          {error.message || '댓글을 불러오지 못했습니다.'}
        </AlertDescription>
      </Alert>
    );
  }

  // No comments state
  if (!commentsData || commentsData.length === 0) {
    return (
      <div
        className={`text-muted-foreground mt-2 border-t pt-2 text-center ${className}`}
      >
        아직 댓글이 없습니다.
      </div>
    );
  }

  // Success state - display comments
  return (
    <div className={`${className}`}>
      <ul className="space-y-3">
        {commentsData.map((comment) => (
          <li key={comment.id}>
            {/* Ensure PostCommentDisplay props match CommentData type */}
            <PostCommentDisplay comment={comment} />
          </li>
        ))}
      </ul>
    </div>
  );
}
