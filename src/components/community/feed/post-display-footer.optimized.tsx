'use client';

import { formatRelativeTime } from '@/lib/utils';
import Link from 'next/link';
import { memo, useMemo } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '../../ui/avatar';
import PostDisplayMeta from './post-display-meta.optimized';

// PostDisplay에서 사용하던 PostType의 user 부분과 필요한 메타 정보를 포함하는 인터페이스
interface PostDisplayFooterProps {
  user?: {
    id: string;
    name?: string | null;
    displayName?: string | null;
    image?: string | null;
  } | null;
  createdAt: Date;
  viewCount: number;
  commentCount: number;
  country?: string | null; // country 필드 추가
}

/**
 * 게시글 푸터 컴포넌트
 * 
 * 작성자 정보, 작성 시간, 조회수, 댓글 수 등을 표시합니다.
 */
function PostDisplayFooter({
  user,
  createdAt,
  viewCount,
  commentCount,
}: PostDisplayFooterProps) {
  // useMemo를 사용하여 계산 결과 메모이제이션
  const authorName = useMemo(() => user?.displayName || user?.name || '익명', [user?.displayName, user?.name]);
  const authorAvatarUrl = user?.image;
  const timeAgo = useMemo(() => formatRelativeTime(createdAt), [createdAt]);

  return (
    <>
      <div className="mt-3 flex justify-end text-xs text-zinc-500 dark:text-zinc-400">
        {timeAgo}
      </div>
      <div className="mt-1 flex justify-between">
        <Link
          href={user?.name ? `/user/@${user.name}` : '#'}
          data-testid="user-profile-link"
          onClick={(e) => e.stopPropagation()} // Footer 내의 링크 클릭 시 상위 Link 이동 방지
          className="flex items-center space-x-2"
        >
          <Avatar className="h-6 w-6">
            <AvatarImage src={authorAvatarUrl ?? undefined} />
            <AvatarFallback>{authorName[0]}</AvatarFallback>
          </Avatar>
          <div className="flex items-center space-x-2 text-sm">
            <span className="font-semibold text-zinc-800 dark:text-zinc-200">
              {authorName}
            </span>
          </div>
        </Link>
        {/* Actions Section */}
        <div className="mt-2 flex justify-end">
          <PostDisplayMeta
            viewCount={viewCount}
            commentCount={commentCount}
          />
        </div>
      </div>
    </>
  );
}

/**
 * 게시글 푸터 컴포넌트를 메모이제이션하여 성능 최적화
 * 
 * 작성자 정보, 작성 시간, 조회수, 댓글 수가 변경되지 않으면 리렌더링하지 않습니다.
 */
export default memo(PostDisplayFooter, (prevProps, nextProps) => {
  // 사용자 정보 비교
  const isSameUser = 
    prevProps.user?.id === nextProps.user?.id &&
    prevProps.user?.name === nextProps.user?.name &&
    prevProps.user?.displayName === nextProps.user?.displayName &&
    prevProps.user?.image === nextProps.user?.image;
  
  // 메타 정보 비교
  const isSameMeta = 
    prevProps.createdAt.getTime() === nextProps.createdAt.getTime() &&
    prevProps.viewCount === nextProps.viewCount &&
    prevProps.commentCount === nextProps.commentCount;
  
  return isSameUser && isSameMeta;
});
