'use client';

import { formatRelativeTime } from '@/lib/utils';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '../../ui/avatar';
import { PostDisplayMeta } from './post-display-meta';

// PostDisplay에서 사용하던 PostType의 user 부분과 필요한 메타 정보를 포함하는 인터페이스
interface PostDisplayFooterProps {
  user?: {
    id: string;
    name?: string | null;
    displayName?: string | null;
    image?: string | null;
  } | null;
  createdAt: Date;
  viewCount: number;
  commentCount: number;
  country?: string | null; // country 필드 추가
}

export function PostDisplayFooter({
  user,
  createdAt,
  viewCount,
  commentCount,
}: PostDisplayFooterProps) {
  const authorName = user?.displayName || user?.name || '익명';
  const authorAvatarUrl = user?.image;
  const timeAgo = formatRelativeTime(createdAt);

  return (
    <>
      <div className="mt-3 flex justify-end text-xs text-zinc-500 dark:text-zinc-400">
        {timeAgo}
      </div>
      <div className="mt-1 flex justify-between">
        <Link
          href={user?.name ? `/user/@${user.name}` : '#'}
          data-testid="user-profile-link"
          onClick={(e) => e.stopPropagation()} // Footer 내의 링크 클릭 시 상위 Link 이동 방지
          className="flex items-center space-x-2"
        >
          <Avatar className="h-6 w-6">
            <AvatarImage src={authorAvatarUrl ?? undefined} />
            <AvatarFallback>{authorName[0]}</AvatarFallback>
          </Avatar>
          <div className="flex items-center space-x-2 text-sm">
            <span className="font-semibold text-zinc-800 dark:text-zinc-200">
              {authorName}
            </span>
          </div>
        </Link>
        {/* Actions Section */}
        <div className="mt-2 flex justify-end">
          <PostDisplayMeta
            viewCount={viewCount}
            commentCount={commentCount}
          />
        </div>
      </div>
    </>
  );
}
