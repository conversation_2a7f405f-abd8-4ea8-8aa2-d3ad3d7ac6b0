'use client';

import { TiptapEditor } from '@/components/editor/tiptap-editor';
import { PreviewData } from '@/types/preview';
import { JsonValue } from '@prisma/client/runtime/library';
import { memo } from 'react';
import { LinkPreview } from './link-preview';

interface PostDisplayMainProps {
  contentHtml?: string | null;
  preview?: JsonValue | PreviewData | null;
}

/**
 * 게시글 본문 컴포넌트
 * 
 * 게시글 내용과 링크 미리보기를 표시합니다.
 */
function PostDisplayMain({
  contentHtml,
  preview,
}: PostDisplayMainProps) {
  const previewData = preview as PreviewData | null; // 타입 단언

  return (
    <div>
      <TiptapEditor
        content={contentHtml || ''}
        editable={false}
        className="post-content prose prose-sm dark:prose-invert max-w-none"
      />

      {/* Link Preview Section */}
      {previewData && (
        <div className="mt-2">
          <LinkPreview
            url={previewData.url}
            previewData={previewData}
          />
        </div>
      )}
    </div>
  );
}

/**
 * 게시글 본문 컴포넌트를 메모이제이션하여 성능 최적화
 * 
 * 게시글 내용과 링크 미리보기가 변경되지 않으면 리렌더링하지 않습니다.
 */
export default memo(PostDisplayMain, (prevProps, nextProps) => {
  // 내용 비교
  const isSameContent = prevProps.contentHtml === nextProps.contentHtml;
  
  // 미리보기 비교 (간단한 비교)
  const isSamePreview = 
    (!prevProps.preview && !nextProps.preview) || 
    (prevProps.preview && nextProps.preview && 
      JSON.stringify(prevProps.preview) === JSON.stringify(nextProps.preview));
  
  return isSameContent && isSamePreview;
});
