'use client';

import { TiptapEditor } from '@/components/editor/tiptap-editor';
import { PreviewData } from '@/types/preview';
import { JsonValue } from '@prisma/client/runtime/library';
import { LinkPreview } from './link-preview';

interface PostDisplayMainProps {
  contentHtml?: string | null;
  preview?: JsonValue | PreviewData | null;
}

export function PostDisplayMain({
  contentHtml,
  preview,
}: PostDisplayMainProps) {
  const previewData = preview as PreviewData | null; // 타입 단언

  return (
    <div>
      <TiptapEditor
        content={contentHtml || ''}
        editable={false}
        className="post-content prose prose-sm dark:prose-invert max-w-none"
      />

      {/* Link Preview Section */}
      {previewData && (
        <div className="mt-2">
          <LinkPreview
            url={previewData.url}
            previewData={previewData}
          />
        </div>
      )}
    </div>
  );
}
