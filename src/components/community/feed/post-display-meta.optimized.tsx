'use client';

import { Eye, MessageSquare } from 'lucide-react';
import { memo } from 'react';

// 인터페이스 이름을 PostDisplayMetaProps로 변경
interface PostDisplayMetaProps {
  viewCount: number;
  commentCount: number;
}

/**
 * 게시글 메타 정보 표시 컴포넌트
 * 
 * 조회수와 댓글 수를 표시합니다.
 */
function PostDisplayMeta({
  viewCount,
  commentCount,
}: PostDisplayMetaProps) {
  return (
    <div className="flex items-center justify-start space-x-2 text-sm">
      <div className="flex items-center space-x-1">
        <Eye className="h-4 w-4" />
        <span>{viewCount}</span>
      </div>
      <div className="flex items-center space-x-1">
        <MessageSquare className="h-4 w-4" />
        <span>{commentCount}</span>
      </div>
    </div>
  );
}

/**
 * 게시글 메타 정보 표시 컴포넌트를 메모이제이션하여 성능 최적화
 * 
 * 조회수와 댓글 수가 변경되지 않으면 리렌더링하지 않습니다.
 */
export default memo(PostDisplayMeta, (prevProps, nextProps) => {
  return (
    prevProps.viewCount === nextProps.viewCount &&
    prevProps.commentCount === nextProps.commentCount
  );
});
