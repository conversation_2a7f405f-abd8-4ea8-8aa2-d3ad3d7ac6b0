'use client';

import { Eye, MessageSquare } from 'lucide-react';

// 인터페이스 이름을 PostDisplayMetaProps로 변경
interface PostDisplayMetaProps {
  viewCount: number;
  commentCount: number;
}

// 함수 이름을 PostDisplayMeta로 변경
export function PostDisplayMeta({
  viewCount,
  commentCount,
}: PostDisplayMetaProps) {
  return (
    <div className="flex items-center justify-start space-x-2 text-sm">
      <div className="flex items-center space-x-1">
        <Eye className="h-4 w-4" />
        <span>{viewCount}</span>
      </div>
      <div className="flex items-center space-x-1">
        <MessageSquare className="h-4 w-4" />
        <span>{commentCount}</span>
      </div>
    </div>
  );
}
