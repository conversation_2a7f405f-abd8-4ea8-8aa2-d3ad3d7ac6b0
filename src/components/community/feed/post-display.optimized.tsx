'use client';

import { cn } from '@/lib/utils';
import { PreviewData } from '@/types/preview';
import { JsonValue } from '@prisma/client/runtime/library';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { memo } from 'react';
import PostDisplayFooter from './post-display-footer.optimized';
import PostDisplayHeader from './post-display-header.optimized';
import PostDisplayMain from './post-display-main.optimized';

// post 객체의 타입을 정의합니다. 실제 타입 정의 위치에 따라 조정해야 할 수 있습니다.
interface PostType {
  id: string;
  content?: string | null;
  contentHtml?: string | null;
  preview?: JsonValue | PreviewData | null;
  createdAt: Date;
  likeCount?: number | null;
  commentCount?: number | null;
  viewCount?: number | null;
  country?: string | null; // country 필드 추가
  user?: {
    id: string;
    name?: string | null;
    displayName?: string | null;
    image?: string | null;
  } | null;
}

interface PostDisplayProps {
  post: PostType;
  className?: string;
}

/**
 * 게시글 표시 컴포넌트
 *
 * 게시글의 헤더, 본문, 푸터를 표시합니다.
 */
function PostDisplay({
  post,
  className = '',
}: PostDisplayProps) {
  const { data: session } = useSession();
  const currentUserId = session?.user?.id;

  return (
    <div
      className={cn(
        'relative flex flex-col rounded-lg bg-zinc-800 p-4',
        className
      )}
    >
      <PostDisplayHeader
        postId={post.id}
        postAuthorId={post.user?.id}
        currentUserId={currentUserId}
      />
      <Link
        href={`/community/feed/${post.id}`}
        className="hover:bg-muted/50 block transition-colors"
      >
        <PostDisplayMain
          contentHtml={post.contentHtml}
          preview={post.preview}
        />
      </Link>
      <PostDisplayFooter
        user={post.user}
        createdAt={post.createdAt}
        viewCount={post.viewCount || 0}
        commentCount={post.commentCount || 0}
        country={post.country} // country 정보 전달
      />
    </div>
  );
}

/**
 * 게시글 표시 컴포넌트를 메모이제이션하여 성능 최적화
 *
 * 게시글 데이터가 변경되지 않으면 리렌더링하지 않습니다.
 */
export default memo(PostDisplay, (prevProps, nextProps) => {
  // 게시글 ID 비교
  if (prevProps.post.id !== nextProps.post.id) return false;

  // 게시글 내용 비교
  const isSameContent =
    prevProps.post.contentHtml === nextProps.post.contentHtml &&
    JSON.stringify(prevProps.post.preview) === JSON.stringify(nextProps.post.preview);

  // 게시글 메타 정보 비교
  const isSameMeta =
    prevProps.post.createdAt.getTime() === nextProps.post.createdAt.getTime() &&
    prevProps.post.viewCount === nextProps.post.viewCount &&
    prevProps.post.commentCount === nextProps.post.commentCount &&
    prevProps.post.likeCount === nextProps.post.likeCount;

  // 사용자 정보 비교
  const isSameUser =
    (!prevProps.post.user && !nextProps.post.user) ||
    (prevProps.post.user && nextProps.post.user &&
      prevProps.post.user.id === nextProps.post.user.id &&
      prevProps.post.user.name === nextProps.post.user.name &&
      prevProps.post.user.displayName === nextProps.post.user.displayName &&
      prevProps.post.user.image === nextProps.post.user.image);

  // 클래스 이름 비교
  const isSameClassName = prevProps.className === nextProps.className;

  return isSameContent && isSameMeta && isSameUser && isSameClassName;
});
