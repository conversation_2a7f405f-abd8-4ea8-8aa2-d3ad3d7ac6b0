'use client';

import { cn } from '@/lib/utils';
import { PreviewData } from '@/types/preview';
import { JsonValue } from '@prisma/client/runtime/library';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { PostDisplayFooter } from './post-display-footer';
import { PostDisplayHeader } from './post-display-header';
import { PostDisplayMain } from './post-display-main';
// post 객체의 타입을 정의합니다. 실제 타입 정의 위치에 따라 조정해야 할 수 있습니다.
interface PostType {
  id: string;
  content?: string | null;
  contentHtml?: string | null;
  preview?: JsonValue | PreviewData | null;
  createdAt: Date;
  likeCount?: number | null;
  commentCount?: number | null;
  viewCount?: number | null;
  country?: string | null; // country 필드 추가
  user?: {
    id: string;
    name?: string | null;
    displayName?: string | null;
    image?: string | null;
  } | null;
}

interface PostDisplayProps {
  post: PostType;
  className?: string;
}

export default function PostDisplay({
  post,
  className = '',
}: PostDisplayProps) {
  const { data: session } = useSession();
  const currentUserId = session?.user?.id;

  return (
    <div
      className={cn(
        'relative flex flex-col rounded-lg bg-zinc-800 p-4',
        className
      )}
    >
      <PostDisplayHeader
        postId={post.id}
        postAuthorId={post.user?.id}
        currentUserId={currentUserId}
      />
      <Link
        href={`/community/feed/${post.id}`}
        className="hover:bg-muted/50 block transition-colors"
      >
        <PostDisplayMain
          contentHtml={post.contentHtml}
          preview={post.preview}
        />
      </Link>
      <PostDisplayFooter
        user={post.user}
        createdAt={post.createdAt}
        viewCount={post.viewCount || 0}
        commentCount={post.commentCount || 0}
        country={post.country} // country 정보 전달
      />
    </div>
  );
}
