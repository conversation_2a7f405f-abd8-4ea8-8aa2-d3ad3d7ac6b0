'use client';

import { ReactNode } from 'react';

interface CommunityClientLayoutProps {
  children: ReactNode;
}

export default function CommunityClientLayout({
  children,
}: CommunityClientLayoutProps) {
  return (
    <main className="mt-3 w-full p-3">
      <div className="min-h-screen">
        <div className="max-w-[var(--main-max-width)] flex-grow">
          {children}
        </div>
      </div>
    </main>
  );
}
