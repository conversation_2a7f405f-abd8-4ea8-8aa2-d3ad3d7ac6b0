'use client';

import Script from 'next/script';
import { useEffect, useRef } from 'react';

declare global {
  interface Window {
    kakao: any;
  }
}

interface KakaoMapProps {
  apiKey: string;
  width?: string | number;
  height?: string | number;
  latitude?: number;
  longitude?: number;
  level?: number;
}

export default function KakaoMap({
  apiKey,
  width = '100%',
  height = 400,
  latitude = 37.5665,
  longitude = 126.978,
  level = 3,
}: KakaoMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);

  useEffect(() => {
    // 카카오맵 API가 로드된 후 실행될 함수
    const initializeMap = () => {
      if (!mapRef.current) return;
      if (!window.kakao) return;

      const options = {
        center: new window.kakao.maps.LatLng(latitude, longitude),
        level,
      };

      // 지도 생성
      const map = new window.kakao.maps.Map(mapRef.current, options);
      mapInstanceRef.current = map;

      // 마커 생성
      const markerPosition = new window.kakao.maps.LatLng(latitude, longitude);
      const marker = new window.kakao.maps.Marker({
        position: markerPosition,
      });
      marker.setMap(map);
    };

    // 카카오맵 API가 이미 로드되었는지 확인
    if (window.kakao && window.kakao.maps) {
      initializeMap();
    } else {
      // 카카오맵 API 로드 완료 이벤트 리스너
      window.kakao?.maps?.load?.(initializeMap);
    }
  }, [latitude, longitude, level]);

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`//dapi.kakao.com/v2/maps/sdk.js?appkey=${apiKey}&autoload=false`}
        onLoad={() => {
          window.kakao?.maps?.load?.(() => {
            if (mapRef.current && !mapInstanceRef.current) {
              const options = {
                center: new window.kakao.maps.LatLng(latitude, longitude),
                level,
              };
              const map = new window.kakao.maps.Map(mapRef.current, options);
              mapInstanceRef.current = map;

              const markerPosition = new window.kakao.maps.LatLng(
                latitude,
                longitude
              );
              const marker = new window.kakao.maps.Marker({
                position: markerPosition,
              });
              marker.setMap(map);
            }
          });
        }}
      />
      <div
        ref={mapRef}
        style={{
          width,
          height,
        }}
      />
    </>
  );
}
