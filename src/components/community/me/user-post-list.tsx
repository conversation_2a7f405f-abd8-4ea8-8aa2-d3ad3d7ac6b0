'use client';

import { getUserPosts } from '@/actions/posts';
import { PostWithAuthorAndCounts } from '@/types';
import Link from 'next/link';
import useSWR from 'swr';

interface UserPostListProps {
  userId: string;
}

export default function UserPostList({ userId }: UserPostListProps) {
  const {
    data: userPosts,
    error: postsError,
    isLoading: isLoadingPosts,
  } = useSWR<PostWithAuthorAndCounts[]>(
    userId ? ['user-posts', userId] : null,
    async ([, currentUserId]: [string, string]) =>
      getUserPosts(currentUserId, { take: 10 })
  );

  if (isLoadingPosts) {
    return <p className="text-muted-foreground mt-4">게시글 로딩 중...</p>;
  }

  if (postsError) {
    return (
      <p className="mt-4 text-red-500">게시글을 불러오는데 실패했습니다.</p>
    );
  }

  if (userPosts && userPosts.length > 0) {
    return (
      <ul className="mt-4 space-y-2">
        {userPosts.map((post) => (
          <li
            key={post.id}
            className="hover:bg-muted rounded-lg border bg-zinc-800 p-3 text-sm"
          >
            <Link
              href={`/community/post/${post.id}`}
              className="font-medium break-words hover:underline"
            >
              {post.content}
            </Link>
            <p className="text-muted-foreground text-xs">
              {new Date(post.createdAt).toLocaleDateString()} - 댓글{' '}
              {post.commentCount}개
            </p>
          </li>
        ))}
      </ul>
    );
  }

  return (
    <p className="text-muted-foreground mt-4">작성한 게시글이 없습니다.</p>
  );
}
