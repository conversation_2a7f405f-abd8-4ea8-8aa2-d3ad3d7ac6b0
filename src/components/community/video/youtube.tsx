import { YouTubeEmbed } from '@next/third-parties/google';

interface YouTubeProps {
  videoId: string;
  height?: number;
  width?: number;
  params?: string;
  title?: string;
}

export default function YouTubeComponent({
  videoId,
  height = 390,
  width = 640,
  params = '',
}: YouTubeProps) {
  return (
    <YouTubeEmbed
      videoid={videoId}
      height={height}
      width={width}
      params={params}
    />
  );
}
