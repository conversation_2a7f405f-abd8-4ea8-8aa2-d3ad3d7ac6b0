'use client';

/**
 * 성능 모니터링 대시보드 컴포넌트
 * 
 * 이 컴포넌트는 개발 모드에서만 사용되며, 애플리케이션의 성능을 모니터링하고 디버깅하는 데 도움을 줍니다.
 */

import { getAllPerformanceMetrics } from '@/lib/performance/web-vitals';
import { useEffect, useState } from 'react';

/**
 * 성능 모니터링 대시보드 컴포넌트
 */
export function PerformanceDashboard() {
  // 개발 환경에서만 렌더링
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const [metrics, setMetrics] = useState<Record<string, number>>({});
  const [isOpen, setIsOpen] = useState(false);

  // 성능 지표 주기적으로 업데이트
  useEffect(() => {
    const updateMetrics = () => {
      setMetrics(getAllPerformanceMetrics());
    };

    // 초기 업데이트
    updateMetrics();

    // 1초마다 업데이트
    const intervalId = setInterval(updateMetrics, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-50 rounded-full bg-primary p-2 text-xs text-primary-foreground shadow-lg"
        aria-label="성능 대시보드 열기"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M12 2v4" />
          <path d="M12 18v4" />
          <path d="m4.93 4.93 2.83 2.83" />
          <path d="m16.24 16.24 2.83 2.83" />
          <path d="M2 12h4" />
          <path d="M18 12h4" />
          <path d="m4.93 19.07 2.83-2.83" />
          <path d="m16.24 7.76 2.83-2.83" />
        </svg>
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-xs rounded-lg bg-background p-4 text-xs shadow-lg">
      <div className="mb-2 flex items-center justify-between">
        <h4 className="font-medium">성능 대시보드</h4>
        <button
          onClick={() => setIsOpen(false)}
          className="rounded-full p-1 hover:bg-muted"
          aria-label="닫기"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M18 6 6 18" />
            <path d="m6 6 12 12" />
          </svg>
        </button>
      </div>

      <div className="space-y-1">
        {Object.keys(metrics).length === 0 ? (
          <div className="text-muted-foreground">측정된 지표가 없습니다.</div>
        ) : (
          Object.entries(metrics).map(([name, value]) => (
            <div
              key={name}
              className="flex justify-between"
            >
              <span>{name}:</span>
              <span>{typeof value === 'number' ? value.toFixed(2) : value}</span>
            </div>
          ))
        )}

        <div
          id="web-vitals-metrics"
          className="mt-2 border-t border-border pt-2"
        >
          <div className="mb-1 font-medium">Core Web Vitals</div>
          <div className="flex justify-between">
            <span>LCP:</span>
            <span id="metric-lcp">측정 중...</span>
          </div>
          <div className="flex justify-between">
            <span>FID:</span>
            <span id="metric-fid">측정 중...</span>
          </div>
          <div className="flex justify-between">
            <span>CLS:</span>
            <span id="metric-cls">측정 중...</span>
          </div>
          <div className="flex justify-between">
            <span>TTFB:</span>
            <span id="metric-ttfb">측정 중...</span>
          </div>
          <div className="flex justify-between">
            <span>INP:</span>
            <span id="metric-inp">측정 중...</span>
          </div>
        </div>
      </div>
    </div>
  );
}
