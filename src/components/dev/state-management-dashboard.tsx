'use client';

/**
 * 상태 관리 모니터링 대시보드 컴포넌트
 *
 * 이 컴포넌트는 개발 모드에서만 사용되며, 애플리케이션의 상태를 모니터링하고 디버깅하는 데 도움을 줍니다.
 */

import {
  CommunityStateAPI,
  DataStateAPI,
  UIStateAPI,
  UserStateAPI,
} from '@/store/api';
import { useEffect, useState } from 'react';

/**
 * 상태 관리 모니터링 대시보드 컴포넌트
 */
export function StateManagementDashboard() {
  // 개발 환경에서만 렌더링
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="bg-background w-full overflow-auto rounded border shadow-lg">
      <div className="space-y-4 p-4">
        <UIStateMonitor />
        <UserStateMonitor />
        <CommunityStateMonitor />
        <DataStateMonitor />
      </div>
    </div>
  );
}

/**
 * UI 상태 모니터
 */
function UIStateMonitor() {
  const [state, setState] = useState(UIStateAPI.getStore());

  useEffect(() => {
    // 상태 변경 시 업데이트
    const unsubscribe = UIStateAPI.getStore().subscribe((newState) => {
      setState(newState);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">UI 상태</h4>
      <div className="space-y-1 text-xs">
        <div>테마: {state.theme}</div>
        <div>
          모달: {state.modal.isOpen ? `${state.modal.type} (열림)` : '닫힘'}
        </div>
        <div>토스트: {state.toasts.length}개</div>
        <div>사이드바: {state.isSidebarOpen ? '열림' : '닫힘'}</div>
        <div>로딩 중: {state.isLoading ? '예' : '아니오'}</div>
      </div>
    </div>
  );
}

/**
 * 사용자 상태 모니터
 */
function UserStateMonitor() {
  const [state, setState] = useState(UserStateAPI.getStore());

  useEffect(() => {
    // 상태 변경 시 업데이트
    const unsubscribe = UserStateAPI.getStore().subscribe((newState) => {
      setState(newState);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">사용자 상태</h4>
      <div className="space-y-1 text-xs">
        <div>인증됨: {state.isAuthenticated ? '예' : '아니오'}</div>
        <div>사용자: {state.data?.user?.name || '없음'}</div>
        <div>언어: {state.preferences.language}</div>
        <div>
          알림: {state.preferences.notificationsEnabled ? '활성화' : '비활성화'}
        </div>
        <div>
          마지막 활동:{' '}
          {state.activity.lastActivity
            ? new Date(state.activity.lastActivity).toLocaleString()
            : '없음'}
        </div>
        <div>방문 횟수: {state.activity.visitCount}</div>
      </div>
    </div>
  );
}

/**
 * 커뮤니티 상태 모니터
 */
function CommunityStateMonitor() {
  const [state, setState] = useState(CommunityStateAPI.getStore());

  useEffect(() => {
    // 상태 변경 시 업데이트
    const unsubscribe = CommunityStateAPI.getStore().subscribe((newState) => {
      setState(newState);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">커뮤니티 상태</h4>
      <div className="space-y-1 text-xs">
        <div>새로고침 필요: {state.needsPostListRefresh ? '예' : '아니오'}</div>
        <div>필터 - 국가: {state.filters.country || '전체'}</div>
        <div>필터 - 정렬: {state.filters.sortBy}</div>
        <div>필터 - 검색어: {state.filters.searchQuery || '없음'}</div>
        <div>최근 본 게시물: {state.recentlyViewedPosts.length}개</div>
        <div>임시 저장: {state.draftPost ? '있음' : '없음'}</div>
        <div>선택된 게시물: {state.selectedPostId || '없음'}</div>
      </div>
    </div>
  );
}

/**
 * 데이터 상태 모니터
 */
function DataStateMonitor() {
  const [state, setState] = useState(DataStateAPI.getStore());

  useEffect(() => {
    // 상태 변경 시 업데이트
    const unsubscribe = DataStateAPI.getStore().subscribe((newState) => {
      setState(newState);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // 캐시 항목 수 계산
  const cacheCount = Object.keys(state.cache).length;

  // 로딩 중인 항목 수 계산
  const loadingCount = state.loadingKeys.length;

  // 에러 항목 수 계산
  const errorCount = Object.values(state.errors).filter(Boolean).length;

  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">데이터 상태</h4>
      <div className="space-y-1 text-xs">
        <div>캐시된 항목: {cacheCount}개</div>
        <div>로딩 중인 항목: {loadingCount}개</div>
        <div>에러 항목: {errorCount}개</div>
        {loadingCount > 0 && (
          <div>
            <div>로딩 중:</div>
            <ul className="ml-4 list-disc">
              {state.loadingKeys.map((key) => (
                <li key={key}>{key}</li>
              ))}
            </ul>
          </div>
        )}
        {errorCount > 0 && (
          <div>
            <div>에러:</div>
            <ul className="ml-4 list-disc">
              {Object.entries(state.errors)
                .filter(([_, error]) => error)
                .map(([key, error]) => (
                  <li key={key}>
                    {key}: {error?.message}
                  </li>
                ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
