'use client';

import { cn } from '@/lib/utils';

interface ContentRendererProps {
  content: string;
  className?: string;
}

/**
 * HTML 콘텐츠를 렌더링하는 컴포넌트
 * 
 * @param content HTML 문자열
 * @param className 추가 클래스명
 */
export default function ContentRenderer({ content, className }: ContentRendererProps) {
  return (
    <div 
      className={cn(
        'prose prose-sm sm:prose lg:prose-lg max-w-none',
        'prose-headings:font-bold prose-headings:text-foreground',
        'prose-p:text-foreground prose-strong:text-foreground',
        'prose-a:text-primary prose-a:no-underline hover:prose-a:underline',
        'prose-code:text-muted-foreground prose-code:bg-muted prose-code:rounded prose-code:px-1',
        'prose-blockquote:text-muted-foreground prose-blockquote:border-l-primary',
        'prose-img:rounded-md',
        'prose-table:border-collapse prose-table:w-full',
        'prose-th:border prose-th:border-border prose-th:p-2 prose-th:bg-muted',
        'prose-td:border prose-td:border-border prose-td:p-2',
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
