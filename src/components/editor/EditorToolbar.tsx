"use client";

import { Editor } from "@tiptap/react";
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link,
  Image,
  Table,
  Youtube,
  Heading1,
  Heading2,
  Heading3,
  Undo,
  Redo,
  Code,
  Quote,
  Unlink,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Toggle } from "@/components/ui/toggle";
import { useState } from "react";
import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ImageUploader from "./ImageUploader";

interface EditorToolbarProps {
  editor: Editor;
  addImage: (url: string, alt?: string) => void;
  addLink: (url: string, text?: string) => void;
  addYoutube: (url: string) => void;
}

export default function EditorToolbar({
  editor,
  addImage,
  addLink,
  addYoutube,
}: EditorToolbarProps) {
  const t = useTranslations("Editor");

  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");
  const [linkUrl, setLinkUrl] = useState("");
  const [linkText, setLinkText] = useState("");
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [codeLanguage, setCodeLanguage] = useState("javascript");

  // 지원하는 코드 언어 목록
  const codeLanguages = [
    { value: "javascript", label: "JavaScript" },
    { value: "typescript", label: "TypeScript" },
    { value: "html", label: "HTML" },
    { value: "css", label: "CSS" },
    { value: "python", label: "Python" },
    { value: "java", label: "Java" },
    { value: "json", label: "JSON" },
    { value: "bash", label: "Bash/Shell" },
  ];

  const handleImageSubmit = () => {
    if (imageUrl) {
      addImage(imageUrl, imageAlt);
      setImageUrl("");
      setImageAlt("");
    }
  };

  const handleLinkSubmit = () => {
    if (linkUrl) {
      addLink(linkUrl, linkText);
      setLinkUrl("");
      setLinkText("");
    }
  };

  const handleYoutubeSubmit = () => {
    if (youtubeUrl) {
      addYoutube(youtubeUrl);
      setYoutubeUrl("");
    }
  };

  if (!editor) {
    return null;
  }

  return (
    <div className="border-b p-1 flex flex-wrap gap-1 items-center overflow-x-auto scrollbar-hide">
      {/* 기본 서식 그룹 */}
      <div className="flex items-center flex-nowrap">
        <Toggle
          size="sm"
          pressed={editor.isActive("bold")}
          onPressedChange={() => editor.chain().focus().toggleBold().run()}
          aria-label={t("bold")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Bold className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("italic")}
          onPressedChange={() => editor.chain().focus().toggleItalic().run()}
          aria-label={t("italic")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Italic className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("underline")}
          onPressedChange={() => editor.chain().focus().toggleUnderline().run()}
          aria-label={t("underline")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Underline className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("strike")}
          onPressedChange={() => editor.chain().focus().toggleStrike().run()}
          aria-label={t("strikethrough")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Strikethrough className="h-4 w-4" />
        </Toggle>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* 제목 그룹 */}
      <div className="flex items-center flex-nowrap">
        <Toggle
          size="sm"
          pressed={editor.isActive("heading", { level: 1 })}
          onPressedChange={() =>
            editor.chain().focus().toggleHeading({ level: 1 }).run()
          }
          aria-label={t("heading1")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Heading1 className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("heading", { level: 2 })}
          onPressedChange={() =>
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }
          aria-label={t("heading2")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Heading2 className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("heading", { level: 3 })}
          onPressedChange={() =>
            editor.chain().focus().toggleHeading({ level: 3 }).run()
          }
          aria-label={t("heading3")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Heading3 className="h-4 w-4" />
        </Toggle>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* 목록 그룹 */}
      <div className="flex items-center flex-nowrap">
        <Toggle
          size="sm"
          pressed={editor.isActive("bulletList")}
          onPressedChange={() =>
            editor.chain().focus().toggleBulletList().run()
          }
          aria-label={t("bulletList")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <List className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("orderedList")}
          onPressedChange={() =>
            editor.chain().focus().toggleOrderedList().run()
          }
          aria-label={t("orderedList")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <ListOrdered className="h-4 w-4" />
        </Toggle>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* 정렬 그룹 */}
      <div className="flex items-center flex-nowrap">
        <Toggle
          size="sm"
          pressed={editor.isActive({ textAlign: "left" })}
          onPressedChange={() =>
            editor.chain().focus().setTextAlign("left").run()
          }
          aria-label={t("alignLeft")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <AlignLeft className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive({ textAlign: "center" })}
          onPressedChange={() =>
            editor.chain().focus().setTextAlign("center").run()
          }
          aria-label={t("alignCenter")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <AlignCenter className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive({ textAlign: "right" })}
          onPressedChange={() =>
            editor.chain().focus().setTextAlign("right").run()
          }
          aria-label={t("alignRight")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <AlignRight className="h-4 w-4" />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive({ textAlign: "justify" })}
          onPressedChange={() =>
            editor.chain().focus().setTextAlign("justify").run()
          }
          aria-label={t("alignJustify")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <AlignJustify className="h-4 w-4" />
        </Toggle>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* 특수 블록 그룹 */}
      <div className="flex items-center flex-nowrap">
        <Toggle
          size="sm"
          pressed={editor.isActive("blockquote")}
          onPressedChange={() =>
            editor.chain().focus().toggleBlockquote().run()
          }
          aria-label={t("blockquote")}
          className="md:h-8 md:w-8 h-9 w-9 p-0 md:p-1"
        >
          <Quote className="h-4 w-4" />
        </Toggle>
        {/* 코드 블록 다이얼로그 */}
        <Dialog>
          <DialogTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive("codeBlock")}
              aria-label={t("codeBlock")}
            >
              <Code className="h-4 w-4" />
            </Toggle>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("addCodeBlock")}</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="code-language" className="text-right">
                  {t("language")}
                </Label>
                <Select value={codeLanguage} onValueChange={setCodeLanguage}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder={t("language")} />
                  </SelectTrigger>
                  <SelectContent>
                    {codeLanguages.map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  type="button"
                  onClick={() => {
                    editor
                      .chain()
                      .focus()
                      .toggleCodeBlock({ language: codeLanguage })
                      .run();
                  }}
                >
                  {t("add")}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Separator orientation="vertical" className="h-6" />

      <div className="flex items-center">
        {/* 링크 추가 다이얼로그 */}
        <Dialog>
          <DialogTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive("link")}
              aria-label="링크"
            >
              <Link className="h-4 w-4" />
            </Toggle>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>링크 추가</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="link-url" className="text-right">
                  URL
                </Label>
                <Input
                  id="link-url"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                  className="col-span-3"
                  placeholder="https://example.com"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="link-text" className="text-right">
                  텍스트
                </Label>
                <Input
                  id="link-text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  className="col-span-3"
                  placeholder="링크 텍스트 (선택사항)"
                />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" onClick={handleLinkSubmit}>
                  추가
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 링크 제거 버튼 */}
        <Toggle
          size="sm"
          pressed={false}
          onPressedChange={() => editor.chain().focus().unsetLink().run()}
          aria-label="링크 제거"
          disabled={!editor.isActive("link")}
        >
          <Unlink className="h-4 w-4" />
        </Toggle>

        {/* 이미지 추가 다이얼로그 */}
        <Dialog>
          <DialogTrigger asChild>
            <Toggle size="sm" aria-label="이미지">
              <Image className="h-4 w-4" />
            </Toggle>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>이미지 추가</DialogTitle>
            </DialogHeader>
            <ImageUploader
              onImageUploaded={(url, alt) => {
                addImage(url, alt);
              }}
            />
          </DialogContent>
        </Dialog>

        {/* 테이블 추가 버튼 */}
        <Toggle
          size="sm"
          pressed={false}
          onPressedChange={() =>
            editor
              .chain()
              .focus()
              .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
              .run()
          }
          aria-label="테이블"
        >
          <Table className="h-4 w-4" />
        </Toggle>

        {/* 유튜브 추가 다이얼로그 */}
        <Dialog>
          <DialogTrigger asChild>
            <Toggle size="sm" aria-label="유튜브">
              <Youtube className="h-4 w-4" />
            </Toggle>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>유튜브 영상 추가</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="youtube-url" className="text-right">
                  유튜브 URL
                </Label>
                <Input
                  id="youtube-url"
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                  className="col-span-3"
                  placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" onClick={handleYoutubeSubmit}>
                  추가
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Separator orientation="vertical" className="h-6" />

      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          aria-label="실행 취소"
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          aria-label="다시 실행"
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
