"use client";

import { useState, useRef, ChangeEvent } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Upload } from "lucide-react";
import { uploadToSupabase } from "@/lib/supabase/storage";
import { v4 as uuidv4 } from "uuid";

interface ImageUploaderProps {
  onImageUploaded: (url: string, alt?: string) => void;
  onClose?: () => void;
}

export default function ImageUploader({
  onImageUploaded,
  onClose,
}: ImageUploaderProps) {
  const t = useTranslations("Editor");

  const [isUploading, setIsUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 파일 선택 핸들러
  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      await uploadFile(files[0]);
    }
  };

  // 드래그 앤 드롭 핸들러
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      await uploadFile(e.dataTransfer.files[0]);
    }
  };

  // 파일 업로드 함수
  const uploadFile = async (file: File) => {
    if (!file.type.startsWith("image/")) {
      alert("이미지 파일만 업로드할 수 있습니다.");
      return;
    }

    setIsUploading(true);
    try {
      // 파일 이름 생성 (중복 방지를 위해 UUID 사용)
      const fileName = `${uuidv4()}-${file.name.replace(
        /[^a-zA-Z0-9.]/g,
        "_"
      )}`;

      // Supabase Storage에 업로드
      const { url, error } = await uploadToSupabase("images", fileName, file);

      if (error) {
        throw error;
      }

      if (url) {
        setImageUrl(url);
      }
    } catch (error) {
      console.error("이미지 업로드 오류:", error);
      alert("이미지 업로드 중 오류가 발생했습니다.");
    } finally {
      setIsUploading(false);
    }
  };

  // 이미지 추가 버튼 클릭 핸들러
  const handleAddImage = () => {
    if (imageUrl) {
      onImageUploaded(imageUrl, imageAlt);
      setImageUrl("");
      setImageAlt("");
      if (onClose) onClose();
    }
  };

  return (
    <div className="space-y-4">
      {/* URL 직접 입력 */}
      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="image-url" className="text-right">
          {t("imageUrl")}
        </Label>
        <Input
          id="image-url"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
          className="col-span-3"
          placeholder="https://example.com/image.jpg"
        />
      </div>

      <div className="grid grid-cols-4 items-center gap-4">
        <Label htmlFor="image-alt" className="text-right">
          {t("altText")}
        </Label>
        <Input
          id="image-alt"
          value={imageAlt}
          onChange={(e) => setImageAlt(e.target.value)}
          className="col-span-3"
          placeholder={t("altText")}
        />
      </div>

      {/* 파일 업로드 영역 */}
      <div className="mt-4">
        <div
          className={`border-2 border-dashed rounded-md p-6 text-center ${
            dragActive ? "border-primary bg-primary/5" : "border-border"
          }`}
          onDragEnter={handleDrag}
          onDragOver={handleDrag}
          onDragLeave={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />

          {isUploading ? (
            <div className="flex flex-col items-center justify-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-sm text-muted-foreground">{t("uploading")}</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-4">
              <Upload className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground mb-1">
                {t("dragImageHere")}
              </p>
              <p className="text-sm text-muted-foreground mb-1">{t("or")}</p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
              >
                {t("selectFile")}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* 미리보기 */}
      {imageUrl && !isUploading && (
        <div className="mt-4">
          <p className="text-sm font-medium mb-2">{t("preview")}:</p>
          <div className="border rounded-md p-2 max-h-[200px] overflow-hidden">
            <div className="relative h-[180px] w-full">
              <Image
                src={imageUrl}
                alt={imageAlt || t("preview")}
                className="mx-auto object-contain"
                fill
                sizes="(max-width: 768px) 100vw, 400px"
              />
            </div>
          </div>
        </div>
      )}

      {/* 버튼 */}
      <div className="flex justify-end mt-4">
        <Button
          type="button"
          onClick={handleAddImage}
          disabled={!imageUrl || isUploading}
        >
          {t("addImage")}
        </Button>
      </div>
    </div>
  );
}
