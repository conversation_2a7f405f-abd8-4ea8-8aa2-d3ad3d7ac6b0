"use client";

import { use<PERSON><PERSON><PERSON>, EditorContent, Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import Placeholder from "@tiptap/extension-placeholder";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Youtube from "@tiptap/extension-youtube";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import { lowlight } from "lowlight";
import "highlight.js/styles/github-dark.css"; // 코드 블록 스타일
import hljs from "highlight.js/lib/core";
// 자주 사용되는 언어 등록
import javascript from "highlight.js/lib/languages/javascript";
import typescript from "highlight.js/lib/languages/typescript";
import html from "highlight.js/lib/languages/xml";
import css from "highlight.js/lib/languages/css";
import python from "highlight.js/lib/languages/python";
import java from "highlight.js/lib/languages/java";
import json from "highlight.js/lib/languages/json";
import bash from "highlight.js/lib/languages/bash";
import { useCallback, useEffect } from "react";
import { useTranslations } from "next-intl";
import EditorToolbar from "./EditorToolbar";
import { cn } from "@/lib/utils";

export interface TiptapEditorProps {
  content: string;
  onChange: (html: string) => void;
  placeholder?: string;
  editable?: boolean;
  className?: string;
}

// highlight.js에 언어 등록
hljs.registerLanguage("javascript", javascript);
hljs.registerLanguage("typescript", typescript);
hljs.registerLanguage("html", html);
hljs.registerLanguage("css", css);
hljs.registerLanguage("python", python);
hljs.registerLanguage("java", java);
hljs.registerLanguage("json", json);
hljs.registerLanguage("bash", bash);

// lowlight에 highlight.js 등록
lowlight.registerAll();

export default function TiptapEditor({
  content,
  onChange,
  placeholder,
  editable = true,
  className,
}: TiptapEditorProps) {
  const t = useTranslations("Editor");
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // StarterKit에서 기본 코드 블록을 비활성화
        codeBlock: false,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-primary underline",
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: "rounded-md max-w-full h-auto",
        },
      }),
      Placeholder.configure({
        placeholder: placeholder || t("placeholder"),
      }),
      Underline,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: "border-collapse table-auto w-full",
        },
      }),
      TableRow,
      TableCell.configure({
        HTMLAttributes: {
          class: "border border-border p-2",
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: "border border-border p-2 bg-muted font-bold",
        },
      }),
      Youtube.configure({
        width: 640,
        height: 360,
        HTMLAttributes: {
          class: "mx-auto my-4",
        },
      }),
      // 구문 강조 기능이 있는 코드 블록 추가
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: "rounded-md p-4 bg-muted font-mono text-sm overflow-x-auto",
        },
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  // 외부에서 content가 변경되었을 때 에디터 내용 업데이트
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // 이미지 추가 함수
  const addImage = useCallback(
    (url: string, alt?: string) => {
      if (editor) {
        editor
          .chain()
          .focus()
          .setImage({ src: url, alt: alt || "" })
          .run();
      }
    },
    [editor]
  );

  // 링크 추가 함수
  const addLink = useCallback(
    (url: string, text?: string) => {
      if (editor) {
        if (text) {
          editor
            .chain()
            .focus()
            .extendMarkRange("link")
            .setLink({ href: url })
            .run();
        } else {
          editor
            .chain()
            .focus()
            .extendMarkRange("link")
            .setLink({ href: url })
            .run();
        }
      }
    },
    [editor]
  );

  // 유튜브 추가 함수
  const addYoutube = useCallback(
    (url: string) => {
      if (editor) {
        editor.chain().focus().setYoutubeVideo({ src: url }).run();
      }
    },
    [editor]
  );

  if (!editor) {
    return null;
  }

  return (
    <div className={cn("border rounded-md", className)}>
      <EditorToolbar
        editor={editor}
        addImage={addImage}
        addLink={addLink}
        addYoutube={addYoutube}
      />
      <EditorContent
        editor={editor}
        className={cn(
          "prose prose-sm sm:prose max-w-none p-4 focus:outline-none",
          "min-h-[200px] md:min-h-[300px]",
          "prose-headings:font-bold prose-headings:text-foreground",
          "prose-p:text-foreground prose-strong:text-foreground",
          "prose-a:text-primary prose-a:no-underline hover:prose-a:underline",
          "prose-code:text-muted-foreground prose-code:bg-muted prose-code:rounded prose-code:px-1",
          "prose-blockquote:text-muted-foreground prose-blockquote:border-l-primary",
          "prose-img:rounded-md",
          "prose-table:border-collapse prose-table:w-full",
          "prose-th:border prose-th:border-border prose-th:p-2 prose-th:bg-muted",
          "prose-td:border prose-td:border-border prose-td:p-2",
          "dark:prose-invert"
        )}
      />
    </div>
  );
}
