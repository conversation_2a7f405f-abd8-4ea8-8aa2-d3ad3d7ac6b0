'use client';

import { Editor } from '@tiptap/react';
import {
  AlignCenter,
  AlignJustify,
  AlignLeft,
  AlignRight,
  Bold,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Image,
  Italic,
  Link,
  List,
  ListOrdered,
  Palette,
  Quote,
  Redo,
  Strikethrough,
  Underline,
  Undo,
} from 'lucide-react';
import { useState } from 'react';

interface EditorMenuBarProps {
  editor: Editor;
}

export default function EditorMenuBar({ editor }: EditorMenuBarProps) {
  const [linkUrl, setLinkUrl] = useState('');
  const [showLinkInput, setShowLinkInput] = useState(false);
  const [showImageInput, setShowImageInput] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);

  if (!editor) {
    return null;
  }

  const addLink = () => {
    if (linkUrl) {
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({ href: linkUrl, target: '_blank' })
        .run();
      setLinkUrl('');
      setShowLinkInput(false);
    }
  };

  const addImage = () => {
    if (imageUrl) {
      editor.chain().focus().setImage({ src: imageUrl }).run();
      setImageUrl('');
      setShowImageInput(false);
    }
  };

  const setColor = (color: string) => {
    editor.chain().focus().setColor(color).run();
    setShowColorPicker(false);
  };

  return (
    <div>
      <div className="flex flex-wrap items-center gap-1">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('bold') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Bold"
        >
          <Bold size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('italic') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Italic"
        >
          <Italic size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('underline') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Underline"
        >
          <Underline size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('strike') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Strikethrough"
        >
          <Strikethrough size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('heading', { level: 1 }) ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Heading 1"
        >
          <Heading1 size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('heading', { level: 2 }) ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Heading 2"
        >
          <Heading2 size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('heading', { level: 3 }) ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Heading 3"
        >
          <Heading3 size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('bulletList') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Bullet List"
        >
          <List size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('orderedList') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Ordered List"
        >
          <ListOrdered size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('blockquote') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Quote"
        >
          <Quote size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('codeBlock') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Code Block"
        >
          <Code size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <div className="relative">
          <button
            onClick={() => setShowLinkInput(!showLinkInput)}
            className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
              editor.isActive('link') ? 'bg-gray-200 dark:bg-gray-700' : ''
            }`}
            title="Link"
          >
            <Link size={18} />
          </button>
          {showLinkInput && (
            <div className="absolute top-full left-0 z-10 mt-1 flex w-64 rounded border bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800">
              <input
                type="text"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
                className="flex-1 rounded-l border border-r-0 p-1 text-sm"
              />
              <button
                onClick={addLink}
                className="rounded-r border border-blue-500 bg-blue-500 px-2 text-sm text-white"
              >
                Add
              </button>
            </div>
          )}
        </div>

        <div className="relative">
          <button
            onClick={() => setShowImageInput(!showImageInput)}
            className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700`}
            title="Image"
          >
            <Image size={18} />
          </button>
          {showImageInput && (
            <div className="absolute top-full left-0 z-10 mt-1 flex w-64 rounded border bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800">
              <input
                type="text"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
                className="flex-1 rounded-l border border-r-0 p-1 text-sm"
              />
              <button
                onClick={addImage}
                className="rounded-r border border-blue-500 bg-blue-500 px-2 text-sm text-white"
              >
                Add
              </button>
            </div>
          )}
        </div>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${!editor.can().undo() ? 'opacity-30' : ''}`}
          title="Undo"
        >
          <Undo size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${!editor.can().redo() ? 'opacity-30' : ''}`}
          title="Redo"
        >
          <Redo size={18} />
        </button>
      </div>
    </div>
  );
}
