'use client';

import { Editor } from '@tiptap/react';
import {
  AlignCenter,
  AlignJustify,
  AlignLeft,
  AlignRight,
  Bold,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Image,
  Italic,
  Link,
  List,
  ListOrdered,
  Palette,
  Quote,
  Redo,
  Strikethrough,
  Underline,
  Undo,
} from 'lucide-react';
import { useState } from 'react';

interface EditorMenuBarProps {
  editor: Editor;
}

export default function EditorMenuBar({ editor }: EditorMenuBarProps) {
  const [linkUrl, setLinkUrl] = useState('');
  const [showLinkInput, setShowLinkInput] = useState(false);
  const [showImageInput, setShowImageInput] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);

  if (!editor) {
    return null;
  }

  const addLink = () => {
    if (linkUrl) {
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({ href: linkUrl, target: '_blank' })
        .run();
      setLinkUrl('');
      setShowLinkInput(false);
    }
  };

  const addImage = (url: string) => {
    if (url) {
      editor.chain().focus().setImage({ src: url }).run();
      setShowImageInput(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 파일 크기 제한 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('이미지 크기는 5MB 이하여야 합니다.');
      return;
    }

    // 이미지 파일 타입 확인 (더 엄격한 검증)
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('지원되는 이미지 형식: JPG, PNG, GIF, WebP');
      return;
    }

    // 이미지 리사이징 및 처리
    const img = document.createElement('img');
    const reader = new FileReader();

    reader.onload = (e) => {
      img.src = e.target?.result as string;

      img.onload = () => {
        // 캔버스를 사용하여 이미지 리사이징
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 캔버스 크기를 800x600으로 설정
        canvas.width = 800;
        canvas.height = 600;

        if (ctx) {
          // 배경을 흰색으로 설정 (투명 배경 방지)
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 이미지를 캔버스 중앙에 맞추기
          const scale = Math.min(
            canvas.width / img.width,
            canvas.height / img.height
          );

          const x = (canvas.width - img.width * scale) / 2;
          const y = (canvas.height - img.height * scale) / 2;

          // 이미지 그리기
          ctx.drawImage(
            img,
            0,
            0,
            img.width,
            img.height,
            x,
            y,
            img.width * scale,
            img.height * scale
          );

          // 캔버스를 이미지로 변환 (품질 0.85로 설정)
          const resizedImage = canvas.toDataURL('image/jpeg', 0.85);

          // 에디터에 이미지 추가
          addImage(resizedImage);
        }
      };
    };

    reader.readAsDataURL(file);
  };

  const setColor = (color: string) => {
    editor.chain().focus().setColor(color).run();
    setShowColorPicker(false);
  };

  return (
    <div>
      <div className="flex flex-wrap items-center gap-1">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700 ${
            editor.isActive('bold') ? 'bg-gray-200 dark:bg-gray-700' : ''
          }`}
          title="Bold"
        >
          <Bold size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('italic') ? 'bg-gray-200' : ''
          }`}
          title="Italic"
        >
          <Italic size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('underline') ? 'bg-gray-200' : ''
          }`}
          title="Underline"
        >
          <Underline size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('strike') ? 'bg-gray-200' : ''
          }`}
          title="Strikethrough"
        >
          <Strikethrough size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300"></div>

        <button
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 1 }).run()
          }
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''
          }`}
          title="Heading 1"
        >
          <Heading1 size={18} />
        </button>
        <button
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''
          }`}
          title="Heading 2"
        >
          <Heading2 size={18} />
        </button>
        <button
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 3 }).run()
          }
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''
          }`}
          title="Heading 3"
        >
          <Heading3 size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('bulletList') ? 'bg-gray-200' : ''
          }`}
          title="Bullet List"
        >
          <List size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('orderedList') ? 'bg-gray-200' : ''
          }`}
          title="Ordered List"
        >
          <ListOrdered size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''
          }`}
          title="Align Left"
        >
          <AlignLeft size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''
          }`}
          title="Align Center"
        >
          <AlignCenter size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''
          }`}
          title="Align Right"
        >
          <AlignRight size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().setTextAlign('justify').run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive({ textAlign: 'justify' }) ? 'bg-gray-200' : ''
          }`}
          title="Justify"
        >
          <AlignJustify size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('codeBlock') ? 'bg-gray-200' : ''
          }`}
          title="Code Block"
        >
          <Code size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={`rounded p-1 hover:bg-gray-200 ${
            editor.isActive('blockquote') ? 'bg-gray-200' : ''
          }`}
          title="Quote"
        >
          <Quote size={18} />
        </button>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <div className="relative">
          <button
            onClick={() => setShowLinkInput(!showLinkInput)}
            className={`rounded p-1 hover:bg-gray-200 ${
              editor.isActive('link') ? 'bg-gray-200' : ''
            }`}
            title="Link"
          >
            <Link size={18} />
          </button>
          {showLinkInput && (
            <div className="absolute top-full left-0 z-10 mt-1 flex w-64 rounded border bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800">
              <input
                type="text"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
                className="flex-1 rounded-l border border-r-0 p-1 text-sm"
              />
              <button
                onClick={addLink}
                className="rounded-r border border-blue-500 bg-blue-500 px-2 text-sm text-white"
              >
                Add
              </button>
            </div>
          )}
        </div>

        <div className="relative">
          <button
            onClick={() => setShowImageInput(!showImageInput)}
            className="rounded p-1 hover:bg-gray-200"
            title="Image"
          >
            <Image size={18} />
          </button>
          {showImageInput && (
            <div className="absolute top-full left-0 z-10 mt-1 w-64 rounded border bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800">
              <div className="mb-2 text-xs text-gray-500">
                이미지 파일을 선택하세요 (5MB 이하)
              </div>
              <label className="flex w-full cursor-pointer items-center justify-center rounded-md border border-dashed border-gray-300 p-2 hover:border-blue-500">
                <input
                  type="file"
                  accept="image/jpeg,image/png,image/gif,image/webp"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <div className="flex items-center text-sm text-gray-600">
                  <Image
                    size={16}
                    className="mr-2"
                  />
                  파일 선택하기
                </div>
              </label>
              <div className="mt-2 flex justify-between">
                <div className="text-xs text-gray-500">
                  800x600으로 리사이징됨
                </div>
                <button
                  onClick={() => setShowImageInput(false)}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  취소
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="relative">
          <button
            onClick={() => setShowColorPicker(!showColorPicker)}
            className="rounded p-1 hover:bg-gray-200"
            title="Text Color"
          >
            <Palette size={18} />
          </button>
          {showColorPicker && (
            <div className="absolute top-full left-0 z-10 mt-1 grid w-36 grid-cols-4 gap-1 rounded border bg-white p-2 shadow-lg dark:border-gray-700 dark:bg-gray-800">
              {[
                '#000000',
                '#FF0000',
                '#00FF00',
                '#0000FF',
                '#FFFF00',
                '#FF00FF',
                '#00FFFF',
                '#FFA500',
                '#800080',
                '#008000',
                '#800000',
                '#008080',
              ].map((color) => (
                <button
                  key={color}
                  onClick={() => setColor(color)}
                  className="h-6 w-6 rounded-full border"
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
          )}
        </div>

        <div className="mx-1 h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

        <button
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          className={`rounded p-1 hover:bg-gray-200 ${!editor.can().undo() ? 'opacity-30' : ''}`}
          title="Undo"
        >
          <Undo size={18} />
        </button>
        <button
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          className={`rounded p-1 hover:bg-gray-200 ${!editor.can().redo() ? 'opacity-30' : ''}`}
          title="Redo"
        >
          <Redo size={18} />
        </button>
      </div>
    </div>
  );
}
