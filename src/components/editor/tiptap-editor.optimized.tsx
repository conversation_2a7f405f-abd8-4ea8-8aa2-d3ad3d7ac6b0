'use client';

import { Image } from '@tiptap/extension-image';
import { Link } from '@tiptap/extension-link';
import { Placeholder } from '@tiptap/extension-placeholder';
import { EditorContent, useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { cn } from '@/lib/utils';
import { memo } from 'react';

interface TiptapEditorProps {
  id?: string;
  content?: string;
  onChangeText?: (contentText: string) => void;
  onChangeHtml?: (contentHtml: string) => void;
  placeholder?: string;
  editable?: boolean;
  className?: string;
}

/**
 * Tiptap 에디터 컴포넌트
 *
 * 리치 텍스트 에디터를 제공합니다.
 */
function TiptapEditor({
  id,
  content = '',
  onChangeText,
  onChangeHtml,
  placeholder,
  className,
  editable = true,
}: TiptapEditorProps) {
  const [_isFocused, setIsFocused] = useState(false);

  // useMemo를 사용하여 확장 기능 메모이제이션
  const extensions = useMemo(() => [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3, 4, 5, 6],
        HTMLAttributes: {
          class: 'tiptap-heading',
        },
      },
    }),
    Image.configure({
      allowBase64: true,
      HTMLAttributes: {
        class: 'rounded-md max-w-full h-auto',
      },
    }),
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        class: 'text-primary underline',
        target: '_blank',
        openOnClick: false,
        autolink: true,
      },
    }),
    Placeholder.configure({
      placeholder,
      emptyEditorClass: 'tiptap-placeholder',
    }),
  ], [placeholder]);

  const editor = useEditor({
    extensions,
    content,
    onUpdate: ({ editor }) => {
      onChangeText?.(editor.getText());
      onChangeHtml?.(editor.getHTML());
    },
    editable,
    enableInputRules: true,
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
  });

  // 외부에서 content가 변경되면 에디터 내용 업데이트
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // 에디터 포커스 핸들러
  const focusEditor = useCallback(() => {
    if (editor) {
      editor.chain().focus().run();
    }
  }, [editor]);

  return (
    <div className={cn('rounded-md', className)}>
      <div
        className="prose prose-sm dark:prose-invert sm:prose prose-headings:my-2 prose-p:my-2 prose-blockquote:my-2 prose-ol:my-2 prose-ul:my-2 max-w-none p-1"
        onClick={focusEditor}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            focusEditor();
          }
        }}
        role="textbox"
        tabIndex={0}
        aria-label="에디터 내용"
      >
        <EditorContent
          id={id}
          editor={editor}
          className="[&_.ProseMirror]:outline-none [&_.ProseMirror:focus]:shadow-none [&_.ProseMirror:focus]:outline-none"
        />
      </div>
    </div>
  );
}

/**
 * 에디터 상태 관리 훅
 *
 * 에디터의 텍스트 및 HTML 내용을 관리합니다.
 */
export function useEditorState() {
  const [contentText, setContentText] = useState('');
  const [contentHtml, setContentHtml] = useState('');

  const handleChangeText = useCallback((content: string) => setContentText(content), []);
  const handleChangeHtml = useCallback((contentHtml: string) => setContentHtml(contentHtml), []);

  return {
    contentText,
    setContentText,
    contentHtml,
    setContentHtml,
    handleChangeText,
    handleChangeHtml,
  };
}

/**
 * Tiptap 에디터 컴포넌트를 메모이제이션하여 성능 최적화
 *
 * 에디터 내용과 설정이 변경되지 않으면 리렌더링하지 않습니다.
 */
export const MemoizedTiptapEditor = memo(TiptapEditor, (prevProps, nextProps) => {
  // 읽기 전용 모드에서만 메모이제이션 적용 (편집 가능한 경우는 항상 리렌더링)
  if (prevProps.editable || nextProps.editable) return false;

  // 내용 비교
  const isSameContent = prevProps.content === nextProps.content;

  // 기타 속성 비교
  const isSameProps =
    prevProps.id === nextProps.id &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.className === nextProps.className;

  return isSameContent && isSameProps;
});

// 기본 내보내기
export { TiptapEditor };
