'use client';

import { Image } from '@tiptap/extension-image';
import { Link } from '@tiptap/extension-link';
import { Placeholder } from '@tiptap/extension-placeholder';
import { EditorContent, useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import { useCallback, useEffect, useState } from 'react';

import { cn } from '@/lib/utils';

interface TiptapEditorProps {
  id?: string;
  content?: string;
  onChangeText?: (contentText: string) => void;
  onChangeHtml?: (contentHtml: string) => void;
  placeholder?: string;
  editable?: boolean;
  className?: string;
}

export function TiptapEditor({
  id,
  content = '',
  onChangeText,
  onChangeHtml,
  placeholder,
  className,
  editable = true,
}: TiptapEditorProps) {
  const [_isFocused, setIsFocused] = useState(false);

  const editor = useEditor({
    // immediatelyRender: false를 제거하여 즉시 렌더링되도록 함
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
          HTMLAttributes: {
            class: 'tiptap-heading',
          },
        },
      }),
      Image.configure({
        allowBase64: true,
        HTMLAttributes: {
          class: 'rounded-md max-w-full h-auto',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline',
          target: '_blank',
          openOnClick: false,
          autolink: true,
        },
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'tiptap-placeholder',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      onChangeText?.(editor.getText());
      onChangeHtml?.(editor.getHTML());
    },
    editable,
    enableInputRules: true,
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
  });

  // 외부에서 content가 변경되면 에디터 내용 업데이트
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // 에디터 포커스 핸들러
  const focusEditor = useCallback(() => {
    if (editor) {
      editor.chain().focus().run();
    }
  }, [editor]);

  // 링크 제거 핸들러
  return (
    <div className={cn('rounded-md', className)}>
      <div
        className="prose prose-sm dark:prose-invert sm:prose prose-headings:my-2 prose-p:my-2 prose-blockquote:my-2 prose-ol:my-2 prose-ul:my-2 max-w-none p-1"
        onClick={focusEditor}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            focusEditor();
          }
        }}
        role="textbox"
        tabIndex={0}
        aria-label="에디터 내용"
      >
        <EditorContent
          id={id}
          editor={editor}
          className="[&_.ProseMirror]:outline-none [&_.ProseMirror:focus]:shadow-none [&_.ProseMirror:focus]:outline-none"
        />
      </div>
    </div>
  );
}

export function useEditorState() {
  const [contentText, setContentText] = useState('');
  const [contentHtml, setContentHtml] = useState('');
  return {
    contentText,
    setContentText,
    contentHtml,
    setContentHtml,
    handleChangeText: (content: string) => setContentText(content),
    handleChangeHtml: (contentHtml: string) => setContentHtml(contentHtml),
  };
}
