'use client';

import { Color } from '@tiptap/extension-color';
import { Image } from '@tiptap/extension-image';
import { Link } from '@tiptap/extension-link';
import { TextAlign } from '@tiptap/extension-text-align';
import { Underline } from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import { useEffect } from 'react';
// CSS is now imported in globals.css

interface TiptapPreviewProps {
  content: string;
  className?: string;
}

export default function TiptapPreview({
  content,
  className = '',
}: TiptapPreviewProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: true,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true,
        },
        listItem: {
          HTMLAttributes: {
            class: 'list-item',
          },
        },
      }),
      Underline,
      Link.configure({
        openOnClick: true,
        HTMLAttributes: {
          class: 'text-blue-500 underline cursor-pointer hover:text-blue-700',
        },
      }),
      Image.configure({
        allowBase64: true,
        HTMLAttributes: {
          class: 'rounded-md max-w-full',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph', 'bulletList', 'orderedList'],
      }),
      Color,
    ],
    content,
    editable: false,
  });

  // content가 변경될 때 에디터 내용 업데이트
  useEffect(() => {
    if (editor && content) {
      // 현재 에디터 내용과 다를 때만 업데이트 (무한 루프 방지)
      if (editor.getHTML() !== content) {
        editor.commands.setContent(content);
      }
    }
  }, [editor, content]);

  return (
    <div
      className={`tiptap-editor rounded-md border border-gray-300 shadow-sm ${className}`}
    >
      <EditorContent
        editor={editor}
        className="prose prose-sm sm:prose-base lg:prose-lg max-w-none"
      />
    </div>
  );
}
