'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ERROR_CODE } from '@/lib/errors/error-codes';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ErrorBoundaryProps {
  error: Error;
  reset: () => void;
}

export default function ErrorBoundary({ error, reset }: ErrorBoundaryProps) {
  const router = useRouter();
  
  // Log the error to the console
  useEffect(() => {
    console.error('Error boundary caught error:', error);
  }, [error]);
  
  // Extract error code if available
  const errorCode = (error as any).code || ERROR_CODE.UNKNOWN_ERROR;
  
  // Determine error message based on error code
  let title = '오류가 발생했습니다';
  let description = '페이지를 표시하는 중 문제가 발생했습니다.';
  
  switch (errorCode) {
    case ERROR_CODE.RESOURCE_NOT_FOUND:
      title = '리소스를 찾을 수 없습니다';
      description = '요청하신 리소스를 찾을 수 없습니다.';
      break;
    case ERROR_CODE.UNAUTHENTICATED:
    case ERROR_CODE.INVALID_CREDENTIALS:
    case ERROR_CODE.EXPIRED_TOKEN:
    case ERROR_CODE.INVALID_TOKEN:
      title = '인증이 필요합니다';
      description = '이 페이지에 접근하려면 로그인이 필요합니다.';
      break;
    case ERROR_CODE.UNAUTHORIZED:
    case ERROR_CODE.INSUFFICIENT_PERMISSIONS:
    case ERROR_CODE.FORBIDDEN_RESOURCE:
      title = '접근 권한이 없습니다';
      description = '이 페이지에 접근할 권한이 없습니다.';
      break;
    case ERROR_CODE.TIMEOUT:
    case ERROR_CODE.EXTERNAL_SERVICE_TIMEOUT:
      title = '요청 시간 초과';
      description = '서버 응답 시간이 초과되었습니다. 나중에 다시 시도해주세요.';
      break;
    case ERROR_CODE.SERVICE_UNAVAILABLE:
      title = '서비스를 사용할 수 없습니다';
      description = '현재 서비스를 사용할 수 없습니다. 나중에 다시 시도해주세요.';
      break;
    default:
      // Use default message for unknown errors
      break;
  }
  
  return (
    <div className="flex min-h-[50vh] items-center justify-center p-4">
      <Card className="mx-auto max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-xl font-semibold">{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="rounded-md bg-muted p-4 text-sm text-muted-foreground">
            <p className="font-mono">{error.message}</p>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/')}
            className="flex items-center gap-1"
          >
            <Home className="h-4 w-4" />
            <span>홈으로</span>
          </Button>
          
          <Button
            onClick={reset}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            <span>다시 시도</span>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
