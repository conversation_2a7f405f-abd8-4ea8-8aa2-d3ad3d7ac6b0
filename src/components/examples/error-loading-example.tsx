'use client';

import { useState } from 'react';
import { usePost } from '@/hooks/use-posts';
import { useLoadingState } from '@/hooks/use-loading-state';
import { useErrorHandler } from '@/hooks/use-error-handler';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { LoadingSkeleton } from '@/components/ui/loading-skeleton';
import { ErrorMessage } from '@/components/ui/error-message';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';

/**
 * 에러 처리 및 로딩 상태 관리 예제 컴포넌트
 * 
 * 이 컴포넌트는 SWR의 에러 처리 및 로딩 상태 관리 기능을 보여줍니다.
 */
export function ErrorLoadingExample() {
  const [postId, setPostId] = useState<string | null>('example-post-id'); // 실제 구현에서는 동적으로 설정
  const [forceError, setForceError] = useState(false);
  
  // 네트워크 상태 감지
  const { isOnline } = useNetworkStatus({
    onOnline: () => toast.success('네트워크 연결이 복구되었습니다.'),
    onOffline: () => toast.error('네트워크 연결이 끊겼습니다.'),
  });
  
  // 에러 핸들러
  const { handleError } = useErrorHandler({
    showToast: true,
    statusMessages: {
      404: '요청한 게시글을 찾을 수 없습니다.',
      500: '서버 오류가 발생했습니다.',
    },
  });
  
  // 게시글 데이터 가져오기
  const {
    post,
    error,
    isLoading: isLoadingRaw,
    mutate,
  } = usePost(postId, {
    onError: handleError,
    // 강제 에러 발생 시뮬레이션
    ...(forceError && {
      fetcher: () => {
        throw new Error('강제로 발생시킨 에러입니다.');
      },
    }),
  });
  
  // 로딩 상태 관리 (지연된 로딩 표시)
  const { isLoading } = useLoadingState(isLoadingRaw, {
    delay: 300, // 300ms 이내에 로딩이 완료되면 로딩 상태를 표시하지 않음
    minDisplayTime: 500, // 최소 500ms 동안 로딩 상태 표시
  });
  
  // 다시 시도 함수
  const handleRetry = () => {
    setForceError(false);
    mutate();
  };
  
  // 강제 에러 발생 함수
  const triggerError = () => {
    setForceError(true);
    mutate();
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>에러 처리 및 로딩 상태 관리</CardTitle>
        <CardDescription>SWR의 에러 처리 및 로딩 상태 관리 기능</CardDescription>
      </CardHeader>
      <CardContent>
        {/* 네트워크 상태 표시 */}
        <div className="mb-4 flex items-center gap-2">
          <div className={`rounded-full p-2 ${isOnline ? 'bg-green-100' : 'bg-red-100'}`}>
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
          </div>
          <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
            {isOnline ? '온라인' : '오프라인'}
          </span>
        </div>
        
        {/* 에러 메시지 */}
        {error && (
          <div className="mb-4">
            <ErrorMessage
              title="게시글 로딩 오류"
              error={error}
              onRetry={handleRetry}
            />
          </div>
        )}
        
        {/* 로딩 상태 */}
        {isLoading && (
          <LoadingSkeleton type="post" count={1} />
        )}
        
        {/* 게시글 내용 */}
        {!isLoading && !error && post && (
          <div className="space-y-4">
            <h2 className="text-xl font-bold">{post.title || '게시글 제목'}</h2>
            <p className="text-muted-foreground">{post.content || '게시글 내용'}</p>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>작성자: {post.author?.name || '익명'}</span>
              <span>•</span>
              <span>댓글: {post._count?.comments || 0}</span>
              <span>•</span>
              <span>좋아요: {post._count?.likes || 0}</span>
            </div>
          </div>
        )}
        
        {/* 데이터가 없는 경우 */}
        {!isLoading && !error && !post && (
          <div className="py-8 text-center text-muted-foreground">
            <p>게시글을 찾을 수 없습니다.</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRetry}
          disabled={isLoading}
        >
          <RefreshCw className="mr-2 h-3 w-3" />
          새로고침
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={triggerError}
          disabled={isLoading || forceError}
        >
          에러 발생시키기
        </Button>
      </CardFooter>
    </Card>
  );
}
