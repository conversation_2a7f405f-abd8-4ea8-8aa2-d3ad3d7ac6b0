'use client';

import { useState } from 'react';
import { useInfinitePosts, useInfiniteComments, useInfiniteJobs } from '@/hooks/use-infinite';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * 무한 스크롤 예제 컴포넌트
 * 
 * 이 컴포넌트는 SWR Infinite를 사용한 무한 스크롤 기능을 보여줍니다.
 */
export function InfiniteScrollExample() {
  const [activeTab, setActiveTab] = useState('posts');
  
  // 무한 스크롤 게시글 목록 가져오기
  const {
    posts,
    isLoading: isPostsLoading,
    isValidating: isPostsValidating,
    loadMore: loadMorePosts,
    loadMoreRef: postsLoadMoreRef,
    isReachingEnd: isPostsReachingEnd,
  } = useInfinitePosts({}, { limit: 5 });
  
  // 무한 스크롤 댓글 목록 가져오기
  const {
    comments,
    isLoading: isCommentsLoading,
    isValidating: isCommentsValidating,
    loadMore: loadMoreComments,
    loadMoreRef: commentsLoadMoreRef,
    isReachingEnd: isCommentsReachingEnd,
  } = useInfiniteComments({}, { limit: 5 });
  
  // 무한 스크롤 구인·구직 정보 목록 가져오기
  const {
    jobs,
    isLoading: isJobsLoading,
    isValidating: isJobsValidating,
    loadMore: loadMoreJobs,
    loadMoreRef: jobsLoadMoreRef,
    isReachingEnd: isJobsReachingEnd,
  } = useInfiniteJobs({}, { limit: 5 });
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>무한 스크롤 예제</CardTitle>
        <CardDescription>SWR Infinite를 사용한 무한 스크롤 구현</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="posts">게시글</TabsTrigger>
            <TabsTrigger value="comments">댓글</TabsTrigger>
            <TabsTrigger value="jobs">구인·구직</TabsTrigger>
          </TabsList>
          
          {/* 게시글 탭 */}
          <TabsContent value="posts" className="space-y-4 py-4">
            {isPostsLoading && posts.length === 0 ? (
              <div className="space-y-4">
                {Array(3).fill(0).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-5 w-2/3" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                ))}
              </div>
            ) : posts.length > 0 ? (
              <div className="space-y-4">
                <ul className="space-y-4">
                  {posts.map((post) => (
                    <li key={post.id} className="border-b pb-2">
                      <h3 className="font-medium">{post.title}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-2">{post.content}</p>
                      <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                        <span>{post.author?.name || '익명'}</span>
                        <span>댓글 {post._count?.comments || 0}</span>
                      </div>
                    </li>
                  ))}
                </ul>
                
                <div ref={postsLoadMoreRef} className="py-2 text-center">
                  {isPostsValidating && (
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-2/3 mx-auto" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  )}
                </div>
                
                {!isPostsReachingEnd && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadMorePosts}
                    disabled={isPostsValidating}
                    className="w-full"
                  >
                    더 보기
                  </Button>
                )}
              </div>
            ) : (
              <p>게시글이 없습니다.</p>
            )}
          </TabsContent>
          
          {/* 댓글 탭 */}
          <TabsContent value="comments" className="space-y-4 py-4">
            {isCommentsLoading && comments.length === 0 ? (
              <div className="space-y-4">
                {Array(3).fill(0).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-1/4" />
                    <Skeleton className="h-3 w-full" />
                  </div>
                ))}
              </div>
            ) : comments.length > 0 ? (
              <div className="space-y-4">
                <ul className="space-y-4">
                  {comments.map((comment) => (
                    <li key={comment.id} className="border-l-2 pl-3">
                      <p className="text-sm font-medium">{comment.author?.name || '익명'}</p>
                      <p className="text-sm">{comment.content}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        게시글: {comment.post?.title || '알 수 없음'}
                      </p>
                    </li>
                  ))}
                </ul>
                
                <div ref={commentsLoadMoreRef} className="py-2 text-center">
                  {isCommentsValidating && (
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-2/3 mx-auto" />
                      <Skeleton className="h-3 w-full" />
                    </div>
                  )}
                </div>
                
                {!isCommentsReachingEnd && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadMoreComments}
                    disabled={isCommentsValidating}
                    className="w-full"
                  >
                    더 보기
                  </Button>
                )}
              </div>
            ) : (
              <p>댓글이 없습니다.</p>
            )}
          </TabsContent>
          
          {/* 구인·구직 탭 */}
          <TabsContent value="jobs" className="space-y-4 py-4">
            {isJobsLoading && jobs.length === 0 ? (
              <div className="space-y-4">
                {Array(3).fill(0).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-5 w-2/3" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                ))}
              </div>
            ) : jobs.length > 0 ? (
              <div className="space-y-4">
                <ul className="space-y-4">
                  {jobs.map((job) => (
                    <li key={job.id} className="border rounded-md p-3">
                      <h3 className="font-medium">{job.title}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-2">{job.description}</p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <span className="text-xs bg-muted px-2 py-1 rounded-full">
                          {job.location}
                        </span>
                        <span className="text-xs bg-muted px-2 py-1 rounded-full">
                          {job.jobType}
                        </span>
                        {job.salary && (
                          <span className="text-xs bg-muted px-2 py-1 rounded-full">
                            {job.salary}
                          </span>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
                
                <div ref={jobsLoadMoreRef} className="py-2 text-center">
                  {isJobsValidating && (
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-2/3 mx-auto" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  )}
                </div>
                
                {!isJobsReachingEnd && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadMoreJobs}
                    disabled={isJobsValidating}
                    className="w-full"
                  >
                    더 보기
                  </Button>
                )}
              </div>
            ) : (
              <p>구인·구직 정보가 없습니다.</p>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          스크롤을 내리면 자동으로 더 많은 데이터를 로드합니다.
        </p>
      </CardFooter>
    </Card>
  );
}
