'use client';

import { useState } from 'react';
import { usePost } from '@/hooks/use-posts';
import { usePostComments } from '@/hooks/use-comments';
import { optimisticAddItem, optimisticUpdateItem, optimisticRemoveItem, optimisticToggle } from '@/lib/swr/optimistic';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Heart, MessageSquare, Bookmark, Trash, Edit, Check, X } from 'lucide-react';

/**
 * 낙관적 UI 업데이트 예제 컴포넌트
 * 
 * 이 컴포넌트는 SWR의 mutate 기능을 사용한 낙관적 UI 업데이트를 보여줍니다.
 */
export function OptimisticUpdateExample() {
  const postId = 'example-post-id'; // 실제 구현에서는 동적으로 설정
  const [newComment, setNewComment] = useState('');
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');
  
  // 게시글 데이터 가져오기
  const {
    post,
    isLoading: isPostLoading,
    mutate: mutatePost,
  } = usePost(postId);
  
  // 댓글 목록 가져오기
  const {
    comments,
    isLoading: isCommentsLoading,
    mutate: mutateComments,
  } = usePostComments(postId);
  
  // 좋아요 토글 함수
  const toggleLike = async () => {
    try {
      // 낙관적 UI 업데이트
      await optimisticToggle(mutatePost, 'isLiked', '_count.likes');
      
      // 실제 API 호출 (여기서는 시뮬레이션)
      // await fetch(`/api/posts/${postId}/like`, { method: 'POST' });
      
      // 성공 메시지
      toast.success(post?.isLiked ? '좋아요를 취소했습니다.' : '좋아요를 눌렀습니다.');
    } catch (error) {
      // 에러 발생 시 롤백 (재검증)
      mutatePost();
      toast.error('작업 중 오류가 발생했습니다.');
    }
  };
  
  // 북마크 토글 함수
  const toggleBookmark = async () => {
    try {
      // 낙관적 UI 업데이트
      await optimisticToggle(mutatePost, 'isBookmarked');
      
      // 실제 API 호출 (여기서는 시뮬레이션)
      // await fetch(`/api/posts/${postId}/bookmark`, { method: 'POST' });
      
      // 성공 메시지
      toast.success(post?.isBookmarked ? '북마크를 취소했습니다.' : '북마크에 추가했습니다.');
    } catch (error) {
      // 에러 발생 시 롤백 (재검증)
      mutatePost();
      toast.error('작업 중 오류가 발생했습니다.');
    }
  };
  
  // 댓글 추가 함수
  const addComment = async () => {
    if (!newComment.trim()) return;
    
    try {
      // 새 댓글 객체 생성
      const newCommentObj = {
        id: `temp-${Date.now()}`, // 임시 ID
        content: newComment,
        authorId: 'current-user-id', // 실제 구현에서는 세션에서 가져옴
        postId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        author: {
          id: 'current-user-id',
          name: '현재 사용자', // 실제 구현에서는 세션에서 가져옴
          image: null,
        },
      };
      
      // 낙관적 UI 업데이트
      await optimisticAddItem(
        mutateComments as any,
        newCommentObj,
        true
      );
      
      // 입력 필드 초기화
      setNewComment('');
      
      // 실제 API 호출 (여기서는 시뮬레이션)
      // const response = await fetch(`/api/posts/${postId}/comments`, {
      //   method: 'POST',
      //   body: JSON.stringify({ content: newComment }),
      // });
      // const data = await response.json();
      
      // 성공 메시지
      toast.success('댓글이 추가되었습니다.');
    } catch (error) {
      // 에러 발생 시 롤백 (재검증)
      mutateComments();
      toast.error('댓글 추가 중 오류가 발생했습니다.');
    }
  };
  
  // 댓글 수정 시작 함수
  const startEditComment = (comment: any) => {
    setEditingCommentId(comment.id);
    setEditedContent(comment.content);
  };
  
  // 댓글 수정 취소 함수
  const cancelEditComment = () => {
    setEditingCommentId(null);
    setEditedContent('');
  };
  
  // 댓글 수정 완료 함수
  const updateComment = async (commentId: string) => {
    if (!editedContent.trim()) return;
    
    try {
      // 수정된 댓글 객체 생성
      const updatedComment = comments.find(c => c.id === commentId);
      if (!updatedComment) return;
      
      const editedComment = {
        ...updatedComment,
        content: editedContent,
        updatedAt: new Date().toISOString(),
      };
      
      // 낙관적 UI 업데이트
      await optimisticUpdateItem(
        mutateComments as any,
        editedComment,
        'id'
      );
      
      // 수정 모드 종료
      setEditingCommentId(null);
      setEditedContent('');
      
      // 실제 API 호출 (여기서는 시뮬레이션)
      // await fetch(`/api/comments/${commentId}`, {
      //   method: 'PATCH',
      //   body: JSON.stringify({ content: editedContent }),
      // });
      
      // 성공 메시지
      toast.success('댓글이 수정되었습니다.');
    } catch (error) {
      // 에러 발생 시 롤백 (재검증)
      mutateComments();
      toast.error('댓글 수정 중 오류가 발생했습니다.');
    }
  };
  
  // 댓글 삭제 함수
  const deleteComment = async (commentId: string) => {
    try {
      // 낙관적 UI 업데이트
      await optimisticRemoveItem(
        mutateComments as any,
        commentId,
        'id'
      );
      
      // 실제 API 호출 (여기서는 시뮬레이션)
      // await fetch(`/api/comments/${commentId}`, {
      //   method: 'DELETE',
      // });
      
      // 성공 메시지
      toast.success('댓글이 삭제되었습니다.');
    } catch (error) {
      // 에러 발생 시 롤백 (재검증)
      mutateComments();
      toast.error('댓글 삭제 중 오류가 발생했습니다.');
    }
  };
  
  return (
    <div className="space-y-6">
      {/* 게시글 카드 */}
      <Card>
        <CardHeader>
          <CardTitle>낙관적 UI 업데이트 예제</CardTitle>
          <CardDescription>SWR mutate를 활용한 낙관적 UI 업데이트</CardDescription>
        </CardHeader>
        <CardContent>
          {isPostLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ) : post ? (
            <div className="space-y-4">
              <h2 className="text-xl font-bold">{post.title || '게시글 제목'}</h2>
              <p className="text-muted-foreground">{post.content || '게시글 내용'}</p>
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className={post.isLiked ? 'text-red-500' : ''}
                  onClick={toggleLike}
                >
                  <Heart className="mr-1 h-4 w-4" />
                  <span>{post._count?.likes || 0}</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                >
                  <MessageSquare className="mr-1 h-4 w-4" />
                  <span>{post._count?.comments || 0}</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={post.isBookmarked ? 'text-blue-500' : ''}
                  onClick={toggleBookmark}
                >
                  <Bookmark className="mr-1 h-4 w-4" />
                  <span>저장</span>
                </Button>
              </div>
            </div>
          ) : (
            <p>게시글을 찾을 수 없습니다.</p>
          )}
        </CardContent>
      </Card>
      
      {/* 댓글 섹션 */}
      <Card>
        <CardHeader>
          <CardTitle>댓글</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 댓글 입력 */}
            <div className="flex gap-2">
              <Textarea
                placeholder="댓글을 입력하세요..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                className="flex-1"
              />
              <Button onClick={addComment}>등록</Button>
            </div>
            
            {/* 댓글 목록 */}
            {isCommentsLoading ? (
              <div className="space-y-4">
                {Array(3).fill(0).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-1/4" />
                    <Skeleton className="h-3 w-full" />
                  </div>
                ))}
              </div>
            ) : comments && comments.length > 0 ? (
              <ul className="space-y-4">
                {comments.map((comment) => (
                  <li key={comment.id} className="border-b pb-3">
                    <div className="flex justify-between items-start">
                      <div className="font-medium text-sm">{comment.author?.name || '익명'}</div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => startEditComment(comment)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-destructive"
                          onClick={() => deleteComment(comment.id)}
                        >
                          <Trash className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {editingCommentId === comment.id ? (
                      <div className="mt-2 space-y-2">
                        <Textarea
                          value={editedContent}
                          onChange={(e) => setEditedContent(e.target.value)}
                          className="text-sm"
                        />
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={cancelEditComment}
                          >
                            <X className="mr-1 h-3 w-3" />
                            취소
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => updateComment(comment.id)}
                          >
                            <Check className="mr-1 h-3 w-3" />
                            저장
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="mt-1 text-sm">{comment.content}</p>
                    )}
                    
                    <div className="mt-1 text-xs text-muted-foreground">
                      {new Date(comment.createdAt).toLocaleString()}
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-center text-muted-foreground py-4">
                아직 댓글이 없습니다. 첫 댓글을 작성해보세요!
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
