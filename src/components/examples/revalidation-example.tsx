'use client';

import { useState, useEffect } from 'react';
import { usePosts } from '@/hooks/use-posts';
import { usePostComments } from '@/hooks/use-comments';
import { revalidateResourceType, revalidateResource, revalidateAll } from '@/lib/swr/revalidation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSkeleton } from '@/components/ui/loading-skeleton';
import { toast } from 'sonner';
import { RefreshCw, Clock, Focus, Wifi } from 'lucide-react';

/**
 * 데이터 재검증 전략 예제 컴포넌트
 * 
 * 이 컴포넌트는 SWR의 다양한 데이터 재검증 전략을 보여줍니다.
 */
export function RevalidationExample() {
  const [activeTab, setActiveTab] = useState('focus');
  const [selectedPostId, setSelectedPostId] = useState<string | null>(null);
  const [intervalMs, setIntervalMs] = useState<number | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  
  // 게시글 목록 가져오기 (포커스 시 재검증)
  const {
    posts: focusPosts,
    isLoading: isFocusPostsLoading,
    mutate: mutateFocusPosts,
  } = usePosts(
    { limit: 5 },
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 5000, // 5초
    }
  );
  
  // 게시글 목록 가져오기 (주기적 재검증)
  const {
    posts: intervalPosts,
    isLoading: isIntervalPostsLoading,
    mutate: mutateIntervalPosts,
  } = usePosts(
    { limit: 5 },
    {
      refreshInterval: intervalMs,
      dedupingInterval: 0, // 중복 제거 비활성화
    }
  );
  
  // 선택된 게시글의 댓글 가져오기
  const {
    comments,
    isLoading: isCommentsLoading,
    mutate: mutateComments,
  } = usePostComments(
    selectedPostId,
    {
      revalidateOnFocus: true,
      revalidateIfStale: true,
    }
  );
  
  // 마지막 업데이트 시간 갱신
  useEffect(() => {
    const updateTimestamp = () => {
      setLastUpdated(new Date());
    };
    
    // 데이터가 변경될 때마다 타임스탬프 업데이트
    updateTimestamp();
    
    // 포커스 이벤트 리스너
    const handleFocus = () => {
      if (activeTab === 'focus') {
        updateTimestamp();
      }
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [focusPosts, intervalPosts, comments, activeTab]);
  
  // 주기적 재검증 간격 변경 함수
  const changeInterval = (ms: number | null) => {
    setIntervalMs(ms);
    toast.success(ms ? `${ms / 1000}초마다 재검증` : '주기적 재검증 비활성화');
  };
  
  // 수동 재검증 함수
  const handleManualRevalidate = async () => {
    try {
      if (activeTab === 'focus') {
        await mutateFocusPosts();
      } else if (activeTab === 'interval') {
        await mutateIntervalPosts();
      } else if (activeTab === 'conditional') {
        if (selectedPostId) {
          await revalidateResource('posts', selectedPostId);
          await mutateComments();
        } else {
          await revalidateResourceType('posts');
        }
      } else if (activeTab === 'event') {
        await revalidateAll();
      }
      
      setLastUpdated(new Date());
      toast.success('데이터가 성공적으로 재검증되었습니다.');
    } catch (error) {
      toast.error('데이터 재검증 중 오류가 발생했습니다.');
    }
  };
  
  // 게시글 선택 함수
  const handleSelectPost = (postId: string) => {
    setSelectedPostId(postId === selectedPostId ? null : postId);
  };
  
  // 마지막 업데이트 시간 포맷팅
  const formatLastUpdated = () => {
    return lastUpdated.toLocaleTimeString();
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>데이터 재검증 전략</CardTitle>
        <CardDescription>SWR의 다양한 데이터 재검증 전략</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="focus">
              <Focus className="mr-2 h-4 w-4" />
              포커스 시
            </TabsTrigger>
            <TabsTrigger value="interval">
              <Clock className="mr-2 h-4 w-4" />
              주기적
            </TabsTrigger>
            <TabsTrigger value="conditional">
              <RefreshCw className="mr-2 h-4 w-4" />
              조건부
            </TabsTrigger>
            <TabsTrigger value="event">
              <Wifi className="mr-2 h-4 w-4" />
              이벤트 기반
            </TabsTrigger>
          </TabsList>
          
          {/* 포커스 시 재검증 탭 */}
          <TabsContent value="focus" className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4 text-sm">
              <p>이 탭에서는 브라우저 창이 포커스될 때 데이터가 자동으로 재검증됩니다.</p>
              <p>다른 탭으로 이동했다가 돌아오면 데이터가 새로고침됩니다.</p>
            </div>
            
            {isFocusPostsLoading ? (
              <LoadingSkeleton type="list" count={3} />
            ) : focusPosts.length > 0 ? (
              <ul className="space-y-2">
                {focusPosts.map((post) => (
                  <li key={post.id} className="border-b pb-2">
                    <h3 className="font-medium">{post.title}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-1">{post.content}</p>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-center text-muted-foreground py-4">게시글이 없습니다.</p>
            )}
          </TabsContent>
          
          {/* 주기적 재검증 탭 */}
          <TabsContent value="interval" className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4 text-sm">
              <p>이 탭에서는 설정한 간격마다 데이터가 자동으로 재검증됩니다.</p>
              <p>아래 버튼을 클릭하여 재검증 간격을 변경할 수 있습니다.</p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button
                variant={intervalMs === null ? "default" : "outline"}
                size="sm"
                onClick={() => changeInterval(null)}
              >
                비활성화
              </Button>
              <Button
                variant={intervalMs === 5000 ? "default" : "outline"}
                size="sm"
                onClick={() => changeInterval(5000)}
              >
                5초
              </Button>
              <Button
                variant={intervalMs === 10000 ? "default" : "outline"}
                size="sm"
                onClick={() => changeInterval(10000)}
              >
                10초
              </Button>
              <Button
                variant={intervalMs === 30000 ? "default" : "outline"}
                size="sm"
                onClick={() => changeInterval(30000)}
              >
                30초
              </Button>
            </div>
            
            {isIntervalPostsLoading ? (
              <LoadingSkeleton type="list" count={3} />
            ) : intervalPosts.length > 0 ? (
              <ul className="space-y-2">
                {intervalPosts.map((post) => (
                  <li key={post.id} className="border-b pb-2">
                    <h3 className="font-medium">{post.title}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-1">{post.content}</p>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-center text-muted-foreground py-4">게시글이 없습니다.</p>
            )}
          </TabsContent>
          
          {/* 조건부 재검증 탭 */}
          <TabsContent value="conditional" className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4 text-sm">
              <p>이 탭에서는 특정 조건에 따라 데이터를 재검증합니다.</p>
              <p>게시글을 클릭하면 해당 게시글의 댓글이 로드되고, 데이터가 오래된 경우 자동으로 재검증됩니다.</p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2">게시글 목록</h3>
                {focusPosts.length > 0 ? (
                  <ul className="space-y-2">
                    {focusPosts.map((post) => (
                      <li
                        key={post.id}
                        className={`border p-2 rounded-md cursor-pointer ${
                          selectedPostId === post.id ? 'bg-muted' : ''
                        }`}
                        onClick={() => handleSelectPost(post.id)}
                      >
                        <h4 className="font-medium">{post.title}</h4>
                        <p className="text-xs text-muted-foreground">
                          댓글 {post._count?.comments || 0}개
                        </p>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-center text-muted-foreground py-4">게시글이 없습니다.</p>
                )}
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">댓글</h3>
                {selectedPostId ? (
                  isCommentsLoading ? (
                    <LoadingSkeleton type="comment" count={2} />
                  ) : comments.length > 0 ? (
                    <ul className="space-y-2">
                      {comments.map((comment) => (
                        <li key={comment.id} className="border-l-2 pl-2">
                          <p className="text-sm font-medium">{comment.author?.name || '익명'}</p>
                          <p className="text-xs">{comment.content}</p>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-muted-foreground py-4">댓글이 없습니다.</p>
                  )
                ) : (
                  <p className="text-center text-muted-foreground py-4">게시글을 선택하세요.</p>
                )}
              </div>
            </div>
          </TabsContent>
          
          {/* 이벤트 기반 재검증 탭 */}
          <TabsContent value="event" className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4 text-sm">
              <p>이 탭에서는 특정 이벤트 발생 시 관련 데이터를 재검증합니다.</p>
              <p>실제 구현에서는 WebSocket이나 Server-Sent Events를 통해 서버에서 이벤트를 수신하여 자동으로 재검증합니다.</p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm font-medium">이벤트 시뮬레이션</p>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    revalidateResourceType('posts');
                    toast.success('게시글 생성 이벤트: 게시글 목록이 재검증되었습니다.');
                    setLastUpdated(new Date());
                  }}
                >
                  게시글 생성
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (selectedPostId) {
                      revalidateResource('posts', selectedPostId);
                      toast.success('게시글 수정 이벤트: 선택된 게시글이 재검증되었습니다.');
                      setLastUpdated(new Date());
                    } else {
                      toast.error('게시글을 먼저 선택해주세요.');
                    }
                  }}
                >
                  게시글 수정
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (selectedPostId) {
                      revalidateResource('posts', selectedPostId);
                      mutateComments();
                      toast.success('댓글 생성 이벤트: 댓글 목록이 재검증되었습니다.');
                      setLastUpdated(new Date());
                    } else {
                      toast.error('게시글을 먼저 선택해주세요.');
                    }
                  }}
                >
                  댓글 생성
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    revalidateAll();
                    toast.success('로그아웃 이벤트: 모든 데이터가 재검증되었습니다.');
                    setLastUpdated(new Date());
                  }}
                >
                  로그아웃
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          마지막 업데이트: {formatLastUpdated()}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleManualRevalidate}
        >
          <RefreshCw className="mr-2 h-3 w-3" />
          수동 재검증
        </Button>
      </CardFooter>
    </Card>
  );
}
