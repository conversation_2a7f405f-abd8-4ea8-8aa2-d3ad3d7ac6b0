'use client';

/**
 * 상태 관리 예제 컴포넌트
 * 
 * 이 컴포넌트는 로컬 상태와 글로벌 상태를 적절히 분리하여 사용하는 방법을 보여줍니다.
 */

import { useFormState, useToggle } from '@/hooks/use-local-state';
import { useTheme, useSetTheme, useAddToast, useUserPreferences, useUpdateUserPreferences } from '@/store';
import { useState } from 'react';

/**
 * 상태 관리 예제 컴포넌트
 */
export function StateManagementExample() {
  // 글로벌 상태 (Zustand 스토어에서 가져옴)
  const theme = useTheme();
  const setTheme = useSetTheme();
  const addToast = useAddToast();
  const userPreferences = useUserPreferences();
  const updateUserPreferences = useUpdateUserPreferences();
  
  // 로컬 상태 (컴포넌트 내부에서만 사용)
  const [isExpanded, toggleExpanded, setExpanded] = useToggle(false);
  const [formValues, handleChange, resetForm, setFormValues] = useFormState({
    name: '',
    email: '',
    message: '',
  });
  
  // 이 컴포넌트에서만 사용되는 UI 상태는 useState로 관리
  const [activeTab, setActiveTab] = useState<'preferences' | 'theme'>('preferences');
  
  // 폼 제출 핸들러
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 폼 데이터 처리 (로컬 상태)
    console.log('Form submitted:', formValues);
    
    // 사용자 설정 업데이트 (글로벌 상태)
    updateUserPreferences({
      notificationsEnabled: true,
      language: 'ko',
    });
    
    // 토스트 메시지 표시 (글로벌 상태)
    addToast({
      type: 'success',
      title: '성공',
      message: '설정이 저장되었습니다.',
    });
    
    // 폼 초기화 (로컬 상태)
    resetForm();
  };
  
  return (
    <div className="p-4 space-y-6">
      <h2 className="text-2xl font-bold">상태 관리 예제</h2>
      
      {/* 탭 UI (로컬 상태로 관리) */}
      <div className="flex space-x-2 border-b">
        <button
          className={`px-4 py-2 ${activeTab === 'preferences' ? 'border-b-2 border-primary' : ''}`}
          onClick={() => setActiveTab('preferences')}
        >
          사용자 설정
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'theme' ? 'border-b-2 border-primary' : ''}`}
          onClick={() => setActiveTab('theme')}
        >
          테마 설정
        </button>
      </div>
      
      {/* 사용자 설정 탭 */}
      {activeTab === 'preferences' && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">사용자 설정</h3>
          
          {/* 현재 사용자 설정 표시 (글로벌 상태) */}
          <div className="bg-muted p-4 rounded">
            <h4 className="font-medium">현재 설정:</h4>
            <p>언어: {userPreferences.language}</p>
            <p>알림: {userPreferences.notificationsEnabled ? '활성화' : '비활성화'}</p>
            <p>이메일 알림: {userPreferences.emailNotificationsEnabled ? '활성화' : '비활성화'}</p>
            <p>기본 국가: {userPreferences.defaultCountry || '설정되지 않음'}</p>
          </div>
          
          {/* 설정 폼 (로컬 상태로 관리) */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium">
                이름
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border border-input px-3 py-2"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium">
                이메일
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formValues.email}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border border-input px-3 py-2"
              />
            </div>
            
            <div>
              <label htmlFor="message" className="block text-sm font-medium">
                메시지
              </label>
              <textarea
                id="message"
                name="message"
                value={formValues.message}
                onChange={handleChange}
                rows={3}
                className="mt-1 block w-full rounded-md border border-input px-3 py-2"
              />
            </div>
            
            <div className="flex space-x-2">
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-primary-foreground rounded"
              >
                저장
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
              >
                초기화
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* 테마 설정 탭 */}
      {activeTab === 'theme' && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">테마 설정</h3>
          
          {/* 현재 테마 표시 (글로벌 상태) */}
          <div className="bg-muted p-4 rounded">
            <p>현재 테마: {theme}</p>
          </div>
          
          {/* 테마 변경 버튼 (글로벌 상태 업데이트) */}
          <div className="flex space-x-2">
            <button
              onClick={() => setTheme('light')}
              className={`px-4 py-2 rounded ${
                theme === 'light' ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'
              }`}
            >
              라이트 모드
            </button>
            <button
              onClick={() => setTheme('dark')}
              className={`px-4 py-2 rounded ${
                theme === 'dark' ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'
              }`}
            >
              다크 모드
            </button>
            <button
              onClick={() => setTheme('system')}
              className={`px-4 py-2 rounded ${
                theme === 'system' ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'
              }`}
            >
              시스템 설정
            </button>
          </div>
        </div>
      )}
      
      {/* 확장/축소 토글 (로컬 상태) */}
      <div className="mt-8">
        <button
          onClick={toggleExpanded}
          className="flex items-center space-x-2 text-sm"
        >
          <span>{isExpanded ? '접기' : '더 보기'}</span>
          <span>{isExpanded ? '▲' : '▼'}</span>
        </button>
        
        {isExpanded && (
          <div className="mt-4 p-4 bg-muted rounded">
            <p>이 섹션은 로컬 상태(isExpanded)에 의해 제어됩니다.</p>
            <p>이 상태는 이 컴포넌트 내에서만 관련이 있으므로 로컬 상태로 관리됩니다.</p>
          </div>
        )}
      </div>
    </div>
  );
}
