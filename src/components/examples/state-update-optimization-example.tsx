'use client';

/**
 * 상태 업데이트 최적화 예제 컴포넌트
 * 
 * 이 컴포넌트는 다양한 상태 업데이트 최적화 기법을 시연합니다.
 */

import { useImmerUpdater } from '@/store/utils/immer-utils';
import { useRenderTracker, useStateChangeTracker } from '@/store/utils/performance-utils';
import { useDerivedState, useSelectors } from '@/store/utils/selector-utils';
import { useCallback, useMemo, useState } from 'react';
import { create } from 'zustand';

// 예제용 Todo 스토어
interface Todo {
  id: number;
  text: string;
  completed: boolean;
}

interface TodoState {
  todos: Todo[];
  addTodo: (text: string) => void;
  toggleTodo: (id: number) => void;
  removeTodo: (id: number) => void;
  clearCompleted: () => void;
}

const useTodoStore = create<TodoState>((set) => ({
  todos: [
    { id: 1, text: '상태 관리 학습하기', completed: false },
    { id: 2, text: 'Zustand 최적화하기', completed: false },
    { id: 3, text: '컴포넌트 리팩토링하기', completed: true },
  ],
  addTodo: (text) => set((state) => ({
    todos: [...state.todos, { id: Date.now(), text, completed: false }],
  })),
  toggleTodo: (id) => set((state) => ({
    todos: state.todos.map((todo) =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ),
  })),
  removeTodo: (id) => set((state) => ({
    todos: state.todos.filter((todo) => todo.id !== id),
  })),
  clearCompleted: () => set((state) => ({
    todos: state.todos.filter((todo) => !todo.completed),
  })),
}));

/**
 * 최적화되지 않은 Todo 목록 컴포넌트
 */
function UnoptimizedTodoList() {
  // 렌더링 추적
  useRenderTracker('UnoptimizedTodoList');
  
  // 전체 상태를 구독 (비효율적)
  const { todos, toggleTodo, removeTodo } = useTodoStore();
  
  return (
    <div className="space-y-2">
      <h3 className="text-lg font-medium">최적화되지 않은 목록</h3>
      <ul className="space-y-1">
        {todos.map((todo) => (
          <li key={todo.id} className="flex items-center justify-between">
            <span
              className={`${todo.completed ? 'line-through text-muted-foreground' : ''}`}
              onClick={() => toggleTodo(todo.id)}
            >
              {todo.text}
            </span>
            <button
              onClick={() => removeTodo(todo.id)}
              className="text-destructive"
            >
              삭제
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * 선택자 패턴으로 최적화된 Todo 목록 컴포넌트
 */
function OptimizedTodoList() {
  // 렌더링 추적
  useRenderTracker('OptimizedTodoList');
  
  // 선택자 패턴 사용 (효율적)
  const todos = useStateChangeTracker(
    useTodoStore,
    (state) => state.todos,
    'OptimizedTodoList-todos'
  );
  const toggleTodo = useTodoStore((state) => state.toggleTodo);
  const removeTodo = useTodoStore((state) => state.removeTodo);
  
  return (
    <div className="space-y-2">
      <h3 className="text-lg font-medium">선택자 패턴으로 최적화된 목록</h3>
      <ul className="space-y-1">
        {todos.map((todo) => (
          <li key={todo.id} className="flex items-center justify-between">
            <span
              className={`${todo.completed ? 'line-through text-muted-foreground' : ''}`}
              onClick={() => toggleTodo(todo.id)}
            >
              {todo.text}
            </span>
            <button
              onClick={() => removeTodo(todo.id)}
              className="text-destructive"
            >
              삭제
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * 다중 선택자 패턴으로 최적화된 Todo 통계 컴포넌트
 */
function TodoStats() {
  // 렌더링 추적
  useRenderTracker('TodoStats');
  
  // 다중 선택자 패턴 사용
  const { total, completed, active } = useSelectors(useTodoStore, {
    total: (state) => state.todos.length,
    completed: (state) => state.todos.filter((todo) => todo.completed).length,
    active: (state) => state.todos.filter((todo) => !todo.completed).length,
  });
  
  return (
    <div className="flex justify-between text-sm text-muted-foreground">
      <span>전체: {total}</span>
      <span>완료: {completed}</span>
      <span>미완료: {active}</span>
    </div>
  );
}

/**
 * 파생 상태를 사용하는 필터링된 Todo 목록 컴포넌트
 */
function FilteredTodoList() {
  // 렌더링 추적
  useRenderTracker('FilteredTodoList');
  
  // 로컬 필터 상태
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');
  
  // 액션 선택자
  const toggleTodo = useTodoStore((state) => state.toggleTodo);
  const removeTodo = useTodoStore((state) => state.removeTodo);
  const clearCompleted = useTodoStore((state) => state.clearCompleted);
  
  // 파생 상태 계산
  const filteredTodos = useDerivedState(
    useTodoStore,
    (state) => state.todos,
    (todos) => {
      switch (filter) {
        case 'active':
          return todos.filter((todo) => !todo.completed);
        case 'completed':
          return todos.filter((todo) => todo.completed);
        default:
          return todos;
      }
    },
    [filter]
  );
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">파생 상태를 사용한 필터링된 목록</h3>
      
      <div className="flex space-x-2">
        <button
          onClick={() => setFilter('all')}
          className={`px-2 py-1 rounded ${
            filter === 'all' ? 'bg-primary text-primary-foreground' : 'bg-secondary'
          }`}
        >
          전체
        </button>
        <button
          onClick={() => setFilter('active')}
          className={`px-2 py-1 rounded ${
            filter === 'active' ? 'bg-primary text-primary-foreground' : 'bg-secondary'
          }`}
        >
          미완료
        </button>
        <button
          onClick={() => setFilter('completed')}
          className={`px-2 py-1 rounded ${
            filter === 'completed' ? 'bg-primary text-primary-foreground' : 'bg-secondary'
          }`}
        >
          완료
        </button>
        <button
          onClick={clearCompleted}
          className="px-2 py-1 rounded bg-destructive text-destructive-foreground ml-auto"
        >
          완료 항목 삭제
        </button>
      </div>
      
      <ul className="space-y-1">
        {filteredTodos.map((todo) => (
          <li key={todo.id} className="flex items-center justify-between">
            <span
              className={`${todo.completed ? 'line-through text-muted-foreground' : ''}`}
              onClick={() => toggleTodo(todo.id)}
            >
              {todo.text}
            </span>
            <button
              onClick={() => removeTodo(todo.id)}
              className="text-destructive"
            >
              삭제
            </button>
          </li>
        ))}
      </ul>
      
      <TodoStats />
    </div>
  );
}

/**
 * Immer를 사용한 Todo 추가 폼 컴포넌트
 */
function TodoForm() {
  // 렌더링 추적
  useRenderTracker('TodoForm');
  
  // 로컬 상태
  const [text, setText] = useState('');
  
  // 기본 액션 선택자
  const addTodo = useTodoStore((state) => state.addTodo);
  
  // Immer 업데이터
  const updateTodos = useImmerUpdater(useTodoStore);
  
  // 일반적인 방식으로 Todo 추가
  const handleAddTodo = useCallback(() => {
    if (text.trim()) {
      addTodo(text);
      setText('');
    }
  }, [text, addTodo]);
  
  // Immer를 사용하여 Todo 추가
  const handleAddTodoWithImmer = useCallback(() => {
    if (text.trim()) {
      updateTodos((draft) => {
        draft.todos.push({
          id: Date.now(),
          text,
          completed: false,
        });
      });
      setText('');
    }
  }, [text, updateTodos]);
  
  return (
    <div className="space-y-2">
      <h3 className="text-lg font-medium">Todo 추가</h3>
      <div className="flex space-x-2">
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="할 일 입력..."
          className="flex-1 px-3 py-2 border rounded"
        />
        <button
          onClick={handleAddTodo}
          className="px-4 py-2 bg-primary text-primary-foreground rounded"
        >
          추가
        </button>
        <button
          onClick={handleAddTodoWithImmer}
          className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
        >
          Immer로 추가
        </button>
      </div>
    </div>
  );
}

/**
 * 상태 업데이트 최적화 예제 컴포넌트
 */
export function StateUpdateOptimizationExample() {
  return (
    <div className="p-4 space-y-8">
      <h2 className="text-2xl font-bold">상태 업데이트 최적화 예제</h2>
      <p className="text-muted-foreground">
        개발자 도구 콘솔에서 각 컴포넌트의 렌더링 횟수와 상태 변경을 확인하세요.
      </p>
      
      <TodoForm />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <UnoptimizedTodoList />
        <OptimizedTodoList />
      </div>
      
      <FilteredTodoList />
    </div>
  );
}
