'use client';

import { useState } from 'react';
import { useCurrentUser } from '@/hooks/use-user';
import { usePosts } from '@/hooks/use-posts';
import { usePostComments } from '@/hooks/use-comments';
import { useInfinitePosts } from '@/hooks/use-infinite';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * SWR 사용 예제 컴포넌트
 * 
 * 이 컴포넌트는 SWR 훅을 사용하여 데이터를 가져오고 표시하는 방법을 보여줍니다.
 */
export function SWRExample() {
  const [selectedPostId, setSelectedPostId] = useState<string | null>(null);
  
  // 현재 사용자 정보 가져오기
  const { user, isLoading: isUserLoading } = useCurrentUser();
  
  // 게시글 목록 가져오기
  const { posts, isLoading: isPostsLoading } = usePosts({ limit: 5 });
  
  // 선택된 게시글의 댓글 가져오기
  const { comments, isLoading: isCommentsLoading } = usePostComments(selectedPostId);
  
  // 무한 스크롤 게시글 목록 가져오기
  const {
    posts: infinitePosts,
    isLoading: isInfiniteLoading,
    loadMore,
    loadMoreRef,
    isReachingEnd,
  } = useInfinitePosts({}, { limit: 3, autoLoadMore: false });
  
  return (
    <div className="space-y-8">
      {/* 현재 사용자 정보 */}
      <Card>
        <CardHeader>
          <CardTitle>현재 사용자</CardTitle>
          <CardDescription>useCurrentUser 훅 사용 예제</CardDescription>
        </CardHeader>
        <CardContent>
          {isUserLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ) : user ? (
            <div>
              <p className="font-medium">{user.name}</p>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          ) : (
            <p>로그인되지 않았습니다.</p>
          )}
        </CardContent>
      </Card>
      
      {/* 게시글 목록 */}
      <Card>
        <CardHeader>
          <CardTitle>게시글 목록</CardTitle>
          <CardDescription>usePosts 훅 사용 예제</CardDescription>
        </CardHeader>
        <CardContent>
          {isPostsLoading ? (
            <div className="space-y-4">
              {Array(3).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-5 w-2/3" />
                  <Skeleton className="h-4 w-full" />
                </div>
              ))}
            </div>
          ) : posts.length > 0 ? (
            <ul className="space-y-4">
              {posts.map((post) => (
                <li key={post.id} className="border-b pb-2">
                  <h3 className="font-medium">{post.title}</h3>
                  <p className="text-sm text-muted-foreground line-clamp-2">{post.content}</p>
                  <Button
                    variant="link"
                    size="sm"
                    className="px-0"
                    onClick={() => setSelectedPostId(post.id)}
                  >
                    댓글 보기
                  </Button>
                </li>
              ))}
            </ul>
          ) : (
            <p>게시글이 없습니다.</p>
          )}
        </CardContent>
      </Card>
      
      {/* 선택된 게시글의 댓글 */}
      {selectedPostId && (
        <Card>
          <CardHeader>
            <CardTitle>댓글</CardTitle>
            <CardDescription>usePostComments 훅 사용 예제</CardDescription>
          </CardHeader>
          <CardContent>
            {isCommentsLoading ? (
              <div className="space-y-3">
                {Array(2).fill(0).map((_, i) => (
                  <div key={i} className="space-y-1">
                    <Skeleton className="h-4 w-1/4" />
                    <Skeleton className="h-3 w-full" />
                  </div>
                ))}
              </div>
            ) : comments.length > 0 ? (
              <ul className="space-y-3">
                {comments.map((comment) => (
                  <li key={comment.id} className="border-l-2 pl-3">
                    <p className="text-sm font-medium">{comment.author?.name || '익명'}</p>
                    <p className="text-sm">{comment.content}</p>
                  </li>
                ))}
              </ul>
            ) : (
              <p>댓글이 없습니다.</p>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" onClick={() => setSelectedPostId(null)}>
              닫기
            </Button>
          </CardFooter>
        </Card>
      )}
      
      {/* 무한 스크롤 게시글 목록 */}
      <Card>
        <CardHeader>
          <CardTitle>무한 스크롤 게시글</CardTitle>
          <CardDescription>useInfinitePosts 훅 사용 예제</CardDescription>
        </CardHeader>
        <CardContent>
          {isInfiniteLoading && infinitePosts.length === 0 ? (
            <div className="space-y-4">
              {Array(3).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-5 w-2/3" />
                  <Skeleton className="h-4 w-full" />
                </div>
              ))}
            </div>
          ) : infinitePosts.length > 0 ? (
            <div className="space-y-4">
              <ul className="space-y-4">
                {infinitePosts.map((post) => (
                  <li key={post.id} className="border-b pb-2">
                    <h3 className="font-medium">{post.title}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-2">{post.content}</p>
                  </li>
                ))}
              </ul>
              
              <div ref={loadMoreRef} className="py-2 text-center">
                {isInfiniteLoading && (
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-2/3 mx-auto" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                )}
              </div>
              
              {!isReachingEnd && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadMore}
                  disabled={isInfiniteLoading}
                  className="w-full"
                >
                  더 보기
                </Button>
              )}
            </div>
          ) : (
            <p>게시글이 없습니다.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
