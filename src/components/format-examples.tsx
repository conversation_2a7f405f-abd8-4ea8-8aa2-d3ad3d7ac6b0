'use client';

import { useFormatter } from 'next-intl';

export default function FormatExamples() {
  const format = useFormatter();
  const now = new Date();
  const price = 49900;

  return (
    <div className="space-y-4 p-4 border rounded-md bg-card">
      <h2 className="text-xl font-semibold">Format Examples</h2>
      <div className="space-y-2">
        <div>
          <span className="font-medium">Short Date: </span>
          <span>{format.dateTime(now, { dateStyle: 'short' })}</span>
        </div>
        <div>
          <span className="font-medium">Long Date: </span>
          <span>{format.dateTime(now, { dateStyle: 'long' })}</span>
        </div>
        <div>
          <span className="font-medium">Time: </span>
          <span>{format.dateTime(now, { timeStyle: 'medium' })}</span>
        </div>
        <div>
          <span className="font-medium">Relative Time (1 day ago): </span>
          <span>
            {format.relativeTime(new Date(now.getTime() - 24 * 60 * 60 * 1000))}
          </span>
        </div>
        <div>
          <span className="font-medium">Currency: </span>
          <span>{format.number(price, { style: 'currency' })}</span>
        </div>
        <div>
          <span className="font-medium">Number: </span>
          <span>{format.number(1234567.89)}</span>
        </div>
      </div>
    </div>
  );
}
