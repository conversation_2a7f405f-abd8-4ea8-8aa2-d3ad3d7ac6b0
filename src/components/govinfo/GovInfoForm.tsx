"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { createGovInfo, updateGovInfo } from "@/actions/govinfo/govinfo";
import { Category } from "@prisma/client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import TiptapEditor from "@/components/shared/TiptapEditor";
import ContentRenderer from "@/components/shared/ContentRenderer";
import { slugify } from "@/lib/utils";

// 폼 스키마 정의
const formSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, '제목은 필수입니다.').max(255, '제목은 최대 255자까지 가능합니다.'),
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.'),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: z.string().min(1, '내용은 필수입니다.'),
  contentHtml: z.string().optional(),
  categoryId: z.string().min(1, '카테고리는 필수입니다.'),
  status: z.enum(['draft', 'published', 'archived']),
  isImportant: z.boolean().default(false),
  govUrl: z.string().url('유효한 URL 형식이 아닙니다.').optional().or(z.literal('')),
  govDepartment: z.string().max(255, '정부 부처/기관명은 최대 255자까지 가능합니다.').optional().or(z.literal('')),
});

type FormValues = z.infer<typeof formSchema>;

interface GovInfoFormProps {
  categories: Category[];
  initialData?: any;
  isEdit?: boolean;
}

export default function GovInfoForm({
  categories,
  initialData,
  isEdit = false,
}: GovInfoFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [editorContent, setEditorContent] = useState(
    initialData?.contentHtml || "<p></p>"
  );

  // 폼 초기화
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: initialData?.id || undefined,
      title: initialData?.title || "",
      slug: initialData?.slug || "",
      summary: initialData?.summary || "",
      content: initialData?.content || "",
      contentHtml: initialData?.contentHtml || "<p></p>",
      categoryId: initialData?.categoryId || "",
      status: initialData?.status || "draft",
      isImportant: initialData?.isImportant || false,
      govUrl: initialData?.govUrl || "",
      govDepartment: initialData?.govDepartment || "",
    },
  });

  // 제목 변경 시 슬러그 자동 생성 (새로 생성하는 경우만)
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    form.setValue("title", title);

    if (!isEdit && title) {
      const newSlug = slugify(title);
      form.setValue("slug", newSlug);
    }
  };

  // 에디터 내용 변경 처리
  const handleEditorChange = (html: string, text: string) => {
    setEditorContent(html);
    form.setValue("contentHtml", html);
    form.setValue("content", text);
  };

  // 폼 제출 처리
  const onSubmit = (values: FormValues) => {
    startTransition(async () => {
      try {
        if (isEdit && initialData?.id) {
          // 수정 모드
          const result = await updateGovInfo({
            ...values,
            id: initialData.id,
          });

          if (result.success) {
            toast.success('정부 정보가 성공적으로 수정되었습니다.');
            router.push(`/admin/gov-info/${result.data.id}`);
            router.refresh();
          } else {
            toast.error(`정부 정보 수정 실패: ${result.error}`);
          }
        } else {
          // 생성 모드
          const result = await createGovInfo(values);

          if (result.success) {
            toast.success('정부 정보가 성공적으로 생성되었습니다.');
            router.push(`/admin/gov-info/${result.data.id}`);
            router.refresh();
          } else {
            toast.error(`정부 정보 생성 실패: ${result.error}`);
          }
        }
      } catch (error) {
        console.error('정부 정보 저장 중 오류 발생:', error);
        toast.error('정부 정보 저장 중 오류가 발생했습니다.');
      }
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="editor">에디터</TabsTrigger>
                <TabsTrigger value="preview">미리보기</TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>제목</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="제목을 입력하세요"
                          {...field}
                          onChange={handleTitleChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>슬러그</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="슬러그를 입력하세요"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        URL에 사용될 고유 식별자입니다. 영문, 숫자, 하이픈(-)만 사용하세요.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="summary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>요약</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="내용을 간략히 요약해주세요"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        최대 500자까지 입력 가능합니다.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>내용</FormLabel>
                      <FormControl>
                        <div className="min-h-[300px] border rounded-md">
                          <TiptapEditor
                            content={editorContent}
                            onChange={handleEditorChange}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="preview">
                <Card>
                  <CardContent className="pt-6">
                    <h1 className="text-2xl font-bold mb-2">
                      {form.watch("title") || "제목 없음"}
                    </h1>
                    {form.watch("summary") && (
                      <p className="text-muted-foreground mb-4">
                        {form.watch("summary")}
                      </p>
                    )}
                    <Separator className="my-4" />
                    <ContentRenderer content={editorContent} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>상태</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="상태 선택" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">임시저장</SelectItem>
                            <SelectItem value="published">발행</SelectItem>
                            <SelectItem value="archived">보관</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>카테고리</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="카테고리 선택" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem
                                key={category.id}
                                value={category.id}
                              >
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isImportant"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>중요 공지사항</FormLabel>
                          <FormDescription>
                            중요 공지사항으로 표시합니다.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="govDepartment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>정부 부처/기관명</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="정부 부처 또는 공공기관 이름"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="govUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>정부/공공기관 웹사이트 URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.gov"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          정부 부처 또는 공공기관 웹사이트 주소를 입력하세요.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Button
              type="submit"
              className="w-full"
              disabled={isPending}
            >
              {isPending
                ? "저장 중..."
                : isEdit
                ? "정부 정보 수정"
                : "정부 정보 생성"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
