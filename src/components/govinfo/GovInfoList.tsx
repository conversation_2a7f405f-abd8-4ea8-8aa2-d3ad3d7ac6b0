"use client";

import Link from "next/link";
import { Category } from "@prisma/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { formatDate } from "@/lib/utils";
import { CalendarIcon, ExternalLink } from "lucide-react";
import Pagination from "@/components/shared/Pagination";

interface GovInfoListProps {
  govInfos: any[];
  categories: Category[];
  totalPages: number;
  currentPage: number;
  isImportant?: boolean;
}

export default function GovInfoList({
  govInfos,
  categories,
  totalPages,
  currentPage,
  isImportant,
}: GovInfoListProps) {
  // 카테고리 변경 핸들러
  const handleCategoryChange = (value: string) => {
    const url = new URL(window.location.href);
    if (value === "all") {
      url.searchParams.delete("categoryId");
    } else {
      url.searchParams.set("categoryId", value);
    }
    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // 중요 공지사항 필터 핸들러
  const handleImportantChange = () => {
    const url = new URL(window.location.href);
    if (isImportant) {
      url.searchParams.delete("isImportant");
    } else {
      url.searchParams.set("isImportant", "true");
    }
    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // 현재 선택된 카테고리 ID
  const currentCategoryId = new URLSearchParams(window.location.search).get("categoryId") || "all";

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex items-center gap-2">
          <Select
            value={currentCategoryId}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="카테고리 선택" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">전체 카테고리</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant={isImportant ? "default" : "outline"}
            onClick={handleImportantChange}
            size="sm"
          >
            중요 공지만 보기
          </Button>
        </div>
      </div>

      {govInfos.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">정부 정보가 없습니다.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {govInfos.map((govInfo) => (
            <Card key={govInfo.id} className="flex flex-col h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">{govInfo.category.name}</Badge>
                  {govInfo.isImportant && (
                    <Badge variant="destructive">중요</Badge>
                  )}
                </div>
                <CardTitle className="text-lg">
                  <Link
                    href={`/gov-info/${govInfo.slug}`}
                    className="hover:underline"
                  >
                    {govInfo.title}
                  </Link>
                </CardTitle>
                {govInfo.summary && (
                  <CardDescription className="line-clamp-2">
                    {govInfo.summary}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className="pb-3 flex-grow">
                {govInfo.govDepartment && (
                  <div className="text-sm text-muted-foreground mb-2">
                    {govInfo.govDepartment}
                  </div>
                )}
                {govInfo.govUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1 text-xs"
                    asChild
                  >
                    <a
                      href={govInfo.govUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink size={12} />
                      웹사이트 바로가기
                    </a>
                  </Button>
                )}
              </CardContent>
              <CardFooter className="pt-0 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <CalendarIcon size={12} />
                  <span>
                    {govInfo.infoUpdatedAt
                      ? `정보 업데이트: ${formatDate(govInfo.infoUpdatedAt)}`
                      : `작성일: ${formatDate(govInfo.createdAt)}`}
                  </span>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {totalPages > 1 && (
        <>
          <Separator className="my-6" />
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            baseUrl={`/gov-info${isImportant ? "?isImportant=true&" : "?"}`}
          />
        </>
      )}
    </div>
  );
}
