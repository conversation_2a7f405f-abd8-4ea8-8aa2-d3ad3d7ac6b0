"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Category } from "@prisma/client";
import { toast } from "sonner";
import { deleteGovInfo, updateGovInfo } from "@/actions/govinfo/govinfo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/lib/utils";
import { MoreHorizontal, ExternalLink } from "lucide-react";
import Pagination from "@/components/shared/Pagination";

interface GovInfoTableProps {
  govInfos: any[];
  categories: Category[];
  pagination: {
    totalPages: number;
  };
  currentPage: number;
  status: "draft" | "published" | "archived";
  categoryId?: string;
}

export default function GovInfoTable({
  govInfos,
  categories,
  pagination,
  currentPage,
  status,
  categoryId,
}: GovInfoTableProps) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [isStatusChanging, setIsStatusChanging] = useState(false);

  // 카테고리 변경 핸들러
  const handleCategoryChange = (value: string) => {
    const url = new URL(window.location.href);
    if (value === "all") {
      url.searchParams.delete("categoryId");
    } else {
      url.searchParams.set("categoryId", value);
    }
    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // 삭제 다이얼로그 열기
  const openDeleteDialog = (id: string) => {
    setDeleteItemId(id);
    setIsDeleting(true);
  };

  // 삭제 다이얼로그 닫기
  const closeDeleteDialog = () => {
    setIsDeleting(false);
    setDeleteItemId(null);
  };

  // 정부 정보 삭제 처리
  const handleDelete = async () => {
    if (!deleteItemId) return;

    try {
      const result = await deleteGovInfo({ id: deleteItemId });
      if (result.success) {
        toast.success("정부 정보가 삭제되었습니다.");
        router.refresh();
      } else {
        toast.error(`삭제 실패: ${result.error}`);
      }
    } catch (error) {
      console.error("정부 정보 삭제 중 오류 발생:", error);
      toast.error("정부 정보 삭제 중 오류가 발생했습니다.");
    } finally {
      closeDeleteDialog();
    }
  };

  // 상태 변경 처리
  const handleStatusChange = async (id: string, newStatus: string) => {
    setIsStatusChanging(true);
    try {
      const result = await updateGovInfo({
        id,
        status: newStatus as "draft" | "published" | "archived",
      });

      if (result.success) {
        toast.success(`상태가 ${getStatusText(newStatus)}(으)로 변경되었습니다.`);
        router.refresh();
      } else {
        toast.error(`상태 변경 실패: ${result.error}`);
      }
    } catch (error) {
      console.error("상태 변경 중 오류 발생:", error);
      toast.error("상태 변경 중 오류가 발생했습니다.");
    } finally {
      setIsStatusChanging(false);
    }
  };

  // 상태 텍스트 변환
  const getStatusText = (status: string) => {
    switch (status) {
      case "draft":
        return "임시저장";
      case "published":
        return "발행됨";
      case "archived":
        return "보관됨";
      default:
        return status;
    }
  };

  // 상태 배지 변형 설정
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "draft":
        return "secondary" as const;
      case "published":
        return "success" as const;
      case "archived":
        return "outline" as const;
      default:
        return "outline" as const;
    }
  };

  // 현재 선택된 카테고리 ID
  const currentCategoryId = categoryId || "all";

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Select
          value={currentCategoryId}
          onValueChange={handleCategoryChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="카테고리 선택" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">전체 카테고리</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {govInfos.length === 0 ? (
        <div className="text-center py-12 bg-muted/30 rounded-md">
          <p className="text-muted-foreground">정부 정보가 없습니다.</p>
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>제목</TableHead>
                <TableHead>카테고리</TableHead>
                <TableHead>중요 여부</TableHead>
                <TableHead>작성일</TableHead>
                <TableHead>조회수</TableHead>
                <TableHead className="w-[100px]">작업</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {govInfos.map((govInfo) => (
                <TableRow key={govInfo.id}>
                  <TableCell className="font-medium">
                    <Link
                      href={`/admin/gov-info/${govInfo.id}`}
                      className="hover:underline"
                    >
                      {govInfo.title}
                    </Link>
                  </TableCell>
                  <TableCell>{govInfo.category.name}</TableCell>
                  <TableCell>
                    {govInfo.isImportant ? (
                      <Badge variant="destructive">중요</Badge>
                    ) : (
                      <span className="text-muted-foreground">일반</span>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(govInfo.createdAt)}</TableCell>
                  <TableCell>{govInfo.viewCount}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">메뉴 열기</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>작업</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/gov-info/${govInfo.id}`}>
                            상세 보기
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/gov-info/${govInfo.id}/edit`}>
                            수정
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link
                            href={`/gov-info/${govInfo.slug}`}
                            target="_blank"
                            className="flex items-center"
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            공개 페이지
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>상태 변경</DropdownMenuLabel>
                        {status !== "draft" && (
                          <DropdownMenuItem
                            onClick={() => handleStatusChange(govInfo.id, "draft")}
                            disabled={isStatusChanging}
                          >
                            임시저장으로 변경
                          </DropdownMenuItem>
                        )}
                        {status !== "published" && (
                          <DropdownMenuItem
                            onClick={() => handleStatusChange(govInfo.id, "published")}
                            disabled={isStatusChanging}
                          >
                            발행으로 변경
                          </DropdownMenuItem>
                        )}
                        {status !== "archived" && (
                          <DropdownMenuItem
                            onClick={() => handleStatusChange(govInfo.id, "archived")}
                            disabled={isStatusChanging}
                          >
                            보관으로 변경
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(govInfo.id)}
                          className="text-destructive focus:text-destructive"
                        >
                          삭제
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {pagination.totalPages > 1 && (
        <Pagination
          totalPages={pagination.totalPages}
          currentPage={currentPage}
          baseUrl={`/admin/gov-info?status=${status}${
            categoryId ? `&categoryId=${categoryId}` : ""
          }&`}
        />
      )}

      <AlertDialog open={isDeleting} onOpenChange={setIsDeleting}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>정부 정보 삭제</AlertDialogTitle>
            <AlertDialogDescription>
              이 정부 정보를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={closeDeleteDialog}>
              취소
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              삭제
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
