"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { toast } from "sonner";
import { uploadGovInfoImage, deleteGovInfoImage } from "@/actions/govinfo/govinfo";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";

interface ImageGalleryProps {
  govInfoId: string;
  images: any[];
  onImagesChange?: () => void;
}

export default function ImageGallery({
  govInfoId,
  images,
  onImagesChange,
}: ImageGalleryProps) {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteImageId, setDeleteImageId] = useState<string | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");
  const [imageCaption, setImageCaption] = useState("");

  // 이미지 업로드 처리
  const handleUpload = async () => {
    if (!imageUrl) {
      toast.error("이미지 URL을 입력해주세요.");
      return;
    }

    setIsUploading(true);
    try {
      const result = await uploadGovInfoImage({
        govInfoId,
        url: imageUrl,
        alt: imageAlt,
        caption: imageCaption,
        order: images.length,
      });

      if (result.success) {
        toast.success("이미지가 성공적으로 업로드되었습니다.");
        setImageUrl("");
        setImageAlt("");
        setImageCaption("");
        setUploadDialogOpen(false);
        router.refresh();
        onImagesChange?.();
      } else {
        toast.error(`이미지 업로드 실패: ${result.error}`);
      }
    } catch (error) {
      console.error("이미지 업로드 중 오류 발생:", error);
      toast.error("이미지 업로드 중 오류가 발생했습니다.");
    } finally {
      setIsUploading(false);
    }
  };

  // 이미지 삭제 다이얼로그 열기
  const openDeleteDialog = (id: string) => {
    setDeleteImageId(id);
    setIsDeleting(true);
  };

  // 이미지 삭제 처리
  const handleDelete = async () => {
    if (!deleteImageId) return;

    try {
      const result = await deleteGovInfoImage({ id: deleteImageId });
      if (result.success) {
        toast.success("이미지가 삭제되었습니다.");
        router.refresh();
        onImagesChange?.();
      } else {
        toast.error(`이미지 삭제 실패: ${result.error}`);
      }
    } catch (error) {
      console.error("이미지 삭제 중 오류 발생:", error);
      toast.error("이미지 삭제 중 오류가 발생했습니다.");
    } finally {
      setIsDeleting(false);
      setDeleteImageId(null);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">이미지 갤러리</h3>
        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <ImagePlus className="mr-2 h-4 w-4" />
              이미지 추가
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>이미지 추가</DialogTitle>
              <DialogDescription>
                정부 정보에 추가할 이미지 정보를 입력하세요.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="imageUrl">이미지 URL</Label>
                <Input
                  id="imageUrl"
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="imageAlt">대체 텍스트</Label>
                <Input
                  id="imageAlt"
                  placeholder="이미지 설명"
                  value={imageAlt}
                  onChange={(e) => setImageAlt(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="imageCaption">캡션</Label>
                <Textarea
                  id="imageCaption"
                  placeholder="이미지 캡션"
                  value={imageCaption}
                  onChange={(e) => setImageCaption(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setUploadDialogOpen(false)}
              >
                취소
              </Button>
              <Button onClick={handleUpload} disabled={isUploading}>
                {isUploading ? "업로드 중..." : "업로드"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {images.length === 0 ? (
        <div className="text-center py-12 bg-muted/30 rounded-md">
          <p className="text-muted-foreground">등록된 이미지가 없습니다.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {images.map((image) => (
            <Card key={image.id}>
              <CardHeader className="p-0">
                <div className="relative aspect-video">
                  <Image
                    src={image.url}
                    alt={image.alt || "정부 정보 이미지"}
                    fill
                    className="object-cover rounded-t-lg"
                  />
                </div>
              </CardHeader>
              <CardContent className="p-4">
                {image.caption && (
                  <p className="text-sm text-muted-foreground">
                    {image.caption}
                  </p>
                )}
              </CardContent>
              <CardFooter className="p-4 pt-0 flex justify-end">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => openDeleteDialog(image.id)}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  삭제
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <AlertDialog open={isDeleting} onOpenChange={setIsDeleting}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>이미지 삭제</AlertDialogTitle>
            <AlertDialogDescription>
              이 이미지를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleting(false)}>
              취소
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              삭제
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
