"use client";

import { useState, FormEvent } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

interface SearchFormProps {
  className?: string;
}

export default function SearchForm({ className }: SearchFormProps) {
  const router = useRouter();
  const [query, setQuery] = useState("");

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    // 현재 URL에서 쿼리 파라미터 유지하면서 검색어 추가
    const url = new URL(window.location.href);
    url.searchParams.set("query", query);
    url.searchParams.set("page", "1");
    router.push(url.toString());
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={`flex items-center space-x-2 ${className}`}
    >
      <div className="relative flex-grow">
        <Search
          className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"
          aria-hidden="true"
        />
        <Input
          type="search"
          placeholder="정부 정보 검색..."
          className="pl-8"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          aria-label="정부 정보 검색어 입력"
        />
      </div>
      <Button type="submit" aria-label="정부 정보 검색 실행">
        검색
      </Button>
    </form>
  );
}
