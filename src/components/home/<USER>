'use client';

import { menuItems } from '@/constants/menu-items';
import Image from 'next/image';
import Link from 'next/link';
import { IrregularGrid } from './irregular-grid';

interface MenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HomeMenu({ isOpen, onClose }: MenuProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden bg-[#1A1A1A] p-4 text-white">
      {/* 불규칙한 그리드 라인 */}
      <IrregularGrid
        variant="menu"
        color="white"
        opacity={5}
      />

      {/* 닫기 버튼 */}
      <div className="absolute top-4 right-4">
        <button
          className="text-sm font-black uppercase"
          onClick={onClose}
        >
          Close
        </button>
      </div>

      {/* 데스크톱 메뉴 레이아웃 */}
      <div className="hidden h-full md:flex md:flex-col md:justify-between">
        <div className="flex h-full">
          {/* 메뉴 아이템 */}
          <div className="mt-[12rem] ml-[1.5rem] flex flex-col space-y-3">
            {menuItems.map((item) => (
              <Link
                key={item.name}
                href={item.path}
                className="text-3xl font-bold transition-opacity hover:opacity-70"
                onClick={onClose}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        {/* 하단 정보 */}
        <div className="absolute bottom-5 w-full">
          <div className="flex justify-between p-5">
            <div className="flex items-center">
              {/* 로고 */}
              <div className="pl-3">
                <Image
                  src="/logo_only_white.svg"
                  alt="logo"
                  width={400}
                  height={400}
                />
              </div>
              <div className="pl-8">
                <p className="text-xs">All rights reserved ©sodamm</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex flex-col space-y-1 pr-10 text-right">
                <Link
                  href="https://www.threads.com/"
                  className="text-xs transition-opacity hover:opacity-70"
                >
                  THREADS
                </Link>
                <Link
                  href="https://instagram.com"
                  className="text-xs transition-opacity hover:opacity-70"
                >
                  INSTAGRAM
                </Link>
              </div>
              <div className="pr-10 text-right">
                <p className="text-xs"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 모바일 메뉴 레이아웃 */}
      <div className="flex h-full flex-col justify-between md:hidden">
        {/* 메뉴 아이템 */}
        <div className="mt-20 px-5">
          <div className="flex flex-col space-y-3">
            {menuItems.map((item) => (
              <Link
                key={item.name}
                href={item.path}
                className="text-3xl font-black transition-opacity hover:opacity-70"
                onClick={onClose}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        {/* 하단 정보 */}
        <div className="p-5">
          <div className="mb-4 flex items-center">
            {/* 로고 */}
            <Image
              src="/logo_only_white.svg"
              alt="logo"
              width={190}
              height={190}
            />
          </div>
          <div className="flex justify-between">
            <p className="text-xs">All rights reserved ©sodamm</p>
            <div className="flex flex-col space-y-1 text-right">
              <Link
                href="https://www.threads.com/"
                className="text-xs transition-opacity hover:opacity-70"
              >
                THREADS
              </Link>
              <Link
                href="https://instagram.com"
                className="text-xs transition-opacity hover:opacity-70"
              >
                INSTAGRAM
              </Link>
            </div>
          </div>
          <div className="mt-2 text-right">
            <p className="text-xs"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  );
}
