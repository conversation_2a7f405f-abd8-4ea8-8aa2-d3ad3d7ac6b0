'use client';

interface IrregularGridProps {
  color?: string;
  opacity?: number;
  variant?: 'home' | 'menu';
}

export function IrregularGrid({
  color = 'black',
  opacity = 10,
  variant = 'home',
}: IrregularGridProps) {
  // 메뉴 그리드 라인 (검은 배경에 흰색 라인)
  if (variant === 'menu') {
    return (
      <div
        className={`pointer-events-none absolute inset-0 opacity-${opacity}`}
      >
        {/* 수직선 - 매우 불규칙한 간격 */}
        <div className="absolute top-[5%] left-[12%] h-[40%] w-[1px] bg-white"></div>
        <div className="absolute top-[60%] left-[12%] h-[25%] w-[1px] bg-white"></div>
        <div className="absolute top-[20%] left-[38%] h-[30%] w-[1px] bg-white"></div>
        <div className="absolute top-[65%] left-[38%] h-[20%] w-[1px] bg-white"></div>
        <div className="absolute top-[10%] left-[67%] h-[35%] w-[1px] bg-white"></div>
        <div className="absolute top-[55%] left-[67%] h-[30%] w-[1px] bg-white"></div>
        <div className="absolute top-[25%] left-[88%] h-[25%] w-[1px] bg-white"></div>

        {/* 수평선 - 매우 불규칙한 간격 */}
        <div className="absolute top-[18%] left-[5%] h-[1px] w-[30%] bg-white"></div>
        <div className="absolute top-[18%] left-[45%] h-[1px] w-[40%] bg-white"></div>
        <div className="absolute top-[42%] left-[15%] h-[1px] w-[25%] bg-white"></div>
        <div className="absolute top-[42%] left-[55%] h-[1px] w-[35%] bg-white"></div>
        <div className="absolute top-[72%] left-[8%] h-[1px] w-[35%] bg-white"></div>
        <div className="absolute top-[72%] left-[60%] h-[1px] w-[25%] bg-white"></div>
      </div>
    );
  }

  // 홈 그리드 라인 (밝은 배경에 어두운 라인)
  return (
    <div className={`pointer-events-none absolute inset-0 opacity-${opacity}`}>
      {/* 수직선 - 불규칙한 간격 */}
      <div
        className={`absolute top-[15%] left-[22%] h-[45%] w-[1px] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[70%] left-[22%] h-[20%] w-[1px] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[10%] left-[58%] h-[30%] w-[1px] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[50%] left-[58%] h-[40%] w-[1px] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[25%] left-[78%] h-[35%] w-[1px] bg-${color}`}
      ></div>

      {/* 수평선 - 불규칙한 간격 */}
      <div
        className={`absolute top-[32%] left-[10%] h-[1px] w-[25%] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[32%] left-[65%] h-[1px] w-[20%] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[62%] left-[15%] h-[1px] w-[30%] bg-${color}`}
      ></div>
      <div
        className={`absolute top-[62%] left-[55%] h-[1px] w-[35%] bg-${color}`}
      ></div>
    </div>
  );
}
