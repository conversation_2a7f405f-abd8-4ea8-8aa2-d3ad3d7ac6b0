"use client";

import { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ko } from "date-fns/locale";
import {
  Building2,
  MapPin,
  Calendar,
  Briefcase,
  Phone,
  Wrench,
  Edit,
  Trash2,
  ArrowLeft,
  Eye,
  Share2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import ContentRenderer from "@/components/editor/ContentRenderer";
import { incrementViewCount, deleteJobPost } from "@/actions/job/jobpost";
import { SocialShareButtons } from "@/components/social-share-buttons";

interface JobPostDetailProps {
  jobPost: any;
  isAuthor: boolean;
  isAdmin: boolean;
}

export default function JobPostDetail({
  jobPost,
  isAuthor,
  isAdmin,
}: JobPostDetailProps) {
  const router = useRouter();

  // 조회수 증가
  useEffect(() => {
    incrementViewCount({ id: jobPost.id });
  }, [jobPost.id]);

  // 삭제 핸들러
  const handleDelete = async () => {
    try {
      const result = await deleteJobPost({ id: jobPost.id });

      if (result.success) {
        router.push("/jobs");
        router.refresh();
      } else {
        console.error("구인·구직 정보 삭제 실패:", result.error);
      }
    } catch (error) {
      console.error("구인·구직 정보 삭제 중 오류 발생:", error);
    }
  };

  // 만료 여부 확인
  const isExpired =
    jobPost.expiresAt && new Date(jobPost.expiresAt) < new Date();

  // 상태에 따른 배지 색상
  const statusBadge = () => {
    if (jobPost.status === "closed" || isExpired) {
      return <Badge variant="destructive">마감됨</Badge>;
    }
    return <Badge variant="outline">진행중</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/jobs">
            <ArrowLeft className="h-4 w-4 mr-2" />
            목록으로
          </Link>
        </Button>

        {(isAuthor || isAdmin) && (
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/jobs/edit/${jobPost.id}`}>
                <Edit className="h-4 w-4 mr-2" />
                수정
              </Link>
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  삭제
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>구인·구직 정보 삭제</AlertDialogTitle>
                  <AlertDialogDescription>
                    이 구인·구직 정보를 삭제하시겠습니까? 이 작업은 되돌릴 수
                    없습니다.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>취소</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete}>
                    삭제
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="flex-1 space-y-6">
          <div>
            <div className="flex flex-wrap gap-2 mb-2">
              <Badge
                variant={jobPost.type === "JOB_OFFER" ? "default" : "secondary"}
              >
                {jobPost.type === "JOB_OFFER" ? "구인" : "구직"}
              </Badge>

              {jobPost.jobType && (
                <Badge variant="outline">
                  {jobPost.jobType === "FULL_TIME" && "정규직"}
                  {jobPost.jobType === "PART_TIME" && "파트타임"}
                  {jobPost.jobType === "CONTRACT" && "계약직"}
                  {jobPost.jobType === "INTERNSHIP" && "인턴십"}
                </Badge>
              )}

              {statusBadge()}

              {jobPost.category && (
                <Badge variant="outline">{jobPost.category.name}</Badge>
              )}
            </div>

            <h1 className="text-2xl font-bold mb-2">{jobPost.title}</h1>

            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                <span>
                  {format(new Date(jobPost.createdAt), "yyyy.MM.dd", {
                    locale: ko,
                  })}
                </span>
              </div>

              <span>•</span>

              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-1" />
                <span>{jobPost.viewCount}</span>
              </div>
            </div>

            <div className="mt-4">
              <SocialShareButtons
                title={`${jobPost.title} - Sodamm 구인구직`}
                description={`${jobPost.company || jobPost.author.name}에서 ${
                  jobPost.title
                } 포지션을 찾고 있습니다. 위치: ${jobPost.location}`}
                hashtags="구인구직,채용정보,소담,한국취업"
                size="sm"
                variant="outline"
              />
            </div>
          </div>

          <Separator />

          <div className="prose max-w-none">
            <ContentRenderer content={jobPost.description} />
          </div>
        </div>

        <div className="md:w-80 space-y-6">
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="relative h-10 w-10 rounded-full overflow-hidden bg-muted">
                  {jobPost.author.image ? (
                    <Image
                      src={jobPost.author.image}
                      alt={jobPost.author.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full w-full bg-primary/10 text-primary font-semibold">
                      {jobPost.author.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <div>
                  <p className="font-medium">
                    {jobPost.author.displayName || jobPost.author.name}
                  </p>
                  <p className="text-sm text-muted-foreground">작성자</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                {jobPost.company && (
                  <div className="flex items-center gap-3">
                    <Building2 className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">회사/기관</p>
                      <p>{jobPost.company}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">지역</p>
                    <p>{jobPost.location}</p>
                  </div>
                </div>

                {jobPost.industry && (
                  <div className="flex items-center gap-3">
                    <Briefcase className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">업종</p>
                      <p>{jobPost.industry}</p>
                    </div>
                  </div>
                )}

                {jobPost.salary && (
                  <div className="flex items-center gap-3">
                    <Briefcase className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">급여</p>
                      <p>{jobPost.salary}</p>
                    </div>
                  </div>
                )}

                {jobPost.requiredSkills && (
                  <div className="flex items-center gap-3">
                    <Wrench className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">필요 기술/자격</p>
                      <p className="whitespace-pre-line">
                        {jobPost.requiredSkills}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">연락처</p>
                    <p>{jobPost.contactInfo}</p>
                  </div>
                </div>

                {jobPost.expiresAt && (
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">마감일</p>
                      <p>
                        {format(
                          new Date(jobPost.expiresAt),
                          "yyyy년 MM월 dd일",
                          { locale: ko }
                        )}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
