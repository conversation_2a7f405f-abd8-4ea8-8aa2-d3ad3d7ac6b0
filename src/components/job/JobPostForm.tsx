'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { createJobPost, updateJobPost } from '@/actions/job/jobpost';
import { cn } from '@/lib/utils';
import TiptapEditor from '@/components/editor/TiptapEditor';
import ContentRenderer from '@/components/editor/ContentRenderer';

// 폼 스키마 정의
const formSchema = z.object({
  title: z.string().min(1, '제목은 필수입니다.').max(255, '제목은 최대 255자까지 가능합니다.'),
  description: z.string().min(1, '내용은 필수입니다.'),
  contentHtml: z.string().optional(),
  type: z.enum(['JOB_OFFER', 'JOB_SEEK'], {
    required_error: '구인/구직 유형을 선택해주세요.',
  }),
  company: z.string().optional(),
  jobType: z.enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP']).optional(),
  industry: z.string().optional(),
  location: z.string().min(1, '지역은 필수입니다.'),
  salary: z.string().optional(),
  requiredSkills: z.string().optional(),
  contactInfo: z.string().min(1, '연락처는 필수입니다.'),
  categoryId: z.string().optional(),
  expiresAt: z.date().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface JobPostFormProps {
  jobPost?: any;
  categories?: any[];
}

export default function JobPostForm({ jobPost, categories }: JobPostFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');

  // 폼 초기값 설정
  const defaultValues: Partial<FormValues> = {
    title: jobPost?.title || '',
    description: jobPost?.description || '',
    contentHtml: jobPost?.description || '',
    type: jobPost?.type || 'JOB_OFFER',
    company: jobPost?.company || '',
    jobType: jobPost?.jobType || undefined,
    industry: jobPost?.industry || '',
    location: jobPost?.location || '',
    salary: jobPost?.salary || '',
    requiredSkills: jobPost?.requiredSkills || '',
    contactInfo: jobPost?.contactInfo || '',
    categoryId: jobPost?.categoryId || undefined,
    expiresAt: jobPost?.expiresAt ? new Date(jobPost.expiresAt) : undefined,
  };

  // 폼 설정
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  // 에디터 내용 변경 핸들러
  const handleEditorChange = (html: string) => {
    form.setValue('contentHtml', html);
    form.setValue('description', html.replace(/<[^>]*>/g, ''));
    setPreviewContent(html);
  };

  // 폼 제출 핸들러
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      const formData = {
        ...values,
        expiresAt: values.expiresAt ? format(values.expiresAt, 'yyyy-MM-dd') : undefined,
      };

      if (jobPost?.id) {
        // 기존 구인·구직 정보 수정
        const result = await updateJobPost({
          id: jobPost.id,
          ...formData,
        });

        if (result.success) {
          router.push(`/jobs/${jobPost.id}`);
          router.refresh();
        } else {
          console.error('구인·구직 정보 수정 실패:', result.error);
        }
      } else {
        // 새 구인·구직 정보 생성
        const result = await createJobPost(formData);

        if (result.success) {
          router.push(`/jobs/${result.data.id}`);
          router.refresh();
        } else {
          console.error('구인·구직 정보 생성 실패:', result.error);
        }
      }
    } catch (error) {
      console.error('구인·구직 정보 저장 중 오류 발생:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="editor">에디터</TabsTrigger>
                <TabsTrigger value="preview">미리보기</TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>제목</FormLabel>
                      <FormControl>
                        <Input placeholder="제목을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>구인/구직 유형</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="유형을 선택하세요" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="JOB_OFFER">구인 정보</SelectItem>
                            <SelectItem value="JOB_SEEK">구직 정보</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="jobType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>고용 형태</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="고용 형태를 선택하세요" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="FULL_TIME">정규직</SelectItem>
                            <SelectItem value="PART_TIME">파트타임</SelectItem>
                            <SelectItem value="CONTRACT">계약직</SelectItem>
                            <SelectItem value="INTERNSHIP">인턴십</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="contentHtml"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>내용</FormLabel>
                      <FormControl>
                        <TiptapEditor
                          content={form.getValues('contentHtml') || ''}
                          onChange={handleEditorChange}
                          placeholder="내용을 입력하세요..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="preview">
                <Card>
                  <CardContent className="pt-6">
                    <h1 className="text-2xl font-bold mb-2">{form.watch('title')}</h1>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {form.watch('type') === 'JOB_OFFER' ? (
                        <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">구인</span>
                      ) : (
                        <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">구직</span>
                      )}
                      {form.watch('jobType') && (
                        <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                          {form.watch('jobType') === 'FULL_TIME' && '정규직'}
                          {form.watch('jobType') === 'PART_TIME' && '파트타임'}
                          {form.watch('jobType') === 'CONTRACT' && '계약직'}
                          {form.watch('jobType') === 'INTERNSHIP' && '인턴십'}
                        </span>
                      )}
                    </div>
                    <Separator className="my-4" />
                    <ContentRenderer content={previewContent || form.watch('contentHtml') || ''} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6 space-y-4">
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>회사/기관명</FormLabel>
                      <FormControl>
                        <Input placeholder="회사 또는 기관명을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>업종</FormLabel>
                      <FormControl>
                        <Input placeholder="업종을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>지역</FormLabel>
                      <FormControl>
                        <Input placeholder="지역을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="salary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>급여</FormLabel>
                      <FormControl>
                        <Input placeholder="급여 정보를 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requiredSkills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>필요 기술/자격</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="필요한 기술이나 자격 요건을 입력하세요"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactInfo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>연락처</FormLabel>
                      <FormControl>
                        <Input placeholder="연락처를 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {categories && categories.length > 0 && (
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>카테고리</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="카테고리를 선택하세요" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="expiresAt"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>만료일</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "yyyy-MM-dd")
                              ) : (
                                <span>만료일 선택</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date(new Date().setHours(0, 0, 0, 0))
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        만료일을 설정하지 않으면 무기한으로 게시됩니다.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? '저장 중...' : jobPost ? '수정하기' : '등록하기'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
