'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { ko } from 'date-fns/locale';
import { 
  Building2, 
  MapPin, 
  Calendar, 
  Briefcase, 
  Search, 
  Plus,
  ChevronLeft,
  ChevronRight,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface JobPostListProps {
  jobPosts: any[];
  categories?: any[];
  total: number;
  page: number;
  limit: number;
  isAuthenticated?: boolean;
}

export default function JobPostList({
  jobPosts,
  categories,
  total,
  page,
  limit,
  isAuthenticated = false,
}: JobPostListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [searchQuery, setSearchQuery] = useState(searchParams.get('query') || '');
  const [selectedType, setSelectedType] = useState(searchParams.get('type') || '');
  const [selectedJobType, setSelectedJobType] = useState(searchParams.get('jobType') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('categoryId') || '');
  const [selectedLocation, setSelectedLocation] = useState(searchParams.get('location') || '');

  // 총 페이지 수 계산
  const totalPages = Math.ceil(total / limit);

  // 검색 핸들러
  const handleSearch = () => {
    const params = new URLSearchParams();
    
    if (searchQuery) params.set('query', searchQuery);
    if (selectedType) params.set('type', selectedType);
    if (selectedJobType) params.set('jobType', selectedJobType);
    if (selectedCategory) params.set('categoryId', selectedCategory);
    if (selectedLocation) params.set('location', selectedLocation);
    params.set('page', '1');
    
    router.push(`/jobs?${params.toString()}`);
  };

  // 필터 초기화 핸들러
  const handleResetFilters = () => {
    setSearchQuery('');
    setSelectedType('');
    setSelectedJobType('');
    setSelectedCategory('');
    setSelectedLocation('');
    
    router.push('/jobs');
  };

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    
    router.push(`/jobs?${params.toString()}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex flex-1 gap-2">
          <div className="relative flex-1">
            <Input
              placeholder="검색어를 입력하세요"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="pr-10"
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-full"
              onClick={handleSearch}
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
          
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>필터</SheetTitle>
                <SheetDescription>
                  원하는 조건으로 구인·구직 정보를 필터링하세요.
                </SheetDescription>
              </SheetHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">구인/구직 유형</h3>
                  <Select
                    value={selectedType}
                    onValueChange={setSelectedType}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="전체" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">전체</SelectItem>
                      <SelectItem value="JOB_OFFER">구인 정보</SelectItem>
                      <SelectItem value="JOB_SEEK">구직 정보</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">고용 형태</h3>
                  <Select
                    value={selectedJobType}
                    onValueChange={setSelectedJobType}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="전체" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">전체</SelectItem>
                      <SelectItem value="FULL_TIME">정규직</SelectItem>
                      <SelectItem value="PART_TIME">파트타임</SelectItem>
                      <SelectItem value="CONTRACT">계약직</SelectItem>
                      <SelectItem value="INTERNSHIP">인턴십</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {categories && categories.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">카테고리</h3>
                    <Select
                      value={selectedCategory}
                      onValueChange={setSelectedCategory}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="전체" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">전체</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">지역</h3>
                  <Input
                    placeholder="지역 검색"
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                  />
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button variant="outline" onClick={handleResetFilters} className="flex-1">
                    초기화
                  </Button>
                  <Button onClick={handleSearch} className="flex-1">
                    적용하기
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
        
        {isAuthenticated && (
          <Button asChild>
            <Link href="/jobs/create">
              <Plus className="h-4 w-4 mr-2" />
              새 글 작성
            </Link>
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {jobPosts.length > 0 ? (
          jobPosts.map((jobPost) => (
            <Link href={`/jobs/${jobPost.id}`} key={jobPost.id}>
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant={jobPost.type === 'JOB_OFFER' ? 'default' : 'secondary'}>
                      {jobPost.type === 'JOB_OFFER' ? '구인' : '구직'}
                    </Badge>
                    {jobPost.jobType && (
                      <Badge variant="outline">
                        {jobPost.jobType === 'FULL_TIME' && '정규직'}
                        {jobPost.jobType === 'PART_TIME' && '파트타임'}
                        {jobPost.jobType === 'CONTRACT' && '계약직'}
                        {jobPost.jobType === 'INTERNSHIP' && '인턴십'}
                      </Badge>
                    )}
                  </div>
                  
                  <h3 className="text-lg font-semibold line-clamp-2 mb-2">
                    {jobPost.title}
                  </h3>
                  
                  <div className="space-y-2 text-sm text-muted-foreground mb-4">
                    {jobPost.company && (
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <span>{jobPost.company}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>{jobPost.location}</span>
                    </div>
                    
                    {jobPost.salary && (
                      <div className="flex items-center gap-2">
                        <Briefcase className="h-4 w-4" />
                        <span>{jobPost.salary}</span>
                      </div>
                    )}
                  </div>
                  
                  <p className="text-sm line-clamp-3 mb-2">
                    {jobPost.description.replace(/<[^>]*>/g, '').substring(0, 150)}
                    {jobPost.description.length > 150 && '...'}
                  </p>
                </CardContent>
                
                <CardFooter className="px-4 py-3 border-t flex justify-between items-center">
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {format(new Date(jobPost.createdAt), 'yyyy.MM.dd', { locale: ko })}
                    </span>
                  </div>
                  
                  {jobPost.expiresAt && (
                    <div className="text-xs text-muted-foreground">
                      ~{format(new Date(jobPost.expiresAt), 'yyyy.MM.dd', { locale: ko })}
                    </div>
                  )}
                </CardFooter>
              </Card>
            </Link>
          ))
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center py-12">
            <p className="text-muted-foreground mb-4">등록된 구인·구직 정보가 없습니다.</p>
            {isAuthenticated && (
              <Button asChild>
                <Link href="/jobs/create">
                  <Plus className="h-4 w-4 mr-2" />
                  새 글 작성하기
                </Link>
              </Button>
            )}
          </div>
        )}
      </div>
      
      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (page > 1) handlePageChange(page - 1);
                }}
                className={cn(page <= 1 && "pointer-events-none opacity-50")}
              />
            </PaginationItem>
            
            {Array.from({ length: totalPages }).map((_, i) => {
              const pageNumber = i + 1;
              // 현재 페이지 주변 페이지만 표시
              if (
                pageNumber === 1 ||
                pageNumber === totalPages ||
                (pageNumber >= page - 1 && pageNumber <= page + 1)
              ) {
                return (
                  <PaginationItem key={pageNumber}>
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(pageNumber);
                      }}
                      isActive={pageNumber === page}
                    >
                      {pageNumber}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              
              // 생략 부호 표시
              if (pageNumber === 2 || pageNumber === totalPages - 1) {
                return (
                  <PaginationItem key={`ellipsis-${pageNumber}`}>
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
              
              return null;
            })}
            
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (page < totalPages) handlePageChange(page + 1);
                }}
                className={cn(page >= totalPages && "pointer-events-none opacity-50")}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
