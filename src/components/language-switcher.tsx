'use client';

import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';
import { setLocale } from '@/i18n/client';
import { locales } from '@/i18n/request';

export default function LanguageSwitcher() {
  const t = useTranslations('Common');
  const currentLocale = useLocale();

  const handleChangeLocale = (locale: string) => {
    setLocale(locale);
  };

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm font-medium">{t('language')}:</span>
      <div className="flex space-x-2">
        {locales.map((locale) => (
          <button
            key={locale}
            onClick={() => handleChangeLocale(locale)}
            className={`px-2 py-1 text-sm rounded ${
              currentLocale === locale
                ? 'bg-primary text-primary-foreground'
                : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
            }`}
            disabled={currentLocale === locale}
          >
            {t(locale === 'ko' ? 'korean' : 'english')}
          </button>
        ))}
      </div>
    </div>
  );
}
