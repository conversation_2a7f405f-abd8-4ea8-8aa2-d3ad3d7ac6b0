'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';

export default function Footer() {
  const t = useTranslations('Footer');
  
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-background border-t">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 로고 및 간단한 설명 */}
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="text-xl font-bold">
              Sodamm
            </Link>
            <p className="mt-2 text-sm text-muted-foreground">
              한국 생활 정보 및 구인구직 플랫폼
            </p>
          </div>
          
          {/* 링크 섹션 1 */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold mb-3">서비스</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/info" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  생활 정보
                </Link>
              </li>
              <li>
                <Link href="/guide" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  서비스 이용 가이드
                </Link>
              </li>
              <li>
                <Link href="/government" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  정부 정보
                </Link>
              </li>
            </ul>
          </div>
          
          {/* 링크 섹션 2 */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold mb-3">커뮤니티</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/jobs" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  구인구직
                </Link>
              </li>
              <li>
                <Link href="/community" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  커뮤니티 게시판
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  자주 묻는 질문
                </Link>
              </li>
            </ul>
          </div>
          
          {/* 링크 섹션 3 */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold mb-3">회사 정보</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  소개
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {t('contact')}
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {t('terms')}
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {t('privacy')}
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        {/* 저작권 정보 */}
        <div className="mt-8 pt-4 border-t text-center">
          <p className="text-sm text-muted-foreground">
            {t('copyright').replace('2024', currentYear.toString())}
          </p>
        </div>
      </div>
    </footer>
  );
}
