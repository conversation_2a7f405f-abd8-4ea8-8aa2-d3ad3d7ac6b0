"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import { Menu, X, Search, User, LogOut, ChevronDown, Bell } from "lucide-react";
import LanguageSwitcher from "@/components/language-switcher";
import { ThemeToggle } from "@/components/theme-toggle";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import SearchForm from "@/components/search/SearchForm";
import { NotificationIcon } from "@/components/notifications/notification-icon";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSession, signOut } from "next-auth/react";

export default function Header() {
  const t = useTranslations("Navigation");
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showSearchInput, setShowSearchInput] = useState(false);

  // 스크롤 감지
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 모바일 메뉴 토글
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // 검색 토글
  const toggleSearch = () => {
    setShowSearchInput(!showSearchInput);
  };

  // 로그아웃 핸들러
  const handleSignOut = async () => {
    try {
      await signOut({ callbackUrl: "/" });
    } catch (error) {
      console.error("로그아웃 중 오류 발생:", error);
    }
  };

  // 네비게이션 링크
  const mainNavLinks = [
    { href: "/", label: t("home") },
    {
      href: "#",
      label: t("livingInfo"),
      children: [
        { href: "/info/housing", label: t("housing") },
        { href: "/info/transportation", label: t("transportation") },
        { href: "/info/healthcare", label: t("healthcare") },
        { href: "/info/education", label: t("education") },
      ],
    },
    { href: "/service-guide", label: "서비스 가이드" },
    { href: "/gov-info", label: "정부 정보" },
    { href: "/jobs", label: t("jobs") },
    { href: "/community", label: t("community") },
  ];

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-background/80 backdrop-blur-md shadow-sm"
          : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* 로고 */}
          <Link href="/" className="text-2xl font-bold">
            Sodamm
          </Link>

          {/* 데스크탑 네비게이션 */}
          <nav className="hidden lg:flex items-center space-x-6">
            {mainNavLinks.map((link) =>
              link.children ? (
                <DropdownMenu key={link.label}>
                  <DropdownMenuTrigger className="flex items-center text-sm font-medium transition-colors hover:text-primary">
                    {link.label} <ChevronDown className="ml-1 h-4 w-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    {link.children.map((child) => (
                      <DropdownMenuItem key={child.href} asChild>
                        <Link href={child.href}>{child.label}</Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    pathname === link.href ||
                    (link.href !== "/" && pathname.startsWith(link.href))
                      ? "text-primary"
                      : "text-foreground/80"
                  }`}
                >
                  {link.label}
                </Link>
              )
            )}
          </nav>

          {/* 데스크탑 우측 메뉴 */}
          <div className="hidden md:flex items-center space-x-4">
            {/* 검색 버튼 및 입력 필드 */}
            <div className="relative">
              {showSearchInput ? (
                <div className="flex items-center">
                  <SearchForm
                    placeholder={t("search")}
                    className="w-48"
                    compact={true}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={toggleSearch}
                    className="ml-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleSearch}
                  aria-label="검색"
                >
                  <Search className="h-4 w-4" />
                </Button>
              )}
            </div>

            <ThemeToggle />
            <LanguageSwitcher />

            {/* 로그인한 경우에만 알림 아이콘 표시 */}
            {status === "authenticated" && session?.user && (
              <NotificationIcon userId={session.user.id} />
            )}

            {/* 로그인 상태에 따른 메뉴 분기 */}
            {status === "loading" ? (
              // 로딩 중일 때 표시할 내용
              <div className="h-9 w-20 bg-muted animate-pulse rounded-md"></div>
            ) : status === "authenticated" && session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    <span className="max-w-[100px] truncate">
                      {session.user?.name || t("profile")}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/profile">{t("profile")}</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/bookmarks">{t("bookmarks")}</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings">{t("settings")}</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings/notifications">
                      {t("notificationSettings")}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="h-4 w-4 mr-2" />
                    {t("logout")}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/login">
                  <Button variant="outline" size="sm">
                    {t("login")}
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button size="sm">{t("signup")}</Button>
                </Link>
              </div>
            )}
          </div>

          {/* 모바일 메뉴 버튼 */}
          <div className="flex items-center space-x-2 md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSearch}
              aria-label="검색"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* 로그인한 경우에만 알림 아이콘 표시 */}
            {status === "authenticated" && session?.user && (
              <NotificationIcon userId={session.user.id} />
            )}

            <button
              className="p-1"
              onClick={toggleMobileMenu}
              aria-label={isMobileMenuOpen ? "메뉴 닫기" : "메뉴 열기"}
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
            >
              {isMobileMenuOpen ? (
                <X size={24} aria-hidden="true" />
              ) : (
                <Menu size={24} aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* 모바일 검색 입력 필드 */}
        {showSearchInput && (
          <div className="mt-4">
            <div className="flex items-center">
              <SearchForm placeholder={t("search")} className="flex-1" />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={toggleSearch}
                className="ml-1"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 모바일 메뉴 */}
      {isMobileMenuOpen && (
        <div
          id="mobile-menu"
          className="md:hidden bg-background border-t"
          role="navigation"
          aria-label="모바일 메뉴"
        >
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-4">
              {mainNavLinks.map((link) =>
                link.children ? (
                  <div key={link.label} className="space-y-2">
                    <div className="font-medium">{link.label}</div>
                    <div className="pl-4 space-y-2 border-l-2 border-primary/20">
                      {link.children.map((child) => (
                        <Link
                          key={child.href}
                          href={child.href}
                          className={`block text-sm transition-colors hover:text-primary ${
                            pathname === child.href
                              ? "text-primary"
                              : "text-foreground/80"
                          }`}
                          onClick={toggleMobileMenu}
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`text-sm font-medium transition-colors hover:text-primary ${
                      pathname === link.href ||
                      (link.href !== "/" && pathname.startsWith(link.href))
                        ? "text-primary"
                        : "text-foreground/80"
                    }`}
                    onClick={toggleMobileMenu}
                  >
                    {link.label}
                  </Link>
                )
              )}
            </nav>

            <div className="mt-6 pt-6 border-t flex flex-col space-y-4">
              <div className="flex justify-between items-center">
                <ThemeToggle />
                <LanguageSwitcher />
              </div>

              {status === "loading" ? (
                // 로딩 중일 때 표시할 내용
                <div className="h-20 bg-muted animate-pulse rounded-md"></div>
              ) : status === "authenticated" && session ? (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span className="font-medium">
                      {session.user?.name || t("profile")}
                    </span>
                  </div>
                  <div className="pl-6 space-y-2">
                    <Link
                      href="/profile"
                      className="block text-sm text-foreground/80 hover:text-primary"
                      onClick={toggleMobileMenu}
                    >
                      {t("profile")}
                    </Link>
                    <Link
                      href="/settings"
                      className="block text-sm text-foreground/80 hover:text-primary"
                      onClick={toggleMobileMenu}
                    >
                      {t("settings")}
                    </Link>
                    <Link
                      href="/settings/notifications"
                      className="block text-sm text-foreground/80 hover:text-primary"
                      onClick={toggleMobileMenu}
                    >
                      {t("notificationSettings")}
                    </Link>
                    <button
                      onClick={handleSignOut}
                      className="flex items-center text-sm text-foreground/80 hover:text-primary"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      {t("logout")}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col space-y-2">
                  <Link href="/login" onClick={toggleMobileMenu}>
                    <Button variant="outline" className="w-full">
                      {t("login")}
                    </Button>
                  </Link>
                  <Link href="/signup" onClick={toggleMobileMenu}>
                    <Button className="w-full">{t("signup")}</Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
