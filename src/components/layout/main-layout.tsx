"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Header from "./header";
import Footer from "./footer";
import Sidebar from "./sidebar";

type MainLayoutProps = {
  children: React.ReactNode;
};

export default function MainLayout({ children }: MainLayoutProps) {
  const pathname = usePathname();
  const [showSidebar, setShowSidebar] = useState(false);

  // 특정 경로에서만 사이드바 표시
  useEffect(() => {
    // 홈페이지, 로그인, 회원가입 페이지 등에서는 사이드바 숨김
    const pathsWithoutSidebar = ["/", "/login", "/signup", "/forgot-password"];
    setShowSidebar(!pathsWithoutSidebar.includes(pathname));
  }, [pathname]);

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <div className="flex flex-1 pt-16">
        {/* 사이드바 (조건부 렌더링) */}
        {showSidebar && <Sidebar />}

        {/* 메인 콘텐츠 */}
        <main
          id="content"
          className={`flex-1 ${showSidebar ? "md:ml-64" : ""}`}
        >
          <div className="container mx-auto px-4 py-8">{children}</div>
        </main>
      </div>

      <Footer />
    </div>
  );
}
