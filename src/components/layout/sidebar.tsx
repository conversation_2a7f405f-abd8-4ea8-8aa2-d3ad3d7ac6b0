"use client";

import { useState } from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { ChevronDown, ChevronLeft, ChevronRight, Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";

type SidebarItem = {
  href: string;
  label: string;
  icon?: React.ReactNode;
  children?: SidebarItem[];
};

export default function Sidebar() {
  const t = useTranslations("Navigation");
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {}
  );
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // 사이드바 접기/펼치기
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // 모바일에서 사이드바 열기/닫기
  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  // 하위 메뉴 접기/펼치기
  const toggleItem = (href: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [href]: !prev[href],
    }));
  };

  // 사이드바 메뉴 아이템
  const sidebarItems: SidebarItem[] = [
    {
      href: "/info",
      label: "생활 정보",
      children: [
        { href: "/info/housing", label: "주거 정보" },
        { href: "/info/transportation", label: "교통 정보" },
        { href: "/info/healthcare", label: "의료 정보" },
        { href: "/info/education", label: "교육 정보" },
      ],
    },
    {
      href: "/guide",
      label: "서비스 이용 가이드",
      children: [
        { href: "/guide/banking", label: "은행 서비스" },
        { href: "/guide/mobile", label: "통신 서비스" },
        { href: "/guide/utilities", label: "공공 요금" },
        { href: "/guide/delivery", label: "배달 서비스" },
      ],
    },
    {
      href: "/gov-info",
      label: "정부 정보",
    },
    {
      href: "/jobs",
      label: "구인구직",
      children: [
        { href: "/jobs", label: "전체 보기" },
        { href: "/jobs?type=JOB_OFFER", label: "구인 정보" },
        { href: "/jobs?type=JOB_SEEK", label: "구직 정보" },
        { href: "/jobs/create", label: "새 글 작성" },
      ],
    },
    {
      href: "/community",
      label: "커뮤니티",
      children: [
        { href: "/community/general", label: "일반 게시판" },
        { href: "/community/questions", label: "질문 게시판" },
        { href: "/community/tips", label: "팁 게시판" },
      ],
    },
  ];

  // 메뉴 아이템 렌더링 함수
  const renderMenuItem = (item: SidebarItem, depth = 0) => {
    const isActive =
      pathname === item.href || pathname.startsWith(`${item.href}/`);
    const isExpanded = expandedItems[item.href] || isActive;
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.href} className="w-full">
        <div className="flex items-center w-full">
          {/* 메인 메뉴 아이템 */}
          <Link
            href={hasChildren ? "#" : item.href}
            onClick={
              hasChildren
                ? (e) => {
                    e.preventDefault();
                    toggleItem(item.href);
                  }
                : undefined
            }
            className={`flex items-center py-2 px-3 rounded-md text-sm w-full ${
              isActive
                ? "bg-primary/10 text-primary font-medium"
                : "text-foreground/80 hover:bg-accent hover:text-accent-foreground"
            }`}
          >
            {item.icon && <span className="mr-2">{item.icon}</span>}
            {!isCollapsed && (
              <>
                <span className="flex-grow">{item.label}</span>
                {hasChildren && (
                  <span className="ml-auto">
                    {isExpanded ? (
                      <ChevronDown size={16} />
                    ) : (
                      <ChevronRight size={16} />
                    )}
                  </span>
                )}
              </>
            )}
          </Link>
        </div>

        {/* 하위 메뉴 아이템 */}
        {hasChildren && isExpanded && !isCollapsed && (
          <div className="ml-4 mt-1 space-y-1 border-l pl-2">
            {item.children.map((child) => renderMenuItem(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  // 모바일 토글 버튼
  const MobileToggle = () => (
    <Button
      variant="ghost"
      size="icon"
      className="md:hidden fixed top-4 left-4 z-50"
      onClick={toggleMobileSidebar}
      aria-label={isMobileOpen ? "Close sidebar" : "Open sidebar"}
    >
      {isMobileOpen ? <X size={20} /> : <Menu size={20} />}
    </Button>
  );

  // 사이드바 컨텐츠
  const SidebarContent = () => (
    <div className="h-full flex flex-col">
      {/* 사이드바 헤더 */}
      <div className="p-4 flex items-center justify-between border-b">
        {!isCollapsed && <h2 className="text-lg font-semibold">메뉴</h2>}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="hidden md:flex"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </Button>
      </div>

      {/* 사이드바 메뉴 */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="space-y-1 px-2">
          {sidebarItems.map((item) => renderMenuItem(item))}
        </nav>
      </div>
    </div>
  );

  return (
    <>
      <MobileToggle />

      {/* 데스크탑 사이드바 */}
      <aside
        className={`hidden md:block h-screen bg-background border-r transition-all duration-300 ${
          isCollapsed ? "w-16" : "w-64"
        } fixed top-0 left-0 z-40`}
      >
        <SidebarContent />
      </aside>

      {/* 모바일 사이드바 오버레이 */}
      {isMobileOpen && (
        <div
          className="md:hidden fixed inset-0 bg-background/80 backdrop-blur-sm z-40"
          onClick={toggleMobileSidebar}
        />
      )}

      {/* 모바일 사이드바 */}
      <aside
        className={`md:hidden fixed top-0 left-0 h-screen bg-background border-r w-64 z-50 transform transition-transform duration-300 ${
          isMobileOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <SidebarContent />
      </aside>

      {/* 사이드바 공간 확보를 위한 여백 */}
      <div
        className={`hidden md:block ${
          isCollapsed ? "w-16" : "w-64"
        } transition-all duration-300`}
      />
    </>
  );
}
