/**
 * 지연 로딩 컴포넌트 모음
 * 
 * 이 파일은 무거운 컴포넌트를 지연 로딩하기 위한 dynamic import를 정의합니다.
 * 페이지 로드 시간을 개선하기 위해 필요한 시점에 컴포넌트를 로드합니다.
 */

import dynamic from 'next/dynamic';
import { Skeleton } from '@/components/ui/skeleton';

// 에디터 컴포넌트 지연 로딩
export const TiptapEditor = dynamic(
  () => import('@/components/editor/TiptapEditor'),
  {
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    ),
    ssr: false, // 클라이언트 사이드에서만 렌더링
  }
);

// 이미지 업로더 컴포넌트 지연 로딩
export const ImageUploader = dynamic(
  () => import('@/components/editor/ImageUploader'),
  {
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    ),
    ssr: false, // 클라이언트 사이드에서만 렌더링
  }
);

// 이미지 갤러리 컴포넌트 지연 로딩
export const ImageGallery = dynamic(
  () => import('@/components/upload/ImageGallery'),
  {
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <div className="grid grid-cols-2 gap-2">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    ),
  }
);

// 댓글 컴포넌트 지연 로딩
export const CommentSection = dynamic(
  () => import('@/components/comment/CommentSection'),
  {
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    ),
  }
);

// 차트 컴포넌트 지연 로딩
export const DashboardCharts = dynamic(
  () => import('@/components/dashboard/DashboardCharts'),
  {
    loading: () => (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Skeleton className="h-60 w-full" />
        <Skeleton className="h-60 w-full" />
      </div>
    ),
  }
);

// 지도 컴포넌트 지연 로딩
export const MapComponent = dynamic(
  () => import('@/components/map/MapComponent'),
  {
    loading: () => <Skeleton className="h-[400px] w-full" />,
    ssr: false, // 클라이언트 사이드에서만 렌더링
  }
);

// 파일 업로더 컴포넌트 지연 로딩
export const FileUploader = dynamic(
  () => import('@/components/upload/FileUploader'),
  {
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    ),
    ssr: false, // 클라이언트 사이드에서만 렌더링
  }
);

// 검색 결과 컴포넌트 지연 로딩
export const SearchResults = dynamic(
  () => import('@/components/search/SearchResults'),
  {
    loading: () => (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    ),
  }
);
