'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, X, Pencil, Save, ArrowUp, ArrowDown } from 'lucide-react';
import { toast } from 'sonner';
import ImageUpload, { UploadedImage } from '@/components/upload/ImageUpload';
import { deleteImage } from '@/lib/storage';
import { addLifeInfoImage, updateLifeInfoImage, deleteLifeInfoImage } from '@/actions/lifeinfo/image';

interface ImageGalleryProps {
  lifeInfoId: string;
  images: any[];
  onImagesChange?: () => void;
}

export default function ImageGallery({
  lifeInfoId,
  images,
  onImagesChange,
}: ImageGalleryProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [editingImageId, setEditingImageId] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<{
    alt: string;
    caption: string;
  }>({
    alt: '',
    caption: '',
  });

  // 이미지 업로드 처리
  const handleImageUploaded = async (uploadedImage: UploadedImage) => {
    setIsLoading(true);

    try {
      const result = await addLifeInfoImage({
        lifeInfoId,
        url: uploadedImage.url,
        alt: uploadedImage.alt,
        caption: uploadedImage.caption,
        order: images.length, // 새 이미지는 마지막에 추가
      });

      if (!result.success) {
        toast.error(`이미지 추가 실패: ${result.error}`);
        return;
      }

      toast.success('이미지가 성공적으로 추가되었습니다.');
      onImagesChange?.();
    } catch (error) {
      console.error('이미지 추가 중 오류 발생:', error);
      toast.error('이미지 추가 중 오류가 발생했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  // 이미지 삭제 처리
  const handleDeleteImage = async (imageId: string, imageUrl: string) => {
    if (!confirm('이미지를 삭제하시겠습니까?')) {
      return;
    }

    setIsLoading(true);

    try {
      // DB에서 이미지 정보 삭제
      const result = await deleteLifeInfoImage({
        id: imageId,
      });

      if (!result.success) {
        toast.error(`이미지 삭제 실패: ${result.error}`);
        return;
      }

      // Storage에서 이미지 파일 삭제
      await deleteImage(imageUrl);

      toast.success('이미지가 성공적으로 삭제되었습니다.');
      onImagesChange?.();
    } catch (error) {
      console.error('이미지 삭제 중 오류 발생:', error);
      toast.error('이미지 삭제 중 오류가 발생했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  // 이미지 편집 시작
  const handleStartEdit = (image: any) => {
    setEditingImageId(image.id);
    setEditValues({
      alt: image.alt || '',
      caption: image.caption || '',
    });
  };

  // 이미지 편집 취소
  const handleCancelEdit = () => {
    setEditingImageId(null);
    setEditValues({ alt: '', caption: '' });
  };

  // 이미지 편집 저장
  const handleSaveEdit = async (imageId: string) => {
    setIsLoading(true);

    try {
      const result = await updateLifeInfoImage({
        id: imageId,
        alt: editValues.alt,
        caption: editValues.caption,
      });

      if (!result.success) {
        toast.error(`이미지 수정 실패: ${result.error}`);
        return;
      }

      toast.success('이미지 정보가 성공적으로 수정되었습니다.');
      setEditingImageId(null);
      onImagesChange?.();
    } catch (error) {
      console.error('이미지 수정 중 오류 발생:', error);
      toast.error('이미지 수정 중 오류가 발생했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  // 이미지 순서 변경
  const handleReorderImage = async (imageId: string, newOrder: number) => {
    if (newOrder < 0 || newOrder >= images.length) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateLifeInfoImage({
        id: imageId,
        order: newOrder,
      });

      if (!result.success) {
        toast.error(`이미지 순서 변경 실패: ${result.error}`);
        return;
      }

      toast.success('이미지 순서가 변경되었습니다.');
      onImagesChange?.();
    } catch (error) {
      console.error('이미지 순서 변경 중 오류 발생:', error);
      toast.error('이미지 순서 변경 중 오류가 발생했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium">이미지 갤러리</h3>

      {/* 이미지 목록 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {images.map((image, index) => (
          <Card key={image.id}>
            <CardContent className="p-4 space-y-4">
              <div className="relative aspect-video w-full overflow-hidden rounded-md">
                <Image
                  src={image.url}
                  alt={image.alt || '이미지'}
                  fill
                  className="object-cover"
                />
              </div>

              {editingImageId === image.id ? (
                <div className="space-y-2">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor={`alt-${image.id}`}>대체 텍스트</Label>
                    <Input
                      id={`alt-${image.id}`}
                      value={editValues.alt}
                      onChange={(e) =>
                        setEditValues({ ...editValues, alt: e.target.value })
                      }
                      placeholder="이미지 설명"
                    />
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor={`caption-${image.id}`}>캡션</Label>
                    <Textarea
                      id={`caption-${image.id}`}
                      value={editValues.caption}
                      onChange={(e) =>
                        setEditValues({ ...editValues, caption: e.target.value })
                      }
                      placeholder="이미지 캡션"
                      rows={2}
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelEdit}
                      disabled={isLoading}
                    >
                      취소
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleSaveEdit(image.id)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <Save className="mr-1 h-4 w-4" />
                          저장
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {image.alt && <p className="text-sm font-medium">{image.alt}</p>}
                  {image.caption && (
                    <p className="text-sm text-muted-foreground">{image.caption}</p>
                  )}

                  <div className="flex justify-between">
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleReorderImage(image.id, index - 1)}
                        disabled={index === 0 || isLoading}
                      >
                        <ArrowUp className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleReorderImage(image.id, index + 1)}
                        disabled={index === images.length - 1 || isLoading}
                      >
                        <ArrowDown className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleStartEdit(image)}
                        disabled={isLoading}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => handleDeleteImage(image.id, image.url)}
                        disabled={isLoading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 이미지 업로드 */}
      <div className="mt-6">
        <h4 className="text-md font-medium mb-2">새 이미지 추가</h4>
        <ImageUpload
          onImageUploaded={handleImageUploaded}
          path={`life-info/${lifeInfoId}`}
        />
      </div>
    </div>
  );
}
