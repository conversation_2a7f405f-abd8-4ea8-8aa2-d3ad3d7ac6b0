'use client';

import { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ContentRenderer from '@/components/editor/ContentRenderer';
import { incrementViewCount } from '@/actions/lifeinfo/lifeinfo';
import { formatDate } from '@/lib/utils';
import { User } from '@prisma/client';

interface LifeInfoDetailProps {
  lifeInfo: any;
  relatedLifeInfos?: any[];
  currentUser?: User | null;
}

export default function LifeInfoDetail({
  lifeInfo,
  relatedLifeInfos = [],
  currentUser,
}: LifeInfoDetailProps) {
  const router = useRouter();
  const isAdmin = currentUser?.isAdmin;

  // 조회수 증가
  useEffect(() => {
    const incrementView = async () => {
      try {
        await incrementViewCount({ id: lifeInfo.id });
      } catch (error) {
        console.error('조회수 증가 중 오류 발생:', error);
      }
    };

    incrementView();
  }, [lifeInfo.id]);

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="outline">{lifeInfo.category.name}</Badge>
          <span className="text-sm text-muted-foreground">
            조회 {lifeInfo.viewCount}
          </span>
          {lifeInfo.featured && <Badge>주요</Badge>}
        </div>
        <h1 className="text-3xl font-bold mb-4">{lifeInfo.title}</h1>
        {lifeInfo.summary && (
          <p className="text-lg text-muted-foreground mb-4">{lifeInfo.summary}</p>
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {lifeInfo.author.image && (
              <Image
                src={lifeInfo.author.image}
                alt={lifeInfo.author.name}
                width={32}
                height={32}
                className="rounded-full"
              />
            )}
            <span className="text-sm">
              {lifeInfo.author.displayName || lifeInfo.author.name}
            </span>
          </div>
          <span className="text-sm text-muted-foreground">
            {formatDate(lifeInfo.publishedAt || lifeInfo.createdAt)}
          </span>
        </div>
      </div>

      {isAdmin && (
        <div className="flex gap-2 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/admin/life-info/edit/${lifeInfo.id}`)}
          >
            수정
          </Button>
        </div>
      )}

      {lifeInfo.images && lifeInfo.images.length > 0 && (
        <div className="mb-6">
          <div className="relative w-full h-[400px] overflow-hidden rounded-lg">
            <Image
              src={lifeInfo.images[0].url}
              alt={lifeInfo.images[0].alt || lifeInfo.title}
              fill
              className="object-cover"
            />
          </div>
          {lifeInfo.images[0].caption && (
            <p className="text-sm text-muted-foreground text-center mt-2">
              {lifeInfo.images[0].caption}
            </p>
          )}
        </div>
      )}

      <Tabs defaultValue="content" className="mb-8">
        <TabsList>
          <TabsTrigger value="content">내용</TabsTrigger>
          {lifeInfo.images && lifeInfo.images.length > 1 && (
            <TabsTrigger value="gallery">갤러리</TabsTrigger>
          )}
        </TabsList>
        <TabsContent value="content" className="mt-4">
          <ContentRenderer content={lifeInfo.contentHtml} />
        </TabsContent>
        {lifeInfo.images && lifeInfo.images.length > 1 && (
          <TabsContent value="gallery" className="mt-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {lifeInfo.images.map((image: any) => (
                <div key={image.id} className="space-y-2">
                  <div className="relative aspect-square overflow-hidden rounded-md">
                    <Image
                      src={image.url}
                      alt={image.alt || lifeInfo.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  {image.caption && (
                    <p className="text-sm text-muted-foreground">{image.caption}</p>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>
        )}
      </Tabs>

      {relatedLifeInfos.length > 0 && (
        <div className="mt-12">
          <Separator className="mb-6" />
          <h2 className="text-xl font-bold mb-4">관련 정보</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {relatedLifeInfos.map((related) => (
              <Link href={`/life-info/${related.slug}`} key={related.id}>
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <h3 className="font-medium line-clamp-2 mb-2">{related.title}</h3>
                    {related.summary && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {related.summary}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
