'use client';

import { useState, useEffect, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import TiptapEditor from '@/components/editor/TiptapEditor';
import ContentRenderer from '@/components/editor/ContentRenderer';
import { createLifeInfo, updateLifeInfo } from '@/actions/lifeinfo/lifeinfo';
import { toast } from 'sonner';
import { Category } from '@prisma/client';
import { slugify } from '@/lib/utils';

// 폼 스키마 정의
const formSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, '제목은 필수입니다.').max(255, '제목은 최대 255자까지 가능합니다.'),
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.'),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: z.string().min(1, '내용은 필수입니다.'),
  contentHtml: z.string().optional(),
  categoryId: z.string().min(1, '카테고리는 필수입니다.'),
  status: z.enum(['draft', 'published', 'archived']),
  featured: z.boolean().default(false),
});

type FormValues = z.infer<typeof formSchema>;

interface LifeInfoFormProps {
  categories: Category[];
  initialData?: any;
  isEdit?: boolean;
}

export default function LifeInfoForm({
  categories,
  initialData,
  isEdit = false,
}: LifeInfoFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [previewContent, setPreviewContent] = useState('');

  // 폼 초기화
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData
      ? {
          ...initialData,
          content: initialData.content || '',
          contentHtml: initialData.contentHtml || '',
        }
      : {
          title: '',
          slug: '',
          summary: '',
          content: '',
          contentHtml: '',
          categoryId: '',
          status: 'draft',
          featured: false,
        },
  });

  // 제목이 변경될 때 슬러그 자동 생성
  useEffect(() => {
    const title = form.watch('title');
    if (title && !isEdit && !form.getValues('slug')) {
      form.setValue('slug', slugify(title));
    }
  }, [form.watch('title'), form, isEdit]);

  // 폼 제출 처리
  const onSubmit = (values: FormValues) => {
    startTransition(async () => {
      try {
        if (isEdit && initialData?.id) {
          // 수정 모드
          const result = await updateLifeInfo({
            ...values,
            id: initialData.id,
          });

          if (result.success) {
            toast.success('생활 정보가 성공적으로 수정되었습니다.');
            router.push(`/admin/life-info/${result.data.id}`);
            router.refresh();
          } else {
            toast.error(`생활 정보 수정 실패: ${result.error}`);
          }
        } else {
          // 생성 모드
          const result = await createLifeInfo(values);

          if (result.success) {
            toast.success('생활 정보가 성공적으로 생성되었습니다.');
            router.push(`/admin/life-info/${result.data.id}`);
            router.refresh();
          } else {
            toast.error(`생활 정보 생성 실패: ${result.error}`);
          }
        }
      } catch (error) {
        console.error('생활 정보 저장 중 오류 발생:', error);
        toast.error('생활 정보 저장 중 오류가 발생했습니다.');
      }
    });
  };

  // 에디터 내용 변경 처리
  const handleEditorChange = (html: string) => {
    form.setValue('contentHtml', html);
    setPreviewContent(html);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="editor">에디터</TabsTrigger>
                <TabsTrigger value="preview">미리보기</TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>제목</FormLabel>
                      <FormControl>
                        <Input placeholder="제목을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>슬러그</FormLabel>
                      <FormControl>
                        <Input placeholder="URL에 사용될 슬러그" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="summary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>요약</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="내용 요약 (선택사항)"
                          className="resize-none"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contentHtml"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>내용</FormLabel>
                      <FormControl>
                        <TiptapEditor
                          content={form.getValues('contentHtml') || ''}
                          onChange={handleEditorChange}
                          placeholder="내용을 입력하세요..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="preview">
                <Card>
                  <CardContent className="pt-6">
                    <h1 className="text-2xl font-bold mb-2">{form.watch('title')}</h1>
                    {form.watch('summary') && (
                      <p className="text-muted-foreground mb-4">{form.watch('summary')}</p>
                    )}
                    <Separator className="my-4" />
                    <ContentRenderer content={previewContent || form.watch('contentHtml') || ''} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>카테고리</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="카테고리 선택" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>상태</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="상태 선택" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">초안</SelectItem>
                            <SelectItem value="published">발행됨</SelectItem>
                            <SelectItem value="archived">보관됨</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="featured"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>주요 콘텐츠로 표시</FormLabel>
                          <p className="text-sm text-muted-foreground">
                            이 콘텐츠를 메인 페이지나 주요 섹션에 표시합니다.
                          </p>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isPending}
              >
                취소
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? '저장 중...' : isEdit ? '수정하기' : '저장하기'}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
}
