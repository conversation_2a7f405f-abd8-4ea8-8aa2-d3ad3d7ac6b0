'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Category } from '@prisma/client';
import { formatDate } from '@/lib/utils';

interface LifeInfoListProps {
  lifeInfos: any[];
  categories: Category[];
  totalPages: number;
  currentPage: number;
}

export default function LifeInfoList({
  lifeInfos,
  categories,
  totalPages,
  currentPage,
}: LifeInfoListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedCategory, setSelectedCategory] = useState<string>(
    searchParams.get('categoryId') || 'all'
  );

  // 카테고리 변경 처리
  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    
    const params = new URLSearchParams(searchParams.toString());
    
    if (categoryId === 'all') {
      params.delete('categoryId');
    } else {
      params.set('categoryId', categoryId);
    }
    
    params.set('page', '1'); // 카테고리 변경 시 첫 페이지로 이동
    router.push(`?${params.toString()}`);
  };

  // 페이지 변경 처리
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    router.push(`?${params.toString()}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-2 mb-4">
        <Button
          variant={selectedCategory === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleCategoryChange('all')}
        >
          전체
        </Button>
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleCategoryChange(category.id)}
          >
            {category.name}
          </Button>
        ))}
      </div>

      {lifeInfos.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">등록된 생활 정보가 없습니다.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {lifeInfos.map((lifeInfo) => (
            <Link href={`/life-info/${lifeInfo.slug}`} key={lifeInfo.id}>
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader className="p-0">
                  {lifeInfo.images && lifeInfo.images.length > 0 ? (
                    <div className="relative w-full h-48 overflow-hidden">
                      <Image
                        src={lifeInfo.images[0].url}
                        alt={lifeInfo.images[0].alt || lifeInfo.title}
                        fill
                        className="object-cover"
                      />
                      {lifeInfo.featured && (
                        <Badge className="absolute top-2 right-2">주요</Badge>
                      )}
                    </div>
                  ) : (
                    <div className="relative w-full h-48 bg-muted flex items-center justify-center">
                      <p className="text-muted-foreground">이미지 없음</p>
                      {lifeInfo.featured && (
                        <Badge className="absolute top-2 right-2">주요</Badge>
                      )}
                    </div>
                  )}
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline">{lifeInfo.category.name}</Badge>
                    <span className="text-xs text-muted-foreground">
                      조회 {lifeInfo.viewCount}
                    </span>
                  </div>
                  <CardTitle className="line-clamp-2 mb-2">{lifeInfo.title}</CardTitle>
                  {lifeInfo.summary && (
                    <p className="text-muted-foreground text-sm line-clamp-3">
                      {lifeInfo.summary}
                    </p>
                  )}
                </CardContent>
                <CardFooter className="text-xs text-muted-foreground">
                  {formatDate(lifeInfo.publishedAt || lifeInfo.createdAt)}
                </CardFooter>
              </Card>
            </Link>
          ))}
        </div>
      )}

      {totalPages > 1 && (
        <Pagination className="mt-8">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage > 1) {
                    handlePageChange(currentPage - 1);
                  }
                }}
                className={currentPage <= 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <PaginationItem key={page}>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(page);
                  }}
                  isActive={page === currentPage}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}
            
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage < totalPages) {
                    handlePageChange(currentPage + 1);
                  }
                }}
                className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
