'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface SearchFormProps {
  placeholder?: string;
  className?: string;
}

export default function SearchForm({
  placeholder = '검색어를 입력하세요...',
  className = '',
}: SearchFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState(searchParams.get('query') || '');

  // URL 쿼리 파라미터가 변경되면 검색어 상태 업데이트
  useEffect(() => {
    setQuery(searchParams.get('query') || '');
  }, [searchParams]);

  // 검색 처리
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      return;
    }

    const params = new URLSearchParams(searchParams.toString());
    params.set('query', query);
    params.set('page', '1'); // 검색 시 첫 페이지로 이동
    
    router.push(`?${params.toString()}`);
  };

  // 검색어 초기화
  const handleClear = () => {
    setQuery('');
    
    const params = new URLSearchParams(searchParams.toString());
    params.delete('query');
    params.set('page', '1');
    
    router.push(`?${params.toString()}`);
  };

  return (
    <form onSubmit={handleSearch} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder={placeholder}
          className="pl-8 pr-20"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
        {query && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-14 top-0 h-full px-2"
            onClick={handleClear}
          >
            초기화
          </Button>
        )}
        <Button
          type="submit"
          size="sm"
          className="absolute right-0 top-0 h-full rounded-l-none"
        >
          검색
        </Button>
      </div>
    </form>
  );
}
