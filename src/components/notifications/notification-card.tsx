'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { ko, enUS } from 'date-fns/locale';
import { useLocale } from 'next-intl';
import { 
  MessageSquare, 
  Heart, 
  Reply, 
  Bell, 
  AtSign, 
  AlertCircle,
  Trash2
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { NotificationWithSender } from '@/types/notification';
import { markNotificationAsRead, deleteNotification } from '@/actions/notification';
import { cn } from '@/lib/utils';

interface NotificationCardProps {
  notification: NotificationWithSender;
  onRead?: () => void;
}

export function NotificationCard({ notification, onRead }: NotificationCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  const locale = useLocale();

  // 알림 유형에 따른 아이콘 선택
  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'COMMENT':
        return <MessageSquare className="h-5 w-5 text-blue-500" />;
      case 'LIKE':
        return <Heart className="h-5 w-5 text-red-500" />;
      case 'REPLY':
        return <Reply className="h-5 w-5 text-green-500" />;
      case 'ANNOUNCEMENT':
        return <Bell className="h-5 w-5 text-yellow-500" />;
      case 'MENTION':
        return <AtSign className="h-5 w-5 text-purple-500" />;
      case 'SYSTEM':
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  // 알림 클릭 처리
  const handleClick = async () => {
    if (!notification.isRead) {
      setIsLoading(true);
      try {
        await markNotificationAsRead(notification.id);
        if (onRead) {
          onRead();
        }
      } catch (error) {
        console.error('Error marking notification as read:', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    // 링크가 있으면 해당 페이지로 이동
    if (notification.link) {
      router.push(notification.link);
    }
  };

  // 알림 삭제 처리
  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleting(true);
    try {
      await deleteNotification(notification.id);
      if (onRead) {
        onRead();
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // 날짜 포맷팅
  const formattedDate = formatDistanceToNow(new Date(notification.createdAt), { 
    addSuffix: true,
    locale: locale === 'ko' ? ko : enUS
  });

  return (
    <Card 
      className={cn(
        "cursor-pointer transition-colors hover:bg-muted/50",
        !notification.isRead && "bg-muted/30 border-primary/20",
        (isLoading || isDeleting) && "opacity-70 pointer-events-none"
      )}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            {notification.sender ? (
              <Avatar className="h-10 w-10">
                <AvatarImage src={notification.sender.image || undefined} alt={notification.sender.name} />
                <AvatarFallback>
                  {notification.sender.displayName?.[0] || notification.sender.name[0]}
                </AvatarFallback>
              </Avatar>
            ) : (
              <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                {getNotificationIcon()}
              </div>
            )}
          </div>
          <div className="flex-1 space-y-1">
            {notification.title && (
              <h3 className="font-medium">{notification.title}</h3>
            )}
            <p className="text-sm">{notification.content}</p>
            <p className="text-xs text-muted-foreground">{formattedDate}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="px-4 py-2 flex justify-end border-t">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleDelete}
          className="h-8 px-2 text-destructive hover:text-destructive hover:bg-destructive/10"
        >
          <Trash2 className="h-4 w-4 mr-1" />
          <span className="text-xs">삭제</span>
        </Button>
      </CardFooter>
    </Card>
  );
}
