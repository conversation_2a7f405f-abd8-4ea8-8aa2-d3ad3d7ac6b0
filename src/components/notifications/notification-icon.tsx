'use client';

import { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { getNotificationsByUserId, markAllNotificationsAsRead } from '@/actions/notification';
import { NotificationWithSender } from '@/types/notification';
import { NotificationItem } from './notification-item';
import { useTranslations } from 'next-intl';

interface NotificationIconProps {
  userId: string;
}

export function NotificationIcon({ userId }: NotificationIconProps) {
  const [notifications, setNotifications] = useState<NotificationWithSender[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const t = useTranslations('Notifications');

  // 알림 데이터 가져오기
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const result = await getNotificationsByUserId(userId, 5, 0, false);
      if (result.success) {
        setNotifications(result.notifications as NotificationWithSender[]);
        setUnreadCount(result.count);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // 모든 알림 읽음 표시
  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllNotificationsAsRead();
      if (result.success) {
        setNotifications(prev => prev.map(notification => ({
          ...notification,
          isRead: true
        })));
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // 알림 페이지로 이동
  const handleViewAllNotifications = () => {
    router.push('/notifications');
  };

  // 컴포넌트 마운트 시 알림 데이터 가져오기
  useEffect(() => {
    if (userId) {
      fetchNotifications();
    }
    
    // 30초마다 알림 데이터 갱신
    const interval = setInterval(() => {
      if (userId) {
        fetchNotifications();
      }
    }, 30000);
    
    return () => clearInterval(interval);
  }, [userId]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>{t('notifications')}</span>
          {unreadCount > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleMarkAllAsRead}
              className="text-xs h-7"
            >
              {t('markAllAsRead')}
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {loading ? (
          <div className="py-2 px-4 text-center text-sm text-muted-foreground">
            {t('loading')}...
          </div>
        ) : notifications.length === 0 ? (
          <div className="py-2 px-4 text-center text-sm text-muted-foreground">
            {t('noNotifications')}
          </div>
        ) : (
          <>
            {notifications.map((notification) => (
              <NotificationItem 
                key={notification.id} 
                notification={notification} 
                onRead={fetchNotifications}
              />
            ))}
          </>
        )}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="cursor-pointer text-center justify-center font-medium"
          onClick={handleViewAllNotifications}
        >
          {t('viewAll')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
