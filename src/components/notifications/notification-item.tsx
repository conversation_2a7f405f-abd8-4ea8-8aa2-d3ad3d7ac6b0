'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { ko, enUS } from 'date-fns/locale';
import { useLocale } from 'next-intl';
import { 
  MessageSquare, 
  Heart, 
  Reply, 
  Bell, 
  AtSign, 
  AlertCircle,
  Check
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { NotificationWithSender } from '@/types/notification';
import { markNotificationAsRead } from '@/actions/notification';
import { cn } from '@/lib/utils';

interface NotificationItemProps {
  notification: NotificationWithSender;
  onRead?: () => void;
}

export function NotificationItem({ notification, onRead }: NotificationItemProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const locale = useLocale();

  // 알림 유형에 따른 아이콘 선택
  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'COMMENT':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'LIKE':
        return <Heart className="h-4 w-4 text-red-500" />;
      case 'REPLY':
        return <Reply className="h-4 w-4 text-green-500" />;
      case 'ANNOUNCEMENT':
        return <Bell className="h-4 w-4 text-yellow-500" />;
      case 'MENTION':
        return <AtSign className="h-4 w-4 text-purple-500" />;
      case 'SYSTEM':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  // 알림 클릭 처리
  const handleClick = async () => {
    if (!notification.isRead) {
      setIsLoading(true);
      try {
        await markNotificationAsRead(notification.id);
        if (onRead) {
          onRead();
        }
      } catch (error) {
        console.error('Error marking notification as read:', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    // 링크가 있으면 해당 페이지로 이동
    if (notification.link) {
      router.push(notification.link);
    }
  };

  // 날짜 포맷팅
  const formattedDate = formatDistanceToNow(new Date(notification.createdAt), { 
    addSuffix: true,
    locale: locale === 'ko' ? ko : enUS
  });

  return (
    <DropdownMenuItem
      className={cn(
        "flex items-start gap-2 p-3 cursor-pointer",
        !notification.isRead && "bg-muted/50",
        isLoading && "opacity-70 pointer-events-none"
      )}
      onClick={handleClick}
    >
      <div className="flex-shrink-0 mt-1">
        {notification.sender ? (
          <Avatar className="h-8 w-8">
            <AvatarImage src={notification.sender.image || undefined} alt={notification.sender.name} />
            <AvatarFallback>
              {notification.sender.displayName?.[0] || notification.sender.name[0]}
            </AvatarFallback>
          </Avatar>
        ) : (
          <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
            {getNotificationIcon()}
          </div>
        )}
      </div>
      <div className="flex-1 space-y-1">
        <p className="text-sm leading-tight">
          {notification.content}
        </p>
        <p className="text-xs text-muted-foreground">
          {formattedDate}
        </p>
      </div>
      {notification.isRead ? (
        <Check className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-1" />
      ) : null}
    </DropdownMenuItem>
  );
}
