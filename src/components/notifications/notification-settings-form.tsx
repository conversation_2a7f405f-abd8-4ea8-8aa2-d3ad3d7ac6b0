'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { getUserNotificationSettings, updateNotificationSettings } from '@/actions/notification-settings';
import { NotificationSettings, UpdateNotificationSettingsParams } from '@/types/notification';

interface NotificationSettingsFormProps {
  userId: string;
}

export function NotificationSettingsForm({ userId }: NotificationSettingsFormProps) {
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const t = useTranslations('Settings');

  // 알림 설정 가져오기
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const result = await getUserNotificationSettings();
      if (result.success && result.settings) {
        setSettings(result.settings as NotificationSettings);
      }
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      toast({
        title: t('error'),
        description: t('errorFetchingSettings'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // 설정 변경 처리
  const handleToggle = (key: keyof UpdateNotificationSettingsParams) => {
    if (!settings) return;
    
    setSettings({
      ...settings,
      [key]: !settings[key],
    } as NotificationSettings);
  };

  // 설정 저장
  const handleSave = async () => {
    if (!settings) return;
    
    setSaving(true);
    try {
      const updateData: UpdateNotificationSettingsParams = {
        enableCommentNotifications: settings.enableCommentNotifications,
        enableReplyNotifications: settings.enableReplyNotifications,
        enableLikeNotifications: settings.enableLikeNotifications,
        enableAnnouncementNotifications: settings.enableAnnouncementNotifications,
        enableMentionNotifications: settings.enableMentionNotifications,
        enableSystemNotifications: settings.enableSystemNotifications,
        enableEmailNotifications: settings.enableEmailNotifications,
      };
      
      const result = await updateNotificationSettings(updateData);
      
      if (result.success) {
        toast({
          title: t('success'),
          description: t('settingsSaved'),
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast({
        title: t('error'),
        description: t('errorSavingSettings'),
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // 컴포넌트 마운트 시 설정 가져오기
  useEffect(() => {
    if (userId) {
      fetchSettings();
    }
  }, [userId]);

  if (loading) {
    return <SettingsSkeleton />;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('notificationPreferences')}</CardTitle>
        <CardDescription>
          {t('chooseNotificationsToReceive')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{t('appNotifications')}</h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="comments" className="flex-1">
                <div className="font-medium">{t('commentNotifications')}</div>
                <div className="text-sm text-muted-foreground">{t('commentNotificationsDescription')}</div>
              </Label>
              <Switch 
                id="comments" 
                checked={settings?.enableCommentNotifications} 
                onCheckedChange={() => handleToggle('enableCommentNotifications')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="replies" className="flex-1">
                <div className="font-medium">{t('replyNotifications')}</div>
                <div className="text-sm text-muted-foreground">{t('replyNotificationsDescription')}</div>
              </Label>
              <Switch 
                id="replies" 
                checked={settings?.enableReplyNotifications} 
                onCheckedChange={() => handleToggle('enableReplyNotifications')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="likes" className="flex-1">
                <div className="font-medium">{t('likeNotifications')}</div>
                <div className="text-sm text-muted-foreground">{t('likeNotificationsDescription')}</div>
              </Label>
              <Switch 
                id="likes" 
                checked={settings?.enableLikeNotifications} 
                onCheckedChange={() => handleToggle('enableLikeNotifications')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="mentions" className="flex-1">
                <div className="font-medium">{t('mentionNotifications')}</div>
                <div className="text-sm text-muted-foreground">{t('mentionNotificationsDescription')}</div>
              </Label>
              <Switch 
                id="mentions" 
                checked={settings?.enableMentionNotifications} 
                onCheckedChange={() => handleToggle('enableMentionNotifications')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="announcements" className="flex-1">
                <div className="font-medium">{t('announcementNotifications')}</div>
                <div className="text-sm text-muted-foreground">{t('announcementNotificationsDescription')}</div>
              </Label>
              <Switch 
                id="announcements" 
                checked={settings?.enableAnnouncementNotifications} 
                onCheckedChange={() => handleToggle('enableAnnouncementNotifications')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="system" className="flex-1">
                <div className="font-medium">{t('systemNotifications')}</div>
                <div className="text-sm text-muted-foreground">{t('systemNotificationsDescription')}</div>
              </Label>
              <Switch 
                id="system" 
                checked={settings?.enableSystemNotifications} 
                onCheckedChange={() => handleToggle('enableSystemNotifications')}
              />
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{t('emailNotifications')}</h3>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="email" className="flex-1">
              <div className="font-medium">{t('emailDigest')}</div>
              <div className="text-sm text-muted-foreground">{t('emailDigestDescription')}</div>
            </Label>
            <Switch 
              id="email" 
              checked={settings?.enableEmailNotifications} 
              onCheckedChange={() => handleToggle('enableEmailNotifications')}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSave} disabled={saving}>
          {saving ? t('saving') : t('saveChanges')}
        </Button>
      </CardFooter>
    </Card>
  );
}

function SettingsSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-8 w-64 mb-2" />
        <Skeleton className="h-4 w-full" />
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-6 w-40" />
          
          <div className="space-y-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-5 w-40" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <Skeleton className="h-6 w-12 rounded-full" />
              </div>
            ))}
          </div>
        </div>
        
        <div className="space-y-4">
          <Skeleton className="h-6 w-40" />
          
          <div className="flex items-center justify-between">
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-4 w-full" />
            </div>
            <Skeleton className="h-6 w-12 rounded-full" />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Skeleton className="h-10 w-32" />
      </CardFooter>
    </Card>
  );
}
