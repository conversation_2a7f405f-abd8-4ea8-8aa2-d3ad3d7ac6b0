'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { getNotificationsByUserId, markAllNotificationsAsRead } from '@/actions/notification';
import { NotificationWithSender } from '@/types/notification';
import { NotificationCard } from './notification-card';

interface NotificationsListProps {
  userId: string;
}

export function NotificationsList({ userId }: NotificationsListProps) {
  const [notifications, setNotifications] = useState<NotificationWithSender[]>([]);
  const [readNotifications, setReadNotifications] = useState<NotificationWithSender[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('unread');
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const t = useTranslations('Notifications');
  const limit = 10;

  // 알림 데이터 가져오기
  const fetchNotifications = async (tabKey: string, pageNum = 0, replace = true) => {
    setLoading(true);
    try {
      const includeRead = tabKey === 'read';
      const result = await getNotificationsByUserId(userId, limit, pageNum * limit, includeRead);
      
      if (result.success) {
        if (tabKey === 'unread') {
          if (replace) {
            setNotifications(result.notifications as NotificationWithSender[]);
          } else {
            setNotifications(prev => [...prev, ...(result.notifications as NotificationWithSender[])]);
          }
        } else {
          if (replace) {
            setReadNotifications(result.notifications as NotificationWithSender[]);
          } else {
            setReadNotifications(prev => [...prev, ...(result.notifications as NotificationWithSender[])]);
          }
        }
        setHasMore(result.hasMore);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // 모든 알림 읽음 표시
  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllNotificationsAsRead();
      if (result.success) {
        // 읽은 알림 목록으로 이동
        setActiveTab('read');
        // 데이터 다시 가져오기
        fetchNotifications('unread');
        fetchNotifications('read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // 더 보기 버튼 클릭 처리
  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchNotifications(activeTab, nextPage, false);
  };

  // 탭 변경 처리
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setPage(0);
    fetchNotifications(value);
  };

  // 알림 읽음 처리 후 목록 갱신
  const handleNotificationRead = () => {
    fetchNotifications('unread');
    fetchNotifications('read');
  };

  // 컴포넌트 마운트 시 알림 데이터 가져오기
  useEffect(() => {
    if (userId) {
      fetchNotifications('unread');
      fetchNotifications('read');
    }
  }, [userId]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Tabs defaultValue="unread" className="w-full" onValueChange={handleTabChange}>
          <TabsList>
            <TabsTrigger value="unread">{t('unread')}</TabsTrigger>
            <TabsTrigger value="read">{t('read')}</TabsTrigger>
          </TabsList>
          
          <div className="mt-4">
            {activeTab === 'unread' && notifications.length > 0 && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleMarkAllAsRead}
                className="mb-4"
              >
                {t('markAllAsRead')}
              </Button>
            )}
            
            <TabsContent value="unread" className="space-y-4">
              {loading && page === 0 ? (
                Array.from({ length: 3 }).map((_, i) => (
                  <NotificationSkeleton key={i} />
                ))
              ) : notifications.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {t('noUnreadNotifications')}
                </div>
              ) : (
                <>
                  {notifications.map((notification) => (
                    <NotificationCard 
                      key={notification.id} 
                      notification={notification}
                      onRead={handleNotificationRead}
                    />
                  ))}
                </>
              )}
            </TabsContent>
            
            <TabsContent value="read" className="space-y-4">
              {loading && page === 0 ? (
                Array.from({ length: 3 }).map((_, i) => (
                  <NotificationSkeleton key={i} />
                ))
              ) : readNotifications.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {t('noReadNotifications')}
                </div>
              ) : (
                <>
                  {readNotifications.map((notification) => (
                    <NotificationCard 
                      key={notification.id} 
                      notification={notification}
                      onRead={handleNotificationRead}
                    />
                  ))}
                </>
              )}
            </TabsContent>
          </div>
        </Tabs>
      </div>
      
      {hasMore && (
        <div className="flex justify-center mt-4">
          <Button 
            variant="outline" 
            onClick={handleLoadMore}
            disabled={loading}
          >
            {loading ? t('loading') : t('loadMore')}
          </Button>
        </div>
      )}
    </div>
  );
}

function NotificationSkeleton() {
  return (
    <div className="flex items-start gap-4 p-4 border rounded-lg">
      <Skeleton className="h-10 w-10 rounded-full" />
      <div className="space-y-2 flex-1">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/4" />
      </div>
    </div>
  );
}
