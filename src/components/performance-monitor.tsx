'use client';

import { useEffect } from 'react';
import { detectLayoutShifts } from '@/lib/monitoring';

/**
 * 성능 모니터링 컴포넌트
 * 
 * 이 컴포넌트는 페이지 성능을 모니터링하고 최적화하기 위한 기능을 제공합니다.
 * - 레이아웃 이동(CLS) 감지 및 보고
 * - 이미지 로딩 최적화
 * - 스크립트 로딩 최적화
 */
export function PerformanceMonitor() {
  useEffect(() => {
    // 레이아웃 이동 감지 시작
    detectLayoutShifts();
    
    // 이미지 지연 로딩 최적화
    if ('loading' in HTMLImageElement.prototype) {
      const images = document.querySelectorAll('img[loading="lazy"]');
      images.forEach(img => {
        // 뷰포트에 가까운 이미지는 즉시 로딩으로 전환
        if (isNearViewport(img)) {
          img.loading = 'eager';
        }
      });
    }
    
    // 스크립트 로딩 최적화
    optimizeScriptLoading();
    
    // 폰트 로딩 최적화
    optimizeFontLoading();
    
    return () => {
      // 정리 작업 (필요한 경우)
    };
  }, []);
  
  return null; // 이 컴포넌트는 UI를 렌더링하지 않음
}

/**
 * 요소가 뷰포트에 가까운지 확인
 * @param element 확인할 요소
 * @returns 뷰포트에 가까운지 여부
 */
function isNearViewport(element: Element): boolean {
  if (!element || typeof window === 'undefined') return false;
  
  const rect = element.getBoundingClientRect();
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
  
  // 뷰포트 아래 500px 이내에 있는지 확인
  return rect.top < viewportHeight + 500;
}

/**
 * 스크립트 로딩 최적화
 */
function optimizeScriptLoading(): void {
  if (typeof window === 'undefined') return;
  
  // 중요하지 않은 스크립트에 defer 속성 추가
  const scripts = document.querySelectorAll('script:not([async]):not([defer])');
  scripts.forEach(script => {
    if (!script.src || script.src.includes('critical')) return;
    
    const newScript = document.createElement('script');
    newScript.src = script.src;
    newScript.defer = true;
    
    script.parentNode?.replaceChild(newScript, script);
  });
}

/**
 * 폰트 로딩 최적화
 */
function optimizeFontLoading(): void {
  if (typeof window === 'undefined' || !('fonts' in document)) return;
  
  // 폰트 로딩 상태 확인
  document.fonts.ready.then(() => {
    // 폰트 로딩 완료 후 클래스 추가
    document.documentElement.classList.add('fonts-loaded');
  });
}
