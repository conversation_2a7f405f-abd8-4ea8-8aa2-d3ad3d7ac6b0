'use client';

import { useState } from 'react';
import { useAction } from '@/hooks';
import { createPost } from '@/actions/content';

export default function PostForm() {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [categoryId, setCategoryId] = useState('');

  const {
    execute: submitPost,
    isPending,
    error,
    fieldErrors,
    isSuccess,
    reset,
  } = useAction(createPost, {
    onSuccess: () => {
      // 폼 초기화
      setTitle('');
      setContent('');
      setCategoryId('');
      
      // 성공 메시지 표시 (예: 토스트 메시지)
      alert('게시글이 성공적으로 작성되었습니다.');
    },
    onError: (error) => {
      // 에러 메시지 표시 (예: 토스트 메시지)
      console.error('게시글 작성 중 오류 발생:', error);
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    await submitPost({
      title,
      content,
      categoryId,
      isPublished: true,
    });
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">새 게시글 작성</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700">
            제목
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            disabled={isPending}
          />
          {fieldErrors?.title && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors.title}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="content" className="block text-sm font-medium text-gray-700">
            내용
          </label>
          <textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={6}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            disabled={isPending}
          />
          {fieldErrors?.content && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors.content}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700">
            카테고리
          </label>
          <select
            id="categoryId"
            value={categoryId}
            onChange={(e) => setCategoryId(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            disabled={isPending}
          >
            <option value="">카테고리 선택</option>
            {/* 실제로는 카테고리 목록을 API로 가져와서 표시 */}
            <option value="category-1">카테고리 1</option>
            <option value="category-2">카테고리 2</option>
            <option value="category-3">카테고리 3</option>
          </select>
          {fieldErrors?.categoryId && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors.categoryId}</p>
          )}
        </div>
        
        {error && (
          <div className="p-3 rounded bg-red-100 text-red-700">
            {error}
          </div>
        )}
        
        {isSuccess && (
          <div className="p-3 rounded bg-green-100 text-green-700">
            게시글이 성공적으로 작성되었습니다.
          </div>
        )}
        
        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={reset}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={isPending}
          >
            초기화
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={isPending}
          >
            {isPending ? '처리 중...' : '작성하기'}
          </button>
        </div>
      </form>
    </div>
  );
}
