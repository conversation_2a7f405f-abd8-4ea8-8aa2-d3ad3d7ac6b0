'use client';

import { useState } from 'react';
import { useOptimisticAction } from '@/hooks';
import { toggleLike } from '@/actions/interaction';

type PostLikeButtonProps = {
  postId: string;
  initialLikeCount: number;
  initialIsLiked: boolean;
};

export default function PostLikeButton({
  postId,
  initialLikeCount,
  initialIsLiked,
}: PostLikeButtonProps) {
  // 초기 상태
  const initialData = {
    count: initialLikeCount,
    isLiked: initialIsLiked,
  };

  // 낙관적 업데이트를 위한 훅 사용
  const {
    execute: handleToggleLike,
    data,
    isPending,
    error,
  } = useOptimisticAction(
    toggleLike,
    initialData,
    {
      // 낙관적 업데이트 로직
      getOptimisticData: (currentData) => ({
        count: currentData.isLiked
          ? currentData.count - 1
          : currentData.count + 1,
        isLiked: !currentData.isLiked,
      }),
      onError: (error) => {
        console.error('좋아요 토글 중 오류 발생:', error);
      },
    }
  );

  // 좋아요 토글 핸들러
  const onLikeClick = async () => {
    try {
      await handleToggleLike({ postId });
    } catch (error) {
      console.error('좋아요 토글 중 오류 발생:', error);
    }
  };

  return (
    <button
      onClick={onLikeClick}
      disabled={isPending}
      className={`flex items-center space-x-1 px-3 py-1 rounded-full ${
        data.isLiked
          ? 'bg-red-100 text-red-600'
          : 'bg-gray-100 text-gray-600'
      } hover:bg-opacity-80 transition-colors`}
      aria-label={data.isLiked ? '좋아요 취소' : '좋아요'}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className={`h-5 w-5 ${
          data.isLiked ? 'fill-red-600' : 'fill-none stroke-current'
        }`}
        viewBox="0 0 24 24"
        strokeWidth={data.isLiked ? 0 : 1.5}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z"
        />
      </svg>
      <span>{data.count}</span>
      {error && (
        <span className="text-xs text-red-500 ml-2">오류 발생</span>
      )}
    </button>
  );
}
