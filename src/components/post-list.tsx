'use client';

import { useState, useEffect } from 'react';
import { useSWRAction } from '@/hooks';
import { getOptimizedPosts } from '@/actions/content';

type Post = {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  author: {
    id: string;
    name: string;
    displayName: string;
    image: string;
  };
  category: {
    id: string;
    name: string;
  };
  _count: {
    likes: number;
    comments: number;
  };
};

type PaginationData = {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasMore: boolean;
};

type PostListProps = {
  categoryId?: string;
  userId?: string;
  initialPage?: number;
  initialLimit?: number;
};

export default function PostList({
  categoryId,
  userId,
  initialPage = 1,
  initialLimit = 10,
}: PostListProps) {
  const [page, setPage] = useState(initialPage);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // 검색어 디바운싱
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // SWR과 Server Action 통합 훅 사용
  const {
    data,
    error,
    isLoading,
    isValidating,
    executeAction,
  } = useSWRAction(
    `posts-${categoryId || 'all'}-${userId || 'all'}-${page}-${initialLimit}-${debouncedQuery}`,
    getOptimizedPosts,
    {
      page,
      limit: initialLimit,
      categoryId,
      userId,
      query: debouncedQuery,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    },
    {
      revalidateOnFocus: false,
      refreshInterval: 0,
      onError: (error) => {
        console.error('게시글 목록 로딩 중 오류 발생:', error);
      },
    }
  );

  // 검색 핸들러
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1); // 검색 시 첫 페이지로 이동
  };

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // 데이터 추출
  const posts: Post[] = data?.data || [];
  const pagination: PaginationData = data?.pagination || {
    page,
    limit: initialLimit,
    totalCount: 0,
    totalPages: 0,
    hasMore: false,
  };

  return (
    <div className="space-y-6">
      {/* 검색 폼 */}
      <form onSubmit={handleSearch} className="flex gap-2">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="게시글 검색..."
          className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          disabled={isLoading || isValidating}
        >
          검색
        </button>
      </form>

      {/* 로딩 상태 */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-gray-500">게시글을 불러오는 중...</p>
        </div>
      )}

      {/* 에러 상태 */}
      {error && (
        <div className="p-4 bg-red-100 text-red-700 rounded-md">
          <p>게시글을 불러오는 중 오류가 발생했습니다.</p>
          <p className="text-sm">{error.message}</p>
        </div>
      )}

      {/* 게시글 목록 */}
      {!isLoading && !error && posts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">게시글이 없습니다.</p>
        </div>
      )}

      {posts.length > 0 && (
        <div className="space-y-4">
          {posts.map((post) => (
            <div key={post.id} className="p-4 border border-gray-200 rounded-md hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold">{post.title}</h3>
              <div className="flex items-center text-sm text-gray-500 mt-2">
                <span>{post.author.displayName || post.author.name}</span>
                <span className="mx-2">•</span>
                <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                <span className="mx-2">•</span>
                <span>{post.category.name}</span>
              </div>
              <p className="mt-2 text-gray-700 line-clamp-2">{post.content}</p>
              <div className="flex items-center mt-3 text-sm text-gray-500">
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  {post._count.likes}
                </span>
                <span className="flex items-center ml-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  {post._count.comments}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 페이지네이션 */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <nav className="inline-flex rounded-md shadow">
            <button
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1 || isLoading}
              className="px-3 py-2 rounded-l-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              이전
            </button>
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              // 현재 페이지 주변의 페이지 번호만 표시
              const pageNum = Math.min(
                Math.max(page - 2, 1) + i,
                pagination.totalPages
              );
              if (pageNum <= 0 || pageNum > pagination.totalPages) return null;
              
              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  disabled={isLoading}
                  className={`px-3 py-2 border border-gray-300 ${
                    pageNum === page
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
            <button
              onClick={() => handlePageChange(page + 1)}
              disabled={page === pagination.totalPages || isLoading}
              className="px-3 py-2 rounded-r-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              다음
            </button>
          </nav>
        </div>
      )}
    </div>
  );
}
