'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { ko } from 'date-fns/locale';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { MoreVertical, Edit, Trash } from 'lucide-react';
import { getInitials } from '@/lib/utils';
import { createPostComment, updatePostComment, deletePostComment } from '@/actions/post';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface PostCommentsProps {
  postId: string;
  initialComments: any[];
}

export default function PostComments({ postId, initialComments }: PostCommentsProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [comments, setComments] = useState(initialComments);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<number | null>(null);
  const [editContent, setEditContent] = useState('');
  const [deleteCommentId, setDeleteCommentId] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 댓글 작성
  const handleSubmitComment = async () => {
    if (!session?.user) {
      toast.error('로그인이 필요합니다.');
      return;
    }

    if (!newComment.trim()) {
      toast.error('댓글 내용을 입력해주세요.');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await createPostComment({
        postId,
        content: newComment,
      });

      if (result.success) {
        setComments([result.data, ...comments]);
        setNewComment('');
        toast.success('댓글이 작성되었습니다.');
        router.refresh();
      } else {
        toast.error(`댓글 작성 실패: ${result.error}`);
      }
    } catch (error) {
      toast.error('댓글 작성 중 오류가 발생했습니다.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 댓글 수정 시작
  const handleStartEdit = (comment: any) => {
    setEditingCommentId(comment.id);
    setEditContent(comment.content);
  };

  // 댓글 수정 취소
  const handleCancelEdit = () => {
    setEditingCommentId(null);
    setEditContent('');
  };

  // 댓글 수정 저장
  const handleSaveEdit = async (commentId: number) => {
    if (!editContent.trim()) {
      toast.error('댓글 내용을 입력해주세요.');
      return;
    }

    try {
      const result = await updatePostComment({
        id: commentId,
        content: editContent,
      });

      if (result.success) {
        setComments(
          comments.map((comment) =>
            comment.id === commentId ? { ...comment, content: editContent } : comment
          )
        );
        setEditingCommentId(null);
        setEditContent('');
        toast.success('댓글이 수정되었습니다.');
        router.refresh();
      } else {
        toast.error(`댓글 수정 실패: ${result.error}`);
      }
    } catch (error) {
      toast.error('댓글 수정 중 오류가 발생했습니다.');
    }
  };

  // 댓글 삭제
  const handleDeleteComment = async () => {
    if (!deleteCommentId) return;

    setIsDeleting(true);

    try {
      const result = await deletePostComment({
        id: deleteCommentId,
      });

      if (result.success) {
        setComments(comments.filter((comment) => comment.id !== deleteCommentId));
        toast.success('댓글이 삭제되었습니다.');
        router.refresh();
      } else {
        toast.error(`댓글 삭제 실패: ${result.error}`);
      }
    } catch (error) {
      toast.error('댓글 삭제 중 오류가 발생했습니다.');
    } finally {
      setIsDeleting(false);
      setDeleteCommentId(null);
    }
  };

  return (
    <div className="space-y-6">
      <CardTitle>댓글 {comments.length}개</CardTitle>

      {/* 댓글 작성 폼 */}
      <Card>
        <CardContent className="pt-6">
          <Textarea
            placeholder={session?.user ? '댓글을 작성해주세요...' : '로그인 후 댓글을 작성할 수 있습니다.'}
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            disabled={!session?.user || isSubmitting}
            className="resize-none mb-4"
            rows={3}
          />
          <div className="flex justify-end">
            <Button
              onClick={handleSubmitComment}
              disabled={!session?.user || isSubmitting || !newComment.trim()}
            >
              {isSubmitting ? '작성 중...' : '댓글 작성'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 댓글 목록 */}
      {comments.length > 0 ? (
        <div className="space-y-4">
          {comments.map((comment) => (
            <Card key={comment.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comment.user?.image || undefined} alt={comment.user?.name} />
                      <AvatarFallback>
                        {getInitials(comment.user?.displayName || comment.user?.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{comment.user?.displayName || comment.user?.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true, locale: ko })}
                      </p>
                    </div>
                  </div>
                  {(session?.user?.id === comment.userId || session?.user?.isAdmin) && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleStartEdit(comment)}>
                          <Edit className="h-4 w-4 mr-2" />
                          수정하기
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setDeleteCommentId(comment.id)}>
                          <Trash className="h-4 w-4 mr-2" />
                          삭제하기
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-2">
                {editingCommentId === comment.id ? (
                  <div className="space-y-2">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="resize-none"
                      rows={3}
                    />
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                        취소
                      </Button>
                      <Button size="sm" onClick={() => handleSaveEdit(comment.id)}>
                        저장
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm">{comment.content}</p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-10 text-muted-foreground">
          <p>아직 댓글이 없습니다.</p>
          <p>첫 번째 댓글을 작성해보세요!</p>
        </div>
      )}

      {/* 댓글 삭제 확인 다이얼로그 */}
      <AlertDialog open={!!deleteCommentId} onOpenChange={(open) => !open && setDeleteCommentId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>댓글 삭제</AlertDialogTitle>
            <AlertDialogDescription>
              이 댓글을 정말 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>취소</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteComment} disabled={isDeleting}>
              {isDeleting ? '삭제 중...' : '삭제'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
