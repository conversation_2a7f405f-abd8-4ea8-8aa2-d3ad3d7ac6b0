"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { formatDistanceToNow } from "date-fns";
import { ko } from "date-fns/locale";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  MessageSquare,
  ThumbsUp,
  Eye,
  MoreVertical,
  Edit,
  Trash,
  Share,
  Bookmark,
} from "lucide-react";
import { getInitials } from "@/lib/utils";
import { deletePost, togglePostLike, getPostLikeStatus } from "@/actions/post";
import {
  getBookmarkStatus,
  toggleBookmark,
} from "@/actions/interaction/bookmark";
import ContentRenderer from "@/components/editor/ContentRenderer";
import BookmarkButton from "@/components/bookmark-button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface PostDetailProps {
  post: any;
}

export default function PostDetail({ post }: PostDetailProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [likeCount, setLikeCount] = useState(post.likeCount);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // 좋아요 상태 확인
  const checkLikeStatus = async () => {
    if (!session?.user) return;

    try {
      const result = await getPostLikeStatus({ postId: post.id });
      if (result.success) {
        setIsLiked(result.data.liked);
        setLikeCount(result.data.likeCount);
      }
    } catch (error) {
      console.error("좋아요 상태 확인 중 오류 발생:", error);
    }
  };

  // 북마크 상태 확인
  const checkBookmarkStatus = async () => {
    if (!session?.user) return;

    try {
      const result = await getBookmarkStatus(session.user.id, post.id);
      if (result.success) {
        setIsBookmarked(result.data.isBookmarked);
      }
    } catch (error) {
      console.error("북마크 상태 확인 중 오류 발생:", error);
    }
  };

  // 컴포넌트 마운트 시 좋아요 및 북마크 상태 확인
  useState(() => {
    checkLikeStatus();
    checkBookmarkStatus();
  });

  // 좋아요 토글
  const handleLikeToggle = async () => {
    if (!session?.user) {
      toast.error("로그인이 필요합니다.");
      return;
    }

    try {
      const result = await togglePostLike({ postId: post.id });
      if (result.success) {
        setIsLiked(result.data.liked);
        setLikeCount(result.data.likeCount);
        toast.success(
          result.data.liked ? "좋아요를 눌렀습니다." : "좋아요를 취소했습니다."
        );
      }
    } catch (error) {
      toast.error("좋아요 처리 중 오류가 발생했습니다.");
    }
  };

  // 북마크 토글
  const handleBookmarkToggle = async () => {
    if (!session?.user) {
      toast.error("로그인이 필요합니다.");
      return;
    }

    try {
      const result = await toggleBookmark({ postId: post.id });
      if (result.success) {
        setIsBookmarked(result.data.action === "bookmarked");
        toast.success(
          result.data.action === "bookmarked"
            ? "북마크에 추가되었습니다."
            : "북마크가 취소되었습니다."
        );
      }
    } catch (error) {
      toast.error("북마크 처리 중 오류가 발생했습니다.");
    }
  };

  // 게시글 삭제
  const handleDelete = async () => {
    if (!session?.user) return;

    setIsDeleting(true);

    try {
      const result = await deletePost({ id: post.id });
      if (result.success) {
        toast.success("게시글이 삭제되었습니다.");
        router.push("/community");
        router.refresh();
      } else {
        toast.error(`게시글 삭제 실패: ${result.error}`);
      }
    } catch (error) {
      toast.error("게시글 삭제 중 오류가 발생했습니다.");
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // 게시글 공유
  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: "커뮤니티 게시글 공유",
          url: window.location.href,
        })
        .catch((error) => console.error("공유 중 오류 발생:", error));
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success("링크가 클립보드에 복사되었습니다.");
    }
  };

  const isAuthor = session?.user?.id === post.userId;
  const isAdmin = session?.user?.isAdmin === true;
  const canEdit = isAuthor || isAdmin;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-2">
            <Avatar className="h-10 w-10">
              <AvatarImage
                src={post.user.image || undefined}
                alt={post.user.name}
              />
              <AvatarFallback>
                {getInitials(post.user.displayName || post.user.name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">
                {post.user.displayName || post.user.name}
              </p>
              <p className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(post.createdAt), {
                  addSuffix: true,
                  locale: ko,
                })}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {post.type !== "NORMAL" && (
              <Badge
                variant={post.type === "QUESTION" ? "secondary" : "default"}
                className="mr-2"
              >
                {post.type === "QUESTION" ? "질문" : "공지"}
              </Badge>
            )}
            {canEdit && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => router.push(`/community/edit/${post.id}`)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    수정하기
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)}>
                    <Trash className="h-4 w-4 mr-2" />
                    삭제하기
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-2">
        <ContentRenderer content={post.contentHtml} />

        {post.category && (
          <div className="mt-4">
            <Badge variant="outline">{post.category.name}</Badge>
          </div>
        )}
      </CardContent>
      <CardFooter className="border-t pt-4 flex justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            className={isLiked ? "text-primary" : ""}
            onClick={handleLikeToggle}
          >
            <ThumbsUp className="h-4 w-4 mr-2" />
            좋아요 {likeCount}
          </Button>
          <Button variant="ghost" size="sm">
            <MessageSquare className="h-4 w-4 mr-2" />
            댓글 {post.commentCount}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={isBookmarked ? "text-primary" : ""}
            onClick={handleBookmarkToggle}
          >
            <Bookmark
              className={`h-4 w-4 mr-2 ${
                isBookmarked ? "fill-current" : "fill-none"
              }`}
            />
            {isBookmarked ? "저장됨" : "저장"}
          </Button>
          <div className="flex items-center text-sm text-muted-foreground">
            <Eye className="h-4 w-4 mr-1" />
            <span>{post.viewCount}</span>
          </div>
        </div>
        <Button variant="ghost" size="sm" onClick={handleShare}>
          <Share className="h-4 w-4 mr-2" />
          공유하기
        </Button>
      </CardFooter>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>게시글 삭제</AlertDialogTitle>
            <AlertDialogDescription>
              이 게시글을 정말 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>취소</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? "삭제 중..." : "삭제"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
