'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import TiptapEditor from '@/components/editor/TiptapEditor';
import ContentRenderer from '@/components/editor/ContentRenderer';
import { createPost, updatePost } from '@/actions/post';

// 폼 스키마
const formSchema = z.object({
  type: z.enum(['NORMAL', 'QUESTION', 'NOTICE']),
  content: z.string().min(1, '내용을 입력해주세요.'),
  contentHtml: z.string().default('<p></p>'),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).default([]),
});

type FormValues = z.infer<typeof formSchema>;

interface PostFormProps {
  categories: any[];
  initialData?: any;
  isEdit?: boolean;
}

export default function PostForm({ categories, initialData, isEdit = false }: PostFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewContent, setPreviewContent] = useState(initialData?.contentHtml || '');

  // 폼 초기화
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: initialData?.type || 'NORMAL',
      content: initialData?.content || '',
      contentHtml: initialData?.contentHtml || '<p></p>',
      categoryId: initialData?.categoryId || undefined,
      tags: initialData?.tags || [],
    },
  });

  // 에디터 내용 변경 처리
  const handleEditorChange = (html: string) => {
    form.setValue('contentHtml', html);
    form.setValue('content', html.replace(/<[^>]*>/g, '').trim());
    setPreviewContent(html);
  };

  // 폼 제출 처리
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);

    try {
      if (isEdit && initialData?.id) {
        // 게시글 수정
        const result = await updatePost({
          id: initialData.id,
          ...values,
        });

        if (result.success) {
          toast.success('게시글이 수정되었습니다.');
          router.push(`/community/${initialData.id}`);
          router.refresh();
        } else {
          toast.error(`게시글 수정 실패: ${result.error}`);
        }
      } else {
        // 게시글 작성
        const result = await createPost(values);

        if (result.success) {
          toast.success('게시글이 작성되었습니다.');
          router.push(`/community/${result.data.id}`);
          router.refresh();
        } else {
          toast.error(`게시글 작성 실패: ${result.error}`);
        }
      }
    } catch (error) {
      toast.error('게시글 저장 중 오류가 발생했습니다.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>게시글 유형</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="게시글 유형 선택" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="NORMAL">일반</SelectItem>
                        <SelectItem value="QUESTION">질문</SelectItem>
                        <SelectItem value="NOTICE">공지</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>카테고리</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="카테고리 선택 (선택사항)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Tabs defaultValue="write">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="write">작성</TabsTrigger>
                <TabsTrigger value="preview">미리보기</TabsTrigger>
              </TabsList>
              <TabsContent value="write">
                <FormField
                  control={form.control}
                  name="contentHtml"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <TiptapEditor
                          content={form.getValues('contentHtml')}
                          onChange={handleEditorChange}
                          placeholder="내용을 입력하세요..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="preview">
                <Card>
                  <CardContent className="pt-6">
                    <ContentRenderer content={previewContent} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">게시글 작성 안내</h3>
                <Separator className="mb-4" />
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium">게시글 유형</h4>
                    <p className="text-muted-foreground">
                      - 일반: 일반적인 게시글<br />
                      - 질문: 다른 사용자에게 질문하는 게시글<br />
                      - 공지: 중요한 공지사항 (관리자만 작성 가능)
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium">카테고리</h4>
                    <p className="text-muted-foreground">
                      게시글의 주제에 맞는 카테고리를 선택하면 다른 사용자가 쉽게 찾을 수 있습니다.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium">에디터 사용법</h4>
                    <p className="text-muted-foreground">
                      - 텍스트 서식: 굵게, 기울임, 밑줄 등<br />
                      - 목록: 글머리 기호, 번호 매기기<br />
                      - 링크: URL 추가<br />
                      - 이미지: 이미지 업로드 또는 URL 추가
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            취소
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? '저장 중...' : isEdit ? '수정하기' : '작성하기'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
