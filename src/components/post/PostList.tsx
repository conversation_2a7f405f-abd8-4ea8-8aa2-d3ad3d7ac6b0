import { getPosts } from '@/actions/post';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/ui/pagination';
import { formatDistanceToNow } from 'date-fns';
import { ko } from 'date-fns/locale';
import Link from 'next/link';
import { MessageSquare, ThumbsUp, Eye } from 'lucide-react';
import { getInitials } from '@/lib/utils';
import PostTypeFilter from './PostTypeFilter';
import PostSortFilter from './PostSortFilter';

interface PostListProps {
  searchParams: {
    page?: string;
    categoryId?: string;
    type?: string;
    query?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default async function PostList({ searchParams }: PostListProps) {
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const { categoryId, type, query, sortBy, sortOrder } = searchParams;

  // 게시글 목록 조회
  const result = await getPosts({
    page,
    limit: 10,
    categoryId,
    type: type as any,
    query,
    sortBy: sortBy || 'createdAt',
    sortOrder: (sortOrder as any) || 'desc',
  });

  if (!result.success) {
    return (
      <div className="text-center py-10">
        <p>게시글을 불러오는 중 오류가 발생했습니다.</p>
        <p className="text-muted-foreground">{result.error}</p>
      </div>
    );
  }

  const { items: posts, meta } = result.data;

  if (posts.length === 0) {
    return (
      <div className="text-center py-10">
        <p>게시글이 없습니다.</p>
        <p className="text-muted-foreground">첫 번째 게시글을 작성해보세요!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PostTypeFilter selectedType={type} />
        <PostSortFilter 
          sortBy={sortBy || 'createdAt'} 
          sortOrder={sortOrder || 'desc'} 
        />
      </div>

      <div className="space-y-4">
        {posts.map((post) => (
          <Card key={post.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={post.user.image || undefined} alt={post.user.name} />
                    <AvatarFallback>{getInitials(post.user.displayName || post.user.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{post.user.displayName || post.user.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(post.createdAt), { addSuffix: true, locale: ko })}
                    </p>
                  </div>
                </div>
                {post.type !== 'NORMAL' && (
                  <Badge variant={post.type === 'QUESTION' ? 'secondary' : 'default'}>
                    {post.type === 'QUESTION' ? '질문' : '공지'}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <Link href={`/community/${post.id}`}>
                <div className="prose prose-sm max-w-none line-clamp-3" dangerouslySetInnerHTML={{ __html: post.contentHtml }} />
              </Link>
            </CardContent>
            <CardFooter className="pt-0 flex justify-between">
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <ThumbsUp className="h-4 w-4 mr-1" />
                  <span>{post.likeCount}</span>
                </div>
                <div className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-1" />
                  <span>{post.commentCount}</span>
                </div>
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  <span>{post.viewCount}</span>
                </div>
              </div>
              {post.category && (
                <Badge variant="outline">{post.category.name}</Badge>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      <Pagination
        currentPage={meta.currentPage}
        totalPages={meta.totalPages}
        baseUrl="/community"
        searchParams={searchParams}
      />
    </div>
  );
}
