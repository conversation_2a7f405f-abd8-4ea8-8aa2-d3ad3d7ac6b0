'use client';

import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface PostSortFilterProps {
  sortBy: string;
  sortOrder: string;
}

export default function PostSortFilter({ sortBy, sortOrder }: PostSortFilterProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleSortChange = (value: string) => {
    const [newSortBy, newSortOrder] = value.split('-');
    const params = new URLSearchParams(searchParams);
    
    // 페이지 초기화
    params.delete('page');
    
    // 정렬 설정
    params.set('sortBy', newSortBy);
    params.set('sortOrder', newSortOrder);
    
    router.push(`${pathname}?${params.toString()}`);
  };

  const currentValue = `${sortBy}-${sortOrder}`;

  return (
    <Select value={currentValue} onValueChange={handleSortChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="정렬 방식" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="createdAt-desc">최신순</SelectItem>
        <SelectItem value="createdAt-asc">오래된순</SelectItem>
        <SelectItem value="likeCount-desc">좋아요순</SelectItem>
        <SelectItem value="commentCount-desc">댓글순</SelectItem>
        <SelectItem value="viewCount-desc">조회수순</SelectItem>
      </SelectContent>
    </Select>
  );
}
