'use client';

import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';

interface PostTypeFilterProps {
  selectedType?: string;
}

export default function PostTypeFilter({ selectedType }: PostTypeFilterProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleTypeChange = (type: string | null) => {
    const params = new URLSearchParams(searchParams);
    
    // 페이지 초기화
    params.delete('page');
    
    // 타입 설정 또는 제거
    if (type) {
      params.set('type', type);
    } else {
      params.delete('type');
    }
    
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex space-x-2">
      <Button
        variant={!selectedType ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleTypeChange(null)}
      >
        전체
      </Button>
      <Button
        variant={selectedType === 'NORMAL' ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleTypeChange('NORMAL')}
      >
        일반
      </Button>
      <Button
        variant={selectedType === 'QUESTION' ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleTypeChange('QUESTION')}
      >
        질문
      </Button>
      <Button
        variant={selectedType === 'NOTICE' ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleTypeChange('NOTICE')}
      >
        공지
      </Button>
    </div>
  );
}
