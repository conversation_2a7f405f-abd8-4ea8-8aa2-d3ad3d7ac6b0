"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { updateSettings, getSettings } from "@/actions/user/settings";
import { Loader2, Trash2 } from "lucide-react";

// 계정 설정 폼 스키마
const accountSettingsSchema = z.object({
  language: z.enum(["ko", "en"]),
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
});

type AccountSettingsValues = z.infer<typeof accountSettingsSchema>;

interface AccountSettingsProps {
  userId?: string;
}

export default function AccountSettings({ userId }: AccountSettingsProps) {
  const t = useTranslations("Profile");
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // 폼 초기화
  const form = useForm<AccountSettingsValues>({
    resolver: zodResolver(accountSettingsSchema),
    defaultValues: {
      language: "ko",
      emailNotifications: true,
      pushNotifications: true,
    },
  });

  // 설정 데이터 로드
  useEffect(() => {
    const loadSettingsData = async () => {
      try {
        const response = await getSettings();
        if (response.success && response.data) {
          form.reset({
            language: response.data.language as "ko" | "en",
            emailNotifications: response.data.emailNotifications,
            pushNotifications: response.data.pushNotifications,
          });
        }
      } catch (error) {
        console.error("설정 데이터 로드 중 오류 발생:", error);
        toast.error(t("loadSettingsError"));
      }
    };

    loadSettingsData();
  }, [form, t]);

  // 설정 업데이트 제출 핸들러
  const onSubmit = async (data: AccountSettingsValues) => {
    setIsLoading(true);
    try {
      const result = await updateSettings(data);
      if (result.success) {
        toast.success(t("settingsUpdateSuccess"));
      } else {
        toast.error(result.error?.message || t("settingsUpdateError"));
      }
    } catch (error) {
      console.error("설정 업데이트 중 오류 발생:", error);
      toast.error(t("settingsUpdateError"));
    } finally {
      setIsLoading(false);
    }
  };

  // 계정 삭제 핸들러
  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    try {
      // 계정 삭제 액션 호출
      const { deleteAccount } = await import("@/actions/user/account");
      const result = await deleteAccount();

      if (result.success) {
        toast.success(t("accountDeleteSuccess"));
        // 로그아웃 및 홈페이지로 리디렉션은 Server Action에서 처리됨
        // 추가적인 리디렉션이 필요한 경우 여기에 구현
        window.location.href = "/";
      } else {
        toast.error(result.error?.message || t("accountDeleteError"));
      }
    } catch (error) {
      console.error("계정 삭제 중 오류 발생:", error);
      toast.error(t("accountDeleteError"));
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* 언어 설정 */}
          <FormField
            control={form.control}
            name="language"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("language")}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectLanguage")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="ko">한국어</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>{t("languageDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 이메일 알림 설정 */}
          <FormField
            control={form.control}
            name="emailNotifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t("emailNotifications")}
                  </FormLabel>
                  <FormDescription>
                    {t("emailNotificationsDescription")}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* 푸시 알림 설정 */}
          <FormField
            control={form.control}
            name="pushNotifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t("pushNotifications")}
                  </FormLabel>
                  <FormDescription>
                    {t("pushNotificationsDescription")}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* 제출 버튼 */}
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("saving")}
              </>
            ) : (
              t("saveSettings")
            )}
          </Button>
        </form>
      </Form>

      {/* 계정 삭제 섹션 */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-medium text-destructive mb-4">
          {t("dangerZone")}
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          {t("deleteAccountWarning")}
        </p>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" disabled={isDeleting}>
              <Trash2 className="mr-2 h-4 w-4" />
              {t("deleteAccount")}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t("deleteAccountConfirmTitle")}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t("deleteAccountConfirmDescription")}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t("cancel")}</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteAccount}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {t("confirmDelete")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}
