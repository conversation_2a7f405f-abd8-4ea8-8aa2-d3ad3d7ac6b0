'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { changePassword } from '@/actions/user/password';
import { Loader2 } from 'lucide-react';

// 비밀번호 변경 폼 스키마
const passwordFormSchema = z
  .object({
    currentPassword: z
      .string()
      .min(8, { message: '비밀번호는 최소 8자 이상이어야 합니다.' }),
    newPassword: z
      .string()
      .min(8, { message: '비밀번호는 최소 8자 이상이어야 합니다.' })
      .regex(/[a-z]/, { message: '소문자를 포함해야 합니다.' })
      .regex(/[A-Z]/, { message: '대문자를 포함해야 합니다.' })
      .regex(/[0-9]/, { message: '숫자를 포함해야 합니다.' })
      .regex(/[^a-zA-Z0-9]/, { message: '특수문자를 포함해야 합니다.' }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: '새 비밀번호와 확인 비밀번호가 일치하지 않습니다.',
    path: ['confirmPassword'],
  });

type PasswordFormValues = z.infer<typeof passwordFormSchema>;

export default function PasswordForm() {
  const t = useTranslations('Profile');
  const [isLoading, setIsLoading] = useState(false);

  // 폼 초기화
  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // 비밀번호 변경 제출 핸들러
  const onSubmit = async (data: PasswordFormValues) => {
    setIsLoading(true);
    try {
      const result = await changePassword(data);
      if (result.success) {
        toast.success(t('passwordUpdateSuccess'));
        form.reset();
      } else {
        toast.error(result.error?.message || t('passwordUpdateError'));
      }
    } catch (error) {
      console.error('비밀번호 변경 중 오류 발생:', error);
      toast.error(t('passwordUpdateError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* 현재 비밀번호 */}
        <FormField
          control={form.control}
          name="currentPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('currentPassword')}</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 새 비밀번호 */}
        <FormField
          control={form.control}
          name="newPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('newPassword')}</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormDescription>{t('passwordRequirements')}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 비밀번호 확인 */}
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('confirmPassword')}</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 제출 버튼 */}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('updating')}
            </>
          ) : (
            t('updatePassword')
          )}
        </Button>
      </form>
    </Form>
  );
}
