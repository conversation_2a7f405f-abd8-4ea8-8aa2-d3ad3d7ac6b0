"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { User } from "next-auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { updateProfile, getProfile } from "@/actions/user/profile";
import { Loader2, Upload } from "lucide-react";

// 프로필 폼 스키마
const profileFormSchema = z.object({
  displayName: z
    .string()
    .min(2, { message: "이름은 최소 2자 이상이어야 합니다." })
    .max(30, { message: "이름은 최대 30자까지 가능합니다." })
    .optional(),
  bio: z
    .string()
    .max(500, { message: "자기소개는 최대 500자까지 가능합니다." })
    .optional(),
  image: z.string().url({ message: "유효한 URL 형식이 아닙니다." }).optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

interface ProfileFormProps {
  user?: User;
}

export default function ProfileForm({ user }: ProfileFormProps) {
  const t = useTranslations("Profile");
  const [isLoading, setIsLoading] = useState(false);
  const [isImageUploading, setIsImageUploading] = useState(false);

  // 폼 초기화
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      displayName: user?.name || "",
      bio: "",
      image: user?.image || "",
    },
  });

  // 프로필 데이터 로드
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        const response = await getProfile();
        if (response.success && response.data) {
          form.reset({
            displayName: response.data.displayName || response.data.name || "",
            bio: response.data.bio || "",
            image: response.data.image || "",
          });
        }
      } catch (error) {
        console.error("프로필 데이터 로드 중 오류 발생:", error);
        toast.error(t("loadError"));
      }
    };

    loadProfileData();
  }, [form, t, user]);

  // 프로필 업데이트 제출 핸들러
  const onSubmit = async (data: ProfileFormValues) => {
    setIsLoading(true);
    try {
      const result = await updateProfile(data);
      if (result.success) {
        toast.success(t("updateSuccess"));
      } else {
        toast.error(result.error?.message || t("updateError"));
      }
    } catch (error) {
      console.error("프로필 업데이트 중 오류 발생:", error);
      toast.error(t("updateError"));
    } finally {
      setIsLoading(false);
    }
  };

  // 이미지 업로드 핸들러 (Supabase Storage 사용)
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 파일 타입 검증
    if (!file.type.startsWith("image/")) {
      toast.error(t("imageTypeError"));
      return;
    }

    // 파일 크기 검증 (2MB 제한)
    if (file.size > 2 * 1024 * 1024) {
      toast.error(t("imageSizeError"));
      return;
    }

    setIsImageUploading(true);
    try {
      // FormData 생성
      const formData = new FormData();
      formData.append("file", file);

      // Server Action을 통해 이미지 업로드
      const { uploadProfileImageAction } = await import(
        "@/actions/user/upload"
      );
      const result = await uploadProfileImageAction(formData);

      if (result.success && result.data?.imageUrl) {
        form.setValue("image", result.data.imageUrl);
        toast.success(t("imageUploadSuccess"));
      } else {
        toast.error(result.error?.message || t("imageUploadError"));
      }
    } catch (error) {
      console.error("이미지 업로드 중 오류 발생:", error);
      toast.error(t("imageUploadError"));
    } finally {
      setIsImageUploading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* 프로필 이미지 */}
        <div className="flex flex-col items-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
          <Avatar className="h-24 w-24">
            <AvatarImage
              src={form.watch("image")}
              alt={form.watch("displayName") || user?.name || ""}
            />
            <AvatarFallback className="text-lg">
              {(form.watch("displayName") ||
                user?.name ||
                "?")[0]?.toUpperCase()}
            </AvatarFallback>
          </Avatar>

          <div className="flex flex-col space-y-2">
            <FormLabel>{t("profileImage")}</FormLabel>
            <div className="flex items-center space-x-2">
              <Input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                disabled={isImageUploading}
                className="hidden"
                id="profile-image-upload"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  document.getElementById("profile-image-upload")?.click()
                }
                disabled={isImageUploading}
              >
                {isImageUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("uploading")}
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    {t("uploadImage")}
                  </>
                )}
              </Button>
            </div>
            <FormDescription>{t("imageDescription")}</FormDescription>
          </div>
        </div>

        {/* 이름 */}
        <FormField
          control={form.control}
          name="displayName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("displayName")}</FormLabel>
              <FormControl>
                <Input placeholder={t("displayNamePlaceholder")} {...field} />
              </FormControl>
              <FormDescription>{t("displayNameDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 자기소개 */}
        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("bio")}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("bioPlaceholder")}
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>{t("bioDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 제출 버튼 */}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("saving")}
            </>
          ) : (
            t("saveProfile")
          )}
        </Button>
      </form>
    </Form>
  );
}
