"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import Link from "next/link";
import { format } from "date-fns";
import { ko } from "date-fns/locale";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Loader2,
  MessageSquare,
  ThumbsUp,
  Bookmark,
  Clock,
} from "lucide-react";
import { getUserActivity, ActivityType } from "@/actions/user/activity";

// 활동 아이템 인터페이스
interface ActivityItem {
  id: string;
  type: ActivityType;
  title: string;
  content?: string;
  targetId: string;
  targetType: string;
  createdAt: string;
}

interface UserActivityProps {
  userId?: string;
}

export default function UserActivity({ userId }: UserActivityProps) {
  const t = useTranslations("Profile");
  const [activeTab, setActiveTab] = useState<ActivityType>("post");
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // 활동 데이터 로드
  useEffect(() => {
    const loadActivityData = async () => {
      if (!userId) return;

      setIsLoading(true);
      try {
        // Server Action을 통해 활동 데이터 로드
        const result = await getUserActivity({
          type: activeTab,
          page,
          limit: 10,
        });

        if (result.success && result.data) {
          const { items, pagination } = result.data;
          setActivities((prev) => (page === 1 ? items : [...prev, ...items]));
          setHasMore(pagination.hasMore);
        } else {
          toast.error(result.error?.message || t("loadActivityError"));
          setActivities([]);
          setHasMore(false);
        }
      } catch (error) {
        console.error("활동 데이터 로드 중 오류 발생:", error);
        toast.error(t("loadActivityError"));
        setActivities([]);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadActivityData();
  }, [userId, activeTab, page, t]);

  // 활동 타입에 따른 제목 반환
  const getActivityTitle = (type: ActivityType): string => {
    switch (type) {
      case "post":
        return "게시글";
      case "comment":
        return "댓글";
      case "like":
        return "좋아요";
      case "bookmark":
        return "북마크";
      default:
        return "";
    }
  };

  // 활동 타입에 따른 아이콘 반환
  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case "post":
        return <MessageSquare className="h-4 w-4" />;
      case "comment":
        return <MessageSquare className="h-4 w-4" />;
      case "like":
        return <ThumbsUp className="h-4 w-4" />;
      case "bookmark":
        return <Bookmark className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // 탭 변경 핸들러
  const handleTabChange = (value: string) => {
    setActiveTab(value as ActivityType);
    setPage(1);
    setActivities([]);
  };

  // 더 보기 핸들러
  const handleLoadMore = () => {
    setPage((prev) => prev + 1);
  };

  // 로딩 중 스켈레톤 UI
  const renderSkeletons = () =>
    Array.from({ length: 3 }).map((_, i) => (
      <Card key={`skeleton-${i}`} className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <Skeleton className="h-5 w-1/3" />
            <Skeleton className="h-4 w-1/4" />
          </div>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-2/3" />
        </CardContent>
      </Card>
    ));

  return (
    <div className="space-y-6">
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="post">{t("posts")}</TabsTrigger>
          <TabsTrigger value="comment">{t("comments")}</TabsTrigger>
          <TabsTrigger value="like">{t("likes")}</TabsTrigger>
          <TabsTrigger value="bookmark">{t("bookmarks")}</TabsTrigger>
        </TabsList>

        {["post", "comment", "like", "bookmark"].map((type) => (
          <TabsContent key={type} value={type} className="space-y-4">
            {isLoading && page === 1 ? (
              renderSkeletons()
            ) : activities.length > 0 ? (
              <>
                {activities.map((activity) => (
                  <Card key={activity.id} className="mb-4">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base font-medium">
                          <Link
                            href={`/${activity.targetType}/${activity.targetId}`}
                            className="hover:underline"
                          >
                            {activity.title}
                          </Link>
                        </CardTitle>
                        <Badge
                          variant="outline"
                          className="flex items-center gap-1"
                        >
                          <Clock className="h-3 w-3" />
                          {format(new Date(activity.createdAt), "yyyy.MM.dd", {
                            locale: ko,
                          })}
                        </Badge>
                      </div>
                    </CardHeader>
                    {activity.content && (
                      <CardContent>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {activity.content}
                        </p>
                      </CardContent>
                    )}
                    <CardFooter className="pt-0">
                      <Badge
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {getActivityIcon(activity.type)}
                        {getActivityTitle(activity.type)}
                      </Badge>
                    </CardFooter>
                  </Card>
                ))}

                {hasMore && (
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={handleLoadMore}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t("loading")}
                        </>
                      ) : (
                        t("loadMore")
                      )}
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">{t("noActivity")}</p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
