"use client";

import { useState, useEffect, FormEvent } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";

interface SearchFormProps {
  placeholder?: string;
  className?: string;
  compact?: boolean;
}

export default function SearchForm({
  placeholder = "검색어를 입력하세요...",
  className = "",
  compact = false,
}: SearchFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState(searchParams.get("query") || "");
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showRecent, setShowRecent] = useState(false);

  // URL 쿼리 파라미터가 변경되면 검색어 상태 업데이트
  useEffect(() => {
    setQuery(searchParams.get("query") || "");
  }, [searchParams]);

  // 컴포넌트 마운트 시 로컬 스토리지에서 최근 검색어 로드
  useEffect(() => {
    const savedSearches = localStorage.getItem("recentSearches");
    if (savedSearches) {
      try {
        const parsedSearches = JSON.parse(savedSearches);
        if (Array.isArray(parsedSearches)) {
          setRecentSearches(parsedSearches.slice(0, 5));
        }
      } catch (error) {
        console.error("최근 검색어 로드 중 오류 발생:", error);
      }
    }
  }, []);

  // 최근 검색어 저장 함수
  const saveRecentSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return;

    const updatedSearches = [
      searchQuery,
      ...recentSearches.filter((item) => item !== searchQuery),
    ].slice(0, 5);

    setRecentSearches(updatedSearches);
    localStorage.setItem("recentSearches", JSON.stringify(updatedSearches));
  };

  // 검색 처리
  const handleSearch = (e: FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      return;
    }

    // 최근 검색어 저장
    saveRecentSearch(query.trim());

    // 검색 페이지로 이동
    router.push(`/search?query=${encodeURIComponent(query.trim())}`);
    setShowRecent(false);
  };

  // 최근 검색어 클릭 처리
  const handleRecentSearchClick = (searchQuery: string) => {
    setQuery(searchQuery);
    router.push(`/search?query=${encodeURIComponent(searchQuery)}`);
    setShowRecent(false);
  };

  // 최근 검색어 삭제 처리
  const handleRemoveRecentSearch = (
    e: React.MouseEvent,
    searchQuery: string
  ) => {
    e.stopPropagation();
    const updatedSearches = recentSearches.filter(
      (item) => item !== searchQuery
    );
    setRecentSearches(updatedSearches);
    localStorage.setItem("recentSearches", JSON.stringify(updatedSearches));
  };

  // 검색어 초기화
  const handleClear = () => {
    setQuery("");
    setShowRecent(false);
  };

  return (
    <div className={`relative ${className}`}>
      <form onSubmit={handleSearch} className="relative">
        <div className="relative">
          <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={placeholder}
            className={`pl-8 ${query ? "pr-20" : "pr-4"} ${
              compact ? "h-9" : ""
            }`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setShowRecent(true)}
            onBlur={() => {
              // 클릭 이벤트가 발생할 시간을 주기 위해 지연
              setTimeout(() => setShowRecent(false), 200);
            }}
            aria-label="검색어 입력"
            aria-autocomplete="list"
            aria-controls={showRecent ? "search-recent-list" : undefined}
            aria-expanded={showRecent}
          />
          {query && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-14 top-0 h-full px-2"
              onClick={handleClear}
              aria-label="검색어 지우기"
            >
              <X className="h-4 w-4" aria-hidden="true" />
            </Button>
          )}
          <Button
            type="submit"
            size="sm"
            className={`absolute right-0 top-0 h-full rounded-l-none ${
              compact ? "px-3" : ""
            }`}
            aria-label="검색 실행"
          >
            검색
          </Button>
        </div>
      </form>

      {/* 최근 검색어 드롭다운 */}
      {showRecent && recentSearches.length > 0 && (
        <div
          className="absolute z-10 top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-md"
          role="dialog"
          aria-label="최근 검색어 목록"
        >
          <div className="p-2 border-b">
            <h3 className="text-sm font-medium" id="search-recent-list">
              최근 검색어
            </h3>
          </div>
          <ul role="listbox" aria-labelledby="search-recent-list">
            {recentSearches.map((item, index) => (
              <li
                key={index}
                className="flex items-center justify-between px-3 py-2 hover:bg-muted cursor-pointer"
                onClick={() => handleRecentSearchClick(item)}
                role="option"
                aria-selected={false}
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    handleRecentSearchClick(item);
                  }
                }}
              >
                <span className="truncate">{item}</span>
                <button
                  onClick={(e) => handleRemoveRecentSearch(e, item)}
                  className="text-muted-foreground hover:text-foreground"
                  aria-label={`${item} 검색어 삭제`}
                >
                  <X className="h-3 w-3" />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
