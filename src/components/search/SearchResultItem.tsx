"use client";

import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import { ko } from "date-fns/locale";
import { SearchResultType, SearchResultItem } from "@/actions/search";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Eye, Calendar, User } from "lucide-react";

interface SearchResultItemProps {
  item: SearchResultItem;
  query: string;
}

export default function SearchResultItemCard({ item, query }: SearchResultItemProps) {
  // 검색 결과 타입에 따른 배지 색상 및 텍스트
  const getBadgeInfo = (type: SearchResultType) => {
    switch (type) {
      case SearchResultType.LIFE_INFO:
        return { color: "bg-green-100 text-green-800", text: "생활 정보" };
      case SearchResultType.SERVICE_GUIDE:
        return { color: "bg-blue-100 text-blue-800", text: "서비스 가이드" };
      case SearchResultType.GOV_INFO:
        return { color: "bg-purple-100 text-purple-800", text: "정부 정보" };
      case SearchResultType.JOB_POST:
        return { color: "bg-orange-100 text-orange-800", text: "구인·구직" };
      case SearchResultType.POST:
        return { color: "bg-gray-100 text-gray-800", text: "커뮤니티" };
      default:
        return { color: "bg-gray-100 text-gray-800", text: "기타" };
    }
  };

  // 검색어 하이라이팅 함수
  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text;

    const regex = new RegExp(`(${query})`, "gi");
    const parts = text.split(regex);

    return parts.map((part, i) => {
      if (part.toLowerCase() === query.toLowerCase()) {
        return (
          <span key={i} className="bg-yellow-200 font-medium">
            {part}
          </span>
        );
      }
      return part;
    });
  };

  // 콘텐츠 요약 생성 (HTML 태그 제거 및 길이 제한)
  const getSummary = () => {
    if (item.summary) return item.summary;

    let content = item.contentHtml
      ? item.contentHtml.replace(/<[^>]*>/g, "")
      : item.content || "";
    
    // 길이 제한 (200자)
    if (content.length > 200) {
      content = content.substring(0, 200) + "...";
    }

    return content;
  };

  const badgeInfo = getBadgeInfo(item.type);
  const summary = getSummary();
  const formattedDate = format(new Date(item.createdAt), "yyyy년 MM월 dd일", { locale: ko });

  return (
    <Card className="h-full flex flex-col hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start gap-2">
          <Badge className={`${badgeInfo.color} mb-2`}>{badgeInfo.text}</Badge>
          {item.category && (
            <Badge variant="outline">{item.category.name}</Badge>
          )}
        </div>
        <Link href={item.url} className="hover:underline">
          <h3 className="text-xl font-semibold line-clamp-2">
            {highlightText(item.title, query)}
          </h3>
        </Link>
      </CardHeader>
      <CardContent className="flex-grow">
        {item.imageUrl && (
          <div className="relative w-full h-40 mb-3 rounded-md overflow-hidden">
            <Image
              src={item.imageUrl}
              alt={item.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover"
            />
          </div>
        )}
        <p className="text-muted-foreground line-clamp-3">
          {highlightText(summary, query)}
        </p>
      </CardContent>
      <CardFooter className="pt-2 text-sm text-muted-foreground">
        <div className="flex flex-wrap gap-3 w-full">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>{formattedDate}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="h-4 w-4" />
            <span>{item.viewCount}회</span>
          </div>
          {item.author && (
            <div className="flex items-center gap-1 ml-auto">
              <User className="h-4 w-4" />
              <span>{item.author.displayName || item.author.name}</span>
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
