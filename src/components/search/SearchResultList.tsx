"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  SearchResultItem as SearchResultItemType,
  SearchResultType,
} from "@/actions/search";
import SearchResultItemCard from "./SearchResultItem";
import { Pagination } from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Filter, SortAsc, SortDesc } from "lucide-react";

interface SearchResultListProps {
  results: SearchResultItemType[];
  query: string;
  totalPages: number;
  currentPage: number;
  totalCount: number;
  categories: {
    id: string;
    name: string;
    slug: string;
  }[];
}

export default function SearchResultList({
  results,
  query,
  totalPages,
  currentPage,
  totalCount,
  categories,
}: SearchResultListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 필터 상태
  const [selectedTypes, setSelectedTypes] = useState<SearchResultType[]>(() => {
    const typesParam = searchParams.get("types");
    return typesParam ? (typesParam.split(",") as SearchResultType[]) : [];
  });

  const [selectedCategoryId, setSelectedCategoryId] = useState<string>(() => {
    return searchParams.get("categoryId") || "";
  });

  const [sortBy, setSortBy] = useState<string>(() => {
    return searchParams.get("sortBy") || "relevance";
  });

  const [sortOrder, setSortOrder] = useState<string>(() => {
    return searchParams.get("sortOrder") || "desc";
  });

  // URL 파라미터가 변경되면 상태 업데이트
  useEffect(() => {
    const typesParam = searchParams.get("types");
    if (typesParam) {
      setSelectedTypes(typesParam.split(",") as SearchResultType[]);
    } else {
      setSelectedTypes([]);
    }

    setSelectedCategoryId(searchParams.get("categoryId") || "");
    setSortBy(searchParams.get("sortBy") || "relevance");
    setSortOrder(searchParams.get("sortOrder") || "desc");
  }, [searchParams]);

  // 타입 필터 변경 핸들러
  const handleTypeChange = (type: SearchResultType) => {
    setSelectedTypes((prev) => {
      if (prev.includes(type)) {
        return prev.filter((t) => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  // 필터 적용 핸들러
  const applyFilters = () => {
    const params = new URLSearchParams(searchParams.toString());

    // 타입 필터
    if (selectedTypes.length > 0) {
      params.set("types", selectedTypes.join(","));
    } else {
      params.delete("types");
    }

    // 카테고리 필터
    if (selectedCategoryId) {
      params.set("categoryId", selectedCategoryId);
    } else {
      params.delete("categoryId");
    }

    // 정렬
    params.set("sortBy", sortBy);
    params.set("sortOrder", sortOrder);

    // 페이지 초기화
    params.set("page", "1");

    router.push(`/search?${params.toString()}`);
  };

  // 필터 초기화 핸들러
  const resetFilters = () => {
    setSelectedTypes([]);
    setSelectedCategoryId("");
    setSortBy("relevance");
    setSortOrder("desc");

    const params = new URLSearchParams(searchParams.toString());
    params.delete("types");
    params.delete("categoryId");
    params.delete("sortBy");
    params.delete("sortOrder");
    params.set("page", "1");

    router.push(`/search?${params.toString()}`);
  };

  // 페이지 변경 핸들러
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    router.push(`/search?${params.toString()}`);
  };

  // 정렬 순서 토글 핸들러
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "desc" ? "asc" : "desc");
  };

  return (
    <div className="space-y-6">
      {/* 필터 및 정렬 컨트롤 */}
      <div className="flex flex-col md:flex-row gap-4 p-4 bg-muted/50 rounded-lg">
        <div className="space-y-4 flex-grow">
          <div>
            <h3 className="text-sm font-medium mb-2 flex items-center">
              <Filter className="h-4 w-4 mr-1" /> 콘텐츠 유형
            </h3>
            <div className="flex flex-wrap gap-3">
              {Object.values(SearchResultType).map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={`type-${type}`}
                    checked={selectedTypes.includes(type)}
                    onCheckedChange={() => handleTypeChange(type)}
                  />
                  <Label htmlFor={`type-${type}`} className="text-sm">
                    {type === SearchResultType.LIFE_INFO && "생활 정보"}
                    {type === SearchResultType.SERVICE_GUIDE && "서비스 가이드"}
                    {type === SearchResultType.GOV_INFO && "정부 정보"}
                    {type === SearchResultType.JOB_POST && "구인·구직"}
                    {type === SearchResultType.POST && "커뮤니티"}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">카테고리</h3>
            <Select
              value={selectedCategoryId}
              onValueChange={setSelectedCategoryId}
            >
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="모든 카테고리" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">모든 카테고리</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">정렬 기준</h3>
            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full md:w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">관련성</SelectItem>
                  <SelectItem value="createdAt">등록일</SelectItem>
                  <SelectItem value="viewCount">조회수</SelectItem>
                  <SelectItem value="title">제목</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={toggleSortOrder}
                title={sortOrder === "desc" ? "내림차순" : "오름차순"}
              >
                {sortOrder === "desc" ? (
                  <SortDesc className="h-4 w-4" />
                ) : (
                  <SortAsc className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={applyFilters} className="flex-grow">
              필터 적용
            </Button>
            <Button variant="outline" onClick={resetFilters}>
              초기화
            </Button>
          </div>
        </div>
      </div>

      {/* 검색 결과 */}
      {results.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {results.map((item) => (
            <SearchResultItemCard
              key={`${item.type}-${item.id}`}
              item={item}
              query={query}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-xl font-medium">검색 결과가 없습니다.</p>
          <p className="text-muted-foreground mt-2">
            다른 검색어로 시도해보세요.
          </p>
        </div>
      )}

      {/* 페이지네이션 */}
      {totalPages > 1 && (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          baseUrl="/search"
          searchParams={{
            query,
            ...(selectedTypes.length > 0
              ? { types: selectedTypes.join(",") }
              : {}),
            ...(selectedCategoryId ? { categoryId: selectedCategoryId } : {}),
            sortBy,
            sortOrder,
          }}
        />
      )}
    </div>
  );
}
