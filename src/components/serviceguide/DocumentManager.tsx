'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Plus, Trash, MoveUp, MoveDown, FileText } from 'lucide-react';
import { createServiceGuideDocument, updateServiceGuideDocument, deleteServiceGuideDocument } from '@/actions/serviceguide';

// 문서 폼 스키마
const documentFormSchema = z.object({
  name: z.string().min(1, '문서 이름은 필수입니다.').max(255, '문서 이름은 최대 255자까지 가능합니다.'),
  description: z.string().optional(),
  required: z.boolean().default(true),
});

type DocumentFormValues = z.infer<typeof documentFormSchema>;

interface DocumentManagerProps {
  serviceGuideId: string;
  documents: any[];
}

export default function DocumentManager({ serviceGuideId, documents: initialDocuments }: DocumentManagerProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [documents, setDocuments] = useState(initialDocuments || []);
  const [editingDocument, setEditingDocument] = useState<any | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // 폼 초기화
  const form = useForm<DocumentFormValues>({
    resolver: zodResolver(documentFormSchema),
    defaultValues: {
      name: '',
      description: '',
      required: true,
    },
  });

  // 문서 추가 다이얼로그 열기
  const openAddDialog = () => {
    setEditingDocument(null);
    form.reset({
      name: '',
      description: '',
      required: true,
    });
    setIsDialogOpen(true);
  };

  // 문서 수정 다이얼로그 열기
  const openEditDialog = (document: any) => {
    setEditingDocument(document);
    form.reset({
      name: document.name,
      description: document.description || '',
      required: document.required,
    });
    setIsDialogOpen(true);
  };

  // 문서 추가/수정 폼 제출 핸들러
  const onSubmit = (values: DocumentFormValues) => {
    startTransition(async () => {
      try {
        if (editingDocument) {
          // 문서 수정
          const result = await updateServiceGuideDocument({
            id: editingDocument.id,
            ...values,
          });

          if (result.success) {
            toast.success('문서가 수정되었습니다.');
            setDocuments(documents.map(doc => 
              doc.id === editingDocument.id ? result.data : doc
            ));
          } else {
            toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
          }
        } else {
          // 문서 추가
          const result = await createServiceGuideDocument({
            serviceGuideId,
            ...values,
            order: documents.length,
          });

          if (result.success) {
            toast.success('문서가 추가되었습니다.');
            setDocuments([...documents, result.data]);
          } else {
            toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
          }
        }

        setIsDialogOpen(false);
        router.refresh();
      } catch (error) {
        toast.error('문서 저장 중 오류가 발생했습니다.');
        console.error(error);
      }
    });
  };

  // 문서 삭제 핸들러
  const handleDeleteDocument = (documentId: string) => {
    if (confirm('정말로 이 문서를 삭제하시겠습니까?')) {
      startTransition(async () => {
        try {
          const result = await deleteServiceGuideDocument({ id: documentId });

          if (result.success) {
            toast.success('문서가 삭제되었습니다.');
            setDocuments(documents.filter(doc => doc.id !== documentId));
            router.refresh();
          } else {
            toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
          }
        } catch (error) {
          toast.error('문서 삭제 중 오류가 발생했습니다.');
          console.error(error);
        }
      });
    }
  };

  // 문서 순서 변경 핸들러
  const handleMoveDocument = (documentId: string, direction: 'up' | 'down') => {
    const documentIndex = documents.findIndex(doc => doc.id === documentId);
    if (documentIndex === -1) return;

    const newDocuments = [...documents];
    
    if (direction === 'up' && documentIndex > 0) {
      // 위로 이동
      [newDocuments[documentIndex - 1], newDocuments[documentIndex]] = [newDocuments[documentIndex], newDocuments[documentIndex - 1]];
    } else if (direction === 'down' && documentIndex < documents.length - 1) {
      // 아래로 이동
      [newDocuments[documentIndex], newDocuments[documentIndex + 1]] = [newDocuments[documentIndex + 1], newDocuments[documentIndex]];
    } else {
      return;
    }

    // 순서 업데이트
    const updatedDocuments = newDocuments.map((doc, index) => ({
      ...doc,
      order: index,
    }));

    setDocuments(updatedDocuments);

    // 서버에 순서 변경 요청
    startTransition(async () => {
      try {
        // 각 문서의 순서를 개별적으로 업데이트
        for (const doc of updatedDocuments) {
          await updateServiceGuideDocument({
            id: doc.id,
            order: doc.order,
          });
        }
        
        toast.success('문서 순서가 변경되었습니다.');
      } catch (error) {
        toast.error('문서 순서 변경 중 오류가 발생했습니다.');
        console.error(error);
        // 실패 시 원래 순서로 복원
        setDocuments(initialDocuments);
      }
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">필요 서류 관리</h2>
        <Button onClick={openAddDialog}>
          <Plus className="mr-2 h-4 w-4" /> 서류 추가
        </Button>
      </div>

      {documents.length === 0 ? (
        <Card>
          <CardContent className="py-10 text-center">
            <p className="text-muted-foreground">아직 추가된 필요 서류가 없습니다.</p>
            <Button onClick={openAddDialog} variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> 첫 서류 추가하기
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>필요 서류 목록</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">#</TableHead>
                  <TableHead>서류명</TableHead>
                  <TableHead>설명</TableHead>
                  <TableHead className="w-24">필수 여부</TableHead>
                  <TableHead className="w-32 text-right">작업</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {documents.map((document, index) => (
                  <TableRow key={document.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell className="font-medium">{document.name}</TableCell>
                    <TableCell>{document.description || '-'}</TableCell>
                    <TableCell>
                      {document.required ? (
                        <Badge>필수</Badge>
                      ) : (
                        <Badge variant="outline">선택</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleMoveDocument(document.id, 'up')}
                          disabled={index === 0}
                        >
                          <MoveUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleMoveDocument(document.id, 'down')}
                          disabled={index === documents.length - 1}
                        >
                          <MoveDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => openEditDialog(document)}
                        >
                          <span className="sr-only">수정</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteDocument(document.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* 문서 추가/수정 다이얼로그 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingDocument ? '서류 수정' : '서류 추가'}
            </DialogTitle>
            <DialogDescription>
              {editingDocument
                ? '서비스 이용에 필요한 서류 정보를 수정합니다.'
                : '서비스 이용에 필요한 서류 정보를 추가합니다.'}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>서류명</FormLabel>
                    <FormControl>
                      <Input placeholder="서류 이름을 입력하세요" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>설명</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="서류에 대한 설명을 입력하세요"
                        className="resize-none"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>
                      서류 발급 방법이나 주의사항 등을 기재하세요.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="required"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>필수 서류</FormLabel>
                      <FormDescription>
                        이 서류가 필수 제출 서류인지 여부를 설정합니다.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              취소
            </Button>
            <Button 
              onClick={form.handleSubmit(onSubmit)}
              disabled={isPending}
            >
              {isPending ? '저장 중...' : editingDocument ? '수정하기' : '추가하기'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
