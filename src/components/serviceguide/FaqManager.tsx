'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { toast } from 'sonner';
import { Plus, Trash, MoveUp, MoveDown, HelpCircle } from 'lucide-react';
import { createServiceGuideFaq, updateServiceGuideFaq, deleteServiceGuideFaq } from '@/actions/serviceguide';

// FAQ 폼 스키마
const faqFormSchema = z.object({
  question: z.string().min(1, '질문은 필수입니다.'),
  answer: z.string().min(1, '답변은 필수입니다.'),
});

type FaqFormValues = z.infer<typeof faqFormSchema>;

interface FaqManagerProps {
  serviceGuideId: string;
  faqs: any[];
}

export default function FaqManager({ serviceGuideId, faqs: initialFaqs }: FaqManagerProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [faqs, setFaqs] = useState(initialFaqs || []);
  const [editingFaq, setEditingFaq] = useState<any | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // 폼 초기화
  const form = useForm<FaqFormValues>({
    resolver: zodResolver(faqFormSchema),
    defaultValues: {
      question: '',
      answer: '',
    },
  });

  // FAQ 추가 다이얼로그 열기
  const openAddDialog = () => {
    setEditingFaq(null);
    form.reset({
      question: '',
      answer: '',
    });
    setIsDialogOpen(true);
  };

  // FAQ 수정 다이얼로그 열기
  const openEditDialog = (faq: any) => {
    setEditingFaq(faq);
    form.reset({
      question: faq.question,
      answer: faq.answer,
    });
    setIsDialogOpen(true);
  };

  // FAQ 추가/수정 폼 제출 핸들러
  const onSubmit = (values: FaqFormValues) => {
    startTransition(async () => {
      try {
        if (editingFaq) {
          // FAQ 수정
          const result = await updateServiceGuideFaq({
            id: editingFaq.id,
            ...values,
          });

          if (result.success) {
            toast.success('FAQ가 수정되었습니다.');
            setFaqs(faqs.map(faq => 
              faq.id === editingFaq.id ? result.data : faq
            ));
          } else {
            toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
          }
        } else {
          // FAQ 추가
          const result = await createServiceGuideFaq({
            serviceGuideId,
            ...values,
            order: faqs.length,
          });

          if (result.success) {
            toast.success('FAQ가 추가되었습니다.');
            setFaqs([...faqs, result.data]);
          } else {
            toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
          }
        }

        setIsDialogOpen(false);
        router.refresh();
      } catch (error) {
        toast.error('FAQ 저장 중 오류가 발생했습니다.');
        console.error(error);
      }
    });
  };

  // FAQ 삭제 핸들러
  const handleDeleteFaq = (faqId: string) => {
    if (confirm('정말로 이 FAQ를 삭제하시겠습니까?')) {
      startTransition(async () => {
        try {
          const result = await deleteServiceGuideFaq({ id: faqId });

          if (result.success) {
            toast.success('FAQ가 삭제되었습니다.');
            setFaqs(faqs.filter(faq => faq.id !== faqId));
            router.refresh();
          } else {
            toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
          }
        } catch (error) {
          toast.error('FAQ 삭제 중 오류가 발생했습니다.');
          console.error(error);
        }
      });
    }
  };

  // FAQ 순서 변경 핸들러
  const handleMoveFaq = (faqId: string, direction: 'up' | 'down') => {
    const faqIndex = faqs.findIndex(faq => faq.id === faqId);
    if (faqIndex === -1) return;

    const newFaqs = [...faqs];
    
    if (direction === 'up' && faqIndex > 0) {
      // 위로 이동
      [newFaqs[faqIndex - 1], newFaqs[faqIndex]] = [newFaqs[faqIndex], newFaqs[faqIndex - 1]];
    } else if (direction === 'down' && faqIndex < faqs.length - 1) {
      // 아래로 이동
      [newFaqs[faqIndex], newFaqs[faqIndex + 1]] = [newFaqs[faqIndex + 1], newFaqs[faqIndex]];
    } else {
      return;
    }

    // 순서 업데이트
    const updatedFaqs = newFaqs.map((faq, index) => ({
      ...faq,
      order: index,
    }));

    setFaqs(updatedFaqs);

    // 서버에 순서 변경 요청
    startTransition(async () => {
      try {
        // 각 FAQ의 순서를 개별적으로 업데이트
        for (const faq of updatedFaqs) {
          await updateServiceGuideFaq({
            id: faq.id,
            order: faq.order,
          });
        }
        
        toast.success('FAQ 순서가 변경되었습니다.');
      } catch (error) {
        toast.error('FAQ 순서 변경 중 오류가 발생했습니다.');
        console.error(error);
        // 실패 시 원래 순서로 복원
        setFaqs(initialFaqs);
      }
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">자주 묻는 질문 관리</h2>
        <Button onClick={openAddDialog}>
          <Plus className="mr-2 h-4 w-4" /> FAQ 추가
        </Button>
      </div>

      {faqs.length === 0 ? (
        <Card>
          <CardContent className="py-10 text-center">
            <p className="text-muted-foreground">아직 추가된 FAQ가 없습니다.</p>
            <Button onClick={openAddDialog} variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> 첫 FAQ 추가하기
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>자주 묻는 질문 목록</CardTitle>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              {faqs.map((faq, index) => (
                <AccordionItem key={faq.id} value={faq.id}>
                  <div className="flex items-center">
                    <AccordionTrigger className="flex-1 hover:no-underline">
                      <div className="flex items-start">
                        <HelpCircle className="h-5 w-5 mr-2 flex-shrink-0 text-primary" />
                        <span className="text-left">{faq.question}</span>
                      </div>
                    </AccordionTrigger>
                    <div className="flex items-center space-x-1 mr-4">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMoveFaq(faq.id, 'up');
                        }}
                        disabled={index === 0}
                      >
                        <MoveUp className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMoveFaq(faq.id, 'down');
                        }}
                        disabled={index === faqs.length - 1}
                      >
                        <MoveDown className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          openEditDialog(faq);
                        }}
                      >
                        <span className="sr-only">수정</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteFaq(faq.id);
                        }}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <AccordionContent className="pl-7">
                    <div className="whitespace-pre-wrap">{faq.answer}</div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>
      )}

      {/* FAQ 추가/수정 다이얼로그 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingFaq ? 'FAQ 수정' : 'FAQ 추가'}
            </DialogTitle>
            <DialogDescription>
              {editingFaq
                ? '자주 묻는 질문과 답변을 수정합니다.'
                : '자주 묻는 질문과 답변을 추가합니다.'}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="question"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>질문</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="질문을 입력하세요"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="answer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>답변</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="답변을 입력하세요"
                        className="resize-none min-h-[150px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              취소
            </Button>
            <Button 
              onClick={form.handleSubmit(onSubmit)}
              disabled={isPending}
            >
              {isPending ? '저장 중...' : editingFaq ? '수정하기' : '추가하기'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
