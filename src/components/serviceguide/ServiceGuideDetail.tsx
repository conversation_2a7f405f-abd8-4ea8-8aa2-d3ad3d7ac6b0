'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';
import { CalendarIcon, Clock, Eye, ExternalLink, FileText, HelpCircle } from 'lucide-react';
import ContentRenderer from '@/components/editor/ContentRenderer';
import { incrementViewCount } from '@/actions/serviceguide';
import { formatDate } from '@/lib/utils';

interface ServiceGuideDetailProps {
  serviceGuide: any;
  currentUser: any;
  relatedGuides?: any[];
}

export default function ServiceGuideDetail({
  serviceGuide,
  currentUser,
  relatedGuides = [],
}: ServiceGuideDetailProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  // 조회수 증가
  useEffect(() => {
    const updateViewCount = async () => {
      try {
        await incrementViewCount({ id: serviceGuide.id });
      } catch (error) {
        console.error('조회수 업데이트 오류:', error);
      }
    };

    updateViewCount();
  }, [serviceGuide.id]);

  // 상태별 배지 색상
  const statusColors: Record<string, string> = {
    draft: 'default',
    published: 'success',
    archived: 'secondary',
  };

  // 상태 텍스트
  const statusText = {
    draft: '초안',
    published: '발행됨',
    archived: '보관됨',
  };

  // 관리자 여부 확인
  const isAdmin = currentUser?.isAdmin === true;

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant={statusColors[serviceGuide.status] as any}>
              {statusText[serviceGuide.status as keyof typeof statusText]}
            </Badge>
            {serviceGuide.featured && (
              <Badge variant="outline" className="bg-yellow-100 dark:bg-yellow-900">추천</Badge>
            )}
          </div>
          {isAdmin && (
            <Button
              variant="outline"
              onClick={() => router.push(`/admin/service-guide/edit/${serviceGuide.id}`)}
            >
              수정
            </Button>
          )}
        </div>
        <h1 className="text-3xl font-bold">{serviceGuide.title}</h1>
        {serviceGuide.summary && (
          <p className="text-lg text-muted-foreground">{serviceGuide.summary}</p>
        )}
        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center">
            <CalendarIcon className="mr-1 h-4 w-4" />
            <time dateTime={serviceGuide.publishedAt || serviceGuide.createdAt}>
              {formatDate(serviceGuide.publishedAt || serviceGuide.createdAt)}
            </time>
          </div>
          <div className="flex items-center">
            <Eye className="mr-1 h-4 w-4" />
            <span>{serviceGuide.viewCount} 조회</span>
          </div>
          <div className="flex items-center">
            <span className="mr-1">카테고리:</span>
            <Link
              href={`/service-guide?categoryId=${serviceGuide.category.id}`}
              className="hover:underline"
            >
              {serviceGuide.category.name}
            </Link>
          </div>
          <div className="flex items-center">
            <Avatar className="h-6 w-6 mr-1">
              <AvatarImage src={serviceGuide.author.image || ''} alt={serviceGuide.author.name} />
              <AvatarFallback>{serviceGuide.author.name.substring(0, 2)}</AvatarFallback>
            </Avatar>
            <span>{serviceGuide.author.displayName || serviceGuide.author.name}</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* 탭 내용 */}
      <Tabs defaultValue="overview" onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">개요</TabsTrigger>
          <TabsTrigger value="steps">단계별 가이드</TabsTrigger>
          <TabsTrigger value="documents">필요 서류</TabsTrigger>
          <TabsTrigger value="faq">자주 묻는 질문</TabsTrigger>
        </TabsList>

        {/* 개요 탭 */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <ContentRenderer content={serviceGuide.contentHtml} />
              
              {serviceGuide.officialLink && (
                <div className="mt-6">
                  <Button variant="outline" asChild>
                    <Link href={serviceGuide.officialLink} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      공식 웹사이트 방문하기
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 단계별 가이드 탭 */}
        <TabsContent value="steps" className="space-y-6">
          {serviceGuide.steps.length === 0 ? (
            <Card>
              <CardContent className="py-10 text-center">
                <p className="text-muted-foreground">아직 등록된 단계별 가이드가 없습니다.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {serviceGuide.steps.map((step: any, index: number) => (
                <Card key={step.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-8 h-8 mr-2">
                        {index + 1}
                      </span>
                      {step.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <ContentRenderer content={step.contentHtml} />
                    
                    {step.images && step.images.length > 0 && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        {step.images.map((image: any) => (
                          <div key={image.id} className="relative">
                            <div className="relative aspect-video overflow-hidden rounded-md">
                              <Image
                                src={image.url}
                                alt={image.alt || `${step.title} 이미지`}
                                fill
                                className="object-cover"
                              />
                            </div>
                            {image.caption && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {image.caption}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* 필요 서류 탭 */}
        <TabsContent value="documents" className="space-y-6">
          {serviceGuide.documents.length === 0 ? (
            <Card>
              <CardContent className="py-10 text-center">
                <p className="text-muted-foreground">아직 등록된 필요 서류가 없습니다.</p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>필요 서류 목록</CardTitle>
                <CardDescription>
                  서비스 이용에 필요한 서류 목록입니다. 필수 서류는 반드시 준비해야 합니다.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-4">
                  {serviceGuide.documents.map((document: any) => (
                    <li key={document.id} className="flex items-start">
                      <FileText className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0 text-primary" />
                      <div>
                        <div className="flex items-center">
                          <h3 className="font-medium">{document.name}</h3>
                          {document.required ? (
                            <Badge className="ml-2">필수</Badge>
                          ) : (
                            <Badge variant="outline" className="ml-2">선택</Badge>
                          )}
                        </div>
                        {document.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {document.description}
                          </p>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* FAQ 탭 */}
        <TabsContent value="faq" className="space-y-6">
          {serviceGuide.faqs.length === 0 ? (
            <Card>
              <CardContent className="py-10 text-center">
                <p className="text-muted-foreground">아직 등록된 자주 묻는 질문이 없습니다.</p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>자주 묻는 질문</CardTitle>
                <CardDescription>
                  서비스 이용에 관한 자주 묻는 질문과 답변입니다.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {serviceGuide.faqs.map((faq: any) => (
                    <AccordionItem key={faq.id} value={faq.id}>
                      <AccordionTrigger className="hover:no-underline">
                        <div className="flex items-start">
                          <HelpCircle className="h-5 w-5 mr-2 flex-shrink-0 text-primary" />
                          <span className="text-left">{faq.question}</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="pl-7">
                        <div className="whitespace-pre-wrap">{faq.answer}</div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* 관련 가이드 */}
      {relatedGuides.length > 0 && (
        <div className="mt-8">
          <h2 className="text-xl font-bold mb-4">관련 서비스 가이드</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {relatedGuides.map((guide: any) => (
              <Card key={guide.id} className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle className="line-clamp-2">
                    <Link href={`/service-guide/${guide.slug}`} className="hover:underline">
                      {guide.title}
                    </Link>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-grow">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {guide.summary}
                  </p>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" asChild className="w-full">
                    <Link href={`/service-guide/${guide.slug}`}>
                      자세히 보기
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
