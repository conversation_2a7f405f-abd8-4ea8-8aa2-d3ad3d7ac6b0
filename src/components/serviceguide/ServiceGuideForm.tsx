'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import TiptapEditor from '@/components/editor/TiptapEditor';
import ContentRenderer from '@/components/editor/ContentRenderer';
import { createServiceGuide, updateServiceGuide } from '@/actions/serviceguide';
import { toast } from 'sonner';
import { Category } from '@prisma/client';
import { slugify } from '@/lib/utils';

// 폼 스키마 정의
const formSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, '제목은 필수입니다.').max(255, '제목은 최대 255자까지 가능합니다.'),
  slug: z.string().min(1, '슬러그는 필수입니다.').max(255, '슬러그는 최대 255자까지 가능합니다.'),
  summary: z.string().max(500, '요약은 최대 500자까지 가능합니다.').optional(),
  content: z.string().min(1, '내용은 필수입니다.'),
  contentHtml: z.string().optional(),
  categoryId: z.string().min(1, '카테고리는 필수입니다.'),
  status: z.enum(['draft', 'published', 'archived']),
  featured: z.boolean().default(false),
  officialLink: z.string().url('유효한 URL을 입력해주세요.').optional().nullable(),
});

type FormValues = z.infer<typeof formSchema>;

interface ServiceGuideFormProps {
  categories: Category[];
  initialData?: any;
  isEdit?: boolean;
}

export default function ServiceGuideForm({
  categories,
  initialData,
  isEdit = false,
}: ServiceGuideFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [previewContent, setPreviewContent] = useState('');

  // 폼 초기화
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData
      ? {
          ...initialData,
          content: initialData.content || '',
          contentHtml: initialData.contentHtml || '',
          officialLink: initialData.officialLink || '',
        }
      : {
          title: '',
          slug: '',
          summary: '',
          content: '',
          contentHtml: '',
          categoryId: '',
          status: 'draft',
          featured: false,
          officialLink: '',
        },
  });

  // 에디터 내용 변경 핸들러
  const handleEditorChange = (html: string) => {
    form.setValue('contentHtml', html);
    setPreviewContent(html);
  };

  // 제목 변경 시 슬러그 자동 생성
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    form.setValue('title', title);
    
    // 슬러그가 수동으로 수정되지 않았거나 비어있는 경우에만 자동 생성
    const currentSlug = form.getValues('slug');
    if (!currentSlug || currentSlug === slugify(form.getValues('title'))) {
      form.setValue('slug', slugify(title));
    }
  };

  // 폼 제출 핸들러
  const onSubmit = (values: FormValues) => {
    startTransition(async () => {
      try {
        const action = isEdit ? updateServiceGuide : createServiceGuide;
        const result = await action(values);

        if (result.success) {
          toast.success(
            isEdit ? '서비스 가이드가 수정되었습니다.' : '서비스 가이드가 생성되었습니다.'
          );
          router.push(`/admin/service-guide/${result.data.id}`);
          router.refresh();
        } else {
          toast.error(`오류: ${result.error?.message || '알 수 없는 오류가 발생했습니다.'}`);
        }
      } catch (error) {
        toast.error('서비스 가이드 저장 중 오류가 발생했습니다.');
        console.error(error);
      }
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="editor">에디터</TabsTrigger>
                <TabsTrigger value="preview">미리보기</TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>제목</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="제목을 입력하세요" 
                          {...field} 
                          onChange={handleTitleChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>슬러그</FormLabel>
                      <FormControl>
                        <Input placeholder="슬러그를 입력하세요" {...field} />
                      </FormControl>
                      <FormDescription>
                        URL에 사용될 고유 식별자입니다. 영문, 숫자, 하이픈(-)만 사용 가능합니다.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="summary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>요약</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="간략한 요약을 입력하세요"
                          className="resize-none"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="officialLink"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>공식 링크</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="관련 기관/기업 공식 웹사이트 URL" 
                          {...field} 
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        관련 기관이나 기업의 공식 웹사이트 URL을 입력하세요.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contentHtml"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>내용</FormLabel>
                      <FormControl>
                        <TiptapEditor
                          content={form.getValues('contentHtml') || ''}
                          onChange={handleEditorChange}
                          placeholder="내용을 입력하세요..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              <TabsContent value="preview">
                <Card>
                  <CardContent className="pt-6">
                    <h1 className="text-2xl font-bold mb-2">{form.watch('title')}</h1>
                    {form.watch('summary') && (
                      <p className="text-muted-foreground mb-4">{form.watch('summary')}</p>
                    )}
                    <Separator className="my-4" />
                    <ContentRenderer content={previewContent || form.watch('contentHtml') || ''} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>상태</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="상태 선택" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">초안</SelectItem>
                            <SelectItem value="published">발행</SelectItem>
                            <SelectItem value="archived">보관</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>카테고리</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="카테고리 선택" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="featured"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>추천 콘텐츠</FormLabel>
                          <FormDescription>
                            이 서비스 가이드를 추천 콘텐츠로 표시합니다.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Button
              type="submit"
              className="w-full"
              disabled={isPending}
            >
              {isPending ? '저장 중...' : isEdit ? '수정하기' : '생성하기'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
