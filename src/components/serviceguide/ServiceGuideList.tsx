'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { CalendarIcon, Eye, Search } from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface ServiceGuideListProps {
  serviceGuides: any[];
  categories: any[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

export default function ServiceGuideList({
  serviceGuides,
  categories,
  pagination,
}: ServiceGuideListProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [searchQuery, setSearchQuery] = useState(searchParams.get('query') || '');
  const currentCategoryId = searchParams.get('categoryId') || '';
  const currentPage = Number(searchParams.get('page') || '1');

  // 카테고리 변경 핸들러
  const handleCategoryChange = (categoryId: string) => {
    const params = new URLSearchParams(searchParams);
    
    if (categoryId) {
      params.set('categoryId', categoryId);
    } else {
      params.delete('categoryId');
    }
    
    params.set('page', '1'); // 카테고리 변경 시 첫 페이지로 이동
    router.push(`/service-guide?${params.toString()}`);
  };

  // 검색 핸들러
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    const params = new URLSearchParams(searchParams);
    
    if (searchQuery.trim()) {
      params.set('query', searchQuery.trim());
    } else {
      params.delete('query');
    }
    
    params.set('page', '1'); // 검색 시 첫 페이지로 이동
    router.push(`/service-guide?${params.toString()}`);
  };

  // 페이지네이션 링크 생성 함수
  const createPageLink = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', page.toString());
    return `/service-guide?${params.toString()}`;
  };

  return (
    <div className="space-y-6">
      {/* 필터 및 검색 */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-64">
          <Select
            value={currentCategoryId || ''}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="모든 카테고리" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">모든 카테고리</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <form onSubmit={handleSearch} className="flex-1 flex gap-2">
          <Input
            placeholder="서비스 가이드 검색..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" variant="secondary">
            <Search className="h-4 w-4 mr-2" />
            검색
          </Button>
        </form>
      </div>

      {/* 서비스 가이드 목록 */}
      {serviceGuides.length === 0 ? (
        <Card>
          <CardContent className="py-10 text-center">
            <p className="text-muted-foreground">검색 결과가 없습니다.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {serviceGuides.map((guide) => (
            <Card key={guide.id} className="h-full flex flex-col">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    {guide.featured && (
                      <Badge variant="outline" className="bg-yellow-100 dark:bg-yellow-900 mb-2">
                        추천
                      </Badge>
                    )}
                  </div>
                  <Badge variant="secondary">
                    {guide.category.name}
                  </Badge>
                </div>
                <CardTitle className="line-clamp-2">
                  <Link href={`/service-guide/${guide.slug}`} className="hover:underline">
                    {guide.title}
                  </Link>
                </CardTitle>
                <CardDescription className="flex items-center text-xs">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  <time dateTime={guide.publishedAt || guide.createdAt}>
                    {formatDate(guide.publishedAt || guide.createdAt)}
                  </time>
                  <span className="mx-2">•</span>
                  <Eye className="h-3 w-3 mr-1" />
                  <span>{guide.viewCount} 조회</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {guide.summary}
                </p>
                
                {/* 단계 수 표시 */}
                {guide.steps && guide.steps.length > 0 && (
                  <div className="mt-4 flex items-center">
                    <Badge variant="outline">
                      단계 {guide.steps.length}개
                    </Badge>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" asChild className="w-full">
                  <Link href={`/service-guide/${guide.slug}`}>
                    자세히 보기
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* 페이지네이션 */}
      {pagination.totalPages > 1 && (
        <Pagination className="mt-8">
          <PaginationContent>
            {currentPage > 1 && (
              <PaginationItem>
                <PaginationPrevious href={createPageLink(currentPage - 1)} />
              </PaginationItem>
            )}
            
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
              .filter(page => 
                page === 1 || 
                page === pagination.totalPages || 
                (page >= currentPage - 1 && page <= currentPage + 1)
              )
              .map((page, index, array) => {
                // 현재 페이지와 이전 페이지 사이에 간격이 있는 경우 줄임표 표시
                if (index > 0 && array[index - 1] !== page - 1) {
                  return (
                    <PaginationItem key={`ellipsis-${page}`}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  );
                }
                
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href={createPageLink(page)}
                      isActive={page === currentPage}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              })
            }
            
            {currentPage < pagination.totalPages && (
              <PaginationItem>
                <PaginationNext href={createPageLink(currentPage + 1)} />
              </PaginationItem>
            )}
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
