"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  Plus,
  Trash,
  MoveUp,
  MoveDown,
  GripVertical,
  ImagePlus,
} from "lucide-react";
import TiptapEditor from "@/components/editor/TiptapEditor";
import ContentRenderer from "@/components/editor/ContentRenderer";
import {
  createServiceGuideStep,
  updateServiceGuideStep,
  deleteServiceGuideStep,
  reorderServiceGuideSteps,
} from "@/actions/serviceguide";
import { addStepImage } from "@/actions/serviceguide/images";
import ImageUpload, { UploadedImage } from "@/components/upload/ImageUpload";

// 단계 폼 스키마
const stepFormSchema = z.object({
  title: z
    .string()
    .min(1, "제목은 필수입니다.")
    .max(255, "제목은 최대 255자까지 가능합니다."),
  content: z.string().min(1, "내용은 필수입니다."),
  contentHtml: z.string().optional(),
});

type StepFormValues = z.infer<typeof stepFormSchema>;

interface StepManagerProps {
  serviceGuideId: string;
  steps: any[];
}

export default function StepManager({
  serviceGuideId,
  steps: initialSteps,
}: StepManagerProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [steps, setSteps] = useState(initialSteps || []);
  const [editingStep, setEditingStep] = useState<any | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [expandedSteps, setExpandedSteps] = useState<string[]>([]);
  const [uploadingStepId, setUploadingStepId] = useState<string | null>(null);

  // 폼 초기화
  const form = useForm<StepFormValues>({
    resolver: zodResolver(stepFormSchema),
    defaultValues: {
      title: "",
      content: "",
      contentHtml: "",
    },
  });

  // 에디터 내용 변경 핸들러
  const handleEditorChange = (html: string) => {
    form.setValue("contentHtml", html);
  };

  // 단계 추가 다이얼로그 열기
  const openAddDialog = () => {
    setEditingStep(null);
    form.reset({
      title: "",
      content: "",
      contentHtml: "",
    });
    setIsDialogOpen(true);
  };

  // 단계 수정 다이얼로그 열기
  const openEditDialog = (step: any) => {
    setEditingStep(step);
    form.reset({
      title: step.title,
      content: step.content,
      contentHtml: step.contentHtml,
    });
    setIsDialogOpen(true);
  };

  // 단계 추가/수정 폼 제출 핸들러
  const onSubmit = (values: StepFormValues) => {
    startTransition(async () => {
      try {
        if (editingStep) {
          // 단계 수정
          const result = await updateServiceGuideStep({
            id: editingStep.id,
            ...values,
          });

          if (result.success) {
            toast.success("단계가 수정되었습니다.");
            setSteps(
              steps.map((step) =>
                step.id === editingStep.id ? result.data : step
              )
            );
          } else {
            toast.error(
              `오류: ${
                result.error?.message || "알 수 없는 오류가 발생했습니다."
              }`
            );
          }
        } else {
          // 단계 추가
          const result = await createServiceGuideStep({
            serviceGuideId,
            ...values,
            order: steps.length,
          });

          if (result.success) {
            toast.success("단계가 추가되었습니다.");
            setSteps([...steps, result.data]);
          } else {
            toast.error(
              `오류: ${
                result.error?.message || "알 수 없는 오류가 발생했습니다."
              }`
            );
          }
        }

        setIsDialogOpen(false);
        router.refresh();
      } catch (error) {
        toast.error("단계 저장 중 오류가 발생했습니다.");
        console.error(error);
      }
    });
  };

  // 단계 삭제 핸들러
  const handleDeleteStep = (stepId: string) => {
    if (confirm("정말로 이 단계를 삭제하시겠습니까?")) {
      startTransition(async () => {
        try {
          const result = await deleteServiceGuideStep({ id: stepId });

          if (result.success) {
            toast.success("단계가 삭제되었습니다.");
            setSteps(steps.filter((step) => step.id !== stepId));
            router.refresh();
          } else {
            toast.error(
              `오류: ${
                result.error?.message || "알 수 없는 오류가 발생했습니다."
              }`
            );
          }
        } catch (error) {
          toast.error("단계 삭제 중 오류가 발생했습니다.");
          console.error(error);
        }
      });
    }
  };

  // 단계 순서 변경 핸들러
  const handleMoveStep = (stepId: string, direction: "up" | "down") => {
    const stepIndex = steps.findIndex((step) => step.id === stepId);
    if (stepIndex === -1) return;

    const newSteps = [...steps];

    if (direction === "up" && stepIndex > 0) {
      // 위로 이동
      [newSteps[stepIndex - 1], newSteps[stepIndex]] = [
        newSteps[stepIndex],
        newSteps[stepIndex - 1],
      ];
    } else if (direction === "down" && stepIndex < steps.length - 1) {
      // 아래로 이동
      [newSteps[stepIndex], newSteps[stepIndex + 1]] = [
        newSteps[stepIndex + 1],
        newSteps[stepIndex],
      ];
    } else {
      return;
    }

    // 순서 업데이트
    const updatedSteps = newSteps.map((step, index) => ({
      ...step,
      order: index,
    }));

    setSteps(updatedSteps);

    // 서버에 순서 변경 요청
    startTransition(async () => {
      try {
        const result = await reorderServiceGuideSteps({
          serviceGuideId,
          steps: updatedSteps.map((step) => ({
            id: step.id,
            order: step.order,
          })),
        });

        if (result.success) {
          toast.success("단계 순서가 변경되었습니다.");
        } else {
          toast.error(
            `오류: ${
              result.error?.message || "알 수 없는 오류가 발생했습니다."
            }`
          );
          // 실패 시 원래 순서로 복원
          setSteps(initialSteps);
        }
      } catch (error) {
        toast.error("단계 순서 변경 중 오류가 발생했습니다.");
        console.error(error);
        // 실패 시 원래 순서로 복원
        setSteps(initialSteps);
      }
    });
  };

  // 아코디언 토글 핸들러
  const toggleAccordion = (stepId: string) => {
    setExpandedSteps((prev) =>
      prev.includes(stepId)
        ? prev.filter((id) => id !== stepId)
        : [...prev, stepId]
    );
  };

  // 이미지 업로드 핸들러
  const handleImageUpload = (stepId: string, image: UploadedImage) => {
    startTransition(async () => {
      try {
        const result = await addStepImage({
          stepId,
          url: image.url,
          alt: image.alt,
          caption: image.caption,
        });

        if (result.success) {
          toast.success("이미지가 추가되었습니다.");
          // 단계 목록 업데이트
          setSteps(
            steps.map((step) => {
              if (step.id === stepId) {
                return {
                  ...step,
                  images: [...(step.images || []), result.data],
                };
              }
              return step;
            })
          );
          router.refresh();
        } else {
          toast.error(
            `오류: ${
              result.error?.message || "알 수 없는 오류가 발생했습니다."
            }`
          );
        }
      } catch (error) {
        toast.error("이미지 업로드 중 오류가 발생했습니다.");
        console.error(error);
      } finally {
        setUploadingStepId(null);
      }
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">단계 관리</h2>
        <Button onClick={openAddDialog}>
          <Plus className="mr-2 h-4 w-4" /> 단계 추가
        </Button>
      </div>

      {steps.length === 0 ? (
        <Card>
          <CardContent className="py-10 text-center">
            <p className="text-muted-foreground">
              아직 추가된 단계가 없습니다.
            </p>
            <Button onClick={openAddDialog} variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> 첫 단계 추가하기
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Accordion type="multiple" value={expandedSteps} className="space-y-4">
          {steps.map((step, index) => (
            <AccordionItem
              key={step.id}
              value={step.id}
              className="border rounded-lg overflow-hidden"
            >
              <div className="flex items-center px-4 py-2 bg-muted/50">
                <GripVertical className="h-5 w-5 text-muted-foreground mr-2" />
                <AccordionTrigger
                  onClick={() => toggleAccordion(step.id)}
                  className="flex-1 hover:no-underline"
                >
                  <div className="flex items-center">
                    <span className="font-medium mr-2">단계 {index + 1}:</span>
                    <span>{step.title}</span>
                  </div>
                </AccordionTrigger>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleMoveStep(step.id, "up")}
                    disabled={index === 0}
                  >
                    <MoveUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleMoveStep(step.id, "down")}
                    disabled={index === steps.length - 1}
                  >
                    <MoveDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openEditDialog(step)}
                  >
                    <span className="sr-only">수정</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-pencil"
                    >
                      <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                      <path d="m15 5 4 4" />
                    </svg>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteStep(step.id)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <AccordionContent className="pt-0">
                <div className="p-4 space-y-4">
                  <ContentRenderer content={step.contentHtml} />

                  {/* 이미지 섹션 */}
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">이미지</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {step.images && step.images.length > 0 ? (
                        step.images.map((image: any) => (
                          <div
                            key={image.id}
                            className="border rounded-md overflow-hidden"
                          >
                            <div className="relative w-full h-40">
                              <Image
                                src={image.url}
                                alt={image.alt || "단계 이미지"}
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              />
                            </div>
                            {image.caption && (
                              <div className="p-2 text-sm text-muted-foreground">
                                {image.caption}
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          이미지가 없습니다.
                        </p>
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => setUploadingStepId(step.id)}
                    >
                      <ImagePlus className="mr-2 h-4 w-4" /> 이미지 추가
                    </Button>

                    {uploadingStepId === step.id && (
                      <div className="mt-4 border rounded-md p-4">
                        <h5 className="font-medium mb-2">이미지 업로드</h5>
                        <ImageUpload
                          onImageUploaded={(image) =>
                            handleImageUpload(step.id, image)
                          }
                          maxSizeMB={5}
                          path={`service-guides/${serviceGuideId}/steps/${step.id}`}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2"
                          onClick={() => setUploadingStepId(null)}
                        >
                          취소
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}

      {/* 단계 추가/수정 다이얼로그 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>{editingStep ? "단계 수정" : "단계 추가"}</DialogTitle>
            <DialogDescription>
              {editingStep
                ? "서비스 이용 가이드의 단계를 수정합니다."
                : "서비스 이용 가이드에 새로운 단계를 추가합니다."}
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="max-h-[70vh] pr-4">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>제목</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="단계 제목을 입력하세요"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contentHtml"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>내용</FormLabel>
                      <FormControl>
                        <TiptapEditor
                          content={form.getValues("contentHtml") || ""}
                          onChange={handleEditorChange}
                          placeholder="단계 내용을 입력하세요..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </ScrollArea>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              취소
            </Button>
            <Button onClick={form.handleSubmit(onSubmit)} disabled={isPending}>
              {isPending ? "저장 중..." : editingStep ? "수정하기" : "추가하기"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
