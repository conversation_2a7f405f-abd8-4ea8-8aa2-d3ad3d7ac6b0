import { signIn } from "@/auth";
import { useTranslations } from "next-intl";

export default function SignIn() {
  const t = useTranslations("Auth");
  return (
    <form
      action={async () => {
        "use server";
        await signIn("google");
      }}
    >
      <button
        type="submit"
        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
      >
        {t("loginWithGoogle")}
      </button>
    </form>
  );
}
