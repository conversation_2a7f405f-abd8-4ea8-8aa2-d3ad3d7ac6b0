"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface SkipLinkProps extends React.HTMLAttributes<HTMLAnchorElement> {
  /**
   * 건너뛸 대상 요소의 ID
   */
  targetId: string
}

/**
 * 스킵 링크 컴포넌트
 * 
 * 키보드 사용자가 반복되는 네비게이션을 건너뛸 수 있도록 하는 접근성 기능
 * 기본적으로 화면에 보이지 않지만, 포커스를 받으면 화면 상단에 표시됨
 * 
 * @example
 * ```tsx
 * // 페이지 상단에 배치
 * <SkipLink targetId="main-content">본문으로 건너뛰기</SkipLink>
 * 
 * // 대상 요소에 ID 지정
 * <main id="main-content">...</main>
 * ```
 */
export function SkipLink({ 
  targetId, 
  children, 
  className, 
  ...props 
}: SkipLinkProps) {
  return (
    <a
      href={`#${targetId}`}
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:rounded-md",
        className
      )}
      {...props}
    >
      {children}
    </a>
  )
}
