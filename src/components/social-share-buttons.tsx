'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
  Facebook,
  Twitter,
  Linkedin,
  Mail,
  Copy,
  Share2,
  X,
} from 'lucide-react';
import {
  getFacebookShareUrl,
  getTwitterShareUrl,
  getLinkedInShareUrl,
  getEmailShareUrl,
  copyToClipboard,
} from '@/lib/social-share';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from '@/components/ui/sheet';

interface SocialShareButtonsProps {
  /**
   * 공유할 URL (기본값: 현재 URL)
   */
  url?: string;
  
  /**
   * 공유 제목
   */
  title?: string;
  
  /**
   * 공유 설명
   */
  description?: string;
  
  /**
   * 공유 이미지 URL
   */
  imageUrl?: string;
  
  /**
   * 해시태그 (쉼표로 구분)
   */
  hashtags?: string;
  
  /**
   * 트위터 계정명
   */
  via?: string;
  
  /**
   * 버튼 크기
   */
  size?: 'sm' | 'default' | 'lg' | 'icon';
  
  /**
   * 버튼 변형
   */
  variant?: 'default' | 'outline' | 'ghost';
  
  /**
   * 버튼 레이아웃 (수평 또는 수직)
   */
  layout?: 'horizontal' | 'vertical';
  
  /**
   * 모바일에서 시트로 표시할지 여부
   */
  useSheet?: boolean;
}

/**
 * 소셜 미디어 공유 버튼 컴포넌트
 */
export function SocialShareButtons({
  url = typeof window !== 'undefined' ? window.location.href : '',
  title = '소담 - 한국 생활 정보 플랫폼',
  description = '외국인 근로자를 위한 한국 생활 정보 및 구인구직 플랫폼',
  imageUrl,
  hashtags = 'sodamm,한국생활,외국인근로자',
  via = 'sodamm',
  size = 'default',
  variant = 'outline',
  layout = 'horizontal',
  useSheet = true,
}: SocialShareButtonsProps) {
  const [isCopied, setIsCopied] = useState(false);
  
  // 클립보드에 URL 복사
  const handleCopyLink = async () => {
    const success = await copyToClipboard(url);
    setIsCopied(success);
    
    if (success) {
      toast.success('링크가 클립보드에 복사되었습니다.');
      setTimeout(() => setIsCopied(false), 3000);
    } else {
      toast.error('링크 복사에 실패했습니다.');
    }
  };
  
  // 소셜 미디어 공유 URL 생성
  const facebookUrl = getFacebookShareUrl(url);
  const twitterUrl = getTwitterShareUrl(url, title, hashtags, via);
  const linkedinUrl = getLinkedInShareUrl(url, title, description);
  const emailUrl = getEmailShareUrl(url, title, description);
  
  // 소셜 미디어 공유 핸들러
  const handleShare = (shareUrl: string) => {
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };
  
  // 모바일 네이티브 공유 API 사용 가능 여부 확인
  const canUseNativeShare = typeof navigator !== 'undefined' && navigator.share;
  
  // 네이티브 공유 핸들러
  const handleNativeShare = async () => {
    if (!canUseNativeShare) return;
    
    try {
      await navigator.share({
        title,
        text: description,
        url,
      });
      toast.success('공유되었습니다.');
    } catch (error) {
      console.error('공유 실패:', error);
    }
  };
  
  // 공유 버튼 렌더링
  const renderShareButtons = () => (
    <div className={`flex ${layout === 'vertical' ? 'flex-col' : 'flex-row'} gap-2`}>
      <Button
        size={size}
        variant={variant}
        onClick={() => handleShare(facebookUrl)}
        aria-label="페이스북에 공유"
      >
        <Facebook className="h-4 w-4 mr-2" />
        {size !== 'icon' && '페이스북'}
      </Button>
      
      <Button
        size={size}
        variant={variant}
        onClick={() => handleShare(twitterUrl)}
        aria-label="트위터에 공유"
      >
        <Twitter className="h-4 w-4 mr-2" />
        {size !== 'icon' && '트위터'}
      </Button>
      
      <Button
        size={size}
        variant={variant}
        onClick={() => handleShare(linkedinUrl)}
        aria-label="링크드인에 공유"
      >
        <Linkedin className="h-4 w-4 mr-2" />
        {size !== 'icon' && '링크드인'}
      </Button>
      
      <Button
        size={size}
        variant={variant}
        onClick={() => handleShare(emailUrl)}
        aria-label="이메일로 공유"
      >
        <Mail className="h-4 w-4 mr-2" />
        {size !== 'icon' && '이메일'}
      </Button>
      
      <Button
        size={size}
        variant={variant}
        onClick={handleCopyLink}
        aria-label="링크 복사"
      >
        <Copy className="h-4 w-4 mr-2" />
        {size !== 'icon' && (isCopied ? '복사됨' : '링크 복사')}
      </Button>
      
      {canUseNativeShare && (
        <Button
          size={size}
          variant="default"
          onClick={handleNativeShare}
          aria-label="공유"
        >
          <Share2 className="h-4 w-4 mr-2" />
          {size !== 'icon' && '공유'}
        </Button>
      )}
    </div>
  );
  
  // 모바일에서 시트로 표시
  if (useSheet && typeof window !== 'undefined' && window.innerWidth < 768) {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <Button
            size={size}
            variant={variant}
            aria-label="공유 옵션 보기"
          >
            <Share2 className="h-4 w-4 mr-2" />
            {size !== 'icon' && '공유'}
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom">
          <SheetHeader>
            <SheetTitle>공유하기</SheetTitle>
            <SheetDescription>
              이 페이지를 소셜 미디어에 공유하세요.
            </SheetDescription>
          </SheetHeader>
          <div className="py-4">
            {renderShareButtons()}
          </div>
          <SheetClose asChild>
            <Button
              variant="outline"
              className="w-full mt-4"
              aria-label="닫기"
            >
              <X className="h-4 w-4 mr-2" />
              닫기
            </Button>
          </SheetClose>
        </SheetContent>
      </Sheet>
    );
  }
  
  // 데스크톱에서는 일반 버튼으로 표시
  return renderShareButtons();
}
