import { cn } from '@/lib/utils';
import React from 'react';

interface SpacerProps {
  size?: number | string;
  axis?: 'horizontal' | 'vertical';
  className?: string;
}

export function Spacer({
  size = 1,
  axis = 'vertical',
  className,
  ...props
}: SpacerProps & React.HTMLAttributes<HTMLDivElement>) {
  const width = axis === 'horizontal' ? size : 1;
  const height = axis === 'vertical' ? size : 1;

  return (
    <div
      className={cn(className)}
      style={{
        width: typeof width === 'number' ? `${width}rem` : width,
        height: typeof height === 'number' ? `${height}rem` : height,
        minWidth: typeof width === 'number' ? `${width}rem` : width,
        minHeight: typeof height === 'number' ? `${height}rem` : height,
        display: 'block',
      }}
      {...props}
    />
  );
}
