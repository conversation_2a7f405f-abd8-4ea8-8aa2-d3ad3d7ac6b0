'use client';

/**
 * 스토어 프로바이더 컴포넌트
 * 모든 Zustand 스토어를 통합하여 제공
 */

import { useInitializeFromSession } from '@/store/user-store';
import { Session } from 'next-auth';
import { useSession } from 'next-auth/react';
import { ReactNode, useEffect } from 'react';

interface StoreProviderProps {
  children: ReactNode;
  initialSession?: Session | null;
}

/**
 * 스토어 프로바이더 컴포넌트
 * 모든 Zustand 스토어를 초기화하고 관리
 */
export function StoreProvider({
  children,
  initialSession,
}: StoreProviderProps) {
  const { data: session, status } = useSession();
  const initializeFromSession = useInitializeFromSession();
  
  // 세션 정보로 사용자 스토어 초기화
  useEffect(() => {
    // 초기 세션이 있으면 사용
    if (initialSession) {
      initializeFromSession(initialSession);
    } 
    // 클라이언트 세션이 로드되면 업데이트
    else if (status === 'authenticated' && session) {
      initializeFromSession(session);
    } 
    // 로그아웃 상태면 초기화
    else if (status === 'unauthenticated') {
      initializeFromSession(null);
    }
  }, [session, status, initializeFromSession, initialSession]);
  
  return <>{children}</>;
}
