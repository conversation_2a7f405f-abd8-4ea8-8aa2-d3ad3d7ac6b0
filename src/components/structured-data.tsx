'use client';

import { useEffect, useState } from 'react';

interface StructuredDataProps {
  data: Record<string, any>;
}

/**
 * 구조화된 데이터(JSON-LD)를 페이지에 추가하는 컴포넌트
 * 
 * @param data JSON-LD 형식의 구조화된 데이터 객체
 */
export function StructuredData({ data }: StructuredDataProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    return () => {
      setMounted(false);
    };
  }, []);

  if (!mounted) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}

/**
 * 여러 구조화된 데이터를 페이지에 추가하는 컴포넌트
 * 
 * @param dataArray JSON-LD 형식의 구조화된 데이터 객체 배열
 */
export function MultipleStructuredData({ dataArray }: { dataArray: Record<string, any>[] }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    return () => {
      setMounted(false);
    };
  }, []);

  if (!mounted) return null;

  return (
    <>
      {dataArray.map((data, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
        />
      ))}
    </>
  );
}
