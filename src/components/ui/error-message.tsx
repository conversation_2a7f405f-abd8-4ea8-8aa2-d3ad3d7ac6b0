'use client';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { AlertCircle, AlertTriangle, Ban, Info, XCircle } from 'lucide-react';
import { ReactNode } from 'react';

/**
 * 에러 메시지 타입
 */
export type ErrorType = 'error' | 'warning' | 'info' | 'validation' | 'auth';

/**
 * 에러 메시지 컴포넌트 속성
 */
export interface ErrorMessageProps {
  /** 에러 메시지 타입 */
  type?: ErrorType;
  /** 에러 제목 */
  title?: string;
  /** 에러 메시지 */
  message?: string | ReactNode;
  /** 에러 코드 */
  code?: string;
  /** 추가 CSS 클래스 */
  className?: string;
  /** 에러 메시지 표시 여부 */
  visible?: boolean;
  /** 에러 메시지 닫기 핸들러 */
  onClose?: () => void;
  /** 에러 메시지 아이콘 */
  icon?: ReactNode;
  /** 에러 메시지 하단 액션 */
  action?: ReactNode;
}

/**
 * 에러 메시지 컴포넌트
 * 
 * 다양한 타입의 에러 메시지를 표시하는 컴포넌트입니다.
 * 
 * @example
 * ```tsx
 * // 기본 에러 메시지
 * <ErrorMessage 
 *   type="error" 
 *   title="오류가 발생했습니다" 
 *   message="요청을 처리하는 중 오류가 발생했습니다." 
 * />
 * 
 * // 유효성 검사 에러 메시지
 * <ErrorMessage 
 *   type="validation" 
 *   title="입력 오류" 
 *   message="이메일 형식이 올바르지 않습니다." 
 * />
 * 
 * // 인증 에러 메시지
 * <ErrorMessage 
 *   type="auth" 
 *   title="인증 필요" 
 *   message="이 기능을 사용하려면 로그인이 필요합니다." 
 *   action={<Button>로그인</Button>}
 * />
 * ```
 */
export function ErrorMessage({
  type = 'error',
  title,
  message,
  code,
  className,
  visible = true,
  onClose,
  icon,
  action,
}: ErrorMessageProps) {
  // 타입에 따른 기본 제목
  const defaultTitles: Record<ErrorType, string> = {
    error: '오류가 발생했습니다',
    warning: '주의',
    info: '안내',
    validation: '입력 오류',
    auth: '인증 필요',
  };

  // 타입에 따른 기본 아이콘
  const defaultIcons: Record<ErrorType, ReactNode> = {
    error: <XCircle className="h-4 w-4" />,
    warning: <AlertTriangle className="h-4 w-4" />,
    info: <Info className="h-4 w-4" />,
    validation: <AlertCircle className="h-4 w-4" />,
    auth: <Ban className="h-4 w-4" />,
  };

  // 타입에 따른 Alert 변형
  const alertVariants: Record<ErrorType, string> = {
    error: 'destructive',
    warning: 'warning',
    info: 'info',
    validation: 'destructive',
    auth: 'default',
  };

  // 표시 여부 확인
  if (!visible) {
    return null;
  }

  // 사용할 제목과 아이콘 결정
  const displayTitle = title || defaultTitles[type];
  const displayIcon = icon || defaultIcons[type];

  return (
    <Alert
      variant={alertVariants[type]}
      className={cn('flex flex-col gap-2', className)}
    >
      <div className="flex items-center gap-2">
        {displayIcon}
        <AlertTitle>{displayTitle}</AlertTitle>
      </div>
      
      {message && (
        <AlertDescription className="mt-1">
          {message}
          {code && (
            <span className="text-xs opacity-70 block mt-1">
              코드: {code}
            </span>
          )}
        </AlertDescription>
      )}
      
      {action && (
        <div className="mt-2">
          {action}
        </div>
      )}
    </Alert>
  );
}
