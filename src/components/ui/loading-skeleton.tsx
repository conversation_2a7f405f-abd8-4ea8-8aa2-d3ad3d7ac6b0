'use client';

import { Skeleton } from '@/components/ui/skeleton';

interface LoadingSkeletonProps {
  /**
   * 스켈레톤 타입
   */
  type?: 'card' | 'list' | 'table' | 'profile' | 'comment' | 'post';
  
  /**
   * 반복 횟수
   */
  count?: number;
  
  /**
   * 추가 클래스명
   */
  className?: string;
}

/**
 * 로딩 상태를 표시하는 스켈레톤 컴포넌트
 */
export function LoadingSkeleton({
  type = 'card',
  count = 1,
  className = '',
}: LoadingSkeletonProps) {
  const renderSkeleton = () => {
    switch (type) {
      case 'card':
        return (
          <div className={`space-y-3 ${className}`}>
            <Skeleton className="h-5 w-2/3" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
          </div>
        );
        
      case 'list':
        return (
          <div className={`space-y-2 ${className}`}>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        );
        
      case 'table':
        return (
          <div className={`space-y-2 ${className}`}>
            <div className="flex gap-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
            </div>
            <Skeleton className="h-px w-full" />
            <div className="flex gap-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
            </div>
          </div>
        );
        
      case 'profile':
        return (
          <div className={`flex items-center gap-3 ${className}`}>
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        );
        
      case 'comment':
        return (
          <div className={`space-y-2 ${className}`}>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>
        );
        
      case 'post':
        return (
          <div className={`space-y-3 ${className}`}>
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <div className="flex gap-2 pt-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        );
        
      default:
        return (
          <div className={`space-y-2 ${className}`}>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        );
    }
  };
  
  return (
    <div className="w-full">
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <div key={index} className="mb-4">
            {renderSkeleton()}
          </div>
        ))}
    </div>
  );
}
