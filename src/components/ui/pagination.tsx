'use client';

import * as React from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

import { cn } from '@/lib/utils';
import { ButtonProps, buttonVariants } from '@/components/ui/button';

interface PaginationProps extends React.ComponentProps<'nav'> {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  searchParams?: Record<string, string | undefined>;
}

function Pagination({
  currentPage,
  totalPages,
  baseUrl,
  searchParams = {},
  className,
  ...props
}: PaginationProps) {
  // 페이지 URL 생성 함수
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams();
    
    // 현재 검색 파라미터 유지
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && key !== 'page') {
        params.set(key, value);
      }
    });
    
    // 페이지 파라미터 설정
    params.set('page', page.toString());
    
    return `${baseUrl}?${params.toString()}`;
  };

  // 표시할 페이지 번호 계산
  const getPageNumbers = () => {
    const pageNumbers = [];
    
    // 항상 첫 페이지 표시
    pageNumbers.push(1);
    
    // 현재 페이지 주변 페이지 표시
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      pageNumbers.push(i);
    }
    
    // 항상 마지막 페이지 표시 (1페이지가 아닌 경우)
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    // 중복 제거 및 정렬
    return [...new Set(pageNumbers)].sort((a, b) => a - b);
  };

  const pageNumbers = getPageNumbers();

  return (
    <nav
      className={cn('flex justify-center', className)}
      {...props}
    >
      <ul className="flex items-center gap-1">
        {/* 이전 페이지 버튼 */}
        <li>
          <Link
            href={currentPage > 1 ? createPageUrl(currentPage - 1) : '#'}
            className={cn(
              buttonVariants({ variant: 'outline', size: 'icon' }),
              currentPage <= 1 && 'pointer-events-none opacity-50'
            )}
            aria-disabled={currentPage <= 1}
            tabIndex={currentPage <= 1 ? -1 : undefined}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">이전 페이지</span>
          </Link>
        </li>
        
        {/* 페이지 번호 */}
        {pageNumbers.map((page, index) => {
          // 이전 페이지와 현재 페이지 사이에 간격이 있는 경우 줄임표 표시
          const showEllipsis = index > 0 && pageNumbers[index - 1] !== page - 1;
          
          return (
            <React.Fragment key={page}>
              {showEllipsis && (
                <li className="flex items-center justify-center h-9 w-9">
                  <MoreHorizontal className="h-4 w-4" />
                </li>
              )}
              <li>
                <Link
                  href={createPageUrl(page)}
                  className={cn(
                    buttonVariants({ variant: page === currentPage ? 'default' : 'outline', size: 'icon' }),
                    'h-9 w-9'
                  )}
                  aria-current={page === currentPage ? 'page' : undefined}
                >
                  {page}
                </Link>
              </li>
            </React.Fragment>
          );
        })}
        
        {/* 다음 페이지 버튼 */}
        <li>
          <Link
            href={currentPage < totalPages ? createPageUrl(currentPage + 1) : '#'}
            className={cn(
              buttonVariants({ variant: 'outline', size: 'icon' }),
              currentPage >= totalPages && 'pointer-events-none opacity-50'
            )}
            aria-disabled={currentPage >= totalPages}
            tabIndex={currentPage >= totalPages ? -1 : undefined}
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">다음 페이지</span>
          </Link>
        </li>
      </ul>
    </nav>
  );
}

export { Pagination };
