'use client';

import { Toaster as Sonner, ToasterProps } from 'sonner';

const Toaster = ({ ...props }: ToasterProps) => {
  // 항상 라이트 테마 사용 (사용자 요구사항에 따라 클래스 모드만 지원)
  const theme = 'light';

  // CSS 변수를 포함한 스타일 객체 정의
  const toasterStyle: React.CSSProperties = {
    '--normal-bg': 'var(--popover)',
    '--normal-text': 'var(--popover-foreground)',
    '--normal-border': 'var(--border)',
  };

  return (
    <Sonner
      theme={theme}
      className="toaster group"
      style={toasterStyle}
      {...props}
    />
  );
};

export { Toaster };
