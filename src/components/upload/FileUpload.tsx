'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Loader2, 
  X, 
  Upload, 
  File as FileIcon,
  Image as ImageIcon,
  FileText,
  FilePdf
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  uploadFile, 
  StorageBucket, 
  BUCKET_CONFIGS,
  FileUploadResult,
  optimizeImage
} from '@/lib/storage';

export interface UploadedFile {
  url: string;
  path: string;
  size: number;
  mimeType: string;
  name: string;
  alt?: string;
  caption?: string;
}

interface FileUploadProps {
  onFileUploaded: (file: UploadedFile) => void;
  bucket: StorageBucket;
  path?: string;
  allowOptimization?: boolean;
  showPreview?: boolean;
  maxSizeMB?: number;
  allowedMimeTypes?: string[];
}

export default function FileUpload({
  onFileUploaded,
  bucket,
  path = '',
  allowOptimization = true,
  showPreview = true,
  maxSizeMB,
  allowedMimeTypes,
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [alt, setAlt] = useState('');
  const [caption, setCaption] = useState('');
  const [optimize, setOptimize] = useState(true);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 버킷 설정 가져오기
  const bucketConfig = BUCKET_CONFIGS[bucket];
  const effectiveMaxSize = maxSizeMB || bucketConfig.maxFileSize;
  const effectiveAllowedTypes = allowedMimeTypes || bucketConfig.allowedMimeTypes;

  // 파일 선택 처리
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 파일 크기 검증
    if (file.size > effectiveMaxSize * 1024 * 1024) {
      toast.error(`파일 크기는 ${effectiveMaxSize}MB 이하여야 합니다.`);
      return;
    }

    // 파일 타입 검증
    if (
      !effectiveAllowedTypes.includes('*/*') && 
      !effectiveAllowedTypes.includes(file.type)
    ) {
      toast.error(`지원되지 않는 파일 형식입니다. 허용된 형식: ${effectiveAllowedTypes.join(', ')}`);
      return;
    }

    setSelectedFile(file);

    // 이미지 파일인 경우 미리보기 생성
    if (file.type.startsWith('image/') && showPreview) {
      const reader = new FileReader();
      reader.onload = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
    }
  };

  // 파일 업로드 처리
  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('업로드할 파일을 선택해주세요.');
      return;
    }

    setIsUploading(true);

    try {
      let fileToUpload = selectedFile;
      
      // 이미지 최적화 (이미지 파일이고 최적화 옵션이 활성화된 경우)
      if (
        selectedFile.type.startsWith('image/') && 
        allowOptimization && 
        optimize
      ) {
        const optimized = await optimizeImage(selectedFile, {
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 0.8,
          format: 'webp',
        });
        
        if (!optimized.error) {
          fileToUpload = optimized.file;
          toast.success(`이미지가 최적화되었습니다. (${Math.round((1 - optimized.size / optimized.originalSize) * 100)}% 감소)`);
        } else {
          toast.error(`이미지 최적화 실패: ${optimized.error}`);
        }
      }

      // 파일 업로드
      const result: FileUploadResult = await uploadFile(
        fileToUpload,
        bucket,
        path
      );

      if (result.error) {
        toast.error(`업로드 실패: ${result.error}`);
        return;
      }

      // 업로드 성공 시 콜백 호출
      onFileUploaded({
        url: result.url,
        path: result.path,
        size: result.size,
        mimeType: result.mimeType,
        name: selectedFile.name,
        alt,
        caption,
      });

      // 상태 초기화
      setPreview(null);
      setAlt('');
      setCaption('');
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      toast.success('파일이 성공적으로 업로드되었습니다.');
    } catch (error) {
      console.error('파일 업로드 중 오류 발생:', error);
      toast.error('파일 업로드 중 오류가 발생했습니다.');
    } finally {
      setIsUploading(false);
    }
  };

  // 미리보기 취소
  const handleCancelPreview = () => {
    setPreview(null);
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 파일 아이콘 선택
  const getFileIcon = () => {
    if (!selectedFile) return <FileIcon className="h-12 w-12" />;
    
    if (selectedFile.type.startsWith('image/')) {
      return <ImageIcon className="h-12 w-12" />;
    } else if (selectedFile.type === 'application/pdf') {
      return <FilePdf className="h-12 w-12" />;
    } else if (selectedFile.type.startsWith('text/')) {
      return <FileText className="h-12 w-12" />;
    } else {
      return <FileIcon className="h-12 w-12" />;
    }
  };

  return (
    <div className="space-y-4">
      {!selectedFile ? (
        <div className="grid w-full items-center gap-1.5">
          <Label htmlFor="file">파일</Label>
          <Input
            id="file"
            type="file"
            accept={effectiveAllowedTypes.join(',')}
            ref={fileInputRef}
            onChange={handleFileChange}
          />
          <p className="text-sm text-muted-foreground">
            최대 {effectiveMaxSize}MB, 
            {effectiveAllowedTypes.includes('*/*') 
              ? '모든 파일 형식 지원' 
              : `지원 형식: ${effectiveAllowedTypes.join(', ')}`}
          </p>
        </div>
      ) : (
        <Card>
          <CardContent className="p-4 space-y-4">
            {preview ? (
              <div className="relative">
                <div className="relative aspect-video w-full overflow-hidden rounded-md">
                  <Image
                    src={preview}
                    alt="미리보기"
                    fill
                    className="object-cover"
                  />
                </div>
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2"
                  onClick={handleCancelPreview}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center justify-center p-8 border border-dashed rounded-md">
                <div className="flex flex-col items-center space-y-2">
                  {getFileIcon()}
                  <div className="text-sm font-medium">{selectedFile.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelPreview}
                  >
                    <X className="h-4 w-4 mr-2" />
                    취소
                  </Button>
                </div>
              </div>
            )}

            {selectedFile.type.startsWith('image/') && (
              <>
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="alt">대체 텍스트</Label>
                  <Input
                    id="alt"
                    placeholder="이미지 대체 텍스트 (접근성 향상)"
                    value={alt}
                    onChange={(e) => setAlt(e.target.value)}
                  />
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="caption">캡션</Label>
                  <Textarea
                    id="caption"
                    placeholder="이미지 캡션 (선택 사항)"
                    value={caption}
                    onChange={(e) => setCaption(e.target.value)}
                  />
                </div>

                {allowOptimization && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="optimize"
                      checked={optimize}
                      onChange={(e) => setOptimize(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <Label htmlFor="optimize" className="text-sm">
                      이미지 최적화 (크기 및 품질 조정)
                    </Label>
                  </div>
                )}
              </>
            )}

            <Button
              className="w-full"
              onClick={handleUpload}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  업로드 중...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  업로드
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
