'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Trash2, 
  Edit, 
  Copy, 
  ExternalLink,
  MoreHorizontal,
  ImagePlus
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { deleteFileByUrl } from '@/lib/storage';
import FileUpload, { UploadedFile } from './FileUpload';
import { StorageBucket } from '@/lib/storage';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';

export interface GalleryImage {
  id: string;
  url: string;
  alt?: string;
  caption?: string;
}

interface ImageGalleryProps {
  images: GalleryImage[];
  onImageAdd?: (image: UploadedFile) => void;
  onImageDelete?: (imageId: string) => void;
  onImageSelect?: (image: GalleryImage) => void;
  bucket?: StorageBucket;
  path?: string;
  editable?: boolean;
  selectable?: boolean;
  maxImages?: number;
}

export default function ImageGallery({
  images,
  onImageAdd,
  onImageDelete,
  onImageSelect,
  bucket = StorageBucket.LIFE_INFO,
  path = 'images',
  editable = true,
  selectable = false,
  maxImages = 0,
}: ImageGalleryProps) {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  // 이미지 삭제 처리
  const handleDeleteImage = async (image: GalleryImage) => {
    if (!onImageDelete) return;

    try {
      // 사용자 확인
      if (!confirm('이미지를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.')) {
        return;
      }

      // Supabase Storage에서 이미지 삭제
      const result = await deleteFileByUrl(image.url);
      
      if (!result.success) {
        toast.error(`이미지 삭제 실패: ${result.error}`);
        return;
      }

      // 콜백 호출
      onImageDelete(image.id);
      toast.success('이미지가 성공적으로 삭제되었습니다.');
    } catch (error) {
      console.error('이미지 삭제 중 오류 발생:', error);
      toast.error('이미지 삭제 중 오류가 발생했습니다.');
    }
  };

  // 이미지 URL 복사
  const handleCopyImageUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    toast.success('이미지 URL이 클립보드에 복사되었습니다.');
  };

  // 이미지 선택 처리
  const handleSelectImage = (image: GalleryImage) => {
    if (onImageSelect) {
      onImageSelect(image);
    }
  };

  // 새 이미지 업로드 처리
  const handleImageUploaded = (file: UploadedFile) => {
    if (onImageAdd) {
      onImageAdd(file);
      setIsUploadDialogOpen(false);
    }
  };

  // 이미지 새 탭에서 열기
  const handleOpenInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  // 최대 이미지 수 확인
  const canAddMoreImages = maxImages === 0 || images.length < maxImages;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">이미지 갤러리</h3>
        {editable && canAddMoreImages && (
          <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <ImagePlus className="h-4 w-4 mr-2" />
                이미지 추가
              </Button>
            </DialogTrigger>
            <DialogContent>
              <div className="p-4">
                <h3 className="text-lg font-medium mb-4">새 이미지 업로드</h3>
                <FileUpload
                  onFileUploaded={handleImageUploaded}
                  bucket={bucket}
                  path={path}
                  allowOptimization={true}
                  showPreview={true}
                />
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {images.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-md">
          <p className="text-muted-foreground">이미지가 없습니다.</p>
          {editable && canAddMoreImages && (
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => setIsUploadDialogOpen(true)}
            >
              <ImagePlus className="h-4 w-4 mr-2" />
              이미지 추가
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <Card key={image.id} className="overflow-hidden">
              <div className="relative aspect-square">
                <Image
                  src={image.url}
                  alt={image.alt || '이미지'}
                  fill
                  className="object-cover"
                />
              </div>
              <CardContent className="p-3">
                <div className="flex justify-between items-center">
                  <div className="truncate text-sm">
                    {image.caption || image.alt || '이미지'}
                  </div>
                  <div className="flex items-center space-x-1">
                    {selectable && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleSelectImage(image)}
                        title="이미지 선택"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleCopyImageUrl(image.url)}>
                          <Copy className="h-4 w-4 mr-2" />
                          URL 복사
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleOpenInNewTab(image.url)}>
                          <ExternalLink className="h-4 w-4 mr-2" />
                          새 탭에서 열기
                        </DropdownMenuItem>
                        {editable && (
                          <DropdownMenuItem 
                            onClick={() => handleDeleteImage(image)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            삭제
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
