'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, X, Upload, Image as ImageIcon } from 'lucide-react';
import { uploadImage } from '@/lib/storage';
import { toast } from 'sonner';

export interface UploadedImage {
  url: string;
  alt?: string;
  caption?: string;
}

interface ImageUploadProps {
  onImageUploaded: (image: UploadedImage) => void;
  maxSizeMB?: number;
  path?: string;
}

export default function ImageUpload({
  onImageUploaded,
  maxSizeMB = 5,
  path = 'life-info',
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [alt, setAlt] = useState('');
  const [caption, setCaption] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 파일 선택 처리
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 파일 크기 검증
    if (file.size > maxSizeMB * 1024 * 1024) {
      toast.error(`파일 크기는 ${maxSizeMB}MB 이하여야 합니다.`);
      return;
    }

    // 파일 타입 검증
    if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
      toast.error('지원되지 않는 파일 형식입니다. (JPEG, PNG, GIF, WEBP만 허용)');
      return;
    }

    // 미리보기 생성
    const reader = new FileReader();
    reader.onload = () => {
      setPreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // 파일 업로드 처리
  const handleUpload = async () => {
    const file = fileInputRef.current?.files?.[0];
    if (!file) {
      toast.error('업로드할 이미지를 선택해주세요.');
      return;
    }

    setIsUploading(true);

    try {
      const result = await uploadImage(file, path);

      if (result.error) {
        toast.error(`업로드 실패: ${result.error}`);
        return;
      }

      // 업로드 성공 시 콜백 호출
      onImageUploaded({
        url: result.url,
        alt,
        caption,
      });

      // 상태 초기화
      setPreview(null);
      setAlt('');
      setCaption('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      toast.success('이미지가 성공적으로 업로드되었습니다.');
    } catch (error) {
      console.error('이미지 업로드 중 오류 발생:', error);
      toast.error('이미지 업로드 중 오류가 발생했습니다.');
    } finally {
      setIsUploading(false);
    }
  };

  // 미리보기 취소
  const handleCancelPreview = () => {
    setPreview(null);
    setAlt('');
    setCaption('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-4">
      {!preview ? (
        <div className="grid w-full items-center gap-1.5">
          <Label htmlFor="image">이미지</Label>
          <Input
            id="image"
            type="file"
            accept="image/jpeg,image/png,image/gif,image/webp"
            ref={fileInputRef}
            onChange={handleFileChange}
          />
          <p className="text-sm text-muted-foreground">
            최대 {maxSizeMB}MB, JPEG, PNG, GIF, WEBP 형식만 지원
          </p>
        </div>
      ) : (
        <Card>
          <CardContent className="p-4 space-y-4">
            <div className="relative">
              <div className="relative aspect-video w-full overflow-hidden rounded-md">
                <Image
                  src={preview}
                  alt="미리보기"
                  fill
                  className="object-cover"
                />
              </div>
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2"
                onClick={handleCancelPreview}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-2">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="alt">대체 텍스트</Label>
                <Input
                  id="alt"
                  value={alt}
                  onChange={(e) => setAlt(e.target.value)}
                  placeholder="이미지 설명 (접근성을 위해 권장)"
                />
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="caption">캡션</Label>
                <Textarea
                  id="caption"
                  value={caption}
                  onChange={(e) => setCaption(e.target.value)}
                  placeholder="이미지 캡션 (선택사항)"
                  rows={2}
                />
              </div>

              <Button
                onClick={handleUpload}
                disabled={isUploading}
                className="w-full"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    업로드 중...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    업로드
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
