'use client';

import {
  CountryWithAll,
  getCountryByCode as fetchCountryByCode,
  getEnabledCountries as fetchEnabledCountries,
  getCountries,
} from '@/actions/country';
import { useCallback, useEffect, useState } from 'react';

// 클라이언트 사이드에서 사용할 Country 타입 정의
export interface Country {
  code: string;
  name: string;
  displayName: string;
  enabled: boolean;
}

// 서버 데이터를 클라이언트 타입으로 변환하는 함수
const mapToClientCountry = (country: CountryWithAll): Country => ({
  code: country.code,
  name: country.name,
  displayName: country.displayName,
  enabled: country.enabled,
});

// 전체 옵션을 포함한 기본 국가 목록 (초기 로딩 상태에서 사용)
export const defaultCountries: Country[] = [
  { code: '', name: '전체', displayName: '전체', enabled: true },
  { code: 'kr', name: '한국', displayName: '한국', enabled: true },
];

// 국가 목록을 가져오는 훅
export const useCountries = () => {
  const [countries, setCountries] = useState<Country[]>(defaultCountries);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        setLoading(true);
        const data = await getCountries();
        // 전체 옵션 추가
        const allOption: Country = {
          code: '',
          name: '전체',
          displayName: '전체',
          enabled: true,
        };
        setCountries([allOption, ...data.map(mapToClientCountry)]);
      } catch (err) {
        console.error('국가 목록을 가져오는 중 오류 발생:', err);
        setError(
          err instanceof Error
            ? err
            : new Error('국가 목록을 가져오는 중 오류가 발생했습니다.')
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCountries();
  }, []);

  return { countries, loading, error };
};

// 활성화된 국가만 가져오는 훅
export const useEnabledCountries = () => {
  const [countries, setCountries] = useState<Country[]>(defaultCountries);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await fetchEnabledCountries();
        // 전체 옵션 추가
        const allOption: Country = {
          code: '',
          name: '전체',
          displayName: '전체',
          enabled: true,
        };
        setCountries([allOption, ...data.map(mapToClientCountry)]);
      } catch (err) {
        console.error('활성화된 국가 목록을 가져오는 중 오류 발생:', err);
        setError(
          err instanceof Error
            ? err
            : new Error('활성화된 국가 목록을 가져오는 중 오류가 발생했습니다.')
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { countries, loading, error };
};

// 코드로 국가 정보 찾기 (클라이언트 컴포넌트에서 사용)
export const useCountryByCode = (code: string) => {
  const [country, setCountry] = useState<Country | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchCountry = useCallback(async (countryCode: string) => {
    try {
      setLoading(true);
      const data = await fetchCountryByCode(countryCode);
      if (data) {
        setCountry(mapToClientCountry(data));
      } else {
        setCountry(null);
      }
    } catch (err) {
      console.error(
        `국가 코드 ${countryCode}에 대한 정보를 가져오는 중 오류 발생:`,
        err
      );
      setError(
        err instanceof Error
          ? err
          : new Error('국가 정보를 가져오는 중 오류가 발생했습니다.')
      );
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCountry(code);
  }, [code, fetchCountry]);

  return { country, loading, error, refetch: () => fetchCountry(code) };
};

// 기본 국가 코드 (한국)
export const DEFAULT_COUNTRY_CODE = 'kr';
