# Sodamm 디자인 시스템

이 디렉토리는 Sodamm 애플리케이션의 디자인 시스템을 포함하고 있습니다. 디자인 시스템은 일관된 사용자 경험을 제공하기 위한 재사용 가능한 컴포넌트, 디자인 토큰, 패턴 및 가이드라인의 모음입니다.

## 디렉토리 구조

```
design-system/
├── components/       # 재사용 가능한 UI 컴포넌트
│   ├── atoms/        # 기본 UI 요소 (버튼, 입력 필드, 아이콘 등)
│   ├── molecules/    # 여러 원자로 구성된 복합 컴포넌트
│   └── organisms/    # 분자와 원자로 구성된 복잡한 UI 패턴
├── tokens/           # 디자인 토큰 정의
│   ├── colors.ts     # 색상 토큰
│   ├── spacing.ts    # 간격 토큰
│   ├── typography.ts # 타이포그래피 토큰
│   └── index.ts      # 모든 토큰을 내보내는 파일
├── hooks/            # 디자인 시스템 관련 React 훅
├── utils/            # 디자인 시스템 유틸리티 함수
└── README.md         # 문서
```

## 사용 방법

### 컴포넌트 가져오기

```tsx
// 원자 컴포넌트 가져오기
import { Button } from '@/design-system/components/atoms/Button';

// 분자 컴포넌트 가져오기
import { Card } from '@/design-system/components/molecules/Card';

// 유기체 컴포넌트 가져오기
import { PostCard } from '@/design-system/components/organisms/PostCard';
```

### 디자인 토큰 사용하기

```tsx
import { colors, spacing, typography } from '@/design-system/tokens';

// 색상 토큰 사용
const primaryColor = colors.primary;

// 간격 토큰 사용
const standardPadding = spacing.md;

// 타이포그래피 토큰 사용
const headingStyle = typography.heading.h1;
```

## 디자인 원칙

1. **일관성**: 모든 컴포넌트는 일관된 디자인 언어를 따릅니다.
2. **재사용성**: 컴포넌트는 다양한 상황에서 재사용할 수 있도록 설계되었습니다.
3. **접근성**: 모든 컴포넌트는 WCAG 접근성 가이드라인을 준수합니다.
4. **반응형**: 컴포넌트는 다양한 화면 크기에 적응합니다.
5. **유지보수성**: 코드는 명확하고 문서화되어 있어 유지보수가 용이합니다.

## 컴포넌트 개발 가이드라인

1. 모든 컴포넌트는 TypeScript로 작성합니다.
2. 컴포넌트는 가능한 한 순수 함수형으로 작성합니다.
3. 컴포넌트는 디자인 토큰을 사용하여 스타일링합니다.
4. 모든 컴포넌트는 적절한 PropTypes 또는 TypeScript 인터페이스를 포함해야 합니다.
5. 컴포넌트는 접근성을 고려하여 설계해야 합니다.
