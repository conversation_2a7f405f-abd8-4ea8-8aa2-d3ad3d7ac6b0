/**
 * Avatar 컴포넌트
 * 
 * 사용자 아바타를 표시하기 위한 컴포넌트입니다.
 * 이미지와 대체 텍스트를 지원합니다.
 */

'use client';

import * as AvatarPrimitive from '@radix-ui/react-avatar';
import * as React from 'react';

import { cn } from '@/lib/utils';
import { tokens } from '../../tokens';

export interface AvatarProps extends React.ComponentProps<typeof AvatarPrimitive.Root> {
  /**
   * 아바타의 크기 (기본값: 'md')
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * 사용자 아바타 컴포넌트
 * 
 * @example
 * ```tsx
 * <Avatar>
 *   <AvatarImage src="/path/to/image.jpg" alt="사용자 이름" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 * ```
 */
function Avatar({
  className,
  size = 'md',
  ...props
}: AvatarProps) {
  const sizeClasses = {
    sm: 'size-6',
    md: 'size-8',
    lg: 'size-12',
    xl: 'size-16',
  };

  return (
    <AvatarPrimitive.Root
      data-slot="avatar"
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full',
        sizeClasses[size],
        className
      )}
      {...props}
    />
  );
}

export interface AvatarImageProps extends React.ComponentProps<typeof AvatarPrimitive.Image> {}

/**
 * 아바타 이미지 컴포넌트
 */
function AvatarImage({
  className,
  ...props
}: AvatarImageProps) {
  return (
    <AvatarPrimitive.Image
      data-slot="avatar-image"
      className={cn('aspect-square size-full', className)}
      {...props}
    />
  );
}

export interface AvatarFallbackProps extends React.ComponentProps<typeof AvatarPrimitive.Fallback> {}

/**
 * 아바타 대체 텍스트 컴포넌트
 */
function AvatarFallback({
  className,
  ...props
}: AvatarFallbackProps) {
  return (
    <AvatarPrimitive.Fallback
      data-slot="avatar-fallback"
      className={cn(
        'bg-muted flex size-full items-center justify-center rounded-full',
        className
      )}
      {...props}
    />
  );
}

export { Avatar, AvatarFallback, AvatarImage };
