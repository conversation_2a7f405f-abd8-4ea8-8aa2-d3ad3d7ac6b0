/**
 * Input 컴포넌트
 * 
 * 다양한 입력 필드를 위한 기본 컴포넌트입니다.
 * 디자인 시스템의 색상 및 간격 토큰을 사용하여 일관된 스타일을 제공합니다.
 */

import * as React from 'react';

import { cn } from '@/lib/utils';
import { tokens } from '../../tokens';

export interface InputProps extends React.ComponentProps<'input'> {
  /**
   * 에러 상태를 표시할지 여부
   */
  error?: boolean;
}

/**
 * 기본 입력 필드 컴포넌트
 * 
 * @example
 * ```tsx
 * <Input placeholder="이름을 입력하세요" />
 * <Input type="email" placeholder="이메일" required />
 * <Input type="password" placeholder="비밀번호" error={true} />
 * ```
 */
function Input({ className, type, error, ...props }: InputProps) {
  return (
    <input
      type={type}
      data-slot="input"
      aria-invalid={error}
      className={cn(
        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
        className
      )}
      {...props}
    />
  );
}

export { Input };
