/**
 * Textarea 컴포넌트
 * 
 * 여러 줄의 텍스트 입력을 위한 기본 컴포넌트입니다.
 * 디자인 시스템의 색상 및 간격 토큰을 사용하여 일관된 스타일을 제공합니다.
 */

import * as React from 'react';

import { cn } from '@/lib/utils';
import { tokens } from '../../tokens';

export interface TextareaProps extends React.ComponentProps<'textarea'> {
  /**
   * 에러 상태를 표시할지 여부
   */
  error?: boolean;
}

/**
 * 여러 줄의 텍스트 입력을 위한 컴포넌트
 * 
 * @example
 * ```tsx
 * <Textarea placeholder="메시지를 입력하세요" />
 * <Textarea rows={5} placeholder="상세 설명" required />
 * <Textarea placeholder="피드백" error={true} />
 * ```
 */
function Textarea({ className, error, ...props }: TextareaProps) {
  return (
    <textarea
      data-slot="textarea"
      aria-invalid={error}
      className={cn(
        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        className
      )}
      {...props}
    />
  );
}

export { Textarea };
