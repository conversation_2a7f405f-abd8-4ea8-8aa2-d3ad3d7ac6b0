'use client';

import { But<PERSON> } from '@/design-system/components/atoms/Button';

/**
 * 버튼 컴포넌트 예제
 *
 * 이 컴포넌트는 다양한 버튼 변형과 크기를 보여줍니다.
 */
export function ButtonExample() {
  return (
    <div className="space-y-8">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">버튼 변형</h3>
        <p className="text-sm text-muted-foreground">
          다양한 상황에 맞는 버튼 변형을 제공합니다.
        </p>
        <div className="flex flex-wrap gap-4">
          <Button variant="default">기본</Button>
          <Button variant="destructive">삭제</Button>
          <Button variant="outline">아웃라인</Button>
          <Button variant="secondary">보조</Button>
          <Button variant="ghost">고스트</Button>
          <Button variant="link">링크</Button>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-medium">버튼 크기</h3>
        <p className="text-sm text-muted-foreground">
          다양한 크기의 버튼을 제공합니다.
        </p>
        <div className="flex flex-wrap items-center gap-4">
          <Button size="sm">작게</Button>
          <Button size="default">기본</Button>
          <Button size="lg">크게</Button>
          <Button size="icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 5v14" />
              <path d="M5 12h14" />
            </svg>
          </Button>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-medium">비활성화된 버튼</h3>
        <p className="text-sm text-muted-foreground">
          비활성화된 상태의 버튼입니다.
        </p>
        <div className="flex flex-wrap gap-4">
          <Button disabled>비활성화</Button>
          <Button variant="destructive" disabled>
            비활성화
          </Button>
          <Button variant="outline" disabled>
            비활성화
          </Button>
        </div>
      </div>
    </div>
  );
}
