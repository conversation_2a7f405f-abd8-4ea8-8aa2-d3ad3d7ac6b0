'use client';

import { Button } from '@/design-system/components/atoms/Button';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/design-system/components/molecules/Card';

/**
 * 카드 컴포넌트 예제
 *
 * 이 컴포넌트는 다양한 카드 변형과 구성 요소를 보여줍니다.
 */
export function CardExample() {
  return (
    <div className="space-y-8">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">카드 변형</h3>
        <p className="text-sm text-muted-foreground">
          다양한 스타일의 카드 변형을 제공합니다.
        </p>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <Card variant="default">
            <CardHeader>
              <CardTitle>기본 카드</CardTitle>
              <CardDescription>기본 스타일의 카드입니다.</CardDescription>
            </CardHeader>
            <CardContent>
              <p>카드 내용이 여기에 들어갑니다.</p>
            </CardContent>
            <CardFooter>
              <Button size="sm">자세히 보기</Button>
            </CardFooter>
          </Card>
          
          <Card variant="outline">
            <CardHeader>
              <CardTitle>아웃라인 카드</CardTitle>
              <CardDescription>테두리만 있는 카드입니다.</CardDescription>
            </CardHeader>
            <CardContent>
              <p>카드 내용이 여기에 들어갑니다.</p>
            </CardContent>
            <CardFooter>
              <Button size="sm" variant="outline">자세히 보기</Button>
            </CardFooter>
          </Card>
          
          <Card variant="elevated">
            <CardHeader>
              <CardTitle>엘리베이티드 카드</CardTitle>
              <CardDescription>그림자가 강조된 카드입니다.</CardDescription>
            </CardHeader>
            <CardContent>
              <p>카드 내용이 여기에 들어갑니다.</p>
            </CardContent>
            <CardFooter>
              <Button size="sm">자세히 보기</Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-medium">카드 구성 요소</h3>
        <p className="text-sm text-muted-foreground">
          카드의 다양한 구성 요소를 보여줍니다.
        </p>
        <div className="grid gap-6 sm:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>제목과 설명이 있는 카드</CardTitle>
              <CardDescription>카드 헤더에 제목과 설명을 포함합니다.</CardDescription>
            </CardHeader>
            <CardContent>
              <p>카드 내용이 여기에 들어갑니다.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>액션이 있는 카드</CardTitle>
              <CardDescription>카드 헤더에 액션 버튼을 포함합니다.</CardDescription>
              <CardAction>
                <Button size="sm" variant="ghost">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="1" />
                    <circle cx="19" cy="12" r="1" />
                    <circle cx="5" cy="12" r="1" />
                  </svg>
                </Button>
              </CardAction>
            </CardHeader>
            <CardContent>
              <p>카드 내용이 여기에 들어갑니다.</p>
            </CardContent>
            <CardFooter className="justify-between">
              <Button size="sm" variant="ghost">취소</Button>
              <Button size="sm">확인</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
