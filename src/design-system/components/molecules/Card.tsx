/**
 * Card 컴포넌트
 * 
 * 콘텐츠를 그룹화하고 표시하기 위한 카드 컴포넌트입니다.
 * 헤더, 콘텐츠, 푸터 등 다양한 구성 요소를 포함합니다.
 */

import * as React from 'react';

import { cn } from '@/lib/utils';
import { tokens } from '../../tokens';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 카드의 변형 (기본값: 'default')
   */
  variant?: 'default' | 'outline' | 'elevated';
}

/**
 * 콘텐츠를 그룹화하고 표시하기 위한 카드 컴포넌트
 * 
 * @example
 * ```tsx
 * <Card>
 *   <CardHeader>
 *     <CardTitle>제목</CardTitle>
 *     <CardDescription>설명</CardDescription>
 *   </CardHeader>
 *   <CardContent>내용</CardContent>
 *   <CardFooter>푸터</CardFooter>
 * </Card>
 * ```
 */
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', ...props }, ref) => {
    const variantClasses = {
      default: 'border border-zinc-200 bg-white text-zinc-950 shadow-sm dark:border-zinc-800 dark:bg-zinc-950 dark:text-zinc-50',
      outline: 'border border-zinc-200 bg-transparent dark:border-zinc-800',
      elevated: 'border-none bg-white text-zinc-950 shadow-md dark:bg-zinc-900 dark:text-zinc-50',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'rounded-lg',
          variantClasses[variant],
          className
        )}
        {...props}
      />
    );
  }
);
Card.displayName = 'Card';

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}

/**
 * 카드 콘텐츠 컴포넌트
 */
const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      data-slot="card-content"
      className={cn('p-6', className)}
      {...props}
    />
  )
);
CardContent.displayName = 'CardContent';

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

/**
 * 카드 헤더 컴포넌트
 */
function CardHeader({ className, ...props }: CardHeaderProps) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',
        className
      )}
      {...props}
    />
  );
}

export interface CardTitleProps extends React.HTMLAttributes<HTMLDivElement> {}

/**
 * 카드 제목 컴포넌트
 */
function CardTitle({ className, ...props }: CardTitleProps) {
  return (
    <div
      data-slot="card-title"
      className={cn('leading-none font-semibold', className)}
      {...props}
    />
  );
}

export interface CardDescriptionProps extends React.HTMLAttributes<HTMLDivElement> {}

/**
 * 카드 설명 컴포넌트
 */
function CardDescription({ className, ...props }: CardDescriptionProps) {
  return (
    <div
      data-slot="card-description"
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  );
}

export interface CardActionProps extends React.HTMLAttributes<HTMLDivElement> {}

/**
 * 카드 액션 컴포넌트
 */
function CardAction({ className, ...props }: CardActionProps) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',
        className
      )}
      {...props}
    />
  );
}

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

/**
 * 카드 푸터 컴포넌트
 */
function CardFooter({ className, ...props }: CardFooterProps) {
  return (
    <div
      data-slot="card-footer"
      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}
      {...props}
    />
  );
}

export {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
};
