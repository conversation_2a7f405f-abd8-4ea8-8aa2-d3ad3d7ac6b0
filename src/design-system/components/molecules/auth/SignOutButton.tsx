'use client';

import { Button } from '@/design-system/components/atoms/Button';
import { signOut } from 'next-auth/react';

/**
 * 로그아웃 버튼 컴포넌트
 * 
 * 사용자 로그아웃 기능을 제공하는 버튼 컴포넌트입니다.
 * 디자인 시스템의 Button 컴포넌트를 기반으로 합니다.
 */
export function SignOutButton() {
  const handleSignOut = async () => {
    try {
      await signOut({
        callbackUrl: '/',
      });
    } catch (error) {
      console.error('Signout error:', error);
    }
  };

  return (
    <Button
      onClick={handleSignOut}
      type="submit"
      className="font-black"
    >
      Sign out
    </Button>
  );
}
