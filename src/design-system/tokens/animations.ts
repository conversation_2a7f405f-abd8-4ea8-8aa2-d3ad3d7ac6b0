/**
 * 애니메이션 토큰 정의
 * 
 * 이 파일은 애플리케이션 전체에서 사용되는 애니메이션 토큰을 정의합니다.
 * 일관된 애니메이션 스타일을 제공하여 디자인의 일관성을 유지합니다.
 */

export type EasingFunction = 
  | 'linear'
  | 'ease'
  | 'ease-in'
  | 'ease-out'
  | 'ease-in-out'
  | 'cubic-bezier(0.4, 0, 0.2, 1)' // 표준 이징 함수
  | 'cubic-bezier(0, 0, 0.2, 1)'   // 부드러운 진입
  | 'cubic-bezier(0.4, 0, 1, 1)'   // 빠른 진출
  | 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'; // 바운스

export type Duration = {
  fastest: string;
  faster: string;
  fast: string;
  normal: string;
  slow: string;
  slower: string;
  slowest: string;
};

export type AnimationTokens = {
  duration: Duration;
  easing: {
    default: EasingFunction;
    linear: EasingFunction;
    in: EasingFunction;
    out: EasingFunction;
    inOut: EasingFunction;
    bounce: EasingFunction;
  };
  transition: {
    default: string;
    transform: string;
    opacity: string;
    background: string;
    shadow: string;
    colors: string;
    all: string;
  };
  keyframes: {
    fadeIn: string;
    fadeOut: string;
    slideIn: string;
    slideOut: string;
    pulse: string;
    spin: string;
    bounce: string;
  };
};

/**
 * 애니메이션 토큰
 * 
 * 이 객체는 애플리케이션 전체에서 사용되는 애니메이션 값을 정의합니다.
 */
export const animations: AnimationTokens = {
  duration: {
    fastest: '50ms',
    faster: '100ms',
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
    slower: '400ms',
    slowest: '500ms',
  },
  easing: {
    default: 'cubic-bezier(0.4, 0, 0.2, 1)',
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
  transition: {
    default: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'transform 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    opacity: 'opacity 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    background: 'background 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    shadow: 'box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    colors: 'color 200ms cubic-bezier(0.4, 0, 0.2, 1), background-color 200ms cubic-bezier(0.4, 0, 0.2, 1), border-color 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    all: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
  keyframes: {
    fadeIn: `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    `,
    fadeOut: `
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
    `,
    slideIn: `
      @keyframes slideIn {
        from { transform: translateY(10px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    `,
    slideOut: `
      @keyframes slideOut {
        from { transform: translateY(0); opacity: 1; }
        to { transform: translateY(10px); opacity: 0; }
      }
    `,
    pulse: `
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    `,
    spin: `
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `,
    bounce: `
      @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
      }
    `,
  },
};
