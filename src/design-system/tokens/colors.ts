/**
 * 색상 토큰 정의
 * 
 * 이 파일은 애플리케이션 전체에서 사용되는 색상 토큰을 정의합니다.
 * CSS 변수와 연결되어 있으며, 다크 모드와 라이트 모드를 모두 지원합니다.
 */

export type ColorToken = {
  light: string;
  dark: string;
};

export type ColorTokens = {
  // 기본 색상
  background: ColorToken;
  foreground: ColorToken;
  
  // 컴포넌트 색상
  primary: ColorToken;
  primaryForeground: ColorToken;
  secondary: ColorToken;
  secondaryForeground: ColorToken;
  accent: ColorToken;
  accentForeground: ColorToken;
  muted: ColorToken;
  mutedForeground: ColorToken;
  
  // UI 요소
  card: ColorToken;
  cardForeground: ColorToken;
  popover: ColorToken;
  popoverForeground: ColorToken;
  border: ColorToken;
  input: ColorToken;
  ring: ColorToken;
  
  // 상태 색상
  destructive: ColorToken;
  destructiveForeground: ColorToken;
  success: ColorToken;
  successForeground: ColorToken;
  warning: ColorToken;
  warningForeground: ColorToken;
  info: ColorToken;
  infoForeground: ColorToken;
  
  // 사이드바 색상
  sidebar: ColorToken;
  sidebarForeground: ColorToken;
  sidebarPrimary: ColorToken;
  sidebarPrimaryForeground: ColorToken;
  sidebarAccent: ColorToken;
  sidebarAccentForeground: ColorToken;
  sidebarBorder: ColorToken;
  sidebarRing: ColorToken;
  
  // 차트 색상
  chart1: ColorToken;
  chart2: ColorToken;
  chart3: ColorToken;
  chart4: ColorToken;
  chart5: ColorToken;
};

/**
 * CSS 변수를 참조하는 색상 토큰
 * 
 * 이 객체는 CSS 변수를 참조하여 현재 테마에 맞는 색상을 제공합니다.
 * 실제 색상 값은 globals.css에 정의되어 있습니다.
 */
export const colors: ColorTokens = {
  // 기본 색상
  background: {
    light: 'var(--background)',
    dark: 'var(--background)',
  },
  foreground: {
    light: 'var(--foreground)',
    dark: 'var(--foreground)',
  },
  
  // 컴포넌트 색상
  primary: {
    light: 'var(--primary)',
    dark: 'var(--primary)',
  },
  primaryForeground: {
    light: 'var(--primary-foreground)',
    dark: 'var(--primary-foreground)',
  },
  secondary: {
    light: 'var(--secondary)',
    dark: 'var(--secondary)',
  },
  secondaryForeground: {
    light: 'var(--secondary-foreground)',
    dark: 'var(--secondary-foreground)',
  },
  accent: {
    light: 'var(--accent)',
    dark: 'var(--accent)',
  },
  accentForeground: {
    light: 'var(--accent-foreground)',
    dark: 'var(--accent-foreground)',
  },
  muted: {
    light: 'var(--muted)',
    dark: 'var(--muted)',
  },
  mutedForeground: {
    light: 'var(--muted-foreground)',
    dark: 'var(--muted-foreground)',
  },
  
  // UI 요소
  card: {
    light: 'var(--card)',
    dark: 'var(--card)',
  },
  cardForeground: {
    light: 'var(--card-foreground)',
    dark: 'var(--card-foreground)',
  },
  popover: {
    light: 'var(--popover)',
    dark: 'var(--popover)',
  },
  popoverForeground: {
    light: 'var(--popover-foreground)',
    dark: 'var(--popover-foreground)',
  },
  border: {
    light: 'var(--border)',
    dark: 'var(--border)',
  },
  input: {
    light: 'var(--input)',
    dark: 'var(--input)',
  },
  ring: {
    light: 'var(--ring)',
    dark: 'var(--ring)',
  },
  
  // 상태 색상
  destructive: {
    light: 'var(--destructive)',
    dark: 'var(--destructive)',
  },
  destructiveForeground: {
    light: 'var(--destructive-foreground, white)',
    dark: 'var(--destructive-foreground, white)',
  },
  success: {
    light: 'var(--success, #10b981)',
    dark: 'var(--success, #10b981)',
  },
  successForeground: {
    light: 'var(--success-foreground, white)',
    dark: 'var(--success-foreground, white)',
  },
  warning: {
    light: 'var(--warning, #f59e0b)',
    dark: 'var(--warning, #f59e0b)',
  },
  warningForeground: {
    light: 'var(--warning-foreground, white)',
    dark: 'var(--warning-foreground, white)',
  },
  info: {
    light: 'var(--info, #3b82f6)',
    dark: 'var(--info, #3b82f6)',
  },
  infoForeground: {
    light: 'var(--info-foreground, white)',
    dark: 'var(--info-foreground, white)',
  },
  
  // 사이드바 색상
  sidebar: {
    light: 'var(--sidebar)',
    dark: 'var(--sidebar)',
  },
  sidebarForeground: {
    light: 'var(--sidebar-foreground)',
    dark: 'var(--sidebar-foreground)',
  },
  sidebarPrimary: {
    light: 'var(--sidebar-primary)',
    dark: 'var(--sidebar-primary)',
  },
  sidebarPrimaryForeground: {
    light: 'var(--sidebar-primary-foreground)',
    dark: 'var(--sidebar-primary-foreground)',
  },
  sidebarAccent: {
    light: 'var(--sidebar-accent)',
    dark: 'var(--sidebar-accent)',
  },
  sidebarAccentForeground: {
    light: 'var(--sidebar-accent-foreground)',
    dark: 'var(--sidebar-accent-foreground)',
  },
  sidebarBorder: {
    light: 'var(--sidebar-border)',
    dark: 'var(--sidebar-border)',
  },
  sidebarRing: {
    light: 'var(--sidebar-ring)',
    dark: 'var(--sidebar-ring)',
  },
  
  // 차트 색상
  chart1: {
    light: 'var(--chart-1)',
    dark: 'var(--chart-1)',
  },
  chart2: {
    light: 'var(--chart-2)',
    dark: 'var(--chart-2)',
  },
  chart3: {
    light: 'var(--chart-3)',
    dark: 'var(--chart-3)',
  },
  chart4: {
    light: 'var(--chart-4)',
    dark: 'var(--chart-4)',
  },
  chart5: {
    light: 'var(--chart-5)',
    dark: 'var(--chart-5)',
  },
};
