/**
 * 반경 토큰 정의
 * 
 * 이 파일은 애플리케이션 전체에서 사용되는 반경(radius) 토큰을 정의합니다.
 * 일관된 모서리 둥글기를 제공하여 디자인의 일관성을 유지합니다.
 */

export type RadiusTokens = {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  full: string;
};

/**
 * 반경 토큰
 * 
 * 이 객체는 애플리케이션 전체에서 사용되는 반경 값을 정의합니다.
 * CSS 변수를 참조하여 현재 테마에 맞는 반경을 제공합니다.
 */
export const radius: RadiusTokens = {
  none: '0',
  sm: 'var(--radius-sm)',
  md: 'var(--radius-md)',
  lg: 'var(--radius-lg)',
  xl: 'var(--radius-xl)',
  full: '9999px',
};
