/**
 * 그림자 토큰 정의
 * 
 * 이 파일은 애플리케이션 전체에서 사용되는 그림자(shadow) 토큰을 정의합니다.
 * 일관된 그림자 스타일을 제공하여 디자인의 일관성을 유지합니다.
 */

export type ShadowTokens = {
  none: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
};

/**
 * 그림자 토큰
 * 
 * 이 객체는 애플리케이션 전체에서 사용되는 그림자 값을 정의합니다.
 * 라이트 모드와 다크 모드에 적합한 그림자 값을 제공합니다.
 */
export const shadows: ShadowTokens = {
  none: 'none',
  xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
};
