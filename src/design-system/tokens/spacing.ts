/**
 * 간격 토큰 정의
 * 
 * 이 파일은 애플리케이션 전체에서 사용되는 간격 토큰을 정의합니다.
 * 일관된 간격 시스템을 제공하여 디자인의 일관성을 유지합니다.
 */

export type SpacingTokens = {
  // 기본 간격
  none: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  
  // 컴포넌트 간격
  button: {
    padding: {
      sm: string;
      md: string;
      lg: string;
    };
    gap: string;
  };
  card: {
    padding: string;
    gap: string;
  };
  form: {
    gap: string;
    fieldGap: string;
  };
  section: {
    padding: string;
    gap: string;
  };
  
  // 레이아웃 간격
  layout: {
    container: {
      padding: {
        sm: string;
        md: string;
        lg: string;
      };
      maxWidth: string;
    };
    sidebar: {
      width: string;
      padding: string;
    };
    header: {
      height: string;
      padding: string;
    };
    footer: {
      padding: string;
    };
  };
};

/**
 * 간격 토큰
 * 
 * 이 객체는 애플리케이션 전체에서 사용되는 간격 값을 정의합니다.
 * rem 단위를 사용하여 접근성과 확장성을 보장합니다.
 */
export const spacing: SpacingTokens = {
  // 기본 간격
  none: '0',
  xs: '0.25rem', // 4px
  sm: '0.5rem',  // 8px
  md: '1rem',    // 16px
  lg: '1.5rem',  // 24px
  xl: '2rem',    // 32px
  '2xl': '2.5rem', // 40px
  '3xl': '3rem',   // 48px
  '4xl': '4rem',   // 64px
  
  // 컴포넌트 간격
  button: {
    padding: {
      sm: '0.5rem 0.75rem', // 8px 12px
      md: '0.625rem 1rem',  // 10px 16px
      lg: '0.75rem 1.5rem', // 12px 24px
    },
    gap: '0.5rem', // 8px
  },
  card: {
    padding: '1.5rem', // 24px
    gap: '1rem',       // 16px
  },
  form: {
    gap: '1.5rem',     // 24px
    fieldGap: '0.5rem', // 8px
  },
  section: {
    padding: '2rem',   // 32px
    gap: '1.5rem',     // 24px
  },
  
  // 레이아웃 간격
  layout: {
    container: {
      padding: {
        sm: '1rem',    // 16px
        md: '2rem',    // 32px
        lg: '4rem',    // 64px
      },
      maxWidth: '80rem', // 1280px
    },
    sidebar: {
      width: '16rem',   // 256px
      padding: '1rem',  // 16px
    },
    header: {
      height: '4rem',   // 64px
      padding: '0 1rem', // 0 16px
    },
    footer: {
      padding: '2rem',  // 32px
    },
  },
};
