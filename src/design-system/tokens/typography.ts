/**
 * 타이포그래피 토큰 정의
 * 
 * 이 파일은 애플리케이션 전체에서 사용되는 타이포그래피 토큰을 정의합니다.
 * 일관된 텍스트 스타일을 제공하여 디자인의 일관성을 유지합니다.
 */

export type FontWeight = 'normal' | 'medium' | 'semibold' | 'bold' | 'black';
export type FontStyle = 'normal' | 'italic';
export type TextTransform = 'none' | 'uppercase' | 'lowercase' | 'capitalize';
export type TextDecoration = 'none' | 'underline' | 'line-through';

export interface TextStyle {
  fontFamily: string;
  fontSize: string;
  fontWeight: FontWeight;
  lineHeight: string;
  letterSpacing?: string;
  fontStyle?: FontStyle;
  textTransform?: TextTransform;
  textDecoration?: TextDecoration;
}

export type TypographyTokens = {
  fontFamily: {
    base: string;
    heading: string;
    mono: string;
  };
  fontWeight: {
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
    black: string;
  };
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
  };
  lineHeight: {
    none: string;
    tight: string;
    snug: string;
    normal: string;
    relaxed: string;
    loose: string;
  };
  letterSpacing: {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
    widest: string;
  };
  heading: {
    h1: TextStyle;
    h2: TextStyle;
    h3: TextStyle;
    h4: TextStyle;
    h5: TextStyle;
    h6: TextStyle;
  };
  body: {
    default: TextStyle;
    sm: TextStyle;
    lg: TextStyle;
  };
  display: {
    default: TextStyle;
    sm: TextStyle;
    lg: TextStyle;
  };
  code: TextStyle;
  caption: TextStyle;
  button: TextStyle;
  label: TextStyle;
};

/**
 * 타이포그래피 토큰
 * 
 * 이 객체는 애플리케이션 전체에서 사용되는 타이포그래피 값을 정의합니다.
 * rem 단위를 사용하여 접근성과 확장성을 보장합니다.
 */
export const typography: TypographyTokens = {
  fontFamily: {
    base: 'var(--font-pretendard), system-ui, sans-serif',
    heading: 'var(--font-pretendard), system-ui, sans-serif',
    mono: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    black: '900',
  },
  fontSize: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
  },
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
  heading: {
    h1: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '2.25rem', // 36px
      fontWeight: 'bold',
      lineHeight: '1.25',
      letterSpacing: '-0.025em',
    },
    h2: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1.875rem', // 30px
      fontWeight: 'bold',
      lineHeight: '1.25',
      letterSpacing: '-0.025em',
    },
    h3: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1.5rem', // 24px
      fontWeight: 'semibold',
      lineHeight: '1.375',
      letterSpacing: '-0.025em',
    },
    h4: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1.25rem', // 20px
      fontWeight: 'semibold',
      lineHeight: '1.375',
    },
    h5: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1.125rem', // 18px
      fontWeight: 'semibold',
      lineHeight: '1.5',
    },
    h6: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1rem', // 16px
      fontWeight: 'semibold',
      lineHeight: '1.5',
    },
  },
  body: {
    default: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1rem', // 16px
      fontWeight: 'normal',
      lineHeight: '1.5',
    },
    sm: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '0.875rem', // 14px
      fontWeight: 'normal',
      lineHeight: '1.5',
    },
    lg: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '1.125rem', // 18px
      fontWeight: 'normal',
      lineHeight: '1.625',
    },
  },
  display: {
    default: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '3rem', // 48px
      fontWeight: 'bold',
      lineHeight: '1.25',
      letterSpacing: '-0.05em',
    },
    sm: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '2.25rem', // 36px
      fontWeight: 'bold',
      lineHeight: '1.25',
      letterSpacing: '-0.05em',
    },
    lg: {
      fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
      fontSize: '3.75rem', // 60px
      fontWeight: 'bold',
      lineHeight: '1.25',
      letterSpacing: '-0.05em',
    },
  },
  code: {
    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
    fontSize: '0.875rem', // 14px
    fontWeight: 'normal',
    lineHeight: '1.5',
  },
  caption: {
    fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
    fontSize: '0.75rem', // 12px
    fontWeight: 'normal',
    lineHeight: '1.5',
  },
  button: {
    fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
    fontSize: '0.875rem', // 14px
    fontWeight: 'medium',
    lineHeight: '1.5',
  },
  label: {
    fontFamily: 'var(--font-pretendard), system-ui, sans-serif',
    fontSize: '0.875rem', // 14px
    fontWeight: 'medium',
    lineHeight: '1.5',
  },
};
