/**
 * 테마 유틸리티 함수
 * 
 * 이 파일은 테마 관련 유틸리티 함수를 제공합니다.
 */

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

/**
 * 현재 테마 모드를 확인하는 훅
 * 
 * @returns 현재 테마 모드 ('light' | 'dark' | 'system')와 관련 함수
 */
export function useThemeMode() {
  const { theme, setTheme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // 컴포넌트가 마운트된 후에만 테마 정보에 접근
  useEffect(() => {
    setMounted(true);
  }, []);

  // 현재 활성화된 테마 모드 (system 설정 고려)
  const activeTheme = mounted
    ? theme === 'system'
      ? systemTheme
      : theme
    : undefined;

  // 다크 모드 여부
  const isDarkMode = activeTheme === 'dark';

  // 테마 토글 함수
  const toggleTheme = () => {
    setTheme(isDarkMode ? 'light' : 'dark');
  };

  return {
    theme,
    setTheme,
    systemTheme,
    activeTheme,
    isDarkMode,
    toggleTheme,
    mounted,
  };
}

/**
 * 테마 모드에 따라 값을 반환하는 함수
 * 
 * @param lightValue 라이트 모드에서 사용할 값
 * @param darkValue 다크 모드에서 사용할 값
 * @param currentTheme 현재 테마 모드 (기본값: 'light')
 * @returns 현재 테마에 맞는 값
 */
export function themeValue<T>(
  lightValue: T,
  darkValue: T,
  currentTheme: 'light' | 'dark' = 'light'
): T {
  return currentTheme === 'dark' ? darkValue : lightValue;
}

/**
 * CSS 변수 값을 가져오는 함수
 * 
 * @param variableName CSS 변수 이름 (--로 시작하는 부분 제외)
 * @returns CSS 변수 값 또는 undefined
 */
export function getCssVariable(variableName: string): string | undefined {
  if (typeof window === 'undefined') return undefined;
  
  const value = getComputedStyle(document.documentElement)
    .getPropertyValue(`--${variableName}`)
    .trim();
    
  return value || undefined;
}

/**
 * CSS 변수 값을 설정하는 함수
 * 
 * @param variableName CSS 변수 이름 (--로 시작하는 부분 제외)
 * @param value 설정할 값
 */
export function setCssVariable(variableName: string, value: string): void {
  if (typeof window === 'undefined') return;
  
  document.documentElement.style.setProperty(`--${variableName}`, value);
}
