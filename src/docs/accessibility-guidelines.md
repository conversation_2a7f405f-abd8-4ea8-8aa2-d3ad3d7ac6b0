# 소담(SODAMM) 접근성 가이드라인

이 문서는 소담(SODAMM) 프로젝트의 웹 접근성 표준 준수를 위한 가이드라인을 제공합니다.

## 1. 키보드 접근성

### 1.1 키보드 네비게이션

- 모든 상호작용 요소(버튼, 링크, 폼 컨트롤 등)는 키보드로 접근 가능해야 합니다.
- `Tab` 키를 사용하여 요소 간 이동, `Enter` 또는 `Space` 키를 사용하여 요소 활성화가 가능해야 합니다.
- 드롭다운 메뉴, 모달 등의 복잡한 UI 요소는 적절한 키보드 상호작용을 지원해야 합니다.

### 1.2 포커스 관리

- 포커스된 요소는 시각적으로 명확하게 표시되어야 합니다.
- 모달이나 다이얼로그가 열릴 때 포커스가 해당 요소로 이동해야 합니다.
- 모달이나 다이얼로그가 닫힐 때 포커스가 이전 위치로 돌아가야 합니다.
- 포커스 트랩(Focus Trap)을 사용하여 모달 내에서 포커스가 유지되도록 합니다.

### 1.3 단축키

- 주요 기능에 대한 단축키를 제공하는 경우, 이를 문서화하고 사용자에게 알려야 합니다.
- 단축키는 기존 브라우저 단축키와 충돌하지 않아야 합니다.

## 2. 스크린 리더 호환성

### 2.1 시맨틱 HTML

- 적절한 HTML 요소를 사용하여 콘텐츠의 의미를 전달해야 합니다.
  - 제목에는 `<h1>` ~ `<h6>` 사용
  - 목록에는 `<ul>`, `<ol>`, `<li>` 사용
  - 네비게이션에는 `<nav>` 사용
  - 주요 콘텐츠에는 `<main>` 사용
  - 독립적인 콘텐츠 섹션에는 `<article>`, `<section>` 사용
  - 푸터에는 `<footer>` 사용

### 2.2 ARIA 속성

- 필요한 경우 ARIA 속성을 사용하여 접근성을 향상시킵니다.
  - `aria-label`: 요소에 대한 접근 가능한 이름 제공
  - `aria-labelledby`: 다른 요소를 참조하여 접근 가능한 이름 제공
  - `aria-describedby`: 요소에 대한 추가 설명 제공
  - `aria-expanded`: 확장 가능한 요소의 상태 표시
  - `aria-hidden`: 스크린 리더에서 요소 숨기기
  - `aria-live`: 동적으로 변경되는 콘텐츠 알림

### 2.3 이미지 및 미디어

- 모든 이미지에 대체 텍스트(`alt` 속성)를 제공해야 합니다.
- 장식용 이미지는 빈 대체 텍스트(`alt=""`)를 사용합니다.
- 복잡한 이미지(차트, 그래프 등)는 상세한 설명을 제공해야 합니다.
- 비디오에는 자막 또는 대본을 제공해야 합니다.

### 2.4 폼 요소

- 모든 폼 요소에 레이블을 연결해야 합니다(`<label>` 요소 사용).
- 필수 입력 필드를 명확하게 표시해야 합니다.
- 오류 메시지는 명확하고 구체적이어야 하며, 스크린 리더에서 인식 가능해야 합니다.

## 3. 시각적 디자인 및 색상

### 3.1 색상 대비

- 텍스트와 배경 간의 색상 대비는 WCAG 2.1 AA 기준을 충족해야 합니다.
  - 일반 텍스트: 최소 4.5:1의 대비율
  - 큰 텍스트(18pt 이상 또는 14pt 이상 굵은 글꼴): 최소 3:1의 대비율
- 다크 모드와 라이트 모드 모두에서 충분한 대비를 유지해야 합니다.

### 3.2 색상 의존성

- 색상만으로 정보를 전달하지 않아야 합니다.
- 색상으로 구분되는 요소에는 추가적인 시각적 단서(아이콘, 패턴 등)를 제공해야 합니다.

### 3.3 텍스트 크기 및 간격

- 텍스트는 최소 16px 크기를 사용하는 것이 좋습니다.
- 줄 간격은 텍스트 크기의 1.5배 이상을 권장합니다.
- 단락 간격은 텍스트 크기의 2배 이상을 권장합니다.

## 4. 콘텐츠 및 언어

### 4.1 명확한 언어

- 간결하고 이해하기 쉬운 언어를 사용해야 합니다.
- 전문 용어나 약어를 사용할 때는 설명을 제공해야 합니다.

### 4.2 페이지 제목 및 구조

- 각 페이지는 고유하고 설명적인 제목(`<title>`)을 가져야 합니다.
- 제목 계층 구조(`<h1>` ~ `<h6>`)를 논리적으로 사용해야 합니다.

### 4.3 다국어 지원

- 페이지의 주요 언어를 `<html lang="ko">` 속성으로 지정해야 합니다.
- 페이지 내에서 언어가 변경되는 부분은 `lang` 속성으로 표시해야 합니다.

## 5. 테스트 및 검증

### 5.1 자동화된 테스트

- Lighthouse, axe, WAVE 등의 도구를 사용하여 접근성 문제를 자동으로 검사합니다.

### 5.2 수동 테스트

- 키보드만 사용하여 모든 기능을 테스트합니다.
- 스크린 리더(NVDA, VoiceOver 등)를 사용하여 콘텐츠를 테스트합니다.
- 다양한 확대/축소 수준에서 페이지를 테스트합니다.
- 다크 모드와 라이트 모드에서 모두 테스트합니다.

### 5.3 사용자 테스트

- 가능하다면 다양한 장애를 가진 사용자와 함께 테스트를 진행합니다.

## 참고 자료

- [WCAG 2.1 지침](https://www.w3.org/TR/WCAG21/)
- [MDN 웹 접근성 가이드](https://developer.mozilla.org/ko/docs/Web/Accessibility)
- [A11Y Project 체크리스트](https://www.a11yproject.com/checklist/)
