generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
  @@map("verification_tokens")
}

model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
  @@map("authenticators")
}

model User {
  id                   String                @id @default(cuid())
  name                 String                @unique @db.VarChar(255)
  email                String?               @unique @db.VarChar(255)
  emailVerified        DateTime?             @map("email_verified")
  image                String?               @map("image")
  displayName          String?               @map("display_name") @db.VarChar(255)
  bio                  String?
  step                 Int                   @default(0) @map("level")
  postCount            Int                   @default(0) @map("post_count")
  postCommentCount     Int                   @default(0) @map("post_comment_count")
  likeCount            Int                   @default(0) @map("like_count")
  isTester             Boolean               @default(false) @map("is_tester")
  isAdmin              Boolean               @default(false) @map("is_admin")
  role                 UserRole              @default(USER) @map("role")
  lastLoginAt          DateTime?             @map("last_login_at")
  createdAt            DateTime              @default(now()) @map("created_at")
  updatedAt            DateTime              @default(now()) @updatedAt @map("updated_at")
  accounts             Account[]
  authenticators       Authenticator[]
  bookmarks            Bookmark[]
  govInfos             GovInfo[]
  jobPosts             JobPost[]
  lifeInfos            LifeInfo[]
  notifications        Notification[]        @relation("UserNotifications")
  sentNotifications    Notification[]        @relation("SentByUser")
  notificationSettings NotificationSettings?
  postComments         PostComment[]
  posts                Post[]
  serviceGuides        ServiceGuide[]
  sessions             Session[]
  permissions          UserPermission[]

  @@map("users")
}

enum UserRole {
  USER
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum NotificationType {
  COMMENT
  REPLY
  LIKE
  ANNOUNCEMENT
  MENTION
  SYSTEM
}

model UserPermission {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  resource    String // 리소스 유형 (예: 'post', 'user', 'category')
  action      String // 권한 액션 (예: 'create', 'read', 'update', 'delete', 'manage')
  conditions  Json? // 조건부 권한 (예: { "authorId": "userId" })
  description String? // 권한 설명
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, resource, action])
  @@index([userId])
  @@index([resource])
  @@index([action])
  @@map("user_permissions")
}

model Post {
  id           String        @id @default(cuid())
  type         String        @default("NORMAL") @db.VarChar(32)
  country      String        @default("kr") @db.VarChar(32)
  userId       String        @map("user_id")
  content      String
  contentHtml  String        @default("<p></p>") @map("content_html")
  preview      Json?         @db.Json
  tags         String[]
  commentCount Int           @default(0) @map("comment_count")
  likeCount    Int           @default(0) @map("like_count")
  viewCount    Int           @default(0) @map("view_count")
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @default(now()) @updatedAt @map("updated_at")
  deletedAt    DateTime?     @map("deleted_at")
  categoryId   String?       @map("category_id")
  bookmark     Bookmark[]
  postComments PostComment[]
  postLikes    PostLike[]
  category     Category?     @relation(fields: [categoryId], references: [id])
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([type])
  @@index([userId])
  @@index([categoryId])
  @@map("posts")
}

model PostComment {
  id        Int       @id @default(autoincrement())
  userId    String    @map("user_id")
  postId    String    @map("post_id")
  content   String
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @default(now()) @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")
  post      Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([postId])
  @@map("post_comments")
}

model PostLike {
  id        Int      @id @default(autoincrement())
  userId    String   @map("user_id")
  postId    String   @map("post_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@index([userId])
  @@index([postId])
  @@map("post_likes")
}

model Bookmark {
  id        String   @id @default(cuid())
  postId    String
  userId    String
  createdAt DateTime @default(now())
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([postId, userId])
  @@map("bookmarks")
}

model JobPost {
  id             String    @id @default(cuid())
  title          String
  description    String
  type           String    @default("JOB_OFFER") // JOB_OFFER, JOB_SEEK
  company        String?
  jobType        String? // FULL_TIME, PART_TIME, CONTRACT, INTERNSHIP
  industry       String?
  location       String
  salary         String?
  requiredSkills String?
  contactInfo    String
  viewCount      Int       @default(0) @map("view_count")
  status         String    @default("active") // active, closed
  authorId       String    @map("author_id")
  categoryId     String?   @map("category_id")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @default(now()) @updatedAt @map("updated_at")
  expiresAt      DateTime? @map("expires_at")
  author         User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category       Category? @relation(fields: [categoryId], references: [id])

  @@index([type])
  @@index([status])
  @@index([authorId])
  @@index([categoryId])
  @@map("job_posts")
}

model News {
  id          String    @id @default(cuid())
  title       String
  content     String?
  contentHtml String?   @default("<p></p>") @map("content_html")
  link        String
  publishedAt DateTime? @map("published_at")
  viewCount   Int       @default(0) @map("view_count")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@map("news")
}

model LifeInfo {
  id           String          @id @default(cuid())
  title        String          @db.VarChar(255)
  slug         String          @unique @db.VarChar(255)
  summary      String?
  content      String
  contentHtml  String          @default("<p></p>") @map("content_html")
  viewCount    Int             @default(0) @map("view_count")
  status       String          @default("draft") @db.VarChar(32)
  featured     Boolean         @default(false)
  translations Json?           @db.Json
  categoryId   String          @map("category_id")
  authorId     String          @map("author_id")
  createdAt    DateTime        @default(now()) @map("created_at")
  updatedAt    DateTime        @default(now()) @updatedAt @map("updated_at")
  publishedAt  DateTime?       @map("published_at")
  deletedAt    DateTime?       @map("deleted_at")
  images       LifeInfoImage[]
  author       User            @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category     Category        @relation(fields: [categoryId], references: [id])

  @@index([categoryId])
  @@index([authorId])
  @@index([status])
  @@index([featured])
  @@index([slug])
  @@map("life_infos")
}

model LifeInfoImage {
  id         String   @id @default(cuid())
  url        String
  alt        String?
  caption    String?
  order      Int      @default(0)
  lifeInfoId String   @map("life_info_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")
  lifeInfo   LifeInfo @relation(fields: [lifeInfoId], references: [id], onDelete: Cascade)

  @@index([lifeInfoId])
  @@index([order])
  @@map("life_info_images")
}

model GovInfo {
  id            String         @id @default(cuid())
  title         String         @db.VarChar(255)
  slug          String         @unique @db.VarChar(255)
  summary       String?
  content       String
  contentHtml   String         @default("<p></p>") @map("content_html")
  viewCount     Int            @default(0) @map("view_count")
  status        String         @default("draft") @db.VarChar(32)
  isImportant   Boolean        @default(false) @map("is_important")
  govUrl        String?        @map("gov_url")
  govDepartment String?        @map("gov_department") @db.VarChar(255)
  translations  Json?          @db.Json
  categoryId    String         @map("category_id")
  authorId      String         @map("author_id")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @updatedAt @map("updated_at")
  publishedAt   DateTime?      @map("published_at")
  infoUpdatedAt DateTime?      @map("info_updated_at")
  deletedAt     DateTime?      @map("deleted_at")
  images        GovInfoImage[]
  author        User           @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category      Category       @relation(fields: [categoryId], references: [id])

  @@index([categoryId])
  @@index([authorId])
  @@index([status])
  @@index([isImportant])
  @@index([slug])
  @@map("gov_infos")
}

model GovInfoImage {
  id        String   @id @default(cuid())
  url       String
  alt       String?
  caption   String?
  order     Int      @default(0)
  govInfoId String   @map("gov_info_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  govInfo   GovInfo  @relation(fields: [govInfoId], references: [id], onDelete: Cascade)

  @@index([govInfoId])
  @@index([order])
  @@map("gov_info_images")
}

model Country {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(32)
  code        String   @unique @db.VarChar(32)
  displayName String   @map("display_name") @db.VarChar(255)
  imageUrl    String?  @map("image_url")
  enabled     Boolean  @default(true) @map("enabled")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("countries")
}

model Category {
  id            String         @id @default(cuid())
  name          String         @db.VarChar(255)
  slug          String         @unique @db.VarChar(255)
  description   String?
  order         Int            @default(0)
  isActive      Boolean        @default(true) @map("is_active")
  icon          String?        @db.VarChar(255)
  imageUrl      String?        @map("image_url")
  translations  Json?          @db.Json
  parentId      String?        @map("parent_id")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @updatedAt @map("updated_at")
  parent        Category?      @relation("CategoryToCategory", fields: [parentId], references: [id])
  children      Category[]     @relation("CategoryToCategory")
  govInfos      GovInfo[]
  jobPosts      JobPost[]
  lifeInfos     LifeInfo[]
  posts         Post[]
  serviceGuides ServiceGuide[]

  @@index([parentId])
  @@index([slug])
  @@index([isActive])
  @@index([order])
  @@map("categories")
}

model ServiceGuide {
  id           String                 @id @default(cuid())
  title        String                 @db.VarChar(255)
  slug         String                 @unique @db.VarChar(255)
  summary      String?
  content      String
  contentHtml  String                 @default("<p></p>") @map("content_html")
  viewCount    Int                    @default(0) @map("view_count")
  status       String                 @default("draft") @db.VarChar(32)
  featured     Boolean                @default(false)
  translations Json?                  @db.Json
  categoryId   String                 @map("category_id")
  authorId     String                 @map("author_id")
  officialLink String?                @map("official_link")
  createdAt    DateTime               @default(now()) @map("created_at")
  updatedAt    DateTime               @default(now()) @updatedAt @map("updated_at")
  publishedAt  DateTime?              @map("published_at")
  deletedAt    DateTime?              @map("deleted_at")
  documents    ServiceGuideDocument[]
  faqs         ServiceGuideFaq[]
  steps        ServiceGuideStep[]
  author       User                   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category     Category               @relation(fields: [categoryId], references: [id])

  @@index([categoryId])
  @@index([authorId])
  @@index([status])
  @@index([featured])
  @@index([slug])
  @@map("service_guides")
}

model ServiceGuideStep {
  id             String                  @id @default(cuid())
  title          String                  @db.VarChar(255)
  content        String
  contentHtml    String                  @default("<p></p>") @map("content_html")
  order          Int                     @default(0)
  serviceGuideId String                  @map("service_guide_id")
  createdAt      DateTime                @default(now()) @map("created_at")
  updatedAt      DateTime                @default(now()) @updatedAt @map("updated_at")
  images         ServiceGuideStepImage[]
  serviceGuide   ServiceGuide            @relation(fields: [serviceGuideId], references: [id], onDelete: Cascade)

  @@index([serviceGuideId])
  @@index([order])
  @@map("service_guide_steps")
}

model ServiceGuideStepImage {
  id        String           @id @default(cuid())
  url       String
  alt       String?
  caption   String?
  order     Int              @default(0)
  stepId    String           @map("step_id")
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @default(now()) @updatedAt @map("updated_at")
  step      ServiceGuideStep @relation(fields: [stepId], references: [id], onDelete: Cascade)

  @@index([stepId])
  @@index([order])
  @@map("service_guide_step_images")
}

model ServiceGuideDocument {
  id             String       @id @default(cuid())
  name           String       @db.VarChar(255)
  description    String?
  required       Boolean      @default(true)
  order          Int          @default(0)
  serviceGuideId String       @map("service_guide_id")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @default(now()) @updatedAt @map("updated_at")
  serviceGuide   ServiceGuide @relation(fields: [serviceGuideId], references: [id], onDelete: Cascade)

  @@index([serviceGuideId])
  @@index([order])
  @@map("service_guide_documents")
}

model ServiceGuideFaq {
  id             String       @id @default(cuid())
  question       String
  answer         String
  order          Int          @default(0)
  serviceGuideId String       @map("service_guide_id")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @default(now()) @updatedAt @map("updated_at")
  serviceGuide   ServiceGuide @relation(fields: [serviceGuideId], references: [id], onDelete: Cascade)

  @@index([serviceGuideId])
  @@index([order])
  @@map("service_guide_faqs")
}

model Notification {
  id                String           @id @default(cuid())
  userId            String           @map("user_id")
  senderId          String?          @map("sender_id")
  type              NotificationType
  title             String?
  content           String
  isRead            Boolean          @default(false) @map("is_read")
  relatedEntityId   String?          @map("related_entity_id")
  relatedEntityType String?          @map("related_entity_type")
  link              String?
  createdAt         DateTime         @default(now()) @map("created_at")
  updatedAt         DateTime         @default(now()) @updatedAt @map("updated_at")
  user              User             @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
  sender            User?            @relation("SentByUser", fields: [senderId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([senderId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
  @@map("notifications")
}

model NotificationSettings {
  id                              String   @id @default(cuid())
  userId                          String   @unique @map("user_id")
  enableCommentNotifications      Boolean  @default(true) @map("enable_comment_notifications")
  enableReplyNotifications        Boolean  @default(true) @map("enable_reply_notifications")
  enableLikeNotifications         Boolean  @default(true) @map("enable_like_notifications")
  enableAnnouncementNotifications Boolean  @default(true) @map("enable_announcement_notifications")
  enableMentionNotifications      Boolean  @default(true) @map("enable_mention_notifications")
  enableSystemNotifications       Boolean  @default(true) @map("enable_system_notifications")
  enableEmailNotifications        Boolean  @default(false) @map("enable_email_notifications")
  createdAt                       DateTime @default(now()) @map("created_at")
  updatedAt                       DateTime @default(now()) @updatedAt @map("updated_at")
  user                            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("notification_settings")
}
