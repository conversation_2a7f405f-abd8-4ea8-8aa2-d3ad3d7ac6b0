'use client';

import { useState, useCallback, useTransition } from 'react';
import { ActionResponse } from '@/actions/utils';

type ActionState<T> = {
  data?: T;
  error?: string;
  fieldErrors?: Record<string, string>;
};

type UseActionOptions<T> = {
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
};

/**
 * Server Action을 클라이언트에서 사용하기 위한 훅
 * @param action Server Action 함수
 * @param options 옵션
 * @returns 액션 실행 함수, 로딩 상태, 데이터, 에러
 */
export function useAction<TInput, TOutput>(
  action: (input: TInput) => Promise<ActionResponse<TOutput>>,
  options: UseActionOptions<TOutput> = {}
) {
  const [isPending, startTransition] = useTransition();
  const [state, setState] = useState<ActionState<TOutput>>({});

  const execute = useCallback(
    async (input: TInput) => {
      startTransition(async () => {
        try {
          const response = await action(input);

          if (response.success) {
            setState({ data: response.data });
            options.onSuccess?.(response.data as TOutput);
          } else {
            if (response.error?.field) {
              setState({
                fieldErrors: {
                  [response.error.field]: response.error.message,
                },
              });
            } else {
              setState({ error: response.error?.message });
              options.onError?.(response.error?.message || '알 수 없는 오류가 발생했습니다.');
            }
          }
        } catch (error) {
          const message = error instanceof Error ? error.message : '알 수 없는 오류가 발생했습니다.';
          setState({ error: message });
          options.onError?.(message);
        } finally {
          options.onComplete?.();
        }
      });
    },
    [action, options]
  );

  return {
    execute,
    isPending,
    data: state.data,
    error: state.error,
    fieldErrors: state.fieldErrors,
    isSuccess: !!state.data,
    isError: !!state.error || !!state.fieldErrors,
    reset: () => setState({}),
  };
}
