'use client';

import { useState } from 'react';
import useSWR, { SWRConfiguration } from 'swr';
import { useErrorHandler } from './use-error-handler';
import { useLoadingState } from './use-loading-state';
import { useOptimisticAction } from './use-optimistic-action';
import { toggleBookmark, getBookmarkStatus, getUserBookmarks } from '@/actions/interaction/bookmark';
import { fetcher } from '@/lib/swr/fetcher';

// 북마크 상태 인터페이스
interface BookmarkStatus {
  isBookmarked: boolean;
  postId: string;
}

// 북마크 토글 결과 인터페이스
interface BookmarkToggleResult {
  action: 'bookmarked' | 'unbookmarked';
  postId: string;
}

// 북마크 목록 필터 인터페이스
interface BookmarkFilters {
  page?: number;
  limit?: number;
  categoryId?: string;
  query?: string;
}

// 북마크 목록 응답 인터페이스
interface BookmarksResponse {
  bookmarks: Array<{
    id: string;
    createdAt: string;
    post: {
      id: string;
      content: string;
      contentHtml: string;
      createdAt: string;
      author: {
        id: string;
        name: string;
        displayName: string | null;
        image: string | null;
      };
      category: {
        id: string;
        name: string;
      } | null;
      _count: {
        likes: number;
        comments: number;
      };
    };
  }>;
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasMore: boolean;
  };
}

/**
 * 게시글의 북마크 상태를 확인하는 훅
 * 
 * @param postId 게시글 ID
 * @param options SWR 옵션
 */
export function useBookmarkStatus(postId: string | null, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 게시글 ID가 없으면 API 호출 안 함
  const shouldFetch = !!postId;
  
  // SWR로 북마크 상태 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<BookmarkStatus>(
    shouldFetch ? `/api/bookmarks/status/${postId}` : null,
    async () => {
      const response = await getBookmarkStatus(postId!);
      if (!response.success) {
        throw new Error(response.error?.message || '북마크 상태 확인 중 오류가 발생했습니다.');
      }
      return response.data;
    },
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    isBookmarked: data?.isBookmarked || false,
    isLoading,
    isValidating,
    error,
    mutate,
  };
}

/**
 * 북마크 토글 기능을 제공하는 훅
 * 
 * @param postId 게시글 ID
 * @param initialIsBookmarked 초기 북마크 상태
 */
export function useBookmarkToggle(postId: string, initialIsBookmarked: boolean = false) {
  // 초기 상태
  const initialData = {
    isBookmarked: initialIsBookmarked,
  };

  // 낙관적 업데이트를 위한 훅 사용
  const {
    execute: handleToggleBookmark,
    data,
    isPending,
    error,
  } = useOptimisticAction(
    toggleBookmark,
    initialData,
    {
      // 낙관적 업데이트 로직
      getOptimisticData: (currentData) => ({
        isBookmarked: !currentData.isBookmarked,
      }),
      onError: (error) => {
        console.error('북마크 토글 중 오류 발생:', error);
      },
    }
  );

  // 북마크 토글 핸들러
  const toggleBookmarkHandler = async () => {
    try {
      await handleToggleBookmark({ postId });
      return true;
    } catch (error) {
      console.error('북마크 토글 중 오류 발생:', error);
      return false;
    }
  };

  return {
    isBookmarked: data.isBookmarked,
    isPending,
    error,
    toggleBookmark: toggleBookmarkHandler,
  };
}

/**
 * 사용자의 북마크 목록을 가져오는 훅
 * 
 * @param filters 북마크 필터링 옵션
 * @param options SWR 옵션
 */
export function useBookmarks(filters: BookmarkFilters = {}, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // 쿼리 파라미터 생성
  const queryParams = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const apiUrl = `/api/bookmarks${queryString ? `?${queryString}` : ''}`;
  
  // SWR로 북마크 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<BookmarksResponse>(
    apiUrl,
    async () => {
      const response = await getUserBookmarks(
        filters.page || 1,
        filters.limit || 10
      );
      if (!response.success) {
        throw new Error(response.error?.message || '북마크 목록 조회 중 오류가 발생했습니다.');
      }
      return response.data;
    },
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  // 더 불러오기 함수
  const loadMore = async () => {
    if (!data || !data.pagination.hasMore || isLoadingMore) return;
    
    try {
      setIsLoadingMore(true);
      const nextPage = data.pagination.page + 1;
      
      const response = await getUserBookmarks(
        nextPage,
        filters.limit || 10
      );
      
      if (response.success) {
        // 기존 데이터와 새 데이터 병합
        const newData = {
          bookmarks: [...data.bookmarks, ...response.data.bookmarks],
          pagination: response.data.pagination,
        };
        
        // SWR 캐시 업데이트
        mutate(newData, false);
      }
    } catch (error) {
      console.error('북마크 더 불러오기 중 오류 발생:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };
  
  return {
    bookmarks: data?.bookmarks || [],
    pagination: data?.pagination,
    isLoading,
    isLoadingMore,
    isValidating,
    error,
    mutate,
    loadMore,
    isEmpty: data?.bookmarks.length === 0,
  };
}
