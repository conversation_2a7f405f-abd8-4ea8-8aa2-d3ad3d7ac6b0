'use client';

import useSWR, { SWRConfiguration } from 'swr';
import { fetcher } from '@/lib/swr/fetcher';
import { useLoadingState } from './use-loading-state';
import { useErrorHandler } from './use-error-handler';

// 댓글 타입 정의
export interface Comment {
  id: string;
  content: string;
  authorId: string;
  postId: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
  // 관계 데이터
  author?: {
    id: string;
    name: string;
    image?: string;
  };
  post?: {
    id: string;
    title: string;
  };
  replies?: Comment[];
  _count?: {
    replies: number;
    likes: number;
  };
  // 추가 필드
  [key: string]: any;
}

// 댓글 필터 타입 정의
export interface CommentFilters {
  postId?: string;
  authorId?: string;
  parentId?: string | null; // null은 최상위 댓글만 가져오기 위함
  page?: number;
  limit?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// 댓글 목록 응답 타입
export interface CommentsResponse {
  comments: Comment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * 댓글 목록을 가져오는 훅
 * 
 * @param filters 댓글 필터링 옵션
 * @param options SWR 옵션
 */
export function useComments(filters: CommentFilters = {}, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 쿼리 파라미터 생성
  const queryParams = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const apiUrl = `/api/comments${queryString ? `?${queryString}` : ''}`;
  
  // SWR로 댓글 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<CommentsResponse>(
    apiUrl,
    fetcher,
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    comments: data?.comments || [],
    total: data?.total || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    totalPages: data?.totalPages || 1,
    isLoading,
    isValidating,
    error,
    mutate,
    isEmpty: data?.comments.length === 0,
  };
}

/**
 * 특정 게시글의 댓글을 가져오는 훅
 * 
 * @param postId 게시글 ID
 * @param options SWR 옵션
 */
export function usePostComments(postId: string | null, options: SWRConfiguration = {}) {
  return useComments(
    postId ? { postId, parentId: null } : {}, // parentId: null은 최상위 댓글만 가져오기 위함
    options
  );
}

/**
 * 특정 댓글의 상세 정보를 가져오는 훅
 * 
 * @param commentId 댓글 ID
 * @param options SWR 옵션
 */
export function useComment(commentId: string | null, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 댓글 ID가 없으면 API 호출 안 함
  const shouldFetch = !!commentId;
  
  // SWR로 댓글 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<Comment>(
    shouldFetch ? `/api/comments/${commentId}` : null,
    fetcher,
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    comment: data,
    isLoading,
    isValidating,
    error,
    mutate,
  };
}
