'use client';

import {
  getCountryByCode as getCountryByCodeAction,
  getEnabledCountries as getEnabledCountriesAction,
} from '@/actions/country';
import {
  DEFAULT_COUNTRY_CODE,
  useCountries as useCountriesHook,
  useCountryByCode as useCountryByCodeHook,
  useEnabledCountries as useEnabledCountriesHook,
} from '@/constants/countries';
import { useCallback } from 'react';

// 국가 정보를 가져오는 훅 (기존 코드와의 호환성을 위해 유지)
export function useCountries() {
  const { countries, loading, error } = useCountriesHook();

  // 활성화된 국가만 필터링하는 함수
  const getEnabledCountries = useCallback(() => {
    return countries.filter((country) => country.enabled);
  }, [countries]);

  // 코드로 국가 정보 찾기 (대소문자 구분 없이)
  const getCountryByCode = useCallback(
    (code: string) => {
      if (!code) return countries[0]; // 코드가 없으면 전체(첫 번째 항목) 반환
      if (countries.length === 0) return undefined;

      const lowerCode = code.toLowerCase();
      return countries.find(
        (country) => country.code.toLowerCase() === lowerCode
      );
    },
    [countries]
  );

  return {
    countries,
    getEnabledCountries,
    getCountryByCode,
    isLoading: loading,
    error,
  };
}

// 서버 액션 직접 내보내기 (필요한 경우 사용)
export {
  DEFAULT_COUNTRY_CODE,
  getCountryByCodeAction as getCountryByCodeServer,
  getEnabledCountriesAction as getEnabledCountriesServer,
  useCountriesHook,
  useCountryByCodeHook,
  useEnabledCountriesHook,
};
