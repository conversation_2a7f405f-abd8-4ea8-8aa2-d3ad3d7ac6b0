'use client';

import { useCallback } from 'react';
import { toast } from 'sonner';

interface ErrorWithStatus extends Error {
  status?: number;
  info?: any;
}

interface UseErrorHandlerOptions {
  /**
   * 에러 발생 시 토스트 메시지 표시 여부
   */
  showToast?: boolean;
  
  /**
   * 에러 발생 시 콘솔에 로그 출력 여부
   */
  logToConsole?: boolean;
  
  /**
   * 특정 에러 상태 코드에 대한 커스텀 메시지
   */
  statusMessages?: Record<number, string>;
  
  /**
   * 기본 에러 메시지
   */
  defaultMessage?: string;
  
  /**
   * 에러 처리 후 호출될 콜백 함수
   */
  onError?: (error: ErrorWithStatus) => void;
}

/**
 * SWR 에러 처리를 위한 훅
 * 
 * 에러 발생 시 사용자에게 적절한 피드백을 제공하고
 * 필요한 경우 추가 처리를 수행합니다.
 */
export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    showToast = true,
    logToConsole = process.env.NODE_ENV !== 'production',
    statusMessages = {
      400: '잘못된 요청입니다.',
      401: '인증이 필요합니다.',
      403: '접근 권한이 없습니다.',
      404: '요청한 리소스를 찾을 수 없습니다.',
      500: '서버 오류가 발생했습니다.',
      502: '게이트웨이 오류가 발생했습니다.',
      503: '서비스를 일시적으로 사용할 수 없습니다.',
      504: '게이트웨이 시간 초과가 발생했습니다.',
    },
    defaultMessage = '요청 처리 중 오류가 발생했습니다.',
    onError,
  } = options;
  
  const handleError = useCallback((error: ErrorWithStatus | null | undefined) => {
    if (!error) return;
    
    // 에러 정보 추출
    const status = error.status || 0;
    const message = error.message || 
      (status && statusMessages[status]) || 
      defaultMessage;
    
    // 콘솔에 로그 출력
    if (logToConsole) {
      console.error('API 오류:', {
        message,
        status,
        info: error.info,
        stack: error.stack,
      });
    }
    
    // 토스트 메시지 표시
    if (showToast) {
      toast.error(message, {
        description: status ? `오류 코드: ${status}` : undefined,
        duration: 5000,
      });
    }
    
    // 추가 에러 처리 콜백 호출
    if (onError) {
      onError(error);
    }
  }, [logToConsole, showToast, statusMessages, defaultMessage, onError]);
  
  return {
    handleError,
  };
}
