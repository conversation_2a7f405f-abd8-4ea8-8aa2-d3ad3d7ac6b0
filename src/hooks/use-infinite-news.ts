'use client';

import { type NewsData, getInfiniteNews } from '@/actions/news'; // getInfiniteNews와 NewsData import 주석 해제
import { type ActionResult } from '@/lib/actions/action-helpers';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import useSWRInfinite from 'swr/infinite';

interface UseInfiniteNewsOptions {
  pageSize?: number;
}

export function useInfiniteNews({
  pageSize = 30,
}: UseInfiniteNewsOptions = {}) {
  const getKey = (
    pageIndex: number,
    previousPageData: ActionResult<{
      news: NewsData[]; // NewsData[] 사용
      nextCursor: string | null;
    }> | null
  ) => {
    if (
      previousPageData &&
      (!previousPageData.success || !previousPageData.data?.nextCursor)
    ) {
      return null;
    }
    if (pageIndex === 0) return ['infiniteNews', null, pageSize];
    if (previousPageData?.success) {
      return ['infiniteNews', previousPageData.data.nextCursor, pageSize];
    }
    return null;
  };

  const fetcher = async ([_, cursor, limit]: [
    string,
    string | null,
    number,
  ]): Promise<
    ActionResult<{ news: NewsData[]; nextCursor: string | null }>
  > => {
    // 반환 타입 명시
    const result = await getInfiniteNews({
      cursor: cursor ?? undefined,
      limit,
    });
    if (!result.success) {
      throw new Error(result.message || '데이터를 불러오는데 실패했습니다.');
    }
    // data가 undefined일 경우를 대비하여 기본값 제공 또는 에러 처리
    if (!result.data) {
      throw new Error('데이터가 존재하지 않습니다.');
    }
    return result as ActionResult<{
      news: NewsData[];
      nextCursor: string | null;
    }>; // 타입 단언 추가
  };

  const { data, error, isLoading, isValidating, size, setSize, mutate } =
    useSWRInfinite<
      ActionResult<{ news: NewsData[]; nextCursor: string | null }>
    >(getKey, fetcher, {
      revalidateFirstPage: false,
    });

  const news: NewsData[] = data // NewsData[] 사용
    ? data.flatMap((page) => (page.success && page.data ? page.data.news : []))
    : [];
  const lastPage = data?.[data.length - 1];
  const hasNextPage = lastPage?.success && !!lastPage.data?.nextCursor;
  const isEmpty = !isLoading && news.length === 0;
  const isReachingEnd =
    isEmpty ||
    (lastPage && lastPage.success && !lastPage.data?.nextCursor) ||
    !!error;

  const { ref: loadMoreRef, inView } = useInView({ threshold: 0 });

  useEffect(() => {
    if (inView && !isLoading && hasNextPage && !isValidating) {
      setSize(size + 1);
    }
  }, [inView, isLoading, hasNextPage, isValidating, setSize, size]);

  return {
    news,
    error,
    isLoading,
    isValidating,
    isReachingEnd,
    isEmpty,
    loadMoreRef,
    mutate,
  };
}
