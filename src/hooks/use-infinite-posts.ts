'use client';

import { PostData, getInfinitePosts } from '@/actions/posts'; // getInfinitePosts와 PostData import (알파벳 순서 정렬)
import { type ActionResult } from '@/lib/actions/action-helpers'; // ActionResult 타입 import
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import useSWRInfinite from 'swr/infinite';

interface UseInfinitePostsOptions {
  pageSize?: number;
  country?: string;
}

export function useInfinitePosts({
  pageSize = 30,
  country,
}: UseInfinitePostsOptions = {}) {
  // SWR 키 생성 함수
  const getKey = (
    pageIndex: number,
    previousPageData: ActionResult<{
      posts: PostData[];
      nextCursor: string | null;
    }> | null
  ) => {
    if (
      previousPageData &&
      (!previousPageData.success || !previousPageData.data?.nextCursor)
    ) {
      return null; // 이전 페이지 로드 실패 또는 마지막 페이지 도달
    }
    if (pageIndex === 0) return ['infinitePosts', null, pageSize, country]; // 첫 페이지
    if (previousPageData?.success) {
      return [
        'infinitePosts',
        previousPageData.data.nextCursor,
        pageSize,
        country,
      ]; // 다음 페이지
    }
    return null;
  };

  // 데이터 fetcher 함수
  const fetcher = async ([_, cursor, limit, countryFilter]: [
    string,
    string | null,
    number,
    string | undefined,
  ]) => {
    const result = await getInfinitePosts({
      cursor: cursor ?? undefined,
      limit,
      country: countryFilter?.toLowerCase(), // 소문자로 변환하여 전달
    });
    if (!result.success) {
      // SWR에서 에러를 처리하도록 throw
      throw new Error(result.message || '데이터를 불러오는데 실패했습니다.');
    }
    // 성공 시 ActionResult<Data> 반환
    return result;
  };

  // useSWRInfinite 훅 사용
  const { data, error, isLoading, isValidating, size, setSize, mutate } =
    useSWRInfinite(getKey, fetcher, {
      revalidateFirstPage: true, // 첫 페이지 자동 재검증 활성화
      revalidateOnFocus: true, // 포커스 시 재검증 활성화
      dedupingInterval: 3000, // 중복 요청 방지 간격 (3초)
    });

  // 데이터 가공
  const posts = data
    ? data.flatMap((page) => (page.success ? page.data.posts : []))
    : [];
  const lastPage = data?.[data.length - 1];
  const hasNextPage = lastPage?.success && !!lastPage.data.nextCursor;
  const isEmpty = !isLoading && posts.length === 0;
  const isReachingEnd =
    isEmpty ||
    (lastPage && lastPage.success && !lastPage.data.nextCursor) ||
    !!error;

  // Intersection Observer 설정
  const { ref: loadMoreRef, inView } = useInView({ threshold: 0 });

  // 스크롤 감지 및 다음 페이지 로드
  useEffect(() => {
    if (inView && !isLoading && hasNextPage && !isValidating) {
      setSize(size + 1);
    }
  }, [inView, isLoading, hasNextPage, isValidating, setSize, size]);

  // 훅 반환 값
  return {
    posts,
    error,
    isLoading,
    isValidating,
    isReachingEnd,
    isEmpty,
    loadMoreRef, // ref 이름 변경 (컴포넌트에서 사용하기 쉽게)
    mutate, // useSWRInfinite가 반환하는 mutate 함수를 반환 값에 추가
  };
}
