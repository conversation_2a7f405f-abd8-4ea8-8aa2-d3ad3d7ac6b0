'use client';

import { useCallback, useEffect, useRef } from 'react';
import useSWRInfinite, { SWRInfiniteConfiguration } from 'swr/infinite';
import { fetcher } from '@/lib/swr/fetcher';
import { useLoadingState } from './use-loading-state';
import { useErrorHandler } from './use-error-handler';
import { PostFilters, PostsResponse } from './use-posts';
import { CommentFilters, CommentsResponse } from './use-comments';
import { JobFilters, JobsResponse } from './use-jobs';

// 무한 스크롤 옵션 타입 정의
interface UseInfiniteScrollOptions extends SWRInfiniteConfiguration {
  /**
   * 페이지당 항목 수
   */
  limit?: number;
  
  /**
   * 스크롤 감지 마진 (px)
   * 하단에서 이 값만큼 떨어진 위치에 도달하면 다음 페이지 로드
   */
  threshold?: number;
  
  /**
   * 자동 스크롤 감지 활성화 여부
   */
  autoLoadMore?: boolean;
}

/**
 * 무한 스크롤 게시글 목록을 가져오는 훅
 * 
 * @param filters 게시글 필터링 옵션
 * @param options 무한 스크롤 옵션
 */
export function useInfinitePosts(
  filters: Omit<PostFilters, 'page' | 'limit'> = {},
  options: UseInfiniteScrollOptions = {}
) {
  const {
    limit = 10,
    threshold = 300,
    autoLoadMore = true,
    ...swrOptions
  } = options;
  
  const { handleError } = useErrorHandler();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  
  // 페이지 키 생성 함수
  const getKey = useCallback((pageIndex: number, previousPageData: PostsResponse | null) => {
    // 이전 페이지가 없거나 더 이상 데이터가 없으면 null 반환
    if (previousPageData && !previousPageData.posts.length) return null;
    
    // 쿼리 파라미터 생성
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    // 페이지 및 한계 추가
    queryParams.append('page', String(pageIndex + 1));
    queryParams.append('limit', String(limit));
    
    const queryString = queryParams.toString();
    return `/api/posts${queryString ? `?${queryString}` : ''}`;
  }, [filters, limit]);
  
  // SWR Infinite로 게시글 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    size,
    setSize,
    mutate,
  } = useSWRInfinite<PostsResponse>(
    getKey,
    fetcher,
    {
      onError: handleError,
      revalidateFirstPage: true,
      ...swrOptions,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  // 다음 페이지 로드 함수
  const loadMore = useCallback(() => {
    if (!isValidating && data && data[data.length - 1]?.posts.length > 0) {
      setSize(size + 1);
    }
  }, [data, isValidating, setSize, size]);
  
  // 스크롤 감지 설정
  useEffect(() => {
    if (!autoLoadMore) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { rootMargin: `0px 0px ${threshold}px 0px` }
    );
    
    observerRef.current = observer;
    
    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }
    
    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef);
      }
      observer.disconnect();
    };
  }, [loadMore, threshold, autoLoadMore]);
  
  // 모든 게시글 데이터 병합
  const posts = data ? data.flatMap(page => page.posts) : [];
  const isReachingEnd = data ? data[data.length - 1]?.posts.length < limit : false;
  
  return {
    posts,
    isLoading,
    isValidating,
    error,
    loadMore,
    loadMoreRef,
    isReachingEnd,
    isEmpty: posts.length === 0,
    mutate,
  };
}

/**
 * 무한 스크롤 댓글 목록을 가져오는 훅
 * 
 * @param filters 댓글 필터링 옵션
 * @param options 무한 스크롤 옵션
 */
export function useInfiniteComments(
  filters: Omit<CommentFilters, 'page' | 'limit'> = {},
  options: UseInfiniteScrollOptions = {}
) {
  const {
    limit = 10,
    threshold = 300,
    autoLoadMore = true,
    ...swrOptions
  } = options;
  
  const { handleError } = useErrorHandler();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  
  // 페이지 키 생성 함수
  const getKey = useCallback((pageIndex: number, previousPageData: CommentsResponse | null) => {
    // 이전 페이지가 없거나 더 이상 데이터가 없으면 null 반환
    if (previousPageData && !previousPageData.comments.length) return null;
    
    // 쿼리 파라미터 생성
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    // 페이지 및 한계 추가
    queryParams.append('page', String(pageIndex + 1));
    queryParams.append('limit', String(limit));
    
    const queryString = queryParams.toString();
    return `/api/comments${queryString ? `?${queryString}` : ''}`;
  }, [filters, limit]);
  
  // SWR Infinite로 댓글 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    size,
    setSize,
    mutate,
  } = useSWRInfinite<CommentsResponse>(
    getKey,
    fetcher,
    {
      onError: handleError,
      revalidateFirstPage: true,
      ...swrOptions,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  // 다음 페이지 로드 함수
  const loadMore = useCallback(() => {
    if (!isValidating && data && data[data.length - 1]?.comments.length > 0) {
      setSize(size + 1);
    }
  }, [data, isValidating, setSize, size]);
  
  // 스크롤 감지 설정
  useEffect(() => {
    if (!autoLoadMore) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { rootMargin: `0px 0px ${threshold}px 0px` }
    );
    
    observerRef.current = observer;
    
    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }
    
    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef);
      }
      observer.disconnect();
    };
  }, [loadMore, threshold, autoLoadMore]);
  
  // 모든 댓글 데이터 병합
  const comments = data ? data.flatMap(page => page.comments) : [];
  const isReachingEnd = data ? data[data.length - 1]?.comments.length < limit : false;
  
  return {
    comments,
    isLoading,
    isValidating,
    error,
    loadMore,
    loadMoreRef,
    isReachingEnd,
    isEmpty: comments.length === 0,
    mutate,
  };
}

/**
 * 무한 스크롤 구인·구직 정보 목록을 가져오는 훅
 * 
 * @param filters 구인·구직 정보 필터링 옵션
 * @param options 무한 스크롤 옵션
 */
export function useInfiniteJobs(
  filters: Omit<JobFilters, 'page' | 'limit'> = {},
  options: UseInfiniteScrollOptions = {}
) {
  const {
    limit = 10,
    threshold = 300,
    autoLoadMore = true,
    ...swrOptions
  } = options;
  
  const { handleError } = useErrorHandler();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  
  // 페이지 키 생성 함수
  const getKey = useCallback((pageIndex: number, previousPageData: JobsResponse | null) => {
    // 이전 페이지가 없거나 더 이상 데이터가 없으면 null 반환
    if (previousPageData && !previousPageData.jobs.length) return null;
    
    // 쿼리 파라미터 생성
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    // 페이지 및 한계 추가
    queryParams.append('page', String(pageIndex + 1));
    queryParams.append('limit', String(limit));
    
    const queryString = queryParams.toString();
    return `/api/jobs${queryString ? `?${queryString}` : ''}`;
  }, [filters, limit]);
  
  // SWR Infinite로 구인·구직 정보 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    size,
    setSize,
    mutate,
  } = useSWRInfinite<JobsResponse>(
    getKey,
    fetcher,
    {
      onError: handleError,
      revalidateFirstPage: true,
      ...swrOptions,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  // 다음 페이지 로드 함수
  const loadMore = useCallback(() => {
    if (!isValidating && data && data[data.length - 1]?.jobs.length > 0) {
      setSize(size + 1);
    }
  }, [data, isValidating, setSize, size]);
  
  // 스크롤 감지 설정
  useEffect(() => {
    if (!autoLoadMore) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { rootMargin: `0px 0px ${threshold}px 0px` }
    );
    
    observerRef.current = observer;
    
    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }
    
    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef);
      }
      observer.disconnect();
    };
  }, [loadMore, threshold, autoLoadMore]);
  
  // 모든 구인·구직 정보 데이터 병합
  const jobs = data ? data.flatMap(page => page.jobs) : [];
  const isReachingEnd = data ? data[data.length - 1]?.jobs.length < limit : false;
  
  return {
    jobs,
    isLoading,
    isValidating,
    error,
    loadMore,
    loadMoreRef,
    isReachingEnd,
    isEmpty: jobs.length === 0,
    mutate,
  };
}
