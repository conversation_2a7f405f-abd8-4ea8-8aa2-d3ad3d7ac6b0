'use client';

import useSWR, { SWRConfiguration } from 'swr';
import { fetcher } from '@/lib/swr/fetcher';
import { useLoadingState } from './use-loading-state';
import { useErrorHandler } from './use-error-handler';

// 구인·구직 정보 타입 정의
export interface Job {
  id: string;
  title: string;
  description: string;
  authorId: string;
  location: string;
  salary?: string;
  companyName?: string;
  contactEmail?: string;
  contactPhone?: string;
  jobType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP' | 'OTHER';
  experienceLevel?: 'ENTRY' | 'JUNIOR' | 'MID' | 'SENIOR' | 'EXECUTIVE';
  isActive: boolean;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  // 관계 데이터
  author?: {
    id: string;
    name: string;
    image?: string;
  };
  _count?: {
    applications: number;
    views: number;
  };
  // 추가 필드
  [key: string]: any;
}

// 구인·구직 정보 필터 타입 정의
export interface JobFilters {
  authorId?: string;
  location?: string;
  jobType?: string;
  experienceLevel?: string;
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// 구인·구직 정보 목록 응답 타입
export interface JobsResponse {
  jobs: Job[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * 구인·구직 정보 목록을 가져오는 훅
 * 
 * @param filters 구인·구직 정보 필터링 옵션
 * @param options SWR 옵션
 */
export function useJobs(filters: JobFilters = {}, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 쿼리 파라미터 생성
  const queryParams = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const apiUrl = `/api/jobs${queryString ? `?${queryString}` : ''}`;
  
  // SWR로 구인·구직 정보 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<JobsResponse>(
    apiUrl,
    fetcher,
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    jobs: data?.jobs || [],
    total: data?.total || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    totalPages: data?.totalPages || 1,
    isLoading,
    isValidating,
    error,
    mutate,
    isEmpty: data?.jobs.length === 0,
  };
}

/**
 * 특정 구인·구직 정보의 상세 정보를 가져오는 훅
 * 
 * @param jobId 구인·구직 정보 ID
 * @param options SWR 옵션
 */
export function useJob(jobId: string | null, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 구인·구직 정보 ID가 없으면 API 호출 안 함
  const shouldFetch = !!jobId;
  
  // SWR로 구인·구직 정보 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<Job>(
    shouldFetch ? `/api/jobs/${jobId}` : null,
    fetcher,
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    job: data,
    isLoading,
    isValidating,
    error,
    mutate,
  };
}
