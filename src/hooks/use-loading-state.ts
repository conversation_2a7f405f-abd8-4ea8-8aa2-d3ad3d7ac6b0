'use client';

import { useState, useEffect } from 'react';

interface UseLoadingStateOptions {
  /**
   * 로딩 상태 표시 지연 시간 (밀리초)
   * 이 시간 내에 데이터가 로드되면 로딩 상태가 표시되지 않음
   */
  delay?: number;
  
  /**
   * 최소 로딩 표시 시간 (밀리초)
   * 로딩이 너무 빨리 끝나는 경우 깜빡임 방지
   */
  minDisplayTime?: number;
}

/**
 * 로딩 상태를 관리하는 훅
 * 
 * 지연된 로딩 상태 표시와 최소 표시 시간을 지원하여
 * 사용자 경험을 향상시킵니다.
 */
export function useLoadingState(
  isLoading: boolean,
  options: UseLoadingStateOptions = {}
) {
  const { delay = 300, minDisplayTime = 500 } = options;
  
  // 실제로 UI에 표시할 로딩 상태
  const [showLoading, setShowLoading] = useState(false);
  // 로딩 시작 시간 추적
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);
  
  useEffect(() => {
    let delayTimer: NodeJS.Timeout | null = null;
    let minDisplayTimer: NodeJS.Timeout | null = null;
    
    if (isLoading) {
      // 로딩이 시작되면 지연 타이머 설정
      const currentTime = Date.now();
      setLoadingStartTime(currentTime);
      
      delayTimer = setTimeout(() => {
        setShowLoading(true);
      }, delay);
    } else {
      // 로딩이 끝났을 때
      if (loadingStartTime && showLoading) {
        const displayTime = Date.now() - loadingStartTime;
        const remainingTime = Math.max(0, minDisplayTime - displayTime);
        
        // 최소 표시 시간이 지나지 않았다면 타이머 설정
        if (remainingTime > 0) {
          minDisplayTimer = setTimeout(() => {
            setShowLoading(false);
            setLoadingStartTime(null);
          }, remainingTime);
        } else {
          // 이미 최소 표시 시간이 지났다면 바로 로딩 상태 해제
          setShowLoading(false);
          setLoadingStartTime(null);
        }
      } else {
        // 로딩 상태가 표시되기 전에 로딩이 끝난 경우
        setShowLoading(false);
        setLoadingStartTime(null);
      }
    }
    
    // 클린업 함수
    return () => {
      if (delayTimer) clearTimeout(delayTimer);
      if (minDisplayTimer) clearTimeout(minDisplayTimer);
    };
  }, [isLoading, delay, minDisplayTime, loadingStartTime, showLoading]);
  
  return {
    isLoading: showLoading,
    isLoadingAny: isLoading, // 실제 로딩 상태 (UI 표시와 무관)
  };
}
