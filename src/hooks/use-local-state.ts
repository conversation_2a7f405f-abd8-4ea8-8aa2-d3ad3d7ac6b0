/**
 * 컴포넌트 로컬 상태 관리를 위한 커스텀 훅
 * 
 * 이 훅은 컴포넌트 로컬 상태를 관리하기 위한 유틸리티 함수들을 제공합니다.
 * useState와 useReducer를 확장하여 더 강력한 기능을 제공합니다.
 */

import { useCallback, useReducer, useState } from 'react';

/**
 * 토글 상태를 관리하는 훅
 * 
 * @param initialState 초기 상태 (기본값: false)
 * @returns [상태, 토글 함수, 설정 함수]
 * 
 * @example
 * ```tsx
 * const [isOpen, toggleOpen, setOpen] = useToggle(false);
 * 
 * return (
 *   <>
 *     <button onClick={toggleOpen}>Toggle</button>
 *     <button onClick={() => setOpen(true)}>Open</button>
 *     <button onClick={() => setOpen(false)}>Close</button>
 *     {isOpen && <div>Content</div>}
 *   </>
 * );
 * ```
 */
export function useToggle(initialState = false): [
  boolean,
  () => void,
  (value: boolean) => void
] {
  const [state, setState] = useState(initialState);
  
  const toggle = useCallback(() => {
    setState((prev) => !prev);
  }, []);
  
  return [state, toggle, setState];
}

/**
 * 폼 입력 상태를 관리하는 훅
 * 
 * @param initialValues 초기 폼 값
 * @returns [폼 값, 입력 변경 핸들러, 폼 리셋 함수, 폼 값 설정 함수]
 * 
 * @example
 * ```tsx
 * const [values, handleChange, resetForm, setValues] = useFormState({
 *   name: '',
 *   email: '',
 * });
 * 
 * return (
 *   <form onSubmit={handleSubmit}>
 *     <input
 *       name="name"
 *       value={values.name}
 *       onChange={handleChange}
 *     />
 *     <input
 *       name="email"
 *       value={values.email}
 *       onChange={handleChange}
 *     />
 *     <button type="button" onClick={resetForm}>Reset</button>
 *     <button type="submit">Submit</button>
 *   </form>
 * );
 * ```
 */
export function useFormState<T extends Record<string, any>>(initialValues: T): [
  T,
  (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void,
  () => void,
  (values: Partial<T>) => void
] {
  const [values, setValues] = useState<T>(initialValues);
  
  const handleChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    setValues((prev) => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : value,
    }));
  }, []);
  
  const resetForm = useCallback(() => {
    setValues(initialValues);
  }, [initialValues]);
  
  const updateValues = useCallback((newValues: Partial<T>) => {
    setValues((prev) => ({ ...prev, ...newValues }));
  }, []);
  
  return [values, handleChange, resetForm, updateValues];
}

/**
 * 비동기 상태를 관리하는 훅
 * 
 * @returns [데이터, 로딩 상태, 에러, 비동기 함수 래퍼]
 * 
 * @example
 * ```tsx
 * const [data, isLoading, error, execute] = useAsync();
 * 
 * const fetchData = async () => {
 *   const result = await execute(async () => {
 *     const response = await fetch('/api/data');
 *     return response.json();
 *   });
 *   console.log(result);
 * };
 * 
 * return (
 *   <div>
 *     <button onClick={fetchData} disabled={isLoading}>
 *       {isLoading ? 'Loading...' : 'Fetch Data'}
 *     </button>
 *     {error && <div>Error: {error.message}</div>}
 *     {data && <div>Data: {JSON.stringify(data)}</div>}
 *   </div>
 * );
 * ```
 */
export function useAsync<T = any>(): [
  T | null,
  boolean,
  Error | null,
  <R = T>(asyncFn: () => Promise<R>) => Promise<R | null>
] {
  type AsyncState = {
    data: T | null;
    isLoading: boolean;
    error: Error | null;
  };
  
  type AsyncAction =
    | { type: 'REQUEST' }
    | { type: 'SUCCESS'; payload: T }
    | { type: 'FAILURE'; error: Error }
    | { type: 'RESET' };
  
  const initialState: AsyncState = {
    data: null,
    isLoading: false,
    error: null,
  };
  
  const asyncReducer = (state: AsyncState, action: AsyncAction): AsyncState => {
    switch (action.type) {
      case 'REQUEST':
        return { ...state, isLoading: true, error: null };
      case 'SUCCESS':
        return { data: action.payload, isLoading: false, error: null };
      case 'FAILURE':
        return { ...state, isLoading: false, error: action.error };
      case 'RESET':
        return initialState;
      default:
        return state;
    }
  };
  
  const [state, dispatch] = useReducer(asyncReducer, initialState);
  
  const execute = useCallback(async <R = T>(asyncFn: () => Promise<R>): Promise<R | null> => {
    try {
      dispatch({ type: 'REQUEST' });
      const result = await asyncFn();
      dispatch({ type: 'SUCCESS', payload: result as unknown as T });
      return result;
    } catch (error) {
      dispatch({ type: 'FAILURE', error: error as Error });
      return null;
    }
  }, []);
  
  return [state.data, state.isLoading, state.error, execute];
}

/**
 * 페이지네이션 상태를 관리하는 훅
 * 
 * @param initialPage 초기 페이지 (기본값: 1)
 * @param initialLimit 초기 페이지당 항목 수 (기본값: 10)
 * @returns 페이지네이션 상태 및 제어 함수
 * 
 * @example
 * ```tsx
 * const {
 *   page,
 *   limit,
 *   offset,
 *   nextPage,
 *   prevPage,
 *   setPage,
 *   setLimit,
 * } = usePagination();
 * 
 * // API 호출 시 사용
 * useEffect(() => {
 *   fetchItems({ offset, limit });
 * }, [offset, limit]);
 * 
 * return (
 *   <div>
 *     <ItemList items={items} />
 *     <div>
 *       <button onClick={prevPage} disabled={page === 1}>Previous</button>
 *       <span>Page {page}</span>
 *       <button onClick={nextPage}>Next</button>
 *     </div>
 *   </div>
 * );
 * ```
 */
export function usePagination(initialPage = 1, initialLimit = 10) {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  
  const nextPage = useCallback(() => {
    setPage((p) => p + 1);
  }, []);
  
  const prevPage = useCallback(() => {
    setPage((p) => (p > 1 ? p - 1 : p));
  }, []);
  
  const offset = (page - 1) * limit;
  
  return {
    page,
    limit,
    offset,
    nextPage,
    prevPage,
    setPage,
    setLimit,
  };
}
