'use client';

import { useState, useEffect } from 'react';

/**
 * 네트워크 상태를 감지하는 훅
 * 
 * 온라인/오프라인 상태를 감지하고 네트워크 상태가 변경될 때 콜백 함수를 실행합니다.
 */
export function useNetworkStatus(options?: {
  /**
   * 온라인 상태로 변경될 때 실행할 콜백 함수
   */
  onOnline?: () => void;
  
  /**
   * 오프라인 상태로 변경될 때 실행할 콜백 함수
   */
  onOffline?: () => void;
}) {
  const { onOnline, onOffline } = options || {};
  
  // 초기 상태는 navigator.onLine으로 설정 (기본값: true)
  const [isOnline, setIsOnline] = useState<boolean>(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  
  useEffect(() => {
    // 온라인 상태 변경 이벤트 핸들러
    const handleOnline = () => {
      setIsOnline(true);
      if (onOnline) onOnline();
    };
    
    // 오프라인 상태 변경 이벤트 핸들러
    const handleOffline = () => {
      setIsOnline(false);
      if (onOffline) onOffline();
    };
    
    // 이벤트 리스너 등록
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // 클린업 함수
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [onOnline, onOffline]);
  
  return { isOnline };
}
