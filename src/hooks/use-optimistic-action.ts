'use client';

import { useState, useCallback, useTransition, useOptimistic } from 'react';
import { ActionResponse } from '@/actions/utils';

type OptimisticActionOptions<T, U> = {
  getOptimisticData: (currentData: T, input: U) => T;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
};

/**
 * 낙관적 업데이트를 지원하는 Server Action 훅
 * @param action Server Action 함수
 * @param initialData 초기 데이터
 * @param options 옵션
 * @returns 액션 실행 함수, 낙관적 데이터, 로딩 상태, 에러
 */
export function useOptimisticAction<TData, TInput>(
  action: (input: TInput) => Promise<ActionResponse<any>>,
  initialData: TData,
  options: OptimisticActionOptions<TData, TInput>
) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | undefined>();
  const [data, setData] = useState<TData>(initialData);
  
  const [optimisticData, updateOptimisticData] = useOptimistic(
    data,
    (currentData, input: TInput) => options.getOptimisticData(currentData, input)
  );

  const execute = useCallback(
    async (input: TInput) => {
      setError(undefined);
      
      // 낙관적 업데이트 적용
      updateOptimisticData(input);
      
      startTransition(async () => {
        try {
          const response = await action(input);

          if (response.success) {
            // 실제 데이터로 업데이트
            setData(prev => options.getOptimisticData(prev, input));
            options.onSuccess?.(response.data);
          } else {
            // 에러 처리
            setError(response.error?.message || '알 수 없는 오류가 발생했습니다.');
            options.onError?.(response.error?.message || '알 수 없는 오류가 발생했습니다.');
          }
        } catch (error) {
          const message = error instanceof Error ? error.message : '알 수 없는 오류가 발생했습니다.';
          setError(message);
          options.onError?.(message);
        } finally {
          options.onComplete?.();
        }
      });
    },
    [action, options, updateOptimisticData]
  );

  return {
    execute,
    data: optimisticData,
    isPending,
    error,
    isError: !!error,
    reset: () => {
      setData(initialData);
      setError(undefined);
    },
  };
}
