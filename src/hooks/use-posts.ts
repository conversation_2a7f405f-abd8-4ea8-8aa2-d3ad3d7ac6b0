'use client';

import useSWR, { SWRConfiguration } from 'swr';
import { fetcher } from '@/lib/swr/fetcher';
import { useLoadingState } from './use-loading-state';
import { useErrorHandler } from './use-error-handler';

// 게시글 타입 정의
export interface Post {
  id: string;
  title: string;
  content: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  published: boolean;
  categoryId?: string;
  // 관계 데이터
  author?: {
    id: string;
    name: string;
    image?: string;
  };
  category?: {
    id: string;
    name: string;
  };
  _count?: {
    comments: number;
    likes: number;
  };
  // 추가 필드
  [key: string]: any;
}

// 게시글 필터 타입 정의
export interface PostFilters {
  categoryId?: string;
  authorId?: string;
  search?: string;
  published?: boolean;
  page?: number;
  limit?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// 게시글 목록 응답 타입
export interface PostsResponse {
  posts: Post[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * 게시글 목록을 가져오는 훅
 * 
 * @param filters 게시글 필터링 옵션
 * @param options SWR 옵션
 */
export function usePosts(filters: PostFilters = {}, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 쿼리 파라미터 생성
  const queryParams = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const apiUrl = `/api/posts${queryString ? `?${queryString}` : ''}`;
  
  // SWR로 게시글 목록 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<PostsResponse>(
    apiUrl,
    fetcher,
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    posts: data?.posts || [],
    total: data?.total || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    totalPages: data?.totalPages || 1,
    isLoading,
    isValidating,
    error,
    mutate,
    isEmpty: data?.posts.length === 0,
  };
}

/**
 * 특정 게시글의 상세 정보를 가져오는 훅
 * 
 * @param postId 게시글 ID
 * @param options SWR 옵션
 */
export function usePost(postId: string | null, options: SWRConfiguration = {}) {
  const { handleError } = useErrorHandler();
  
  // 게시글 ID가 없으면 API 호출 안 함
  const shouldFetch = !!postId;
  
  // SWR로 게시글 데이터 가져오기
  const {
    data,
    error,
    isLoading: isLoadingRaw,
    isValidating,
    mutate,
  } = useSWR<Post>(
    shouldFetch ? `/api/posts/${postId}` : null,
    fetcher,
    {
      onError: handleError,
      ...options,
    }
  );
  
  // 로딩 상태 관리
  const { isLoading } = useLoadingState(isLoadingRaw);
  
  return {
    post: data,
    isLoading,
    isValidating,
    error,
    mutate,
  };
}
