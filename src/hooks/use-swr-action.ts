'use client';

import { useCallback } from 'react';
import useS<PERSON>, { KeyedMutator } from 'swr';
import { ActionResponse } from '@/actions/utils';

type UseSWRActionOptions<T> = {
  fallbackData?: T;
  revalidateOnFocus?: boolean;
  revalidateOnMount?: boolean;
  revalidateOnReconnect?: boolean;
  refreshInterval?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
};

/**
 * SWR과 Server Action을 통합한 훅
 * @param key SWR 캐시 키
 * @param action Server Action 함수
 * @param input Server Action 입력 데이터
 * @param options SWR 옵션
 * @returns SWR 결과 및 액션 실행 함수
 */
export function useSWRAction<TInput, TOutput>(
  key: string,
  action: (input: TInput) => Promise<ActionResponse<TOutput>>,
  input: TInput,
  options: UseSWRActionOptions<TOutput> = {}
) {
  // SWR fetcher 함수
  const fetcher = useCallback(async () => {
    const response = await action(input);
    
    if (!response.success) {
      throw new Error(response.error?.message || '알 수 없는 오류가 발생했습니다.');
    }
    
    return response.data;
  }, [action, input]);

  // SWR 훅 사용
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  } = useSWR<TOutput, Error>(key, fetcher, {
    fallbackData: options.fallbackData,
    revalidateOnFocus: options.revalidateOnFocus ?? false,
    revalidateOnMount: options.revalidateOnMount ?? true,
    revalidateOnReconnect: options.revalidateOnReconnect ?? false,
    refreshInterval: options.refreshInterval,
    onSuccess: options.onSuccess,
    onError: options.onError,
  });

  // 액션 실행 함수
  const executeAction = useCallback(
    async (newInput: TInput): Promise<TOutput | undefined> => {
      try {
        const response = await action(newInput);
        
        if (response.success) {
          // 캐시 업데이트
          await mutate(response.data, {
            revalidate: false,
          });
          
          return response.data;
        } else {
          throw new Error(response.error?.message || '알 수 없는 오류가 발생했습니다.');
        }
      } catch (error) {
        if (options.onError && error instanceof Error) {
          options.onError(error);
        }
        throw error;
      }
    },
    [action, mutate, options.onError]
  );

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate: mutate as KeyedMutator<TOutput>,
    executeAction,
    isError: !!error,
  };
}
