import { getUserWithActivity } from '@/actions/users';
import { useSession } from 'next-auth/react';
import useSWR from 'swr';

/**
 * 사용자 데이터 타입
 */
export interface IUserData {
  /** 사용자 ID */
  id: string;
  /** 사용자 이름 */
  name: string | null;
  /** 표시 이름 */
  displayName: string | null;
  /** 이메일 */
  email: string | null;
  /** 프로필 이미지 */
  image: string | null;
  /** 게시물 수 */
  postCount: number;
  /** 댓글 수 */
  postCommentCount: number;
}

/**
 * useUser 훅 옵션 타입
 */
export interface IUseUserOptions {
  /** 조회할 사용자 ID */
  userId?: string;
  /** 조회할 사용자 이름 (@ 제외) */
  name?: string;
  /** 초기 데이터 (선택 사항) */
  fallbackData?: IUserData;
}

/**
 * 사용자 데이터 타입 가드 함수
 *
 * @param data 확인할 데이터
 * @returns 사용자 데이터인지 여부
 */
export function isUserData(data: any): data is IUserData {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.id === 'string' &&
    (data.name === null || typeof data.name === 'string') &&
    (data.displayName === null || typeof data.displayName === 'string') &&
    typeof data.postCount === 'number' &&
    typeof data.postCommentCount === 'number'
  );
}

/**
 * 사용자 정보를 가져오는 커스텀 훅
 *
 * @param options 옵션 객체
 * @returns 사용자 정보와 로딩 상태
 */
export function useUser(options: IUseUserOptions = {}) {
  const { userId, name, fallbackData } = options;
  const { status } = useSession();

  // SWR 키 생성 - 사용자 ID 또는 이름이 있으면 해당 값으로, 없으면 현재 사용자
  const swrKey = name
    ? ['user', name]
    : userId
      ? ['user', userId]
      : status === 'authenticated'
        ? ['user', 'me']
        : null;

  const { data, error, isLoading, isValidating, mutate } = useSWR(
    swrKey,
    async () => {
      // name 또는 userId 중 하나를 전달
      const input = name ? { name: name } : userId ? { id: userId } : undefined;
      const result = await getUserWithActivity(input);

      if (result.success && result.data?.user) {
        const userData = result.data.user;

        // 타입 가드를 사용하여 데이터 유효성 검증
        if (isUserData(userData)) {
          return userData;
        }

        // 데이터가 예상 형식이 아닌 경우 변환 시도
        if (userData && typeof userData === 'object') {
          const convertedData: IUserData = {
            id: typeof userData.id === 'string' ? userData.id : '',
            name: typeof userData.name === 'string' ? userData.name : null,
            displayName:
              typeof userData.displayName === 'string'
                ? userData.displayName
                : null,
            email: typeof userData.email === 'string' ? userData.email : null,
            image: typeof userData.image === 'string' ? userData.image : null,
            postCount:
              typeof userData.postCount === 'number' ? userData.postCount : 0,
            postCommentCount:
              typeof userData.postCommentCount === 'number'
                ? userData.postCommentCount
                : 0,
          };
          return convertedData;
        }
      }

      throw new Error('사용자 정보를 불러올 수 없습니다.');
    },
    {
      fallbackData,
      revalidateOnFocus: true,
      dedupingInterval: 60000, // 1분간 중복 요청 방지
    }
  );

  /**
   * 훅 반환 값
   */
  return {
    /** 사용자 데이터 */
    user: data,
    /** 에러 */
    error,
    /** 로딩 상태 */
    isLoading,
    /** 유효성 검사 중 상태 */
    isValidating,
    /** 데이터 갱신 함수 */
    mutate,
  };
}
