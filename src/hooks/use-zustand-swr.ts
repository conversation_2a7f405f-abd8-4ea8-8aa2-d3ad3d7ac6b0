/**
 * SWR과 Zustand를 통합하여 사용하는 커스텀 훅
 */

import { useDataStore } from '@/store/data-store';
import { useCallback, useEffect } from 'react';
import useSWR, { SWRConfiguration, SWRResponse } from 'swr';

/**
 * SWR과 Zustand를 통합하여 사용하는 커스텀 훅
 * 
 * @param key SWR 캐시 키
 * @param fetcher 데이터 페칭 함수
 * @param config SWR 설정
 * @returns SWR 응답 객체
 */
export function useZustandSWR<Data = any, Error = any>(
  key: string | null,
  fetcher: (key: string) => Promise<Data>,
  config?: SWRConfiguration<Data, Error>
): SWRResponse<Data, Error> {
  // Zustand 스토어에서 필요한 함수들 가져오기
  const setCache = useDataStore((state) => state.setCache);
  const getCache = useDataStore((state) => state.getCache);
  const setLoading = useDataStore((state) => state.setLoading);
  const setError = useDataStore((state) => state.setError);
  
  // 캐시 키 생성 (null이면 페칭하지 않음)
  const cacheKey = key ? `swr:${key}` : null;
  
  // 캐시된 데이터를 초기 데이터로 사용
  const initialData = cacheKey ? getCache<Data>(cacheKey) : undefined;
  
  // SWR 래퍼 fetcher 함수
  const wrappedFetcher = useCallback(
    async (k: string) => {
      if (!k) return null;
      
      try {
        // 로딩 상태 설정
        setLoading(cacheKey!, true);
        
        // 데이터 페칭
        const data = await fetcher(k);
        
        // 캐시에 데이터 저장
        setCache(cacheKey!, data, config?.dedupingInterval || 2000);
        
        // 에러 상태 초기화
        setError(cacheKey!, null);
        
        return data;
      } catch (err) {
        // 에러 상태 설정
        setError(cacheKey!, err as Error);
        throw err;
      } finally {
        // 로딩 상태 해제
        setLoading(cacheKey!, false);
      }
    },
    [cacheKey, fetcher, setCache, setLoading, setError, config?.dedupingInterval]
  );
  
  // SWR 훅 사용
  const swr = useSWR<Data, Error>(
    key,
    wrappedFetcher,
    {
      ...config,
      fallbackData: initialData,
    }
  );
  
  // 데이터가 변경될 때마다 캐시 업데이트
  useEffect(() => {
    if (cacheKey && swr.data !== undefined) {
      setCache(cacheKey, swr.data);
    }
  }, [cacheKey, swr.data, setCache]);
  
  return swr;
}

/**
 * 무한 스크롤을 위한 SWR과 Zustand 통합 훅
 * 
 * @param getKey 페이지 인덱스와 이전 페이지 데이터를 기반으로 다음 키를 생성하는 함수
 * @param fetcher 데이터 페칭 함수
 * @param config SWR 설정
 * @returns SWR 무한 스크롤 응답 객체
 */
export function useZustandSWRInfinite<Data = any, Error = any>(
  getKey: (pageIndex: number, previousPageData: Data | null) => string | null,
  fetcher: (key: string) => Promise<Data>,
  config?: SWRConfiguration<Data, Error>
) {
  // 여기에 무한 스크롤을 위한 SWR과 Zustand 통합 로직 구현
  // useSWRInfinite와 유사하게 구현
  
  // 이 부분은 프로젝트의 요구사항에 맞게 추가 구현 필요
  
  return useSWR<Data, Error>(null, () => Promise.resolve(null as any), config);
}
