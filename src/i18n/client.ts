import { createSharedPathnamesNavigation } from 'next-intl/navigation';
import { locales, defaultLocale } from './request';

// 쿠키에 로케일 설정 저장
export function setLocale(locale: string) {
  if (locales.includes(locale)) {
    document.cookie = `NEXT_LOCALE=${locale}; path=/; max-age=31536000; SameSite=Lax`;
    window.location.reload();
  }
}

// 현재 로케일 가져오기
export function getLocaleFromCookie(): string {
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'NEXT_LOCALE') {
      return value;
    }
  }
  return defaultLocale;
}

// 기본 경로 설정 (URL 구조 변경 없음)
export const { Link, redirect, usePathname, useRouter } = createSharedPathnamesNavigation({
  locales,
  localePrefix: 'never'
});
