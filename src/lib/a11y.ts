/**
 * 접근성 관련 유틸리티 함수
 */

import { KeyboardEvent } from 'react';

/**
 * 키보드 이벤트 핸들러 - Enter 또는 Space 키 입력 시 클릭 이벤트 실행
 * 
 * @param onClick 클릭 이벤트 핸들러
 * @returns 키보드 이벤트 핸들러
 * 
 * @example
 * <div
 *   role="button"
 *   tabIndex={0}
 *   onClick={handleClick}
 *   onKeyDown={handleKeyboardClick(handleClick)}
 * >
 *   클릭 가능한 요소
 * </div>
 */
export function handleKeyboardClick<T>(
  onClick: (event: React.MouseEvent<T> | React.KeyboardEvent<T>) => void
) {
  return (event: React.KeyboardEvent<T>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick(event);
    }
  };
}

/**
 * 키보드 이벤트 핸들러 - Escape 키 입력 시 콜백 실행
 * 
 * @param callback Escape 키 입력 시 실행할 콜백 함수
 * @returns 키보드 이벤트 핸들러
 * 
 * @example
 * <div
 *   onKeyDown={handleEscapeKey(() => setIsOpen(false))}
 * >
 *   Escape 키로 닫을 수 있는 요소
 * </div>
 */
export function handleEscapeKey(callback: () => void) {
  return (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      callback();
    }
  };
}

/**
 * 키보드 이벤트 핸들러 - 화살표 키로 포커스 이동
 * 
 * @param direction 이동 방향 ('horizontal' 또는 'vertical')
 * @param elements 포커스 가능한 요소 배열 또는 선택자
 * @returns 키보드 이벤트 핸들러
 * 
 * @example
 * <ul
 *   role="tablist"
 *   onKeyDown={handleArrowNavigation('horizontal', '.tab-item')}
 * >
 *   <li className="tab-item" tabIndex={0} role="tab">탭 1</li>
 *   <li className="tab-item" tabIndex={-1} role="tab">탭 2</li>
 * </ul>
 */
export function handleArrowNavigation(
  direction: 'horizontal' | 'vertical',
  elements: string | HTMLElement[]
) {
  return (event: KeyboardEvent) => {
    // 방향에 따른 키 매핑
    const keys = direction === 'horizontal' 
      ? { prev: 'ArrowLeft', next: 'ArrowRight' }
      : { prev: 'ArrowUp', next: 'ArrowDown' };
    
    // 키가 화살표 키가 아니면 무시
    if (event.key !== keys.prev && event.key !== keys.next) {
      return;
    }
    
    event.preventDefault();
    
    // 포커스 가능한 요소 가져오기
    let focusableElements: HTMLElement[];
    if (typeof elements === 'string') {
      focusableElements = Array.from(
        document.querySelectorAll(elements)
      ) as HTMLElement[];
    } else {
      focusableElements = elements;
    }
    
    if (focusableElements.length === 0) return;
    
    // 현재 포커스된 요소의 인덱스 찾기
    const currentIndex = focusableElements.findIndex(
      el => el === document.activeElement
    );
    
    // 다음/이전 요소 인덱스 계산
    let nextIndex;
    if (event.key === keys.next) {
      nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
    } else {
      nextIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
    }
    
    // 다음/이전 요소에 포커스
    focusableElements[nextIndex].focus();
  };
}

/**
 * 스크린 리더에서만 보이는 텍스트를 위한 클래스
 * 
 * @example
 * <span className={srOnly}>스크린 리더에서만 보이는 텍스트</span>
 */
export const srOnly = 'absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0';

/**
 * 스크린 리더에서만 보이는 텍스트 컴포넌트
 * 
 * @example
 * <SROnly>스크린 리더에서만 보이는 텍스트</SROnly>
 */
export function SROnly({ children }: { children: React.ReactNode }) {
  return <span className={srOnly}>{children}</span>;
}
