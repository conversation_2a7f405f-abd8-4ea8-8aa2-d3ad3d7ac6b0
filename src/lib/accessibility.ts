/**
 * 접근성 관련 유틸리티 함수 모음
 */

/**
 * 키보드 이벤트 핸들러 - Enter 또는 Space 키 입력 시 콜백 함수 실행
 * 비버튼 요소에 버튼 동작을 추가할 때 사용
 * @param callback 실행할 콜백 함수
 * @returns 키보드 이벤트 핸들러
 */
export function handleKeyboardActivation(
  callback: () => void
): React.KeyboardEventHandler {
  return (event) => {
    if (event.key === "Enter" || event.key === "Space" || event.key === " ") {
      event.preventDefault();
      callback();
    }
  };
}

/**
 * 포커스 트랩 - 모달 내에서 포커스가 유지되도록 함
 * @param containerRef 포커스를 가둘 컨테이너 요소의 ref
 * @param isActive 포커스 트랩 활성화 여부
 */
export function useFocusTrap(
  containerRef: React.RefObject<HTMLElement>,
  isActive: boolean = true
): void {
  React.useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== "Tab") return;

      if (event.shiftKey) {
        // Shift + Tab: 첫 번째 요소에서 마지막 요소로 이동
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab: 마지막 요소에서 첫 번째 요소로 이동
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    // 초기 포커스 설정
    firstElement.focus();

    // 이벤트 리스너 등록
    container.addEventListener("keydown", handleTabKey);

    return () => {
      container.removeEventListener("keydown", handleTabKey);
    };
  }, [containerRef, isActive]);
}

/**
 * 스크롤 잠금 - 모달이 열려 있을 때 배경 스크롤 방지
 * @param isLocked 스크롤 잠금 여부
 */
export function useScrollLock(isLocked: boolean): void {
  React.useEffect(() => {
    if (isLocked) {
      // 현재 스크롤 위치 저장
      const scrollY = window.scrollY;
      
      // 스크롤 잠금
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
      document.body.style.overflowY = "scroll";
      
      return () => {
        // 스크롤 잠금 해제
        document.body.style.position = "";
        document.body.style.top = "";
        document.body.style.width = "";
        document.body.style.overflowY = "";
        
        // 이전 스크롤 위치로 복원
        window.scrollTo(0, scrollY);
      };
    }
  }, [isLocked]);
}

/**
 * 스킵 링크 대상 ID 생성
 * 스킵 링크는 키보드 사용자가 반복되는 네비게이션을 건너뛸 수 있게 해줌
 * @param id 스킵 링크 대상 ID
 * @returns 스킵 링크 대상 ID 속성
 */
export function getSkipLinkTargetProps(id: string): { id: string; tabIndex: -1 } {
  return {
    id,
    tabIndex: -1,
  };
}

/**
 * 색상 대비 검사
 * WCAG 2.1 AA 기준 (일반 텍스트 4.5:1, 큰 텍스트 3:1)
 * @param foreground 전경색 (HEX)
 * @param background 배경색 (HEX)
 * @param isLargeText 큰 텍스트 여부
 * @returns 색상 대비가 충분한지 여부
 */
export function hasAdequateContrast(
  foreground: string,
  background: string,
  isLargeText: boolean = false
): boolean {
  // HEX to RGB 변환
  const hexToRgb = (hex: string): number[] => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return [r, g, b];
  };

  // 상대적 휘도 계산
  const getLuminance = (rgb: number[]): number => {
    const [r, g, b] = rgb.map((v) => {
      v /= 255;
      return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const rgb1 = hexToRgb(foreground);
  const rgb2 = hexToRgb(background);
  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);

  // 대비율 계산
  const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);

  // WCAG 2.1 AA 기준 검사
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
}
