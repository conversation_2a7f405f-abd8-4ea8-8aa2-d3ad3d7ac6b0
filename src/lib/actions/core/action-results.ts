/**
 * Utility functions for creating standardized action results
 */

import { z } from 'zod';
import {
  ACTION_ERROR_CODES,
  ActionErrorCode,
  ActionErrorResult,
  ActionResult,
  ActionSuccessResult,
} from './action-types';

/**
 * Create a success result
 */
export function createSuccessResult<T>(
  data: T,
  message?: string
): ActionSuccessResult<T> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create an error result
 */
export function createErrorResult(
  message: string,
  code: ActionErrorCode = ACTION_ERROR_CODES.INTERNAL_ERROR,
  error?: any
): ActionErrorResult {
  return {
    success: false,
    message,
    code,
    error,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create a validation error result from a Zod error
 */
export function createValidationErrorResult(
  zodError: z.ZodError,
  message: string = '입력 값이 유효하지 않습니다.'
): ActionErrorResult {
  return createErrorResult(
    message,
    ACTION_ERROR_CODES.VALIDATION_ERROR,
    zodError.flatten()
  );
}

/**
 * Create an authentication error result
 */
export function createAuthenticationErrorResult(
  message: string = '로그인이 필요합니다.'
): ActionErrorResult {
  return createErrorResult(message, ACTION_ERROR_CODES.AUTHENTICATION_ERROR);
}

/**
 * Create an authorization error result
 */
export function createAuthorizationErrorResult(
  message: string = '접근 권한이 없습니다.'
): ActionErrorResult {
  return createErrorResult(message, ACTION_ERROR_CODES.AUTHORIZATION_ERROR);
}

/**
 * Create a not found error result
 */
export function createNotFoundErrorResult(
  message: string = '요청한 리소스를 찾을 수 없습니다.'
): ActionErrorResult {
  return createErrorResult(message, ACTION_ERROR_CODES.NOT_FOUND);
}

/**
 * Create a conflict error result
 */
export function createConflictErrorResult(
  message: string = '리소스 충돌이 발생했습니다.'
): ActionErrorResult {
  return createErrorResult(message, ACTION_ERROR_CODES.CONFLICT);
}

/**
 * Create a bad request error result
 */
export function createBadRequestErrorResult(
  message: string = '잘못된 요청입니다.',
  error?: any
): ActionErrorResult {
  return createErrorResult(message, ACTION_ERROR_CODES.BAD_REQUEST, error);
}

/**
 * Create an internal error result
 */
export function createInternalErrorResult(
  message: string = '서버 내부 오류가 발생했습니다.',
  error?: any
): ActionErrorResult {
  return createErrorResult(message, ACTION_ERROR_CODES.INTERNAL_ERROR, error);
}

/**
 * Create an error result from an unknown error
 */
export function createErrorResultFromUnknown(
  error: unknown,
  defaultMessage: string = '알 수 없는 오류가 발생했습니다.'
): ActionErrorResult {
  if (error instanceof z.ZodError) {
    return createValidationErrorResult(error);
  }

  const message = error instanceof Error ? error.message : defaultMessage;
  return createInternalErrorResult(message, error);
}

/**
 * Check if a result is a success result
 */
export function isSuccessResult<T>(
  result: ActionResult<T>
): result is ActionSuccessResult<T> {
  return result.success === true;
}

/**
 * Check if a result is an error result
 */
export function isErrorResult(
  result: ActionResult<any>
): result is ActionErrorResult {
  return result.success === false;
}
