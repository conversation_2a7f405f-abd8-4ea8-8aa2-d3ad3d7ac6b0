/**
 * Core types for server actions
 */

import { Session } from 'next-auth';
import { z } from 'zod';

/**
 * Standard error result for server actions
 */
export interface ActionErrorResult {
  success: false;
  message: string;
  code?: string;
  error?: any;
  timestamp?: string;
}

/**
 * Standard success result for server actions
 */
export interface ActionSuccessResult<T = any> {
  success: true;
  data: T;
  message?: string;
  timestamp?: string;
}

/**
 * Union type for action results
 */
export type ActionResult<T = any> = ActionSuccessResult<T> | ActionErrorResult;

/**
 * Type definition for the actual server action logic function.
 * It receives validated data (if a schema is provided) or the raw input.
 */
export type ActionFunction<InputType, ReturnType> = (
  input: InputType
) => Promise<ReturnType>;

/**
 * Type definition for the actual server action logic function requiring authentication.
 * It receives the authenticated user session and validated data (if a schema is provided) or the raw input.
 */
export type ActionFunctionWithAuth<InputType, ReturnType> = (
  session: Session,
  input: InputType
) => Promise<ReturnType>;

/**
 * Type definition for the actual server action logic function requiring authentication with role check.
 * It receives the authenticated user session, roles, and validated data (if a schema is provided) or the raw input.
 */
export type ActionFunctionWithAuthAndRoles<InputType, ReturnType> = (
  session: Session,
  roles: string[],
  input: InputType
) => Promise<ReturnType>;

/**
 * Error codes for server actions
 */
export const ACTION_ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
} as const;

/**
 * Type for action error codes
 */
export type ActionErrorCode = typeof ACTION_ERROR_CODES[keyof typeof ACTION_ERROR_CODES];

/**
 * Options for action handlers
 */
export interface ActionHandlerOptions<SchemaType extends z.ZodSchema<any> | undefined> {
  schema?: SchemaType;
  errorMessages?: {
    validation?: string;
    internal?: string;
  };
  logErrors?: boolean;
}
