/**
 * Authentication utilities for server actions
 */

'use server';

import { auth } from '@/auth';
import { Session } from 'next-auth';
import {
  createAuthenticationErrorResult,
  createAuthorizationErrorResult,
  createInternalErrorResult,
} from './action-results';
import { ActionErrorResult } from './action-types';

/**
 * Get the current user session
 * @returns The user session or null if not authenticated
 */
export async function getSession(): Promise<Session | null> {
  try {
    return await auth();
  } catch (error) {
    console.error('Error getting session:', error);
    return null;
  }
}

/**
 * Check if the user is authenticated
 * @returns True if authenticated, false otherwise
 */
export async function isAuthenticated(): Promise<boolean> {
  const session = await getSession();
  return !!session?.user?.id;
}

/**
 * Require authentication and return the session
 * @returns The user session or an error result if not authenticated
 */
export async function requireAuthSession(): Promise<
  Session | ActionErrorResult
> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return createAuthenticationErrorResult();
    }

    return session;
  } catch (error) {
    console.error('Authentication check failed:', error);
    return createInternalErrorResult(
      '인증 확인 중 오류가 발생했습니다.',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Check if the user has the required role
 * @param session The user session
 * @param role The required role
 * @returns True if the user has the role, false otherwise
 */
export async function hasRole(
  session: Session,
  role: string
): Promise<boolean> {
  if (role === 'ADMIN') {
    return !!session.user?.isAdmin;
  }
  if (role === 'TESTER') {
    return !!session.user?.isTester;
  }
  // 기본 사용자 역할은 항상 가지고 있음
  if (role === 'USER') {
    return true;
  }
  return false;
}

/**
 * Check if the user has any of the required roles
 * @param session The user session
 * @param roles The required roles
 * @returns True if the user has any of the roles, false otherwise
 */
export async function hasAnyRole(
  session: Session,
  roles: string[]
): Promise<boolean> {
  // 관리자 역할 확인
  if (roles.includes('ADMIN') && session.user?.isAdmin) {
    return true;
  }

  // 테스터 역할 확인
  if (roles.includes('TESTER') && session.user?.isTester) {
    return true;
  }

  // 일반 사용자 역할 확인
  if (roles.includes('USER')) {
    return true;
  }

  return false;
}

/**
 * Require the user to have the specified role
 * @param session The user session
 * @param role The required role
 * @returns The session if the user has the role, an error result otherwise
 */
export async function requireRole(
  session: Session,
  role: string
): Promise<Session | ActionErrorResult> {
  if (!(await hasRole(session, role))) {
    return createAuthorizationErrorResult(
      `이 작업을 수행하려면 '${role}' 역할이 필요합니다.`
    );
  }

  return session;
}

/**
 * Require the user to have any of the specified roles
 * @param session The user session
 * @param roles The required roles
 * @returns The session if the user has any of the roles, an error result otherwise
 */
export async function requireAnyRole(
  session: Session,
  roles: string[]
): Promise<Session | ActionErrorResult> {
  if (!(await hasAnyRole(session, roles))) {
    return createAuthorizationErrorResult(
      `이 작업을 수행하려면 다음 역할 중 하나가 필요합니다: ${roles.join(', ')}`
    );
  }

  return session;
}

/**
 * Check if the user owns the resource
 * @param session The user session
 * @param resourceUserId The user ID associated with the resource
 * @returns True if the user owns the resource, false otherwise
 */
export async function isResourceOwner(
  session: Session,
  resourceUserId: string
): Promise<boolean> {
  return session.user?.id === resourceUserId;
}

/**
 * Require the user to own the resource
 * @param session The user session
 * @param resourceUserId The user ID associated with the resource
 * @returns The session if the user owns the resource, an error result otherwise
 */
export async function requireResourceOwner(
  session: Session,
  resourceUserId: string
): Promise<Session | ActionErrorResult> {
  if (!(await isResourceOwner(session, resourceUserId))) {
    return createAuthorizationErrorResult(
      '이 리소스에 대한 접근 권한이 없습니다.'
    );
  }

  return session;
}
