import {
  ActionResponse,
  createValidationErrorResponse,
} from '@/utils/response';
import { z } from 'zod';

/**
 * Zod 스키마를 사용하여 입력 데이터 유효성 검사를 수행하는 고차 함수
 * @param schema Zod 스키마
 * @param handler 유효성 검사 후 실행할 핸들러 함수
 */
export function withValidation<T, R>(
  schema: z.<PERSON><T>,
  handler: (data: T) => Promise<ActionResponse<R>>
) {
  return async (data: unknown): Promise<ActionResponse<R>> => {
    try {
      // 입력 데이터 유효성 검사
      const validatedData = schema.parse(data);

      // 유효성 검사 통과 후 핸들러 실행
      return await handler(validatedData);
    } catch (error) {
      // Zod 유효성 검사 에러 처리
      if (error instanceof z.ZodError) {
        const firstError = error.errors[0];
        const path = firstError.path.join('.');
        return createValidationErrorResponse(
          firstError.message,
          path
        ) as ActionResponse<R>;
      }

      // 기타 에러 처리
      const message =
        error instanceof Error
          ? error.message
          : '알 수 없는 오류가 발생했습니다.';
      return createValidationErrorResponse(message) as ActionResponse<R>;
    }
  };
}
