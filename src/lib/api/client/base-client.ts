import {
  ApiError,
  BadRequestException,
  InternalServerErrorException,
  NetworkError,
  NotFoundException,
  TimeoutError,
  UnauthorizedException,
} from '../types/error';
import { ApiErrorResponse } from '../types/response';

export interface FetchOptions extends RequestInit {
  timeout?: number;
}

/**
 * Base API client for making HTTP requests
 */
export class BaseApiClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;
  private defaultTimeout: number;

  constructor(
    baseUrl: string = '/api',
    defaultHeaders: HeadersInit = {},
    defaultTimeout: number = 10000
  ) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders,
    };
    this.defaultTimeout = defaultTimeout;
  }

  /**
   * Make a GET request
   */
  async get<T>(path: string, options: FetchOptions = {}): Promise<T> {
    return this.request<T>(path, { ...options, method: 'GET' });
  }

  /**
   * Make a POST request
   */
  async post<T>(
    path: string,
    data?: any,
    options: FetchOptions = {}
  ): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PUT request
   */
  async put<T>(
    path: string,
    data?: any,
    options: FetchOptions = {}
  ): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PATCH request
   */
  async patch<T>(
    path: string,
    data?: any,
    options: FetchOptions = {}
  ): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(path: string, options: FetchOptions = {}): Promise<T> {
    return this.request<T>(path, { ...options, method: 'DELETE' });
  }

  /**
   * Make a request with timeout
   */
  private async request<T>(
    path: string,
    options: FetchOptions = {}
  ): Promise<T> {
    const { timeout = this.defaultTimeout, headers, ...restOptions } = options;

    const url = this.resolveUrl(path);
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...restOptions,
        headers: {
          ...this.defaultHeaders,
          ...headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData =
          await this.parseResponseData<ApiErrorResponse>(response);

        // 상태 코드에 따라 적절한 예외 클래스 사용
        const errorMessage =
          errorData?.error?.message || `HTTP error ${response.status}`;
        const errorDetails = errorData?.error?.data;

        switch (response.status) {
          case 400:
            throw new BadRequestException(errorMessage, errorDetails);
          case 401:
            throw new UnauthorizedException(errorMessage);
          case 404:
            throw new NotFoundException(errorMessage);
          case 500:
            throw new InternalServerErrorException(errorMessage);
          default:
            throw new ApiError(errorMessage, response.status, errorDetails);
        }
      }

      return await this.parseResponseData<T>(response);
    } catch (error) {
      clearTimeout(timeoutId);

      if (
        error instanceof ApiError ||
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }

      if (error instanceof DOMException && error.name === 'AbortError') {
        throw new TimeoutError();
      }

      throw new NetworkError(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Parse response data
   */
  private async parseResponseData<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return await response.json();
    }

    const text = await response.text();

    try {
      return JSON.parse(text) as T;
    } catch {
      return text as unknown as T;
    }
  }

  /**
   * Resolve URL
   */
  private resolveUrl(path: string): string {
    if (path.startsWith('http')) {
      return path;
    }

    // 절대 경로인 경우 (예: /api/users)
    if (path.startsWith('/')) {
      return path;
    }

    const baseUrl = this.baseUrl.endsWith('/')
      ? this.baseUrl.slice(0, -1)
      : this.baseUrl;

    const normalizedPath = `/${path}`;

    return `${baseUrl}${normalizedPath}`;
  }
}
