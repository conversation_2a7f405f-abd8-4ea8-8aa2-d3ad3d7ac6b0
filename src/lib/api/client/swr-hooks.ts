'use client';

import useS<PERSON>, { SWRConfiguration, SWRResponse } from 'swr';
import useSWRInfinite, {
  SWRInfiniteConfiguration,
  SWRInfiniteResponse,
} from 'swr/infinite';

import {
  CursorPaginationParams,
  GetNextPageParamFunction,
  PaginationParams,
} from '../types/pagination';
import { ApiCursorResponse } from '../types/response';
import { BaseApiClient } from './base-client';

// Default API client instance
const defaultApiClient = new BaseApiClient();

/**
 * Type for the fetcher function
 * @private 내부적으로만 사용하는 타입
 */
type _Fetcher<Data> = (...args: any[]) => Promise<Data>;

/**
 * Create a fetcher function for SWR
 */
export function createFetcher<Data>(
  apiClient: BaseApiClient = defaultApiClient
): (url: string) => Promise<Data> {
  return (url: string) => apiClient.get<Data>(url);
}

/**
 * Base hook for using SWR with API
 */
export function useApi<Data, Error = any>(
  url: string | null,
  config?: SWRConfiguration<Data, Error>,
  apiClient: BaseApiClient = defaultApiClient
): SWRResponse<Data, Error> {
  const fetcher = createFetcher<Data>(apiClient);

  return useSWR<Data, Error>(url, fetcher, {
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Hook for using SWR with API response transformation
 */
export function useApiWithTransform<ApiData, TransformedData, Error = any>(
  url: string | null,
  transform: (data: ApiData) => TransformedData,
  config?: SWRConfiguration<TransformedData, Error>,
  apiClient: BaseApiClient = defaultApiClient
): SWRResponse<TransformedData, Error> {
  const fetcher = async (url: string) => {
    const data = await apiClient.get<ApiData>(url);
    return transform(data);
  };

  return useSWR<TransformedData, Error>(url, fetcher, {
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Hook for using SWR with paginated API
 */
export function usePaginatedApi<_T, Error = any>(
  baseUrl: string,
  params: PaginationParams,
  config?: SWRConfiguration<any, Error>,
  apiClient: BaseApiClient = defaultApiClient
): SWRResponse<any, Error> {
  const { page = 1, size = 10, sort, order } = params; // pageSize에서 size로 변경

  const url = baseUrl
    ? `${baseUrl}?page=${page}&size=${size}${sort ? `&sort=${sort}` : ''}${order ? `&order=${order}` : ''}`
    : null;

  // 로그 추가
  console.log('Paginated API URL:', url);

  return useApi<any, Error>(url, config, apiClient);
}

/**
 * Hook for using SWRInfinite with cursor-based pagination
 */
export function useInfiniteApi<T, C = string, Error = any>(
  getKey: (
    pageIndex: number,
    previousPageData: ApiCursorResponse<T, C> | null
  ) => string | null,
  config?: SWRInfiniteConfiguration<ApiCursorResponse<T, C>, Error>,
  apiClient: BaseApiClient = defaultApiClient
): SWRInfiniteResponse<ApiCursorResponse<T, C>, Error> {
  const fetcher = createFetcher<ApiCursorResponse<T, C>>(apiClient);

  return useSWRInfinite<ApiCursorResponse<T, C>, Error>(getKey, fetcher, {
    revalidateAll: false,
    revalidateFirstPage: true,
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Create a key generator for cursor-based pagination
 */
export function createCursorPaginationKeyGenerator<T, C = string>(
  baseUrl: string,
  getNextPageParam: GetNextPageParamFunction<ApiCursorResponse<T, C>, C>,
  initialParams?: CursorPaginationParams<C>
) {
  const { limit = 10 } = initialParams || {};

  return (
    pageIndex: number,
    previousPageData: ApiCursorResponse<T, C> | null
  ): string | null => {
    // First page
    if (pageIndex === 0) {
      return `${baseUrl}?limit=${limit}`;
    }

    // Reached the end
    if (previousPageData && !previousPageData.hasMore) {
      return null;
    }

    // Get next cursor
    const cursor = previousPageData
      ? getNextPageParam(previousPageData, [])
      : null;

    // No cursor available
    if (!cursor) {
      return null;
    }

    // Next page with cursor
    return `${baseUrl}?cursor=${encodeURIComponent(String(cursor))}&limit=${limit}`;
  };
}

/**
 * Hook for using SWRInfinite with cursor-based pagination and transformation
 */
export function useInfiniteApiWithTransform<
  ApiData,
  TransformedData,
  _C = string, // 현재 사용하지 않지만 타입 호환성을 위해 유지
  Error = any,
>(
  getKey: (
    pageIndex: number,
    previousPageData: ApiData | null
  ) => string | null,
  transform: (data: ApiData) => TransformedData,
  config?: SWRInfiniteConfiguration<TransformedData, Error>,
  apiClient: BaseApiClient = defaultApiClient
): SWRInfiniteResponse<TransformedData, Error> {
  const fetcher = async (url: string) => {
    const data = await apiClient.get<ApiData>(url);
    return transform(data);
  };

  return useSWRInfinite<TransformedData, Error>(getKey, fetcher, {
    revalidateAll: false,
    revalidateFirstPage: true,
    revalidateOnFocus: false,
    ...config,
  });
}
