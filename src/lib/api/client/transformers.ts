import {
  ApiCursorResponse,
  ApiListResponse,
  ApiResponse,
} from '../types/response';

/**
 * Base transformer interface
 */
export interface Transformer<ApiData, ModelData> {
  fromApi(apiData: ApiData): ModelData;
  toApi?(modelData: ModelData): ApiData;
}

/**
 * Create a transformer for API responses
 */
export function createTransformer<ApiData, ModelData>(
  fromApi: (apiData: ApiData) => ModelData,
  toApi?: (modelData: ModelData) => ApiData
): Transformer<ApiData, ModelData> {
  return {
    fromApi,
    toApi,
  };
}

/**
 * Transform a list of items using a transformer
 */
export function transformList<ApiItem, ModelItem>(
  items: ApiItem[],
  transformer: Transformer<ApiItem, ModelItem>
): ModelItem[] {
  return items.map((item) => transformer.fromApi(item));
}

/**
 * Transform an API list response
 */
export function transformApiListResponse<ApiItem, ModelItem>(
  response: ApiListResponse<ApiItem>,
  transformer: Transformer<ApiItem, ModelItem>
): ApiListResponse<ModelItem> {
  return {
    ...response,
    items: transformList(response.items, transformer),
  };
}

/**
 * Transform an API cursor response
 */
export function transformApiCursorResponse<ApiItem, ModelItem, C = string>(
  response: ApiCursorResponse<ApiItem, C>,
  transformer: Transformer<ApiItem, ModelItem>
): ApiCursorResponse<ModelItem, C> {
  return {
    ...response,
    items: transformList(response.items, transformer),
  };
}

/**
 * Transform a generic API response
 */
export function transformApiResponse<ApiData, ModelData>(
  response: ApiResponse<ApiData>,
  transformer: Transformer<ApiData, ModelData>
): ApiResponse<ModelData> {
  return {
    ...response,
    data: transformer.fromApi(response.data),
  };
}
