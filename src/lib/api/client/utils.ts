import { ApiCursorResponse } from '../types/response';

/**
 * Create query string from parameters
 */
export function createQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
}

/**
 * Combine multiple pages of cursor-based responses
 */
export function combineCursorPages<T, C = string>(
  pages: ApiCursorResponse<T, C>[]
): ApiCursorResponse<T, C> {
  if (!pages.length) {
    return { items: [], hasMore: false };
  }

  const lastPage = pages[pages.length - 1];

  return {
    items: pages.flatMap((page) => page.items),
    nextCursor: lastPage.nextCursor,
    prevCursor: pages[0].prevCursor,
    hasMore: lastPage.hasMore,
  };
}

/**
 * Get next cursor from a cursor-based response
 */
export function getNextCursor<T, C = string>(
  page: ApiCursorResponse<T, C>
): C | null | undefined {
  return page.nextCursor;
}

/**
 * Check if there are more pages
 */
export function hasMorePages<T, C = string>(
  page: ApiCursorResponse<T, C>
): boolean {
  return page.hasMore;
}

/**
 * Create a URL with query parameters
 */
export function createUrl(
  baseUrl: string,
  params: Record<string, any>
): string {
  const queryString = createQueryString(params);
  return `${baseUrl}${queryString}`;
}
