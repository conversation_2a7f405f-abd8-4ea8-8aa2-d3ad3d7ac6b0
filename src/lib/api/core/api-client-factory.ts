/**
 * Factory for creating domain-specific API clients
 */

import { ApiClient, RequestInterceptor, ResponseInterceptor } from './api-client';
import { createAuthInterceptor, createErrorInterceptor, createLoggingInterceptor } from './interceptors';

export interface ApiClientOptions {
  baseUrl?: string;
  defaultHeaders?: HeadersInit;
  defaultTimeout?: number;
  withAuth?: boolean;
  withLogging?: boolean;
  withErrorHandling?: boolean;
  getToken?: () => Promise<string | null> | string | null;
  additionalRequestInterceptors?: RequestInterceptor[];
  additionalResponseInterceptors?: ResponseInterceptor[];
}

/**
 * Create an API client with common interceptors
 */
export function createApiClient(options: ApiClientOptions = {}): ApiClient {
  const {
    baseUrl = '/api',
    defaultHeaders = {},
    defaultTimeout = 10000,
    withAuth = false,
    withLogging = process.env.NODE_ENV !== 'production',
    withErrorHandling = true,
    getToken = () => null,
    additionalRequestInterceptors = [],
    additionalResponseInterceptors = [],
  } = options;

  // Create the API client
  const apiClient = new ApiClient(baseUrl, defaultHeaders, defaultTimeout);

  // Add request interceptors
  if (withAuth) {
    apiClient.addRequestInterceptor(createAuthInterceptor(getToken));
  }

  if (withLogging) {
    const loggingInterceptor = createLoggingInterceptor();
    apiClient.addRequestInterceptor(loggingInterceptor);
    apiClient.addResponseInterceptor(loggingInterceptor);
  }

  // Add additional request interceptors
  for (const interceptor of additionalRequestInterceptors) {
    apiClient.addRequestInterceptor(interceptor);
  }

  // Add response interceptors
  if (withErrorHandling) {
    apiClient.addResponseInterceptor(createErrorInterceptor());
  }

  // Add additional response interceptors
  for (const interceptor of additionalResponseInterceptors) {
    apiClient.addResponseInterceptor(interceptor);
  }

  return apiClient;
}

/**
 * Create a domain-specific API client
 */
export function createDomainApiClient<T extends Record<string, Function>>(
  basePath: string,
  methods: (apiClient: ApiClient) => T,
  options: ApiClientOptions = {}
): T & { apiClient: ApiClient } {
  // Create the API client with the base path
  const apiClient = createApiClient({
    ...options,
    baseUrl: options.baseUrl ? `${options.baseUrl}${basePath}` : basePath,
  });

  // Create the domain-specific methods
  const domainMethods = methods(apiClient);

  // Return the methods and the API client
  return {
    ...domainMethods,
    apiClient,
  };
}
