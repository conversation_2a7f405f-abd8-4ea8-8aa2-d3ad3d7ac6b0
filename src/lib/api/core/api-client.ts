/**
 * Core API client for making HTTP requests
 * This is the foundation of the centralized API client architecture
 */

import { ApiError, NetworkError, TimeoutError } from '../types/error';
import { ApiErrorResponse } from '../types/response';

export interface RequestOptions extends RequestInit {
  timeout?: number;
  baseUrl?: string;
  headers?: HeadersInit;
  params?: Record<string, string | number | boolean | undefined | null>;
}

export interface RequestInterceptor {
  onRequest: (config: RequestOptions) => Promise<RequestOptions> | RequestOptions;
}

export interface ResponseInterceptor<T = any> {
  onResponse: (response: Response) => Promise<T> | T;
  onError: (error: any) => Promise<T> | never;
}

/**
 * Core API client for making HTTP requests
 */
export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;
  private defaultTimeout: number;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];

  constructor(
    baseUrl: string = '/api',
    defaultHeaders: HeadersInit = {},
    defaultTimeout: number = 10000
  ) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders,
    };
    this.defaultTimeout = defaultTimeout;
  }

  /**
   * Add a request interceptor
   */
  addRequestInterceptor(interceptor: RequestInterceptor): () => void {
    this.requestInterceptors.push(interceptor);
    return () => {
      const index = this.requestInterceptors.indexOf(interceptor);
      if (index !== -1) {
        this.requestInterceptors.splice(index, 1);
      }
    };
  }

  /**
   * Add a response interceptor
   */
  addResponseInterceptor<T = any>(interceptor: ResponseInterceptor<T>): () => void {
    this.responseInterceptors.push(interceptor as ResponseInterceptor);
    return () => {
      const index = this.responseInterceptors.indexOf(interceptor as ResponseInterceptor);
      if (index !== -1) {
        this.responseInterceptors.splice(index, 1);
      }
    };
  }

  /**
   * Make a GET request
   */
  async get<T>(path: string, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(path, { ...options, method: 'GET' });
  }

  /**
   * Make a POST request
   */
  async post<T>(path: string, data?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PUT request
   */
  async put<T>(path: string, data?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PATCH request
   */
  async patch<T>(path: string, data?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(path, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(path: string, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(path, { ...options, method: 'DELETE' });
  }

  /**
   * Make a request with timeout and interceptors
   */
  private async request<T>(
    path: string,
    options: RequestOptions = {}
  ): Promise<T> {
    try {
      let { 
        timeout = this.defaultTimeout, 
        headers, 
        baseUrl = this.baseUrl,
        params,
        ...restOptions 
      } = options;

      // Apply request interceptors
      let requestConfig: RequestOptions = {
        timeout,
        headers: {
          ...this.defaultHeaders,
          ...headers,
        },
        baseUrl,
        params,
        ...restOptions,
      };

      // Apply request interceptors in sequence
      for (const interceptor of this.requestInterceptors) {
        requestConfig = await interceptor.onRequest(requestConfig);
      }

      // Extract the final config
      ({ timeout, headers, baseUrl, params, ...restOptions } = requestConfig);

      // Build the URL with query parameters
      let url = this.resolveUrl(path, baseUrl);
      if (params) {
        const queryParams = new URLSearchParams();
        for (const [key, value] of Object.entries(params)) {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        }
        const queryString = queryParams.toString();
        if (queryString) {
          url += (url.includes('?') ? '&' : '?') + queryString;
        }
      }

      // Set up timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        const response = await fetch(url, {
          ...restOptions,
          headers,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Process response through interceptors
        let result: any = response;

        // Apply response interceptors in sequence
        for (const interceptor of this.responseInterceptors) {
          try {
            if (!response.ok) {
              // If response is not OK, call onError
              result = await interceptor.onError(response);
            } else {
              // If response is OK, call onResponse
              result = await interceptor.onResponse(response);
            }
          } catch (error) {
            // If an interceptor throws, pass to the next onError
            result = await interceptor.onError(error);
          }
        }

        // If we still have a Response object after interceptors, parse it
        if (result instanceof Response) {
          if (!result.ok) {
            await this.handleErrorResponse(result);
          }
          return await this.parseResponseData<T>(result);
        }

        // Otherwise, return the result from interceptors
        return result as T;
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        throw new TimeoutError();
      }
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new NetworkError();
      }
      throw error;
    }
  }

  /**
   * Handle error response
   */
  private async handleErrorResponse(response: Response): Promise<never> {
    const errorData = await this.parseResponseData<ApiErrorResponse>(response);
    
    // Extract error details
    const errorMessage = errorData?.error?.message || `HTTP error ${response.status}`;
    const errorStatus = errorData?.error?.status || response.status;
    const errorDetails = errorData?.error?.data;

    // Throw appropriate error
    throw new ApiError(errorMessage, errorStatus, errorDetails);
  }

  /**
   * Parse response data
   */
  private async parseResponseData<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      return await response.json();
    }
    return await response.text() as unknown as T;
  }

  /**
   * Resolve URL
   */
  private resolveUrl(path: string, baseUrl: string): string {
    // If path is already a full URL, return it
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    // If path starts with a slash, append it to the base URL
    if (path.startsWith('/')) {
      // Remove trailing slash from baseUrl if it exists
      const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
      return `${base}${path}`;
    }

    // Otherwise, join baseUrl and path with a slash
    const base = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
    return `${base}${path}`;
  }
}
