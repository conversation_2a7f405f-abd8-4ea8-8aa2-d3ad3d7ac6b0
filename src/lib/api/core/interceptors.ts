/**
 * API client interceptors for common functionality
 */

import { RequestInterceptor, ResponseInterceptor, RequestOptions } from './api-client';
import { ApiError, UnauthorizedException } from '../types/error';
import { ApiErrorResponse } from '../types/response';

/**
 * Authentication interceptor that adds auth token to requests
 */
export function createAuthInterceptor(
  getToken: () => Promise<string | null> | string | null
): RequestInterceptor {
  return {
    onRequest: async (config: RequestOptions) => {
      const token = await getToken();
      
      if (token) {
        return {
          ...config,
          headers: {
            ...config.headers,
            Authorization: `Bearer ${token}`,
          },
        };
      }
      
      return config;
    },
  };
}

/**
 * Logging interceptor that logs requests and responses
 */
export function createLoggingInterceptor(
  enabledInProduction: boolean = false
): RequestInterceptor & ResponseInterceptor {
  const isProduction = process.env.NODE_ENV === 'production';
  const shouldLog = !isProduction || enabledInProduction;
  
  return {
    onRequest: (config: RequestOptions) => {
      if (shouldLog) {
        const { method, baseUrl, params } = config;
        console.log(`🚀 API Request: ${method} ${baseUrl}`, { params });
      }
      return config;
    },
    
    onResponse: (response: Response) => {
      if (shouldLog) {
        console.log(`✅ API Response: ${response.status} ${response.url}`);
      }
      return response;
    },
    
    onError: (error: any) => {
      if (shouldLog) {
        if (error instanceof Response) {
          console.error(`❌ API Error: ${error.status} ${error.url}`);
        } else if (error instanceof ApiError) {
          console.error(`❌ API Error: ${error.statusCode} ${error.message}`);
        } else {
          console.error('❌ API Error:', error);
        }
      }
      throw error;
    },
  };
}

/**
 * Error handling interceptor that transforms response errors into typed exceptions
 */
export function createErrorInterceptor(): ResponseInterceptor {
  return {
    onResponse: (response: Response) => {
      return response;
    },
    
    onError: async (error: any) => {
      // If already an ApiError instance, just rethrow
      if (error instanceof ApiError) {
        throw error;
      }
      
      // If it's a Response object with an error status
      if (error instanceof Response && !error.ok) {
        let errorData: ApiErrorResponse | null = null;
        
        try {
          errorData = await error.json() as ApiErrorResponse;
        } catch (e) {
          // If we can't parse JSON, create a generic error
          throw new ApiError(
            `HTTP error ${error.status}`,
            error.status
          );
        }
        
        const message = errorData?.error?.message || `HTTP error ${error.status}`;
        const status = errorData?.error?.status || error.status;
        const data = errorData?.error?.data;
        
        // Create specific error types based on status code
        switch (status) {
          case 400:
            throw new ApiError(message, status, data);
          case 401:
            throw new UnauthorizedException(message);
          case 404:
            throw new ApiError(message, status, data);
          default:
            throw new ApiError(message, status, data);
        }
      }
      
      // For other types of errors, just rethrow
      throw error;
    },
  };
}

/**
 * Retry interceptor that retries failed requests
 */
export function createRetryInterceptor(
  maxRetries: number = 3,
  retryDelay: number = 1000,
  shouldRetry: (error: any) => boolean = defaultShouldRetry
): ResponseInterceptor {
  return {
    onResponse: (response: Response) => {
      return response;
    },
    
    onError: async (error: any) => {
      // Implementation will be added in a future update
      throw error;
    },
  };
}

/**
 * Default function to determine if a request should be retried
 */
function defaultShouldRetry(error: any): boolean {
  // Retry network errors and 5xx server errors
  if (error instanceof ApiError) {
    return error.statusCode >= 500 && error.statusCode < 600;
  }
  
  return error.name === 'NetworkError' || error.name === 'TimeoutError';
}
