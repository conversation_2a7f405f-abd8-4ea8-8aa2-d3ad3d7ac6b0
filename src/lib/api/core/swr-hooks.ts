/**
 * <PERSON><PERSON> hooks for data fetching with the API client
 */

'use client';

import useS<PERSON>, { SWRConfiguration, SWRResponse } from 'swr';
import useSWRInfinite, {
  SWRInfiniteConfiguration,
  SWRInfiniteResponse,
} from 'swr/infinite';
import { z } from 'zod';

import { ApiClient } from './api-client';
import {
  CursorPaginationParams,
  GetNextPageParamFunction,
  PaginationParams,
} from '../types/pagination';
import { ApiCursorResponse, ApiListResponse, ApiResponse } from '../types/response';

// Default API client instance
let defaultApiClient: ApiClient;

/**
 * Set the default API client instance
 */
export function setDefaultApiClient(apiClient: ApiClient): void {
  defaultApiClient = apiClient;
}

/**
 * Get the default API client instance
 */
export function getDefaultApiClient(): ApiClient {
  if (!defaultApiClient) {
    defaultApiClient = new ApiClient();
  }
  return defaultApiClient;
}

/**
 * Create a fetcher function for SWR
 */
export function createFetcher<Data>(
  apiClient: ApiClient = getDefaultApiClient()
): (url: string) => Promise<Data> {
  return (url: string) => apiClient.get<Data>(url);
}

/**
 * Base hook for using SWR with API
 */
export function useApi<Data, Error = any>(
  url: string | null,
  config?: SWRConfiguration<Data, Error>,
  apiClient: ApiClient = getDefaultApiClient()
): SWRResponse<Data, Error> {
  const fetcher = createFetcher<Data>(apiClient);

  return useSWR<Data, Error>(url, fetcher, {
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Hook for using SWR with API and schema validation
 */
export function useApiWithSchema<Schema extends z.ZodType<any>, Error = any>(
  url: string | null,
  schema: Schema,
  config?: SWRConfiguration<z.infer<Schema>, Error>,
  apiClient: ApiClient = getDefaultApiClient()
): SWRResponse<z.infer<Schema>, Error> {
  const fetcher = async (url: string) => {
    const data = await apiClient.get(url);
    return schema.parse(data);
  };

  return useSWR<z.infer<Schema>, Error>(url, fetcher, {
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Hook for using SWR with API response transformation
 */
export function useApiWithTransform<ApiData, TransformedData, Error = any>(
  url: string | null,
  transform: (data: ApiData) => TransformedData,
  config?: SWRConfiguration<TransformedData, Error>,
  apiClient: ApiClient = getDefaultApiClient()
): SWRResponse<TransformedData, Error> {
  const fetcher = async (url: string) => {
    const data = await apiClient.get<ApiData>(url);
    return transform(data);
  };

  return useSWR<TransformedData, Error>(url, fetcher, {
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Hook for using SWR with paginated API
 */
export function usePaginatedApi<T, Error = any>(
  baseUrl: string,
  params: PaginationParams = {},
  config?: SWRConfiguration<ApiListResponse<T>, Error>,
  apiClient: ApiClient = getDefaultApiClient()
): SWRResponse<ApiListResponse<T>, Error> {
  const { page = 1, size = 10, sort, order } = params;

  const url = baseUrl
    ? `${baseUrl}?page=${page}&size=${size}${sort ? `&sort=${sort}` : ''}${
        order ? `&order=${order}` : ''
      }`
    : null;

  return useApi<ApiListResponse<T>, Error>(url, config, apiClient);
}

/**
 * Create a key generator function for cursor-based pagination
 */
export function createCursorPaginationKeyGenerator<C = string>(
  baseUrl: string,
  params: CursorPaginationParams<C> = {}
): (pageIndex: number, previousPageData: ApiCursorResponse<any, C> | null) => string | null {
  const { limit = 10, direction = 'next' } = params;

  return (pageIndex: number, previousPageData: ApiCursorResponse<any, C> | null) => {
    // First page, no cursor needed
    if (pageIndex === 0) {
      return `${baseUrl}?limit=${limit}&direction=${direction}`;
    }

    // Reached the end
    if (previousPageData && !previousPageData.hasMore) {
      return null;
    }

    // Get the cursor for the next page
    const cursor = previousPageData?.nextCursor;
    if (!cursor) {
      return null;
    }

    // Add cursor to the URL
    return `${baseUrl}?cursor=${encodeURIComponent(
      String(cursor)
    )}&limit=${limit}&direction=${direction}`;
  };
}

/**
 * Hook for using SWRInfinite with cursor-based pagination
 */
export function useInfiniteApi<T, C = string, Error = any>(
  getKey: (
    pageIndex: number,
    previousPageData: ApiCursorResponse<T, C> | null
  ) => string | null,
  config?: SWRInfiniteConfiguration<ApiCursorResponse<T, C>, Error>,
  apiClient: ApiClient = getDefaultApiClient()
): SWRInfiniteResponse<ApiCursorResponse<T, C>, Error> {
  const fetcher = createFetcher<ApiCursorResponse<T, C>>(apiClient);

  return useSWRInfinite<ApiCursorResponse<T, C>, Error>(getKey, fetcher, {
    revalidateAll: false,
    revalidateFirstPage: true,
    revalidateOnFocus: false,
    ...config,
  });
}

/**
 * Hook for using SWRInfinite with cursor-based pagination and transformation
 */
export function useInfiniteApiWithTransform<ApiData, TransformedData, C = string, Error = any>(
  getKey: (pageIndex: number, previousPageData: ApiData | null) => string | null,
  transform: (data: ApiData) => TransformedData,
  config?: SWRInfiniteConfiguration<TransformedData, Error>,
  apiClient: ApiClient = getDefaultApiClient()
): SWRInfiniteResponse<TransformedData, Error> {
  const fetcher = async (url: string) => {
    const data = await apiClient.get<ApiData>(url);
    return transform(data);
  };

  return useSWRInfinite<TransformedData, Error>(getKey, fetcher, {
    revalidateAll: false,
    revalidateFirstPage: true,
    revalidateOnFocus: false,
    ...config,
  });
}
