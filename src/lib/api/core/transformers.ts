/**
 * Transformers for API responses
 */

import { z } from 'zod';
import { ApiCursorResponse, ApiListResponse, ApiResponse } from '../types/response';

/**
 * Create a transformer function that validates and transforms API response data
 * using a Zod schema
 */
export function createTransformer<ApiData, TransformedData>(
  schema: z.ZodType<TransformedData>,
  transform?: (data: ApiData) => TransformedData
): (apiData: ApiData) => TransformedData {
  return (apiData: ApiData): TransformedData => {
    // If a custom transform function is provided, use it first
    const transformedData = transform ? transform(apiData) : apiData;
    
    // Validate and parse the data with Zod
    return schema.parse(transformedData);
  };
}

/**
 * Transform a generic API response
 */
export function transformApiResponse<T, R = T>(
  response: ApiResponse<T>,
  transform?: (data: T) => R
): R {
  if (!response.data) {
    throw new Error('API response data is missing');
  }
  
  return transform ? transform(response.data) : (response.data as unknown as R);
}

/**
 * Transform a list API response
 */
export function transformApiListResponse<T, R = T>(
  response: ApiListResponse<T>,
  transform?: (item: T) => R
): {
  items: R[];
  total: number;
  page?: number;
  size?: number;
  totalPages?: number;
} {
  if (!response.items) {
    throw new Error('API response items are missing');
  }
  
  return {
    items: transform ? response.items.map(transform) : (response.items as unknown as R[]),
    total: response.total,
    page: response.page,
    size: response.size,
    totalPages: response.totalPages,
  };
}

/**
 * Transform a cursor-based API response
 */
export function transformApiCursorResponse<T, R = T, C = string>(
  response: ApiCursorResponse<T, C>,
  transform?: (item: T) => R
): {
  items: R[];
  nextCursor?: C | null;
  prevCursor?: C | null;
  hasMore: boolean;
} {
  if (!response.items) {
    throw new Error('API response items are missing');
  }
  
  return {
    items: transform ? response.items.map(transform) : (response.items as unknown as R[]),
    nextCursor: response.nextCursor,
    prevCursor: response.prevCursor,
    hasMore: response.hasMore,
  };
}

/**
 * Create a model transformer that converts API response to a domain model
 */
export function createModelTransformer<ApiModel, DomainModel>(
  transform: (apiModel: ApiModel) => DomainModel
): (apiModel: ApiModel) => DomainModel {
  return transform;
}

/**
 * Create a list model transformer that converts API response items to domain models
 */
export function createListModelTransformer<ApiModel, DomainModel>(
  transform: (apiModel: ApiModel) => DomainModel
): (apiListResponse: ApiListResponse<ApiModel>) => {
  items: DomainModel[];
  total: number;
  page?: number;
  size?: number;
  totalPages?: number;
} {
  return (apiListResponse: ApiListResponse<ApiModel>) => {
    return transformApiListResponse(apiListResponse, transform);
  };
}

/**
 * Create a cursor model transformer that converts API response items to domain models
 */
export function createCursorModelTransformer<ApiModel, DomainModel, C = string>(
  transform: (apiModel: ApiModel) => DomainModel
): (apiCursorResponse: ApiCursorResponse<ApiModel, C>) => {
  items: DomainModel[];
  nextCursor?: C | null;
  prevCursor?: C | null;
  hasMore: boolean;
} {
  return (apiCursorResponse: ApiCursorResponse<ApiModel, C>) => {
    return transformApiCursorResponse(apiCursorResponse, transform);
  };
}
