/**
 * API Provider component for initializing the API client
 */

'use client';

import { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { SWRConfig } from 'swr';
import { ApiClient } from '../core/api-client';
import { initializeApiClient } from '../init';

// Create a context for the API client
const ApiClientContext = createContext<ApiClient | null>(null);

// Hook for accessing the API client
export function useApiClient(): ApiClient {
  const apiClient = useContext(ApiClientContext);
  if (!apiClient) {
    throw new Error('useApiClient must be used within an ApiProvider');
  }
  return apiClient;
}

interface ApiProviderProps {
  children: ReactNode;
  swrConfig?: Record<string, any>;
}

export function ApiProvider({ children, swrConfig = {} }: ApiProviderProps) {
  const [apiClient, setApiClient] = useState<ApiClient | null>(null);

  useEffect(() => {
    // Initialize the API client on the client side
    const client = initializeApiClient();
    setApiClient(client);
  }, []);

  // Default SWR configuration
  const defaultConfig = {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
    errorRetryCount: 3,
    ...swrConfig,
  };

  // Don't render until the API client is initialized
  if (!apiClient) {
    return null;
  }

  return (
    <ApiClientContext.Provider value={apiClient}>
      <SWRConfig value={defaultConfig}>{children}</SWRConfig>
    </ApiClientContext.Provider>
  );
}
