import { auth } from '@/auth';
import { ApiError } from '@/lib/api/types/error';
import {
  HTTP_STATUS,
  responseBadRequest,
  responseError,
  responseOk,
} from '@/lib/api/types/response';
import { z } from 'zod';
import { ApiFunctionWithAuth, parseRequestBody } from './api-utils';

/**
 * Enhanced API handler with authentication and schema validation
 *
 * @param req The Next.js request object
 * @param context The context object containing route parameters
 * @param fn The API function to execute
 * @param options Optional configuration for request validation
 * @returns A Next.js response
 */
export async function apiHandlerWithAuth<T, BodyType = any>(
  req: Request,
  context: { params: Record<string, string> },
  fn: ApiFunctionWithAuth<T>,
  options?: {
    bodySchema?: z.ZodSchema<BodyType>;
  }
): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session) {
      return responseError(HTTP_STATUS.UNAUTHORIZED, 'Not authenticated');
    }

    // For GET requests, no body is needed
    if (req.method === 'GET') {
      const result = await fn(session.user, {}, req, context);
      return responseOk(result);
    }

    // Parse and validate request body
    let body;
    try {
      body = await parseRequestBody(req, options?.bodySchema);
    } catch (error) {
      console.error('Request parsing error:', error);

      if (error instanceof z.ZodError) {
        return responseBadRequest('Validation failed', {
          errors: error.errors,
        });
      }

      if (error instanceof SyntaxError) {
        return responseBadRequest('Invalid request format');
      }

      if (error instanceof ApiError) {
        return responseError(error.statusCode, error.message, error.data);
      }

      throw error;
    }

    // Execute the API function with the authenticated user and parsed body
    const result = await fn(session.user, body, req, context);
    return responseOk(result);
  } catch (error) {
    console.error('API Error:', error);

    if (error instanceof ApiError) {
      return responseError(error.statusCode, error.message, error.data);
    }

    return responseError();
  }
}
