import { ApiError } from '@/lib/api/types/error';
import {
  HTTP_STATUS,
  responseError,
  responseOk,
} from '@/lib/api/types/response';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ApiFunction, parseQueryParams, parseRequestBody } from './api-utils';

/**
 * Enhanced API handler with schema validation
 *
 * @param req The Next.js request object
 * @param context The context object containing route parameters
 * @param fn The API function to execute
 * @param options Optional configuration for request validation
 * @returns A Next.js response
 */
export async function apiHandler<T, BodyType = any, QueryType = any>(
  req: NextRequest,
  context: { params: Record<string, string> },
  fn: ApiFunction<T>,
  options?: {
    bodySchema?: z.ZodSchema<BodyType>;
    querySchema?: z.ZodSchema<QueryType>;
  }
): Promise<NextResponse> {
  try {
    // Validate query parameters if schema is provided
    if (options?.querySchema) {
      parseQueryParams(req, options.querySchema);
    }

    // For methods with body, validate request body if schema is provided
    if (req.method !== 'GET' && req.method !== 'HEAD' && options?.bodySchema) {
      await parseRequestBody(req.clone(), options.bodySchema);
    }

    // Execute the API function
    const result = await fn(req, context);
    return responseOk(result);
  } catch (error) {
    console.error('[API Error]', error);

    if (error instanceof ApiError) {
      return responseError(error.statusCode, error.message);
    }

    if (error instanceof z.ZodError) {
      return responseError(HTTP_STATUS.BAD_REQUEST, 'Validation failed', {
        errors: error.errors,
      });
    }

    return responseError();
  }
}
