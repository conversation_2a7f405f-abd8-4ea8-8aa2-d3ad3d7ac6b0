/**
 * Server-side API utilities
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ApiError } from '../types/error';
import { HTTP_STATUS, responseError, responseOk } from '../types/response';

/**
 * Type for API handler function
 */
export type ApiFunction<T = any> = (
  req: NextRequest,
  context: { params: Record<string, string> }
) => Promise<T>;

/**
 * Type for API handler function with authentication
 */
export type ApiFunctionWithAuth<T = any> = (
  user: any,
  body: any,
  req: Request,
  context: { params: Record<string, string> }
) => Promise<T>;

/**
 * Parse request body with schema validation
 */
export async function parseRequestBody<T>(
  req: Request,
  schema?: z.ZodSchema<T>
): Promise<T> {
  try {
    // Handle different content types
    const contentType = req.headers.get('content-type') || '';
    
    let body: any;
    
    if (contentType.includes('multipart/form-data')) {
      body = await req.formData();
    } else if (contentType.includes('application/json')) {
      body = await req.json();
    } else {
      body = await req.text();
    }
    
    // Validate with schema if provided
    if (schema) {
      return schema.parse(body);
    }
    
    return body as T;
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError('Validation failed', HTTP_STATUS.BAD_REQUEST, {
        errors: error.errors,
      });
    }
    
    if (error instanceof SyntaxError) {
      throw new ApiError('Invalid request format', HTTP_STATUS.BAD_REQUEST);
    }
    
    throw error;
  }
}

/**
 * Parse query parameters with schema validation
 */
export function parseQueryParams<T>(
  req: NextRequest,
  schema?: z.ZodSchema<T>
): T {
  try {
    const searchParams = req.nextUrl.searchParams;
    const params: Record<string, string> = {};
    
    // Convert URLSearchParams to plain object
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
    
    // Validate with schema if provided
    if (schema) {
      return schema.parse(params);
    }
    
    return params as T;
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError('Invalid query parameters', HTTP_STATUS.BAD_REQUEST, {
        errors: error.errors,
      });
    }
    
    throw error;
  }
}

/**
 * Handle API request with error handling
 */
export async function apiHandler<T>(
  req: NextRequest,
  context: { params: Record<string, string> },
  fn: ApiFunction<T>
): Promise<NextResponse> {
  try {
    const result = await fn(req, context);
    return responseOk(result);
  } catch (error) {
    console.error('[API Error]', error);
    
    if (error instanceof ApiError) {
      return responseError(error.statusCode, error.message);
    }
    
    return responseError();
  }
}

/**
 * Get authentication token from request headers
 */
export function getAuthToken(req: Request): string | null {
  const authHeader = req.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}
