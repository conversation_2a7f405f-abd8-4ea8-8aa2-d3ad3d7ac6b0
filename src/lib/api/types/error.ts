import {
  API_ERROR_CODE,
  ApiError,
  InternalServerError as InternalServerErrorBase,
  NetworkError as NetworkErrorBase,
  NotFoundError as NotFoundErrorBase,
  UnauthorizedError as UnauthorizedErrorBase,
  ValidationError as ValidationErrorBase,
} from '@/types/api/error';
import { HTTP_STATUS } from '@/types/api/response';

// 기존 클래스 이름을 유지하기 위한 별칭 정의
export { ApiError };

/**
 * Internal server error class
 */
export class InternalServerErrorException extends InternalServerErrorBase {
  constructor(message = 'Internal Server Error') {
    super(message);
    this.name = 'InternalServerErrorException';
  }
}

/**
 * Unauthorized error class
 */
export class UnauthorizedException extends UnauthorizedErrorBase {
  constructor(message = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedException';
  }
}

/**
 * Not found error class
 */
export class NotFoundException extends NotFoundErrorBase {
  constructor(message = 'Not Found') {
    super(message);
    this.name = 'NotFoundException';
  }
}

/**
 * Bad request error class
 */
export class BadRequestException extends ValidationErrorBase {
  constructor(message = 'Bad Request', data?: any) {
    super(message, data);
    this.name = 'BadRequestException';
  }
}

/**
 * Network error class
 */
export class NetworkError extends NetworkErrorBase {
  constructor(message: string = 'Network error occurred') {
    super(message);
    this.name = 'NetworkError';
  }
}

/**
 * Timeout error class
 */
export class TimeoutError extends Error {
  constructor(message: string = 'Request timed out') {
    super(message);
    this.name = 'TimeoutError';
  }
}

/**
 * Conflict error class
 */
export class ConflictException extends ApiError {
  constructor(message = 'Conflict') {
    super(message, HTTP_STATUS.CONFLICT, API_ERROR_CODE.CONFLICT);
    this.name = 'ConflictException';
  }
}
