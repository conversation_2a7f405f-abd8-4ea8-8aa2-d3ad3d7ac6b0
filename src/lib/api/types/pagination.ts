import {
  ICursorPaginationParams,
  IInfiniteScrollState,
  IPaginationParams,
  TGetNextPageParamFunction,
} from '@/types/common/pagination';

// 기존 타입을 새로운 타입으로 재정의하여 하위 호환성 유지
export type PaginationParams = IPaginationParams;
export type CursorPaginationParams<C = string> = ICursorPaginationParams<C>;
export type InfiniteScrollState<T, C = string> = IInfiniteScrollState<T, C>;
export type GetNextPageParamFunction<T, C = string> = TGetNextPageParamFunction<
  T,
  C
>;
