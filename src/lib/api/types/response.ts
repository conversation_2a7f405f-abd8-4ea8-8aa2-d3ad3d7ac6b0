import {
  HTTP_STATUS,
  IApiCursorResponse,
  IApiErrorResponse,
  IApiListResponse,
  IApiResponse,
  IBaseApiResponse,
  apiCursorResponseSchema,
  apiListResponseSchema,
  apiResponseSchema,
  baseApiResponseSchema,
  cursorPaginationMetaSchema,
  paginationMetaSchema,
  validateApiResponse,
} from '@/types/api/response';
import {
  ICursorPaginationMeta,
  IPaginationMeta,
} from '@/types/common/pagination';
import { NextResponse } from 'next/server';
import { z } from 'zod';

// 기존 타입을 새로운 타입으로 재정의하여 하위 호환성 유지
export type BaseApiResponse = IBaseApiResponse;
export type ApiResponse<T> = IApiResponse<T>;
export type ApiErrorResponse = IApiErrorResponse;
export type PaginationMeta = IPaginationMeta;
export type ApiListResponse<T> = IApiListResponse<T>;
export type CursorPaginationMeta<C = string> = ICursorPaginationMeta<C>;
export type ApiCursorResponse<T, C = string> = IApiCursorResponse<T, C>;

/**
 * HTTP status codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

/**
 * 성공 응답 생성 (200 OK)
 */
export function responseOk<T>(
  data?: T,
  message: string = 'Success',
  metadata?: Record<string, any>
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    status: HTTP_STATUS.OK,
    message,
    data: data as T,
    timestamp: new Date().toISOString(),
    meta: metadata,
  };

  return NextResponse.json(response, { status: HTTP_STATUS.OK });
}

/**
 * 리소스 생성 성공 응답 생성 (201 Created)
 */
export function responseCreated<T>(
  data?: T,
  message: string = 'Created',
  metadata?: Record<string, any>
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    status: HTTP_STATUS.CREATED,
    message,
    data: data as T,
    timestamp: new Date().toISOString(),
    meta: metadata,
  };

  return NextResponse.json(response, { status: HTTP_STATUS.CREATED });
}

/**
 * 콘텐츠 없음 응답 생성 (204 No Content)
 */
export function responseNoContent(): NextResponse {
  return new NextResponse(null, { status: HTTP_STATUS.NO_CONTENT });
}

/**
 * 에러 응답 생성 옵션
 */
export interface ErrorResponseOptions {
  /** 에러 메시지 */
  message?: string;
  /** 에러 코드 */
  code?: string;
  /** 에러 카테고리 */
  category?: string;
  /** 추가 에러 데이터 */
  data?: any;
}

/**
 * 에러 응답 생성
 */
export function responseError(
  statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
  messageOrOptions: string | ErrorResponseOptions = 'Internal Server Error',
  data?: any,
  errorCode?: string
): NextResponse<ApiErrorResponse> {
  let message: string;
  let code: string;
  let category: string | undefined;
  let errorData: any;

  // 옵션 객체 또는 문자열 메시지 처리
  if (typeof messageOrOptions === 'string') {
    message = messageOrOptions;
    code = errorCode || 'internal_error';
    errorData = data;
  } else {
    message = messageOrOptions.message || 'Internal Server Error';
    code = messageOrOptions.code || errorCode || 'internal_error';
    category = messageOrOptions.category;
    errorData = messageOrOptions.data || data;
  }

  const response: ApiErrorResponse = {
    status: statusCode,
    timestamp: new Date().toISOString(),
    error: {
      message,
      status: statusCode,
      code,
      category,
      data: errorData,
    },
  };

  return NextResponse.json(response, { status: statusCode });
}

/**
 * 잘못된 요청 에러 응답 생성 (400 Bad Request)
 */
export function responseBadRequest(
  messageOrOptions: string | ErrorResponseOptions = 'Bad Request',
  data?: any,
  errorCode: string = 'bad_request'
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.BAD_REQUEST,
    messageOrOptions,
    data,
    errorCode
  );
}

/**
 * 유효성 검사 에러 응답 생성 (400 Bad Request)
 */
export function responseValidationError(
  messageOrOptions:
    | string
    | ErrorResponseOptions = '입력 값이 유효하지 않습니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.BAD_REQUEST,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : {
          ...messageOrOptions,
          code: 'validation_failed',
          category: 'validation',
        },
    data,
    'validation_failed'
  );
}

/**
 * 인증 필요 에러 응답 생성 (401 Unauthorized)
 */
export function responseUnauthorized(
  messageOrOptions: string | ErrorResponseOptions = '인증이 필요합니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.UNAUTHORIZED,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : {
          ...messageOrOptions,
          code: 'unauthorized',
          category: 'authentication',
        },
    data,
    'unauthorized'
  );
}

/**
 * 접근 권한 없음 에러 응답 생성 (403 Forbidden)
 */
export function responseForbidden(
  messageOrOptions: string | ErrorResponseOptions = '접근 권한이 없습니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.FORBIDDEN,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : { ...messageOrOptions, code: 'forbidden', category: 'authorization' },
    data,
    'forbidden'
  );
}

/**
 * 리소스 찾을 수 없음 에러 응답 생성 (404 Not Found)
 */
export function responseNotFound(
  messageOrOptions:
    | string
    | ErrorResponseOptions = '요청한 리소스를 찾을 수 없습니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.NOT_FOUND,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : { ...messageOrOptions, code: 'not_found', category: 'resource' },
    data,
    'not_found'
  );
}

/**
 * 충돌 에러 응답 생성 (409 Conflict)
 */
export function responseConflict(
  messageOrOptions:
    | string
    | ErrorResponseOptions = '리소스 충돌이 발생했습니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.CONFLICT,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : { ...messageOrOptions, code: 'conflict', category: 'resource' },
    data,
    'conflict'
  );
}

/**
 * 서버 내부 에러 응답 생성 (500 Internal Server Error)
 */
export function responseInternalError(
  messageOrOptions:
    | string
    | ErrorResponseOptions = '서버 내부 오류가 발생했습니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.INTERNAL_SERVER_ERROR,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : {
          ...messageOrOptions,
          code: 'internal_error',
          category: 'infrastructure',
        },
    data,
    'internal_error'
  );
}

/**
 * 서비스 이용 불가 에러 응답 생성 (503 Service Unavailable)
 */
export function responseServiceUnavailable(
  messageOrOptions:
    | string
    | ErrorResponseOptions = '서비스를 일시적으로 이용할 수 없습니다',
  data?: any
): NextResponse<ApiErrorResponse> {
  return responseError(
    HTTP_STATUS.SERVICE_UNAVAILABLE,
    typeof messageOrOptions === 'string'
      ? messageOrOptions
      : {
          ...messageOrOptions,
          code: 'service_unavailable',
          category: 'infrastructure',
        },
    data,
    'service_unavailable'
  );
}

/**
 * Zod 스키마: 기본 API 응답
 */
export const baseApiResponseSchema = z.object({
  status: z.number(),
  message: z.string().optional(),
  timestamp: z.string().datetime(),
});

/**
 * Zod 스키마: API 응답
 */
export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  baseApiResponseSchema.extend({
    data: dataSchema,
    meta: z.record(z.string(), z.any()).optional(),
  });

/**
 * Zod 스키마: API 에러 응답
 */
export const apiErrorResponseSchema = baseApiResponseSchema.extend({
  error: z.object({
    message: z.string(),
    status: z.number(),
    code: z.string(),
    category: z.string().optional(),
    data: z.any().optional(),
  }),
});

/**
 * Zod 스키마: 페이지네이션 메타데이터
 */
export const paginationMetaSchema = z.object({
  total: z.number().int().nonnegative(),
  page: z.number().int().positive(),
  size: z.number().int().positive(),
  totalPages: z.number().int().nonnegative(),
});

/**
 * Zod 스키마: 페이지 기반 리스트 응답
 */
export const apiListResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  baseApiResponseSchema.extend({
    items: z.array(itemSchema),
    pagination: paginationMetaSchema,
    meta: z.record(z.string(), z.any()).optional(),
  });

/**
 * Zod 스키마: 커서 기반 페이지네이션 메타데이터
 */
export const cursorPaginationMetaSchema = <
  C extends z.ZodTypeAny = z.ZodString,
>(
  cursorSchema: C = z.string() as any
) =>
  z.object({
    nextCursor: cursorSchema.nullable().optional(),
    prevCursor: cursorSchema.nullable().optional(),
    hasMore: z.boolean(),
  });

/**
 * Zod 스키마: 커서 기반 리스트 응답
 */
export const apiCursorResponseSchema = <
  T extends z.ZodTypeAny,
  C extends z.ZodTypeAny = z.ZodString,
>(
  itemSchema: T,
  cursorSchema: C = z.string() as any
) =>
  baseApiResponseSchema.extend({
    items: z.array(itemSchema),
    pagination: cursorPaginationMetaSchema(cursorSchema),
    meta: z.record(z.string(), z.any()).optional(),
  });

/**
 * API 응답 유효성 검사
 */
export function validateApiResponse<T>(
  response: unknown,
  schema: z.ZodType<T>
): T {
  try {
    return schema.parse(response);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`API 응답 유효성 검사 실패: ${error.message}`);
    }
    throw error;
  }
}

/**
 * API 응답 데이터 변환
 */
export function transformApiResponse<T, U>(
  response: unknown,
  schema: z.ZodType<T>,
  transform: (data: T) => U
): U {
  const validatedResponse = validateApiResponse(response, schema);
  return transform(validatedResponse);
}

/**
 * API 응답에서 데이터 추출
 */
export function extractApiResponseData<T>(
  response: unknown,
  dataSchema: z.ZodType<T>
): T {
  const schema = apiResponseSchema(dataSchema);
  const validatedResponse = validateApiResponse(response, schema);
  return validatedResponse.data;
}

/**
 * API 리스트 응답에서 항목 추출
 */
export function extractApiListItems<T>(
  response: unknown,
  itemSchema: z.ZodType<T>
): T[] {
  const schema = apiListResponseSchema(itemSchema);
  const validatedResponse = validateApiResponse(response, schema);
  return validatedResponse.items;
}

/**
 * API 커서 응답에서 항목 추출
 */
export function extractApiCursorItems<T>(
  response: unknown,
  itemSchema: z.ZodType<T>
): T[] {
  const schema = apiCursorResponseSchema(itemSchema);
  const validatedResponse = validateApiResponse(response, schema);
  return validatedResponse.items;
}
