import { Envs } from '@/types/env';

export type MessageType = 'NEW_POST' | 'NEW_USER' | 'SYSTEM_ALERT' | 'GENERAL'; // 필요에 따라 타입 추가

export interface DiscordEmbedOptions {
  title?: string;
  description?: string;
  color?: number;
  url?: string;
  fields?: { name: string; value: string; inline?: boolean }[];
  footer?: { text: string; icon_url?: string };
  image?: { url: string };
  thumbnail?: { url: string };
}

export async function sendDiscordMessage(
  content: string, // This can be an empty string if description is provided in embedOptions
  embedOptions?: DiscordEmbedOptions
) {
  if (!Envs.discordWebhookUrl) {
    console.warn('DISCORD_WEBHOOK_URL is not set. Skipping notification.');
    return;
  }
  try {
    const environment = Envs.nodeEnv || process.env.NODE_ENV || 'unknown';

    const finalEmbedOptions: DiscordEmbedOptions = { ...embedOptions };

    if (finalEmbedOptions.title) {
      finalEmbedOptions.title = `${finalEmbedOptions.title}`;
    } else {
      finalEmbedOptions.title = '알림';
    }

    // Set color: embedOptions.color > messageType.defaultColor > environment-based color
    if (finalEmbedOptions.color === undefined) {
      finalEmbedOptions.color =
        environment === 'production' ? 0x64e2b7 : 0xfb4141;
    }

    // Ensure description is set if content is provided and embed description is not
    if (content && !finalEmbedOptions.description) {
      finalEmbedOptions.description = content;
    }

    finalEmbedOptions.footer = {
      ...(finalEmbedOptions.footer || {}), // Preserve existing footer text/icon if any
      text: `${finalEmbedOptions.footer?.text ? finalEmbedOptions.footer.text + ' | ' : ''}from ${environment}`,
    };

    const payload: any = {};
    if (
      content &&
      !finalEmbedOptions.description &&
      Object.keys(finalEmbedOptions).length === 0
    ) {
      payload.content = content; // Only use top-level content if no embed or embed description
    }

    if (Object.keys(finalEmbedOptions).length > 0) {
      payload.embeds = [finalEmbedOptions];
    } else if (content) {
      payload.content = content;
    }

    if (Object.keys(payload).length === 0) {
      console.warn('Attempted to send an empty Discord message.');
      return;
    }

    await fetch(Envs.discordWebhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
  } catch (error) {
    console.error('Failed to send Discord notification:', error);
  }
}
