/**
 * 동적 임포트를 위한 유틸리티 함수
 *
 * 이 파일은 코드 스플리팅을 위한 동적 임포트 함수를 제공합니다.
 */

'use client';

import dynamic from 'next/dynamic';
import { ComponentType, ReactNode } from 'react';

/**
 * 로딩 컴포넌트 옵션
 */
interface LoadingOptions {
  /**
   * 로딩 중 표시할 컴포넌트
   */
  loading?: ComponentType;
  /**
   * 서버 사이드 렌더링 여부
   */
  ssr?: boolean;
}

/**
 * 기본 로딩 컴포넌트
 */
const DefaultLoading = () => {
  return (
    <div className="flex h-full w-full items-center justify-center p-4">
      <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-primary"></div>
    </div>
  );
};

/**
 * 동적 임포트 함수
 *
 * @param importFunc 임포트 함수
 * @param options 로딩 옵션
 * @returns 동적으로 로드된 컴포넌트
 *
 * @example
 * ```tsx
 * const DynamicComponent = dynamicImport(() => import('@/components/heavy-component'));
 * ```
 */
export const dynamicImport = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: LoadingOptions = {}
): T => {
  const {
    loading = DefaultLoading,
    ssr = true,
  } = options;

  return dynamic(importFunc, {
    loading,
    ssr,
  });
}

/**
 * UI 컴포넌트 동적 임포트
 *
 * @param componentName 컴포넌트 이름
 * @param options 로딩 옵션
 * @returns 동적으로 로드된 UI 컴포넌트
 *
 * @example
 * ```tsx
 * const DynamicDialog = dynamicUI('dialog');
 * ```
 */
export const dynamicUI = <T extends ComponentType<any>>(
  componentName: string,
  options: LoadingOptions = {}
): T => {
  return dynamicImport(() => import(`@/components/ui/${componentName}`), options);
}

/**
 * 에디터 컴포넌트 동적 임포트
 *
 * @param componentName 컴포넌트 이름
 * @param options 로딩 옵션
 * @returns 동적으로 로드된 에디터 컴포넌트
 *
 * @example
 * ```tsx
 * const DynamicTiptapEditor = dynamicEditor('tiptap-editor');
 * ```
 */
export const dynamicEditor = <T extends ComponentType<any>>(
  componentName: string,
  options: LoadingOptions = {}
): T => {
  return dynamicImport(() => import(`@/components/editor/dynamic/${componentName}`), options);
}

/**
 * 모달 컴포넌트 동적 임포트
 *
 * @param componentName 컴포넌트 이름
 * @param options 로딩 옵션
 * @returns 동적으로 로드된 모달 컴포넌트
 *
 * @example
 * ```tsx
 * const DynamicConfirmDialog = dynamicModal('confirm-dialog');
 * ```
 */
export const dynamicModal = <T extends ComponentType<any>>(
  componentName: string,
  options: LoadingOptions = {}
): T => {
  return dynamicImport(() => import(`@/components/ui/dynamic/${componentName}`), options);
}

/**
 * 커뮤니티 컴포넌트 동적 임포트
 *
 * @param componentName 컴포넌트 이름
 * @param options 로딩 옵션
 * @returns 동적으로 로드된 커뮤니티 컴포넌트
 *
 * @example
 * ```tsx
 * const DynamicPostDisplay = dynamicCommunity('feed/post-display');
 * ```
 */
export const dynamicCommunity = <T extends ComponentType<any>>(
  componentName: string,
  options: LoadingOptions = {}
): T => {
  return dynamicImport(() => import(`@/components/community/${componentName}`), options);
};

// 명시적으로 모든 함수를 다시 export
// export { dynamicImport, dynamicUI, dynamicEditor, dynamicModal, dynamicCommunity };
