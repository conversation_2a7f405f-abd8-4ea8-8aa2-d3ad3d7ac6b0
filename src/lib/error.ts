import * as Sentry from '@sentry/nextjs';

/**
 * 에러 심각도 레벨
 */
export enum ErrorSeverity {
  FATAL = 'fatal',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  DEBUG = 'debug',
}

/**
 * 에러 컨텍스트 인터페이스
 */
export interface ErrorContext {
  userId?: string;
  username?: string;
  email?: string;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
}

/**
 * 에러를 Sentry에 보고하는 함수
 * @param error 에러 객체
 * @param context 에러 컨텍스트
 * @param severity 에러 심각도
 */
export function captureError(
  error: Error | string,
  context?: ErrorContext,
  severity: ErrorSeverity = ErrorSeverity.ERROR
): void {
  // 에러 객체 생성
  const errorObj = typeof error === 'string' ? new Error(error) : error;
  
  // 사용자 컨텍스트 설정
  if (context?.userId || context?.username || context?.email) {
    Sentry.setUser({
      id: context.userId,
      username: context.username,
      email: context.email,
    });
  }
  
  // 태그 설정
  if (context?.tags) {
    Object.entries(context.tags).forEach(([key, value]) => {
      Sentry.setTag(key, value);
    });
  }
  
  // 추가 데이터 설정
  if (context?.extra) {
    Object.entries(context.extra).forEach(([key, value]) => {
      Sentry.setExtra(key, value);
    });
  }
  
  // 심각도 설정
  Sentry.withScope((scope) => {
    scope.setLevel(severity);
    Sentry.captureException(errorObj);
  });
  
  // 개발 환경에서는 콘솔에도 출력
  if (process.env.NODE_ENV === 'development') {
    console.error('[Error Captured]', {
      error: errorObj,
      context,
      severity,
    });
  }
}

/**
 * 성능 모니터링을 위한 트랜잭션 시작 함수
 * @param name 트랜잭션 이름
 * @param op 트랜잭션 작업 유형
 * @returns Sentry 트랜잭션 객체
 */
export function startTransaction(name: string, op: string) {
  return Sentry.startTransaction({
    name,
    op,
  });
}

/**
 * 에러 처리 래퍼 함수
 * @param fn 실행할 함수
 * @param context 에러 컨텍스트
 * @returns 함수 실행 결과 또는 에러
 */
export async function withErrorHandling<T>(
  fn: () => Promise<T>,
  context?: ErrorContext
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    captureError(error instanceof Error ? error : new Error(String(error)), context);
    throw error;
  }
}
