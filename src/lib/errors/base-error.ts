/**
 * Base error class for application errors
 */

import { ERROR_CODE, ERROR_CODE_TO_CATEGORY, ERROR_CODE_TO_STATUS } from './error-codes';

/**
 * Base error class for all application errors
 */
export class AppError extends Error {
  /**
   * HTTP status code
   */
  readonly statusCode: number;
  
  /**
   * Error code
   */
  readonly code: string;
  
  /**
   * Error category
   */
  readonly category: string;
  
  /**
   * Additional error data
   */
  readonly data?: any;
  
  /**
   * Error timestamp
   */
  readonly timestamp: string;
  
  /**
   * Whether this error is operational (expected) or programming (unexpected)
   */
  readonly isOperational: boolean;
  
  /**
   * Create a new AppError
   * 
   * @param message Error message
   * @param code Error code
   * @param data Additional error data
   * @param isOperational Whether this error is operational (expected) or programming (unexpected)
   */
  constructor(
    message: string,
    code: string = ERROR_CODE.UNKNOWN_ERROR,
    data?: any,
    isOperational: boolean = true
  ) {
    super(message);
    
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = ERROR_CODE_TO_STATUS[code] || 500;
    this.category = ERROR_CODE_TO_CATEGORY[code] || 'unknown';
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.isOperational = isOperational;
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
  
  /**
   * Convert error to a plain object for serialization
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      category: this.category,
      data: this.data,
      timestamp: this.timestamp,
      isOperational: this.isOperational,
      stack: this.stack,
    };
  }
  
  /**
   * Create a new AppError from an unknown error
   * 
   * @param error Unknown error
   * @param defaultMessage Default error message
   * @param defaultCode Default error code
   */
  static fromUnknown(
    error: unknown,
    defaultMessage: string = '알 수 없는 오류가 발생했습니다.',
    defaultCode: string = ERROR_CODE.UNKNOWN_ERROR
  ): AppError {
    // If already an AppError, return it
    if (error instanceof AppError) {
      return error;
    }
    
    // If it's a standard Error, use its message
    if (error instanceof Error) {
      return new AppError(
        error.message || defaultMessage,
        defaultCode,
        { originalError: error.name, stack: error.stack },
        false
      );
    }
    
    // For other types, create a new AppError
    return new AppError(
      defaultMessage,
      defaultCode,
      { originalError: error },
      false
    );
  }
}
