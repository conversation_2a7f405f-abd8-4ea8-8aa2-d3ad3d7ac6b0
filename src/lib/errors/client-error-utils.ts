'use client';

/**
 * Client-side error handling utilities
 */

import { toast } from 'sonner';
import { ERROR_CODE } from './error-codes';

/**
 * 클라이언트 측 에러 응답 인터페이스
 */
export interface ClientErrorResponse {
  error: {
    message: string;
    code: string;
    statusCode?: number;
    category?: string;
    data?: any;
    timestamp?: string;
  };
}

/**
 * 클라이언트 측 서버 액션 에러 응답 인터페이스
 */
export interface ClientActionErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    statusCode?: number;
    category?: string;
    data?: any;
  };
  timestamp?: string;
}

/**
 * 클라이언트 측 에러 처리 옵션
 */
export interface ClientErrorHandlerOptions {
  /** 토스트 메시지 표시 여부 */
  showToast?: boolean;
  /** 기본 에러 메시지 */
  defaultMessage?: string;
  /** 에러 처리 콜백 함수 */
  onError?: (error: ClientErrorResponse | ClientActionErrorResponse) => void;
  /** 에러 코드별 처리 함수 */
  errorHandlers?: Record<string, (error: unknown) => void>;
}

/**
 * Check if a response is an error response
 */
export function isErrorResponse(
  response: any
): response is ClientErrorResponse {
  return response && typeof response === 'object' && 'error' in response;
}

/**
 * Check if a server action response is an error response
 */
export function isActionErrorResponse(
  response: any
): response is ClientActionErrorResponse {
  return (
    response &&
    typeof response === 'object' &&
    'success' in response &&
    response.success === false &&
    'error' in response
  );
}

/**
 * API 호출에서 발생한 에러 응답 처리
 */
export function handleErrorResponse(
  error: unknown,
  options?: ClientErrorHandlerOptions
): ClientErrorResponse {
  const {
    showToast = true,
    defaultMessage = '오류가 발생했습니다. 다시 시도해주세요.',
    onError,
    errorHandlers = {},
  } = options || {};

  let errorResponse: ClientErrorResponse;

  // Parse the error into a standard format
  if (isErrorResponse(error)) {
    // Already in the correct format
    errorResponse = error;
  } else if (error instanceof Response) {
    // Fetch API Response object
    errorResponse = {
      error: {
        message: `HTTP error ${error.status}`,
        code: ERROR_CODE.UNKNOWN_ERROR,
        statusCode: error.status,
      },
    };
  } else if (error instanceof Error) {
    // Standard Error object
    errorResponse = {
      error: {
        message: error.message || defaultMessage,
        code: ERROR_CODE.UNKNOWN_ERROR,
        data: { name: error.name, stack: error.stack },
      },
    };
  } else {
    // Unknown error type
    errorResponse = {
      error: {
        message: defaultMessage,
        code: ERROR_CODE.UNKNOWN_ERROR,
        data: { originalError: error },
      },
    };
  }

  // 에러 코드 확인
  const errorCode = errorResponse.error.code || ERROR_CODE.UNKNOWN_ERROR;

  // 특정 에러 코드에 대한 처리
  if (errorCode in errorHandlers) {
    errorHandlers[errorCode](errorResponse);
  }

  // 토스트 메시지 표시
  if (showToast) {
    toast.error(errorResponse.error.message);
  }

  // 사용자 정의 에러 핸들러 호출
  if (onError) {
    onError(errorResponse);
  }

  return errorResponse;
}

/**
 * 서버 액션에서 발생한 에러 응답 처리
 */
export function handleActionErrorResponse(
  response: any,
  options?: ClientErrorHandlerOptions
): ClientActionErrorResponse | null {
  // 에러 응답이 아니면 null 반환
  if (!isActionErrorResponse(response)) {
    return null;
  }

  const {
    showToast = true,
    defaultMessage = '오류가 발생했습니다. 다시 시도해주세요.',
    onError,
    errorHandlers = {},
  } = options || {};

  const errorResponse = response;

  // 메시지가 없으면 기본 메시지 사용
  if (!errorResponse.error.message) {
    errorResponse.error.message = defaultMessage;
  }

  // 에러 코드 확인
  const errorCode = errorResponse.error.code || ERROR_CODE.UNKNOWN_ERROR;

  // 특정 에러 코드에 대한 처리
  if (errorCode in errorHandlers) {
    errorHandlers[errorCode](errorResponse);
  }

  // 토스트 메시지 표시
  if (showToast) {
    toast.error(errorResponse.error.message);
  }

  // 사용자 정의 에러 핸들러 호출
  if (onError) {
    onError(errorResponse);
  }

  return errorResponse;
}

/**
 * 함수 실행 시도 및 에러 처리
 */
export async function clientTryCatch<T>(
  fn: () => Promise<T>,
  options?: ClientErrorHandlerOptions & {
    fallbackValue?: T;
  }
): Promise<T | null> {
  try {
    return await fn();
  } catch (error) {
    handleErrorResponse(error, options);
    return options?.fallbackValue !== undefined ? options.fallbackValue : null;
  }
}

/**
 * 서버 액션 실행 및 에러 처리
 */
export async function executeAction<T>(
  action: (...args: any[]) => Promise<any>,
  args: any[],
  options?: ClientErrorHandlerOptions & {
    showSuccessToast?: boolean;
    successMessage?: string;
    defaultErrorMessage?: string;
    onSuccess?: (data: T) => void;
  }
): Promise<T | null> {
  try {
    const response = await action(...args);

    // 응답이 에러인지 확인
    if (isActionErrorResponse(response)) {
      handleActionErrorResponse(response, {
        showToast: options?.showToast,
        defaultMessage: options?.defaultErrorMessage,
        onError: options?.onError,
        errorHandlers: options?.errorHandlers,
      });
      return null;
    }

    // Extract data from success response
    const data = response.data as T;

    // Show success toast if enabled
    if (options?.showSuccessToast && options?.successMessage) {
      toast.success(options.successMessage);
    }

    // Call success handler if provided
    if (options?.onSuccess) {
      options.onSuccess(data);
    }

    return data;
  } catch (error) {
    // 예상치 못한 에러 처리
    handleErrorResponse(error, {
      showToast: options?.showToast,
      defaultMessage: options?.defaultErrorMessage,
      errorHandlers: options?.errorHandlers,
      onError: (err) => {
        if (options?.onError) {
          options.onError({
            success: false,
            error: {
              message: err.error.message,
              code: err.error.code,
              statusCode: err.error.statusCode,
              category: err.error.category,
              data: err.error.data,
            },
            timestamp: err.error.timestamp,
          });
        }
      },
    });
    return null;
  }
}
