/**
 * Centralized error code system
 */

/**
 * HTTP status codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

/**
 * Error categories
 */
export const ERROR_CATEGORY = {
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  RESOURCE: 'resource',
  BUSINESS: 'business',
  INFRASTRUCTURE: 'infrastructure',
  EXTERNAL: 'external',
  UNKNOWN: 'unknown',
} as const;

/**
 * Error codes
 */
export const ERROR_CODE = {
  // Validation errors (400)
  VALIDATION_FAILED: 'validation_failed',
  INVALID_INPUT: 'invalid_input',
  MISSING_REQUIRED_FIELD: 'missing_required_field',
  INVALID_FORMAT: 'invalid_format',
  
  // Authentication errors (401)
  UNAUTHENTICATED: 'unauthenticated',
  INVALID_CREDENTIALS: 'invalid_credentials',
  EXPIRED_TOKEN: 'expired_token',
  INVALID_TOKEN: 'invalid_token',
  
  // Authorization errors (403)
  UNAUTHORIZED: 'unauthorized',
  INSUFFICIENT_PERMISSIONS: 'insufficient_permissions',
  FORBIDDEN_RESOURCE: 'forbidden_resource',
  
  // Resource errors (404, 409)
  RESOURCE_NOT_FOUND: 'resource_not_found',
  RESOURCE_ALREADY_EXISTS: 'resource_already_exists',
  RESOURCE_CONFLICT: 'resource_conflict',
  
  // Business logic errors (422)
  BUSINESS_RULE_VIOLATION: 'business_rule_violation',
  OPERATION_NOT_ALLOWED: 'operation_not_allowed',
  INVALID_STATE: 'invalid_state',
  
  // Infrastructure errors (500, 503, 504)
  INTERNAL_ERROR: 'internal_error',
  DATABASE_ERROR: 'database_error',
  SERVICE_UNAVAILABLE: 'service_unavailable',
  TIMEOUT: 'timeout',
  
  // External service errors
  EXTERNAL_SERVICE_ERROR: 'external_service_error',
  EXTERNAL_SERVICE_TIMEOUT: 'external_service_timeout',
  
  // Unknown errors
  UNKNOWN_ERROR: 'unknown_error',
} as const;

/**
 * Map error codes to HTTP status codes
 */
export const ERROR_CODE_TO_STATUS: Record<string, number> = {
  // Validation errors
  [ERROR_CODE.VALIDATION_FAILED]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODE.INVALID_INPUT]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODE.MISSING_REQUIRED_FIELD]: HTTP_STATUS.BAD_REQUEST,
  [ERROR_CODE.INVALID_FORMAT]: HTTP_STATUS.BAD_REQUEST,
  
  // Authentication errors
  [ERROR_CODE.UNAUTHENTICATED]: HTTP_STATUS.UNAUTHORIZED,
  [ERROR_CODE.INVALID_CREDENTIALS]: HTTP_STATUS.UNAUTHORIZED,
  [ERROR_CODE.EXPIRED_TOKEN]: HTTP_STATUS.UNAUTHORIZED,
  [ERROR_CODE.INVALID_TOKEN]: HTTP_STATUS.UNAUTHORIZED,
  
  // Authorization errors
  [ERROR_CODE.UNAUTHORIZED]: HTTP_STATUS.FORBIDDEN,
  [ERROR_CODE.INSUFFICIENT_PERMISSIONS]: HTTP_STATUS.FORBIDDEN,
  [ERROR_CODE.FORBIDDEN_RESOURCE]: HTTP_STATUS.FORBIDDEN,
  
  // Resource errors
  [ERROR_CODE.RESOURCE_NOT_FOUND]: HTTP_STATUS.NOT_FOUND,
  [ERROR_CODE.RESOURCE_ALREADY_EXISTS]: HTTP_STATUS.CONFLICT,
  [ERROR_CODE.RESOURCE_CONFLICT]: HTTP_STATUS.CONFLICT,
  
  // Business logic errors
  [ERROR_CODE.BUSINESS_RULE_VIOLATION]: HTTP_STATUS.UNPROCESSABLE_ENTITY,
  [ERROR_CODE.OPERATION_NOT_ALLOWED]: HTTP_STATUS.UNPROCESSABLE_ENTITY,
  [ERROR_CODE.INVALID_STATE]: HTTP_STATUS.UNPROCESSABLE_ENTITY,
  
  // Infrastructure errors
  [ERROR_CODE.INTERNAL_ERROR]: HTTP_STATUS.INTERNAL_SERVER_ERROR,
  [ERROR_CODE.DATABASE_ERROR]: HTTP_STATUS.INTERNAL_SERVER_ERROR,
  [ERROR_CODE.SERVICE_UNAVAILABLE]: HTTP_STATUS.SERVICE_UNAVAILABLE,
  [ERROR_CODE.TIMEOUT]: HTTP_STATUS.GATEWAY_TIMEOUT,
  
  // External service errors
  [ERROR_CODE.EXTERNAL_SERVICE_ERROR]: HTTP_STATUS.BAD_GATEWAY,
  [ERROR_CODE.EXTERNAL_SERVICE_TIMEOUT]: HTTP_STATUS.GATEWAY_TIMEOUT,
  
  // Unknown errors
  [ERROR_CODE.UNKNOWN_ERROR]: HTTP_STATUS.INTERNAL_SERVER_ERROR,
};

/**
 * Map error codes to categories
 */
export const ERROR_CODE_TO_CATEGORY: Record<string, string> = {
  // Validation errors
  [ERROR_CODE.VALIDATION_FAILED]: ERROR_CATEGORY.VALIDATION,
  [ERROR_CODE.INVALID_INPUT]: ERROR_CATEGORY.VALIDATION,
  [ERROR_CODE.MISSING_REQUIRED_FIELD]: ERROR_CATEGORY.VALIDATION,
  [ERROR_CODE.INVALID_FORMAT]: ERROR_CATEGORY.VALIDATION,
  
  // Authentication errors
  [ERROR_CODE.UNAUTHENTICATED]: ERROR_CATEGORY.AUTHENTICATION,
  [ERROR_CODE.INVALID_CREDENTIALS]: ERROR_CATEGORY.AUTHENTICATION,
  [ERROR_CODE.EXPIRED_TOKEN]: ERROR_CATEGORY.AUTHENTICATION,
  [ERROR_CODE.INVALID_TOKEN]: ERROR_CATEGORY.AUTHENTICATION,
  
  // Authorization errors
  [ERROR_CODE.UNAUTHORIZED]: ERROR_CATEGORY.AUTHORIZATION,
  [ERROR_CODE.INSUFFICIENT_PERMISSIONS]: ERROR_CATEGORY.AUTHORIZATION,
  [ERROR_CODE.FORBIDDEN_RESOURCE]: ERROR_CATEGORY.AUTHORIZATION,
  
  // Resource errors
  [ERROR_CODE.RESOURCE_NOT_FOUND]: ERROR_CATEGORY.RESOURCE,
  [ERROR_CODE.RESOURCE_ALREADY_EXISTS]: ERROR_CATEGORY.RESOURCE,
  [ERROR_CODE.RESOURCE_CONFLICT]: ERROR_CATEGORY.RESOURCE,
  
  // Business logic errors
  [ERROR_CODE.BUSINESS_RULE_VIOLATION]: ERROR_CATEGORY.BUSINESS,
  [ERROR_CODE.OPERATION_NOT_ALLOWED]: ERROR_CATEGORY.BUSINESS,
  [ERROR_CODE.INVALID_STATE]: ERROR_CATEGORY.BUSINESS,
  
  // Infrastructure errors
  [ERROR_CODE.INTERNAL_ERROR]: ERROR_CATEGORY.INFRASTRUCTURE,
  [ERROR_CODE.DATABASE_ERROR]: ERROR_CATEGORY.INFRASTRUCTURE,
  [ERROR_CODE.SERVICE_UNAVAILABLE]: ERROR_CATEGORY.INFRASTRUCTURE,
  [ERROR_CODE.TIMEOUT]: ERROR_CATEGORY.INFRASTRUCTURE,
  
  // External service errors
  [ERROR_CODE.EXTERNAL_SERVICE_ERROR]: ERROR_CATEGORY.EXTERNAL,
  [ERROR_CODE.EXTERNAL_SERVICE_TIMEOUT]: ERROR_CATEGORY.EXTERNAL,
  
  // Unknown errors
  [ERROR_CODE.UNKNOWN_ERROR]: ERROR_CATEGORY.UNKNOWN,
};
