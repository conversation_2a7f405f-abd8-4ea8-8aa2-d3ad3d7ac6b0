/**
 * Error handling middleware for API routes and server actions
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { AppError } from './base-error';
import { ERROR_CODE } from './error-codes';
import { ValidationError } from './error-types';
import { createErrorNextResponse, handleUnknownError, logError } from './error-utils';

/**
 * Type for API handler function
 */
export type A<PERSON><PERSON>and<PERSON><T = any> = (
  req: NextRequest,
  context: { params: Record<string, string> }
) => Promise<T>;

/**
 * Middleware for handling errors in API routes
 */
export function withErrorHandling<T>(
  handler: <PERSON>piHandler<T>,
  options?: {
    logErrors?: boolean;
  }
): ApiHandler<NextResponse> {
  const { logErrors = true } = options || {};
  
  return async (req: NextRequest, context: { params: Record<string, string> }) => {
    try {
      const result = await handler(req, context);
      
      // If the result is already a NextResponse, return it
      if (result instanceof NextResponse) {
        return result;
      }
      
      // Otherwise, create a success response
      return NextResponse.json(
        {
          data: result,
          status: 200,
          timestamp: new Date().toISOString(),
        },
        { status: 200 }
      );
    } catch (error) {
      // Log the error if enabled
      if (logErrors) {
        logError(error, {
          url: req.url,
          method: req.method,
          params: context.params,
        });
      }
      
      // Convert to AppError if it's not already
      const appError = error instanceof AppError
        ? error
        : handleUnknownError(error);
      
      // Return error response
      return createErrorNextResponse(appError);
    }
  };
}

/**
 * Type for server action function
 */
export type ServerAction<Input, Output> = (input: Input) => Promise<Output>;

/**
 * Type for server action result
 */
export interface ServerActionResult<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    data?: any;
  };
  timestamp: string;
}

/**
 * Middleware for handling errors in server actions
 */
export function withServerActionErrorHandling<Input, Output>(
  action: ServerAction<Input, Output>,
  options?: {
    schema?: z.ZodSchema<Input>;
    logErrors?: boolean;
  }
): ServerAction<Input, ServerActionResult<Output>> {
  const { schema, logErrors = true } = options || {};
  
  return async (input: Input): Promise<ServerActionResult<Output>> => {
    try {
      // Validate input if schema is provided
      let validatedInput = input;
      if (schema) {
        try {
          validatedInput = schema.parse(input) as Input;
        } catch (error) {
          if (error instanceof z.ZodError) {
            throw ValidationError.fromZodError(error);
          }
          throw error;
        }
      }
      
      // Execute the action
      const result = await action(validatedInput);
      
      // Return success result
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      // Log the error if enabled
      if (logErrors) {
        logError(error, { input });
      }
      
      // Convert to AppError if it's not already
      const appError = error instanceof AppError
        ? error
        : handleUnknownError(error);
      
      // Return error result
      return {
        success: false,
        error: {
          message: appError.message,
          code: appError.code,
          data: appError.data,
        },
        timestamp: appError.timestamp,
      };
    }
  };
}

/**
 * Type for server action function with authentication
 */
export type ServerActionWithAuth<Input, Output> = (
  session: any,
  input: Input
) => Promise<Output>;

/**
 * Middleware for handling errors in server actions with authentication
 */
export function withServerActionAuthErrorHandling<Input, Output>(
  action: ServerActionWithAuth<Input, Output>,
  options?: {
    schema?: z.ZodSchema<Input>;
    logErrors?: boolean;
    requireAuth?: (session: any) => Promise<any>;
  }
): ServerAction<Input, ServerActionResult<Output>> {
  const { schema, logErrors = true, requireAuth } = options || {};
  
  return async (input: Input): Promise<ServerActionResult<Output>> => {
    try {
      // Get authentication session
      let session;
      if (requireAuth) {
        session = await requireAuth(null);
      } else {
        // Default authentication check
        throw new AppError(
          '인증 확인 함수가 제공되지 않았습니다.',
          ERROR_CODE.INTERNAL_ERROR,
          null,
          false
        );
      }
      
      // Validate input if schema is provided
      let validatedInput = input;
      if (schema) {
        try {
          validatedInput = schema.parse(input) as Input;
        } catch (error) {
          if (error instanceof z.ZodError) {
            throw ValidationError.fromZodError(error);
          }
          throw error;
        }
      }
      
      // Execute the action
      const result = await action(session, validatedInput);
      
      // Return success result
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      // Log the error if enabled
      if (logErrors) {
        logError(error, { input });
      }
      
      // Convert to AppError if it's not already
      const appError = error instanceof AppError
        ? error
        : handleUnknownError(error);
      
      // Return error result
      return {
        success: false,
        error: {
          message: appError.message,
          code: appError.code,
          data: appError.data,
        },
        timestamp: appError.timestamp,
      };
    }
  };
}
