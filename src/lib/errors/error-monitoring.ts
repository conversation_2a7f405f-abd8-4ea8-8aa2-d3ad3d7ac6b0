'use client';

/**
 * 에러 모니터링 및 로깅 유틸리티
 * 
 * 이 모듈은 클라이언트 및 서버 측 에러를 모니터링하고 로깅하는 기능을 제공합니다.
 * 프로덕션 환경에서는 외부 에러 모니터링 서비스(예: Sentry)와 통합할 수 있습니다.
 */

import { AppError } from './base-error';
import { ERROR_CATEGORY } from './error-codes';
import { handleUnknownError } from './error-utils';

/**
 * 에러 심각도 레벨
 */
export enum ErrorSeverity {
  DEBUG = 'debug',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  FATAL = 'fatal',
}

/**
 * 에러 카테고리에 따른 기본 심각도 매핑
 */
const CATEGORY_TO_SEVERITY: Record<string, ErrorSeverity> = {
  [ERROR_CATEGORY.VALIDATION]: ErrorSeverity.WARNING,
  [ERROR_CATEGORY.AUTHENTICATION]: ErrorSeverity.WARNING,
  [ERROR_CATEGORY.AUTHORIZATION]: ErrorSeverity.WARNING,
  [ERROR_CATEGORY.RESOURCE]: ErrorSeverity.INFO,
  [ERROR_CATEGORY.BUSINESS]: ErrorSeverity.INFO,
  [ERROR_CATEGORY.INFRASTRUCTURE]: ErrorSeverity.ERROR,
  [ERROR_CATEGORY.EXTERNAL]: ErrorSeverity.ERROR,
  [ERROR_CATEGORY.UNKNOWN]: ErrorSeverity.ERROR,
};

/**
 * 에러 모니터링 옵션
 */
interface ErrorMonitoringOptions {
  /**
   * 에러 심각도
   */
  severity?: ErrorSeverity;
  
  /**
   * 추가 컨텍스트 정보
   */
  context?: Record<string, any>;
  
  /**
   * 사용자 정보
   */
  user?: {
    id?: string;
    email?: string;
    name?: string;
  };
  
  /**
   * 태그 (분류 및 필터링용)
   */
  tags?: Record<string, string>;
}

/**
 * 에러를 로깅하고 모니터링 서비스에 보고
 */
export function reportError(
  error: unknown,
  options?: ErrorMonitoringOptions
): void {
  // AppError로 변환
  const appError = error instanceof AppError
    ? error
    : handleUnknownError(error);
  
  // 심각도 결정
  const severity = options?.severity || CATEGORY_TO_SEVERITY[appError.category] || ErrorSeverity.ERROR;
  
  // 에러 정보 구성
  const errorInfo = {
    ...appError.toJSON(),
    severity,
    context: options?.context || {},
    user: options?.user || {},
    tags: options?.tags || {},
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
  };
  
  // 개발 환경에서는 콘솔에 로깅
  if (process.env.NODE_ENV === 'development') {
    logToConsole(severity, errorInfo);
  }
  
  // 프로덕션 환경에서는 모니터링 서비스로 전송
  if (process.env.NODE_ENV === 'production') {
    // TODO: 외부 에러 모니터링 서비스 연동 (예: Sentry)
    // sendToMonitoringService(errorInfo);
  }
}

/**
 * 콘솔에 에러 로깅
 */
function logToConsole(severity: ErrorSeverity, errorInfo: any): void {
  switch (severity) {
    case ErrorSeverity.DEBUG:
      console.debug('[DEBUG]', errorInfo);
      break;
    case ErrorSeverity.INFO:
      console.info('[INFO]', errorInfo);
      break;
    case ErrorSeverity.WARNING:
      console.warn('[WARNING]', errorInfo);
      break;
    case ErrorSeverity.ERROR:
      console.error('[ERROR]', errorInfo);
      break;
    case ErrorSeverity.FATAL:
      console.error('[FATAL]', errorInfo);
      break;
    default:
      console.error('[ERROR]', errorInfo);
  }
}

/**
 * 전역 에러 핸들러 설정
 */
export function setupGlobalErrorHandlers(): void {
  if (typeof window !== 'undefined') {
    // 처리되지 않은 Promise 에러 처리
    window.addEventListener('unhandledrejection', (event) => {
      reportError(event.reason, {
        severity: ErrorSeverity.ERROR,
        tags: { type: 'unhandled_promise_rejection' },
      });
    });
    
    // 전역 에러 처리
    window.addEventListener('error', (event) => {
      reportError(event.error || new Error(event.message), {
        severity: ErrorSeverity.ERROR,
        context: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
        tags: { type: 'global_error' },
      });
    });
  }
}
