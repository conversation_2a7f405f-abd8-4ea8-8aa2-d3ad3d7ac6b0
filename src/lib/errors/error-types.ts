/**
 * Specific error types for different error categories
 */

import { z } from 'zod';
import { AppError } from './base-error';
import { ERROR_CODE } from './error-codes';

/**
 * Validation error
 */
export class ValidationError extends AppError {
  constructor(message: string = '입력 값이 유효하지 않습니다.', data?: any) {
    super(message, ERROR_CODE.VALIDATION_FAILED, data, true);
  }
  
  /**
   * Create a validation error from a Zod error
   */
  static fromZodError(error: z.ZodError, message?: string): ValidationError {
    return new ValidationError(
      message || '입력 값이 유효하지 않습니다.',
      {
        errors: error.errors,
        formattedErrors: error.format(),
      }
    );
  }
}

/**
 * Authentication error
 */
export class AuthenticationError extends AppError {
  constructor(
    message: string = '인증이 필요합니다.',
    code: string = ERROR_CODE.UNAUTHENTICATED,
    data?: any
  ) {
    super(message, code, data, true);
  }
}

/**
 * Invalid credentials error
 */
export class InvalidCredentialsError extends AuthenticationError {
  constructor(message: string = '잘못된 인증 정보입니다.', data?: any) {
    super(message, ERROR_CODE.INVALID_CREDENTIALS, data);
  }
}

/**
 * Token error
 */
export class TokenError extends AuthenticationError {
  constructor(
    message: string = '유효하지 않은 토큰입니다.',
    code: string = ERROR_CODE.INVALID_TOKEN,
    data?: any
  ) {
    super(message, code, data);
  }
}

/**
 * Expired token error
 */
export class ExpiredTokenError extends TokenError {
  constructor(message: string = '만료된 토큰입니다.', data?: any) {
    super(message, ERROR_CODE.EXPIRED_TOKEN, data);
  }
}

/**
 * Authorization error
 */
export class AuthorizationError extends AppError {
  constructor(
    message: string = '접근 권한이 없습니다.',
    code: string = ERROR_CODE.UNAUTHORIZED,
    data?: any
  ) {
    super(message, code, data, true);
  }
}

/**
 * Insufficient permissions error
 */
export class InsufficientPermissionsError extends AuthorizationError {
  constructor(message: string = '권한이 부족합니다.', data?: any) {
    super(message, ERROR_CODE.INSUFFICIENT_PERMISSIONS, data);
  }
}

/**
 * Resource error
 */
export class ResourceError extends AppError {
  constructor(
    message: string,
    code: string = ERROR_CODE.RESOURCE_NOT_FOUND,
    data?: any
  ) {
    super(message, code, data, true);
  }
}

/**
 * Resource not found error
 */
export class ResourceNotFoundError extends ResourceError {
  constructor(
    resource: string,
    id?: string | number,
    data?: any
  ) {
    const message = id
      ? `${resource}(${id})를 찾을 수 없습니다.`
      : `${resource}를 찾을 수 없습니다.`;
    super(message, ERROR_CODE.RESOURCE_NOT_FOUND, data);
  }
}

/**
 * Resource already exists error
 */
export class ResourceAlreadyExistsError extends ResourceError {
  constructor(
    resource: string,
    id?: string | number,
    data?: any
  ) {
    const message = id
      ? `${resource}(${id})가 이미 존재합니다.`
      : `${resource}가 이미 존재합니다.`;
    super(message, ERROR_CODE.RESOURCE_ALREADY_EXISTS, data);
  }
}

/**
 * Business error
 */
export class BusinessError extends AppError {
  constructor(
    message: string,
    code: string = ERROR_CODE.BUSINESS_RULE_VIOLATION,
    data?: any
  ) {
    super(message, code, data, true);
  }
}

/**
 * Infrastructure error
 */
export class InfrastructureError extends AppError {
  constructor(
    message: string = '서버 내부 오류가 발생했습니다.',
    code: string = ERROR_CODE.INTERNAL_ERROR,
    data?: any
  ) {
    super(message, code, data, false);
  }
}

/**
 * Database error
 */
export class DatabaseError extends InfrastructureError {
  constructor(message: string = '데이터베이스 오류가 발생했습니다.', data?: any) {
    super(message, ERROR_CODE.DATABASE_ERROR, data);
  }
}

/**
 * External service error
 */
export class ExternalServiceError extends AppError {
  constructor(
    message: string = '외부 서비스 오류가 발생했습니다.',
    code: string = ERROR_CODE.EXTERNAL_SERVICE_ERROR,
    data?: any
  ) {
    super(message, code, data, false);
  }
}

/**
 * Network error
 */
export class NetworkError extends ExternalServiceError {
  constructor(message: string = '네트워크 오류가 발생했습니다.', data?: any) {
    super(message, ERROR_CODE.EXTERNAL_SERVICE_ERROR, data);
  }
}

/**
 * Timeout error
 */
export class TimeoutError extends ExternalServiceError {
  constructor(message: string = '요청 시간이 초과되었습니다.', data?: any) {
    super(message, ERROR_CODE.TIMEOUT, data);
  }
}
