/**
 * Error handling utilities
 */

import { NextResponse } from 'next/server';
import { z } from 'zod';
import { AppError } from './base-error';
import { ERROR_CODE } from './error-codes';
import {
  AuthenticationError,
  AuthorizationError,
  BusinessError,
  DatabaseError,
  ExternalServiceError,
  InfrastructureError,
  ResourceError,
  ValidationError,
} from './error-types';

/**
 * 표준 에러 응답 형식
 */
export interface ErrorResponse {
  error: {
    message: string;
    code: string;
    statusCode: number;
    category: string;
    data?: any;
    timestamp: string;
  };
}

/**
 * 에러 응답 생성 옵션
 */
export interface ErrorResponseOptions {
  /** 에러 메시지 */
  message?: string;
  /** 에러 코드 */
  code?: string;
  /** HTTP 상태 코드 */
  statusCode?: number;
  /** 에러 카테고리 */
  category?: string;
  /** 추가 에러 데이터 */
  data?: any;
}

/**
 * AppError에서 에러 응답 생성
 */
export function createErrorResponse(error: AppError): ErrorResponse {
  return {
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      category: error.category,
      data: error.data,
      timestamp: error.timestamp,
    },
  };
}

/**
 * 에러 옵션에서 에러 응답 생성
 */
export function createErrorResponseFromOptions(
  options: ErrorResponseOptions
): ErrorResponse {
  const {
    message = '알 수 없는 오류가 발생했습니다.',
    code = ERROR_CODE.UNKNOWN_ERROR,
    statusCode,
    category,
    data,
  } = options;

  // 코드에 따른 상태 코드와 카테고리 결정
  const resolvedStatusCode = statusCode || ERROR_CODE_TO_STATUS[code] || 500;
  const resolvedCategory =
    category || ERROR_CODE_TO_CATEGORY[code] || 'unknown';

  return {
    error: {
      message,
      code,
      statusCode: resolvedStatusCode,
      category: resolvedCategory,
      data,
      timestamp: new Date().toISOString(),
    },
  };
}

/**
 * AppError에서 NextResponse 생성
 */
export function createErrorNextResponse(
  error: AppError
): NextResponse<ErrorResponse> {
  return NextResponse.json(createErrorResponse(error), {
    status: error.statusCode,
  });
}

/**
 * 에러 옵션에서 NextResponse 생성
 */
export function createErrorNextResponseFromOptions(
  options: ErrorResponseOptions
): NextResponse<ErrorResponse> {
  const errorResponse = createErrorResponseFromOptions(options);
  const statusCode = errorResponse.error.statusCode;

  return NextResponse.json(errorResponse, { status: statusCode });
}

/**
 * Handle an unknown error and convert it to an AppError
 */
export function handleUnknownError(
  error: unknown,
  defaultMessage: string = '알 수 없는 오류가 발생했습니다.'
): AppError {
  // If already an AppError, return it
  if (error instanceof AppError) {
    return error;
  }

  // Handle Zod validation errors
  if (error instanceof z.ZodError) {
    return ValidationError.fromZodError(error);
  }

  // Handle standard Error objects
  if (error instanceof Error) {
    // Try to categorize based on error name or message
    if (
      error.name === 'PrismaClientKnownRequestError' ||
      error.name === 'PrismaClientUnknownRequestError' ||
      error.name === 'PrismaClientRustPanicError' ||
      error.name === 'PrismaClientInitializationError' ||
      error.name === 'PrismaClientValidationError'
    ) {
      return new DatabaseError(error.message, { originalError: error });
    }

    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError' ||
      error.name === 'NotBeforeError'
    ) {
      return new AuthenticationError(error.message, ERROR_CODE.INVALID_TOKEN, {
        originalError: error,
      });
    }

    if (error.name === 'FetchError' || error.name === 'AbortError') {
      return new ExternalServiceError(
        error.message,
        ERROR_CODE.EXTERNAL_SERVICE_ERROR,
        { originalError: error }
      );
    }

    // Generic error with the original error's message
    return new AppError(
      error.message || defaultMessage,
      ERROR_CODE.UNKNOWN_ERROR,
      { originalError: error.name, stack: error.stack },
      false
    );
  }

  // For other types, create a generic AppError
  return new AppError(
    defaultMessage,
    ERROR_CODE.UNKNOWN_ERROR,
    { originalError: error },
    false
  );
}

/**
 * Log an error with appropriate level based on its type
 */
export function logError(error: unknown, context?: Record<string, any>): void {
  const appError =
    error instanceof AppError ? error : handleUnknownError(error);

  const errorInfo = {
    ...appError.toJSON(),
    context,
  };

  // Log based on error category and operational status
  if (!appError.isOperational) {
    // Programming errors are always logged as errors
    console.error('[ERROR]', errorInfo);
  } else if (
    appError instanceof ValidationError ||
    appError instanceof AuthenticationError ||
    appError instanceof AuthorizationError
  ) {
    // Client errors are logged as warnings
    console.warn('[WARNING]', errorInfo);
  } else if (
    appError instanceof ResourceError ||
    appError instanceof BusinessError
  ) {
    // Business logic errors are logged as info
    console.info('[INFO]', errorInfo);
  } else if (
    appError instanceof InfrastructureError ||
    appError instanceof ExternalServiceError
  ) {
    // Infrastructure errors are logged as errors
    console.error('[ERROR]', errorInfo);
  } else {
    // Unknown errors are logged as errors
    console.error('[ERROR]', errorInfo);
  }
}

/**
 * Try to execute a function and handle any errors
 */
export async function tryCatch<T>(
  fn: () => Promise<T>,
  errorHandler?: (error: unknown) => Promise<T> | T
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (errorHandler) {
      return errorHandler(error);
    }
    throw handleUnknownError(error);
  }
}
