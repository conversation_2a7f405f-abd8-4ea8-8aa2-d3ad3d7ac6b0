'use client';

/**
 * API 에러 처리를 위한 React 훅
 */

import { ApiError } from '@/lib/api/types/error';
import { isErrorResponse } from '@/lib/errors/client-error-utils';
import { ERROR_CODE } from '@/lib/errors/error-codes';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import {
  ErrorHandlerOptions,
  ErrorHandlerResult,
  useErrorHandler,
} from './useErrorHandler';

/**
 * API 에러 처리 훅 옵션
 */
export interface ApiErrorHandlerOptions extends ErrorHandlerOptions {
  /** 인증 오류 발생 시 리디렉션할 경로 */
  authRedirectPath?: string;
  /** 권한 오류 발생 시 리디렉션할 경로 */
  forbiddenRedirectPath?: string;
  /** 404 오류 발생 시 리디렉션할 경로 */
  notFoundRedirectPath?: string;
  /** 자동 리디렉션 활성화 여부 */
  enableAutoRedirect?: boolean;
}

/**
 * API 에러 처리 훅 반환 값
 */
export interface ApiErrorHandlerResult extends ErrorHandlerResult {
  /** API 에러 객체 */
  apiError: ApiError | null;
  /** API 에러 상태 코드 */
  statusCode: number | null;
  /** API 에러 데이터 */
  errorData: any;
  /** API 에러 처리 함수 */
  handleApiError: (error: unknown) => void;
  /** 인증 오류인지 확인하는 함수 */
  isAuthError: () => boolean;
  /** 권한 오류인지 확인하는 함수 */
  isForbiddenError: () => boolean;
  /** 404 오류인지 확인하는 함수 */
  isNotFoundError: () => boolean;
  /** 유효성 검사 오류인지 확인하는 함수 */
  isValidationError: () => boolean;
}

/**
 * API 에러 처리를 위한 React 훅
 *
 * @param options API 에러 처리 옵션
 * @returns API 에러 처리 관련 상태와 함수
 *
 * @example
 * ```tsx
 * const {
 *   apiError,
 *   statusCode,
 *   errorMessage,
 *   handleApiError,
 *   isAuthError
 * } = useApiError({
 *   showToast: true,
 *   authRedirectPath: '/auth/login',
 *   enableAutoRedirect: true
 * });
 *
 * // API 호출 시 에러 처리
 * const fetchData = async () => {
 *   try {
 *     const data = await apiClient.get('/api/data');
 *     return data;
 *   } catch (error) {
 *     handleApiError(error);
 *     return null;
 *   }
 * };
 * ```
 */
export function useApiError(
  options: ApiErrorHandlerOptions = {}
): ApiErrorHandlerResult {
  const {
    authRedirectPath = '/auth/login',
    forbiddenRedirectPath = '/',
    notFoundRedirectPath = '/',
    enableAutoRedirect = false,
    ...errorHandlerOptions
  } = options;

  const router = useRouter();

  // 기본 에러 핸들러 사용
  const errorHandler = useErrorHandler({
    ...errorHandlerOptions,
    // 자동 리디렉션이 활성화된 경우 리디렉션 비활성화 (직접 처리)
    redirectPath: enableAutoRedirect
      ? undefined
      : errorHandlerOptions.redirectPath,
  });

  // API 에러 상태
  const [apiError, setApiError] = useState<ApiError | null>(null);
  const [statusCode, setStatusCode] = useState<number | null>(null);
  const [errorData, setErrorData] = useState<any>(null);

  /**
   * API 에러 처리 함수
   */
  const handleApiError = useCallback(
    (err: unknown) => {
      // 기본 에러 처리
      errorHandler.handleError(err);

      // API 에러 처리
      if (err instanceof ApiError) {
        setApiError(err);
        setStatusCode(err.statusCode);
        setErrorData(err.data);

        // 자동 리디렉션 처리
        if (enableAutoRedirect) {
          if (err.statusCode === 401) {
            router.push(authRedirectPath);
          } else if (err.statusCode === 403) {
            router.push(forbiddenRedirectPath);
          } else if (err.statusCode === 404) {
            router.push(notFoundRedirectPath);
          }
        }
      } else if (isErrorResponse(err) && err.error.statusCode) {
        setApiError(null);
        setStatusCode(err.error.statusCode);
        setErrorData(err.error.data);

        // 자동 리디렉션 처리
        if (enableAutoRedirect) {
          if (err.error.statusCode === 401) {
            router.push(authRedirectPath);
          } else if (err.error.statusCode === 403) {
            router.push(forbiddenRedirectPath);
          } else if (err.error.statusCode === 404) {
            router.push(notFoundRedirectPath);
          }
        }
      } else {
        setApiError(null);
        setStatusCode(null);
        setErrorData(null);
      }
    },
    [
      errorHandler,
      enableAutoRedirect,
      authRedirectPath,
      forbiddenRedirectPath,
      notFoundRedirectPath,
      router,
    ]
  );

  /**
   * 에러 상태 초기화 함수
   */
  const clearError = useCallback(() => {
    errorHandler.clearError();
    setApiError(null);
    setStatusCode(null);
    setErrorData(null);
  }, [errorHandler]);

  /**
   * 인증 오류인지 확인하는 함수
   */
  const isAuthError = useCallback((): boolean => {
    return (
      statusCode === 401 ||
      errorHandler.isErrorCode(ERROR_CODE.UNAUTHENTICATED) ||
      errorHandler.isErrorCode(ERROR_CODE.INVALID_CREDENTIALS) ||
      errorHandler.isErrorCode(ERROR_CODE.EXPIRED_TOKEN) ||
      errorHandler.isErrorCode(ERROR_CODE.INVALID_TOKEN)
    );
  }, [statusCode, errorHandler]);

  /**
   * 권한 오류인지 확인하는 함수
   */
  const isForbiddenError = useCallback((): boolean => {
    return (
      statusCode === 403 ||
      errorHandler.isErrorCode(ERROR_CODE.UNAUTHORIZED) ||
      errorHandler.isErrorCode(ERROR_CODE.INSUFFICIENT_PERMISSIONS) ||
      errorHandler.isErrorCode(ERROR_CODE.FORBIDDEN_RESOURCE)
    );
  }, [statusCode, errorHandler]);

  /**
   * 404 오류인지 확인하는 함수
   */
  const isNotFoundError = useCallback((): boolean => {
    return (
      statusCode === 404 ||
      errorHandler.isErrorCode(ERROR_CODE.RESOURCE_NOT_FOUND)
    );
  }, [statusCode, errorHandler]);

  /**
   * 유효성 검사 오류인지 확인하는 함수
   */
  const isValidationError = useCallback((): boolean => {
    return (
      statusCode === 400 ||
      errorHandler.isErrorCode(ERROR_CODE.VALIDATION_FAILED) ||
      errorHandler.isErrorCode(ERROR_CODE.INVALID_INPUT) ||
      errorHandler.isErrorCode(ERROR_CODE.MISSING_REQUIRED_FIELD) ||
      errorHandler.isErrorCode(ERROR_CODE.INVALID_FORMAT)
    );
  }, [statusCode, errorHandler]);

  return {
    ...errorHandler,
    apiError,
    statusCode,
    errorData,
    handleApiError,
    isAuthError,
    isForbiddenError,
    isNotFoundError,
    isValidationError,
    clearError,
  };
}
