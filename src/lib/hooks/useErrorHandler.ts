'use client';

/**
 * 에러 처리를 위한 React 훅
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { 
  ClientErrorResponse, 
  ClientActionErrorResponse,
  isErrorResponse, 
  isActionErrorResponse 
} from '@/lib/errors/client-error-utils';
import { ERROR_CODE } from '@/lib/errors/error-codes';

/**
 * 에러 처리 훅 옵션
 */
export interface ErrorHandlerOptions {
  /** 에러 발생 시 토스트 메시지 표시 여부 */
  showToast?: boolean;
  /** 기본 에러 메시지 */
  defaultMessage?: string;
  /** 에러 발생 시 실행할 콜백 함수 */
  onError?: (error: unknown) => void;
  /** 에러 발생 시 리디렉션할 경로 (인증 오류 등에 사용) */
  redirectPath?: string;
  /** 특정 에러 코드에 대한 처리 함수 */
  errorHandlers?: Record<string, (error: unknown) => void>;
}

/**
 * 에러 처리 훅 반환 값
 */
export interface ErrorHandlerResult {
  /** 현재 에러 상태 */
  error: unknown | null;
  /** 에러 메시지 */
  errorMessage: string | null;
  /** 에러 코드 */
  errorCode: string | null;
  /** 에러 상태 여부 */
  hasError: boolean;
  /** 에러 처리 함수 */
  handleError: (error: unknown) => void;
  /** 에러 상태 초기화 함수 */
  clearError: () => void;
  /** 에러가 특정 코드인지 확인하는 함수 */
  isErrorCode: (code: string) => boolean;
}

/**
 * 에러 처리를 위한 React 훅
 * 
 * @param options 에러 처리 옵션
 * @returns 에러 처리 관련 상태와 함수
 * 
 * @example
 * ```tsx
 * const { error, errorMessage, handleError, clearError } = useErrorHandler({
 *   showToast: true,
 *   defaultMessage: '오류가 발생했습니다',
 *   onError: (error) => console.error('에러 발생:', error)
 * });
 * 
 * // API 호출 시 에러 처리
 * const fetchData = async () => {
 *   try {
 *     const data = await api.getData();
 *     return data;
 *   } catch (error) {
 *     handleError(error);
 *     return null;
 *   }
 * };
 * ```
 */
export function useErrorHandler(options: ErrorHandlerOptions = {}): ErrorHandlerResult {
  const { 
    showToast = true, 
    defaultMessage = '오류가 발생했습니다. 다시 시도해주세요.',
    onError,
    redirectPath,
    errorHandlers = {}
  } = options;

  const [error, setError] = useState<unknown | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [errorCode, setErrorCode] = useState<string | null>(null);

  /**
   * 에러 처리 함수
   */
  const handleError = useCallback((err: unknown) => {
    setError(err);

    // 에러 응답 형식 확인 및 처리
    if (isErrorResponse(err)) {
      // API 에러 응답 처리
      const message = err.error.message || defaultMessage;
      const code = err.error.code || ERROR_CODE.UNKNOWN_ERROR;
      
      setErrorMessage(message);
      setErrorCode(code);

      // 토스트 메시지 표시
      if (showToast) {
        toast.error(message);
      }

      // 특정 에러 코드에 대한 처리
      if (code in errorHandlers) {
        errorHandlers[code](err);
      }
    } else if (isActionErrorResponse(err)) {
      // 서버 액션 에러 응답 처리
      const message = err.error.message || defaultMessage;
      const code = err.error.code || ERROR_CODE.UNKNOWN_ERROR;
      
      setErrorMessage(message);
      setErrorCode(code);

      // 토스트 메시지 표시
      if (showToast) {
        toast.error(message);
      }

      // 특정 에러 코드에 대한 처리
      if (code in errorHandlers) {
        errorHandlers[code](err);
      }
    } else if (err instanceof Error) {
      // 일반 Error 객체 처리
      setErrorMessage(err.message || defaultMessage);
      setErrorCode(ERROR_CODE.UNKNOWN_ERROR);

      // 토스트 메시지 표시
      if (showToast) {
        toast.error(err.message || defaultMessage);
      }
    } else {
      // 기타 에러 처리
      setErrorMessage(defaultMessage);
      setErrorCode(ERROR_CODE.UNKNOWN_ERROR);

      // 토스트 메시지 표시
      if (showToast) {
        toast.error(defaultMessage);
      }
    }

    // 사용자 정의 에러 핸들러 호출
    if (onError) {
      onError(err);
    }

    // 리디렉션 처리
    if (redirectPath) {
      // 클라이언트 측 리디렉션 로직 (필요시 구현)
      // window.location.href = redirectPath;
    }
  }, [showToast, defaultMessage, onError, redirectPath, errorHandlers]);

  /**
   * 에러 상태 초기화 함수
   */
  const clearError = useCallback(() => {
    setError(null);
    setErrorMessage(null);
    setErrorCode(null);
  }, []);

  /**
   * 에러가 특정 코드인지 확인하는 함수
   */
  const isErrorCode = useCallback((code: string): boolean => {
    return errorCode === code;
  }, [errorCode]);

  return {
    error,
    errorMessage,
    errorCode,
    hasError: error !== null,
    handleError,
    clearError,
    isErrorCode
  };
}
