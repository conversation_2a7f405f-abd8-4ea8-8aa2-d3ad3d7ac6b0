import { Metadata, ResolvingMetadata } from 'next';

/**
 * 기본 메타데이터 설정
 */
export const defaultMetadata: Metadata = {
  title: {
    default: 'Sodamm - 한국 생활 정보 플랫폼',
    template: '%s | Sodamm',
  },
  description: '외국인 근로자를 위한 한국 생활 정보 및 구인구직 플랫폼',
  keywords: [
    '한국 생활',
    '외국인 근로자',
    '생활 정보',
    '구인구직',
    '커뮤니티',
    '한국 서비스',
    '정부 정보',
  ],
  authors: [
    {
      name: 'Sodamm Team',
      url: 'https://sodamm.com',
    },
  ],
  creator: 'Sodamm Team',
  publisher: 'Sodamm',
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com'
  ),
  alternates: {
    canonical: '/',
    languages: {
      'ko-KR': '/ko',
      'en-US': '/en',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'ko_KR',
    alternateLocale: ['en_US'],
    siteName: 'Sodamm',
    title: 'Sodamm - 한국 생활 정보 플랫폼',
    description: '외국인 근로자를 위한 한국 생활 정보 및 구인구직 플랫폼',
    url: 'https://sodamm.com',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Sodamm - 한국 생활 정보 플랫폼',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sodamm - 한국 생활 정보 플랫폼',
    description: '외국인 근로자를 위한 한국 생활 정보 및 구인구직 플랫폼',
    creator: '@sodamm',
    images: ['/images/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
      },
    ],
  },
  manifest: '/site.webmanifest',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    naver: process.env.NEXT_PUBLIC_NAVER_SITE_VERIFICATION,
  },
  category: '생활 정보',
};

/**
 * 페이지별 메타데이터 생성 함수
 * 
 * @param title 페이지 제목
 * @param description 페이지 설명
 * @param image 오픈 그래프 이미지 URL
 * @param noIndex 검색 엔진 인덱싱 여부
 * @param parent 부모 메타데이터
 * @returns 메타데이터 객체
 */
export async function constructMetadata({
  title,
  description,
  image,
  noIndex = false,
  parent,
}: {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  parent?: ResolvingMetadata;
}): Promise<Metadata> {
  // 부모 메타데이터 가져오기
  const previousImages = (await parent)?.openGraph?.images || [];
  const previousTwitterImages = (await parent)?.twitter?.images || [];
  
  // 이미지 URL 구성
  const imageUrl = image
    ? new URL(image, process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com').toString()
    : undefined;
  
  return {
    title: title,
    description: description,
    openGraph: {
      title: title,
      description: description,
      ...(imageUrl && {
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title,
          },
          ...previousImages,
        ],
      }),
    },
    twitter: {
      title: title,
      description: description,
      ...(imageUrl && {
        images: [imageUrl, ...previousTwitterImages],
      }),
    },
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
        googleBot: {
          index: false,
          follow: false,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    }),
  };
}
