import * as Sentry from "@sentry/nextjs";
import { captureError, ErrorSeverity } from "./error";

/**
 * 성능 메트릭 인터페이스
 */
export interface PerformanceMetrics {
  // Core Web Vitals
  LCP?: number; // Largest Contentful Paint
  FID?: number; // First Input Delay
  CLS?: number; // Cumulative Layout Shift
  INP?: number; // Interaction to Next Paint (새로운 Core Web Vital)

  // 추가 메트릭
  TTFB?: number; // Time to First Byte
  FCP?: number; // First Contentful Paint
  TTI?: number; // Time to Interactive
  TBT?: number; // Total Blocking Time
  FMP?: number; // First Meaningful Paint
}

/**
 * Core Web Vitals 점수 등급
 */
export enum WebVitalScore {
  GOOD = "good",
  NEEDS_IMPROVEMENT = "needs-improvement",
  POOR = "poor",
}

/**
 * Core Web Vitals 점수 평가 함수
 *
 * @param name 메트릭 이름
 * @param value 메트릭 값
 * @returns 점수 등급
 */
export function getWebVitalScore(name: string, value: number): WebVitalScore {
  switch (name) {
    case "LCP": // 최대 콘텐츠풀 페인트 (밀리초)
      return value <= 2500
        ? WebVitalScore.GOOD
        : value <= 4000
        ? WebVitalScore.NEEDS_IMPROVEMENT
        : WebVitalScore.POOR;

    case "FID": // 최초 입력 지연 (밀리초)
      return value <= 100
        ? WebVitalScore.GOOD
        : value <= 300
        ? WebVitalScore.NEEDS_IMPROVEMENT
        : WebVitalScore.POOR;

    case "CLS": // 누적 레이아웃 이동 (점수)
      return value <= 0.1
        ? WebVitalScore.GOOD
        : value <= 0.25
        ? WebVitalScore.NEEDS_IMPROVEMENT
        : WebVitalScore.POOR;

    case "INP": // 상호작용 후 다음 페인트 (밀리초)
      return value <= 200
        ? WebVitalScore.GOOD
        : value <= 500
        ? WebVitalScore.NEEDS_IMPROVEMENT
        : WebVitalScore.POOR;

    case "TTFB": // 최초 바이트까지의 시간 (밀리초)
      return value <= 800
        ? WebVitalScore.GOOD
        : value <= 1800
        ? WebVitalScore.NEEDS_IMPROVEMENT
        : WebVitalScore.POOR;

    case "FCP": // 최초 콘텐츠풀 페인트 (밀리초)
      return value <= 1800
        ? WebVitalScore.GOOD
        : value <= 3000
        ? WebVitalScore.NEEDS_IMPROVEMENT
        : WebVitalScore.POOR;

    default:
      return WebVitalScore.GOOD;
  }
}

/**
 * 성능 메트릭을 Sentry에 보고하는 함수
 * @param metrics 성능 메트릭 객체
 * @param name 측정 이름
 */
export function reportPerformanceMetrics(
  metrics: PerformanceMetrics,
  name: string = "page-load"
): void {
  try {
    const transaction = Sentry.startTransaction({
      name: `performance-${name}`,
      op: "measure",
    });

    // 메트릭을 트랜잭션에 추가
    Object.entries(metrics).forEach(([key, value]) => {
      if (value !== undefined) {
        transaction.setMeasurement(key, value, "millisecond");

        // 메트릭 점수 평가 및 태그 추가
        const score = getWebVitalScore(key, value);
        transaction.setTag(`${key.toLowerCase()}_score`, score);
      }
    });

    // 트랜잭션 완료
    transaction.finish();

    // 개발 환경에서는 콘솔에도 출력
    if (process.env.NODE_ENV === "development") {
      console.info("[Performance Metrics]", metrics);

      // 점수 평가 출력
      Object.entries(metrics).forEach(([key, value]) => {
        if (value !== undefined) {
          const score = getWebVitalScore(key, value);
          console.info(`[${key}] ${value} - ${score}`);
        }
      });
    }
  } catch (error) {
    captureError(
      error instanceof Error ? error : new Error(String(error)),
      { extra: { metrics, name } },
      ErrorSeverity.WARNING
    );
  }
}

/**
 * Web Vitals 메트릭을 Sentry에 보고하는 함수
 * @param metric Web Vitals 메트릭
 */
export function reportWebVitals(metric: any): void {
  try {
    const { name, value, id } = metric;

    // 메트릭 이름에 따라 적절한 키로 변환
    const metricKey =
      name === "LCP"
        ? "LCP"
        : name === "FID"
        ? "FID"
        : name === "CLS"
        ? "CLS"
        : name === "INP"
        ? "INP"
        : name === "TTFB"
        ? "TTFB"
        : name === "FCP"
        ? "FCP"
        : name;

    // 메트릭 객체 생성
    const metrics: PerformanceMetrics = {
      [metricKey]: value,
    };

    // 성능 메트릭 보고
    reportPerformanceMetrics(metrics, `web-vitals-${id}`);

    // 브라우저 성능 API를 사용하여 추가 메트릭 수집 (클라이언트 사이드에서만)
    if (typeof window !== "undefined" && window.performance) {
      try {
        // 네비게이션 타이밍 API
        const navEntry = window.performance.getEntriesByType(
          "navigation"
        )[0] as PerformanceNavigationTiming;
        if (navEntry) {
          const ttfb = navEntry.responseStart - navEntry.requestStart;
          const domLoad =
            navEntry.domContentLoadedEventEnd - navEntry.fetchStart;

          // 추가 메트릭 보고
          reportPerformanceMetrics(
            {
              TTFB: ttfb,
              TTI: domLoad,
            },
            `navigation-${id}`
          );
        }
      } catch (e) {
        // 성능 API 오류는 무시
      }
    }
  } catch (error) {
    captureError(
      error instanceof Error ? error : new Error(String(error)),
      { extra: { metric } },
      ErrorSeverity.WARNING
    );
  }
}

/**
 * 사용자 행동을 추적하는 함수
 * @param action 사용자 행동
 * @param category 행동 카테고리
 * @param label 행동 라벨
 * @param value 행동 값
 */
export function trackUserAction(
  action: string,
  category: string,
  label?: string,
  value?: number
): void {
  try {
    Sentry.addBreadcrumb({
      category: "user-action",
      message: action,
      data: {
        category,
        label,
        value,
      },
      level: "info",
    });

    // 개발 환경에서는 콘솔에도 출력
    if (process.env.NODE_ENV === "development") {
      console.info("[User Action]", {
        action,
        category,
        label,
        value,
      });
    }

    // 사용자 상호작용 시 성능 측정 (INP 최적화)
    if (
      typeof window !== "undefined" &&
      window.performance &&
      window.performance.now
    ) {
      const startTime = window.performance.now();

      // 다음 프레임에서 상호작용 지연 시간 측정
      requestAnimationFrame(() => {
        const endTime = window.performance.now();
        const interactionTime = endTime - startTime;

        // 상호작용 지연 시간이 100ms 이상인 경우 보고
        if (interactionTime > 100) {
          reportPerformanceMetrics(
            {
              INP: interactionTime,
            },
            `interaction-${action.replace(/\s+/g, "-").toLowerCase()}`
          );
        }
      });
    }
  } catch (error) {
    captureError(
      error instanceof Error ? error : new Error(String(error)),
      {
        extra: {
          action,
          category,
          label,
          value,
        },
      },
      ErrorSeverity.WARNING
    );
  }
}

/**
 * 레이아웃 이동 감지 및 보고 함수
 * CLS(Cumulative Layout Shift) 최적화를 위한 함수
 */
export function detectLayoutShifts(): void {
  if (typeof window === "undefined" || !window.PerformanceObserver) return;

  try {
    // 레이아웃 이동 관찰자 생성
    const layoutShiftObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        // LayoutShift 엔트리인지 확인
        if (
          entry.entryType === "layout-shift" &&
          !(entry as any).hadRecentInput
        ) {
          const layoutShiftEntry = entry as any; // LayoutShift 타입이 TS에 없을 수 있음

          // 레이아웃 이동 값이 0.05 이상인 경우 보고
          if (layoutShiftEntry.value >= 0.05) {
            reportPerformanceMetrics(
              {
                CLS: layoutShiftEntry.value,
              },
              `layout-shift-${Date.now()}`
            );
          }
        }
      }
    });

    // 레이아웃 이동 관찰 시작
    layoutShiftObserver.observe({ type: "layout-shift", buffered: true });
  } catch (error) {
    // 성능 API 오류는 무시
  }
}
