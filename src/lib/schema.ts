/**
 * Schema.org 구조화된 데이터 유틸리티 함수
 * 
 * JSON-LD 형식의 구조화된 데이터를 생성하는 함수들을 제공합니다.
 * 검색 엔진이 콘텐츠를 더 잘 이해할 수 있도록 도와줍니다.
 */

/**
 * 조직 스키마 생성
 */
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Sodamm',
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com',
    logo: `${process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com'}/logo.png`,
    sameAs: [
      'https://facebook.com/sodamm',
      'https://twitter.com/sodamm',
      'https://instagram.com/sodamm',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+82-2-123-4567',
      contactType: 'customer service',
      availableLanguage: ['Korean', 'English'],
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'KR',
      addressLocality: 'Seoul',
      postalCode: '12345',
      streetAddress: '123 Gangnam-daero, Gangnam-gu',
    },
    description: '외국인 근로자를 위한 한국 생활 정보 및 구인구직 플랫폼',
  };
}

/**
 * 웹사이트 스키마 생성
 */
export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Sodamm',
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com',
    potentialAction: {
      '@type': 'SearchAction',
      target: `${process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com'}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
    inLanguage: ['ko-KR', 'en-US'],
  };
}

/**
 * 게시글(Article) 스키마 생성
 */
export function generateArticleSchema(article: {
  title: string;
  description: string;
  url: string;
  imageUrl?: string;
  authorName: string;
  authorUrl?: string;
  publishedTime: string;
  modifiedTime?: string;
  category?: string;
  tags?: string[];
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    image: article.imageUrl,
    author: {
      '@type': 'Person',
      name: article.authorName,
      url: article.authorUrl,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Sodamm',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://sodamm.com'}/logo.png`,
      },
    },
    url: article.url,
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    articleSection: article.category,
    keywords: article.tags?.join(', '),
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': article.url,
    },
  };
}

/**
 * 구인 정보(JobPosting) 스키마 생성
 */
export function generateJobPostingSchema(job: {
  title: string;
  description: string;
  url: string;
  datePosted: string;
  validThrough?: string;
  employmentType: string;
  hiringOrganization: {
    name: string;
    url?: string;
    logo?: string;
  };
  jobLocation: {
    addressLocality: string;
    addressRegion?: string;
    addressCountry: string;
    postalCode?: string;
    streetAddress?: string;
  };
  baseSalary?: {
    currency: string;
    value: number | string;
    unitText: 'HOUR' | 'DAY' | 'WEEK' | 'MONTH' | 'YEAR';
  };
  skills?: string[];
  qualifications?: string;
  educationRequirements?: string;
  experienceRequirements?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'JobPosting',
    title: job.title,
    description: job.description,
    datePosted: job.datePosted,
    validThrough: job.validThrough,
    employmentType: job.employmentType,
    hiringOrganization: {
      '@type': 'Organization',
      name: job.hiringOrganization.name,
      url: job.hiringOrganization.url,
      logo: job.hiringOrganization.logo,
    },
    jobLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality: job.jobLocation.addressLocality,
        addressRegion: job.jobLocation.addressRegion,
        addressCountry: job.jobLocation.addressCountry,
        postalCode: job.jobLocation.postalCode,
        streetAddress: job.jobLocation.streetAddress,
      },
    },
    ...(job.baseSalary && {
      baseSalary: {
        '@type': 'MonetaryAmount',
        currency: job.baseSalary.currency,
        value: {
          '@type': 'QuantitativeValue',
          value: job.baseSalary.value,
          unitText: job.baseSalary.unitText,
        },
      },
    }),
    skills: job.skills?.join(', '),
    qualifications: job.qualifications,
    educationRequirements: job.educationRequirements,
    experienceRequirements: job.experienceRequirements,
  };
}

/**
 * FAQ 스키마 생성
 */
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

/**
 * 빵 부스러기(BreadcrumbList) 스키마 생성
 */
export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}
