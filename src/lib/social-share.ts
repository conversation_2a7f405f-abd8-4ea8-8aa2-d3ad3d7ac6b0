/**
 * 소셜 미디어 공유 유틸리티 함수
 */

/**
 * 페이스북 공유 URL 생성
 * 
 * @param url 공유할 URL
 * @returns 페이스북 공유 URL
 */
export function getFacebookShareUrl(url: string): string {
  return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
}

/**
 * 트위터 공유 URL 생성
 * 
 * @param url 공유할 URL
 * @param text 공유 텍스트
 * @param hashtags 해시태그 (쉼표로 구분)
 * @param via 트위터 계정명
 * @returns 트위터 공유 URL
 */
export function getTwitterShareUrl(
  url: string,
  text?: string,
  hashtags?: string,
  via?: string
): string {
  let twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
  
  if (text) {
    twitterUrl += `&text=${encodeURIComponent(text)}`;
  }
  
  if (hashtags) {
    twitterUrl += `&hashtags=${encodeURIComponent(hashtags)}`;
  }
  
  if (via) {
    twitterUrl += `&via=${encodeURIComponent(via)}`;
  }
  
  return twitterUrl;
}

/**
 * 링크드인 공유 URL 생성
 * 
 * @param url 공유할 URL
 * @param title 공유 제목
 * @param summary 공유 요약
 * @param source 공유 소스
 * @returns 링크드인 공유 URL
 */
export function getLinkedInShareUrl(
  url: string,
  title?: string,
  summary?: string,
  source?: string
): string {
  let linkedInUrl = `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(url)}`;
  
  if (title) {
    linkedInUrl += `&title=${encodeURIComponent(title)}`;
  }
  
  if (summary) {
    linkedInUrl += `&summary=${encodeURIComponent(summary)}`;
  }
  
  if (source) {
    linkedInUrl += `&source=${encodeURIComponent(source)}`;
  }
  
  return linkedInUrl;
}

/**
 * 카카오톡 공유 URL 생성
 * 
 * @param url 공유할 URL
 * @param title 공유 제목
 * @param description 공유 설명
 * @param imageUrl 공유 이미지 URL
 * @returns 카카오톡 공유 URL
 */
export function getKakaoShareUrl(
  url: string,
  title?: string,
  description?: string,
  imageUrl?: string
): string {
  // 카카오톡 공유는 JavaScript SDK를 사용해야 하므로 여기서는 URL만 반환
  return url;
}

/**
 * 이메일 공유 URL 생성
 * 
 * @param url 공유할 URL
 * @param subject 이메일 제목
 * @param body 이메일 본문
 * @returns 이메일 공유 URL
 */
export function getEmailShareUrl(
  url: string,
  subject?: string,
  body?: string
): string {
  let emailUrl = 'mailto:?';
  
  if (subject) {
    emailUrl += `subject=${encodeURIComponent(subject)}&`;
  }
  
  const emailBody = body ? `${body}\n\n${url}` : url;
  emailUrl += `body=${encodeURIComponent(emailBody)}`;
  
  return emailUrl;
}

/**
 * 클립보드에 URL 복사
 * 
 * @param url 복사할 URL
 * @param callback 복사 완료 후 콜백 함수
 */
export async function copyToClipboard(
  url: string,
  callback?: (success: boolean) => void
): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(url);
    if (callback) callback(true);
    return true;
  } catch (error) {
    console.error('클립보드 복사 실패:', error);
    if (callback) callback(false);
    return false;
  }
}
