import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

// 버킷 이름 정의
const LIFE_INFO_BUCKET = 'life-info';

/**
 * 파일 확장자 가져오기
 * @param filename 파일 이름
 * @returns 파일 확장자
 */
export function getFileExtension(filename: string): string {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
}

/**
 * 파일 크기 검증
 * @param file 파일 객체
 * @param maxSizeMB 최대 파일 크기 (MB)
 * @returns 유효성 검사 결과
 */
export function validateFileSize(file: File, maxSizeMB: number = 5): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

/**
 * 파일 타입 검증
 * @param file 파일 객체
 * @param allowedTypes 허용된 MIME 타입 배열
 * @returns 유효성 검사 결과
 */
export function validateFileType(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
): boolean {
  return allowedTypes.includes(file.type);
}

/**
 * 이미지 파일 업로드
 * @param file 파일 객체
 * @param path 저장 경로 (기본값: 'images')
 * @returns 업로드 결과
 */
export async function uploadImage(file: File, path: string = 'images'): Promise<{
  url: string;
  error?: string;
}> {
  try {
    // 파일 크기 검증
    if (!validateFileSize(file)) {
      return { url: '', error: '파일 크기는 5MB 이하여야 합니다.' };
    }

    // 파일 타입 검증
    if (!validateFileType(file)) {
      return { url: '', error: '지원되지 않는 파일 형식입니다. (JPEG, PNG, GIF, WEBP만 허용)' };
    }

    // 파일 이름 생성 (중복 방지를 위해 UUID 사용)
    const extension = getFileExtension(file.name);
    const filename = `${uuidv4()}.${extension}`;
    const fullPath = `${path}/${filename}`;

    // Supabase Storage에 파일 업로드
    const { data, error } = await supabase.storage
      .from(LIFE_INFO_BUCKET)
      .upload(fullPath, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      console.error('이미지 업로드 오류:', error);
      return { url: '', error: error.message };
    }

    // 업로드된 파일의 공개 URL 생성
    const { data: publicUrlData } = supabase.storage
      .from(LIFE_INFO_BUCKET)
      .getPublicUrl(data.path);

    return { url: publicUrlData.publicUrl };
  } catch (error) {
    console.error('이미지 업로드 중 예외 발생:', error);
    return { url: '', error: '이미지 업로드 중 오류가 발생했습니다.' };
  }
}

/**
 * 이미지 파일 삭제
 * @param url 이미지 URL
 * @returns 삭제 결과
 */
export async function deleteImage(url: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // URL에서 경로 추출
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/');
    const bucketIndex = pathSegments.findIndex(segment => segment === LIFE_INFO_BUCKET);
    
    if (bucketIndex === -1 || bucketIndex === pathSegments.length - 1) {
      return { success: false, error: '유효하지 않은 이미지 URL입니다.' };
    }
    
    // 버킷 이름 이후의 경로 추출
    const filePath = pathSegments.slice(bucketIndex + 1).join('/');
    
    // Supabase Storage에서 파일 삭제
    const { error } = await supabase.storage
      .from(LIFE_INFO_BUCKET)
      .remove([filePath]);
    
    if (error) {
      console.error('이미지 삭제 오류:', error);
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    console.error('이미지 삭제 중 예외 발생:', error);
    return { success: false, error: '이미지 삭제 중 오류가 발생했습니다.' };
  }
}

/**
 * 여러 이미지 파일 업로드
 * @param files 파일 객체 배열
 * @param path 저장 경로 (기본값: 'images')
 * @returns 업로드 결과 배열
 */
export async function uploadMultipleImages(
  files: File[],
  path: string = 'images'
): Promise<{ url: string; error?: string }[]> {
  const uploadPromises = files.map(file => uploadImage(file, path));
  return Promise.all(uploadPromises);
}
