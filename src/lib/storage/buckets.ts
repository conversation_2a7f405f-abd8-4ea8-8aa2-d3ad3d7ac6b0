import { supabase } from '../supabase';

/**
 * Supabase Storage 버킷 목록
 */
export enum StorageBucket {
  PROFILES = 'profiles',
  LIFE_INFO = 'life-info',
  SERVICES = 'services',
  GOVERNMENT = 'government',
  COMMUNITY = 'community',
  JOBS = 'jobs',
  DOCUMENTS = 'documents',
  TEMP = 'temp',
}

/**
 * 버킷 설정 인터페이스
 */
export interface BucketConfig {
  isPublic: boolean;
  maxFileSize: number; // MB 단위
  allowedMimeTypes: string[];
  description: string;
}

/**
 * 버킷별 설정
 */
export const BUCKET_CONFIGS: Record<StorageBucket, BucketConfig> = {
  [StorageBucket.PROFILES]: {
    isPublic: true,
    maxFileSize: 2, // 2MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
    description: '사용자 프로필 이미지 저장소',
  },
  [StorageBucket.LIFE_INFO]: {
    isPublic: true,
    maxFileSize: 5, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    description: '생활 정보 관련 이미지 저장소',
  },
  [StorageBucket.SERVICES]: {
    isPublic: true,
    maxFileSize: 5, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    description: '서비스 이용 가이드 관련 이미지 저장소',
  },
  [StorageBucket.GOVERNMENT]: {
    isPublic: true,
    maxFileSize: 5, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'],
    description: '정부 정보 관련 이미지 및 문서 저장소',
  },
  [StorageBucket.COMMUNITY]: {
    isPublic: true,
    maxFileSize: 5, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    description: '커뮤니티 게시판 관련 이미지 저장소',
  },
  [StorageBucket.JOBS]: {
    isPublic: true,
    maxFileSize: 5, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'],
    description: '구인/구직 관련 이미지 및 문서 저장소',
  },
  [StorageBucket.DOCUMENTS]: {
    isPublic: false,
    maxFileSize: 10, // 10MB
    allowedMimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
    ],
    description: '문서 저장소 (비공개)',
  },
  [StorageBucket.TEMP]: {
    isPublic: true,
    maxFileSize: 10, // 10MB
    allowedMimeTypes: ['*/*'],
    description: '임시 파일 저장소 (24시간 후 자동 삭제)',
  },
};

/**
 * 버킷 존재 여부 확인
 * @param bucket 버킷 이름
 * @returns 버킷 존재 여부
 */
export async function bucketExists(bucket: string): Promise<boolean> {
  try {
    const { data, error } = await supabase.storage.getBucket(bucket);
    return !error && !!data;
  } catch (error) {
    console.error('버킷 존재 여부 확인 중 오류 발생:', error);
    return false;
  }
}

/**
 * 버킷 생성
 * @param bucket 버킷 이름
 * @param isPublic 공개 여부
 * @returns 생성 성공 여부
 */
export async function createBucket(bucket: string, isPublic: boolean = false): Promise<boolean> {
  try {
    // 버킷이 이미 존재하는지 확인
    const exists = await bucketExists(bucket);
    if (exists) {
      console.log(`버킷 '${bucket}'이(가) 이미 존재합니다.`);
      return true;
    }

    // 버킷 생성
    const { error } = await supabase.storage.createBucket(bucket, {
      public: isPublic,
    });

    if (error) {
      console.error(`버킷 '${bucket}' 생성 중 오류 발생:`, error);
      return false;
    }

    console.log(`버킷 '${bucket}'이(가) 성공적으로 생성되었습니다.`);
    return true;
  } catch (error) {
    console.error(`버킷 '${bucket}' 생성 중 오류 발생:`, error);
    return false;
  }
}

/**
 * 버킷 삭제
 * @param bucket 버킷 이름
 * @returns 삭제 성공 여부
 */
export async function deleteBucket(bucket: string): Promise<boolean> {
  try {
    const { error } = await supabase.storage.deleteBucket(bucket);
    
    if (error) {
      console.error(`버킷 '${bucket}' 삭제 중 오류 발생:`, error);
      return false;
    }
    
    console.log(`버킷 '${bucket}'이(가) 성공적으로 삭제되었습니다.`);
    return true;
  } catch (error) {
    console.error(`버킷 '${bucket}' 삭제 중 오류 발생:`, error);
    return false;
  }
}

/**
 * 버킷 공개 여부 업데이트
 * @param bucket 버킷 이름
 * @param isPublic 공개 여부
 * @returns 업데이트 성공 여부
 */
export async function updateBucketPublic(bucket: string, isPublic: boolean): Promise<boolean> {
  try {
    const { error } = await supabase.storage.updateBucket(bucket, {
      public: isPublic,
    });
    
    if (error) {
      console.error(`버킷 '${bucket}' 공개 여부 업데이트 중 오류 발생:`, error);
      return false;
    }
    
    console.log(`버킷 '${bucket}'의 공개 여부가 성공적으로 업데이트되었습니다.`);
    return true;
  } catch (error) {
    console.error(`버킷 '${bucket}' 공개 여부 업데이트 중 오류 발생:`, error);
    return false;
  }
}

/**
 * 모든 필요한 버킷 초기화
 * @returns 초기화 성공 여부
 */
export async function initializeBuckets(): Promise<boolean> {
  try {
    let success = true;
    
    for (const [bucket, config] of Object.entries(BUCKET_CONFIGS)) {
      const result = await createBucket(bucket, config.isPublic);
      if (!result) {
        success = false;
      }
    }
    
    return success;
  } catch (error) {
    console.error('버킷 초기화 중 오류 발생:', error);
    return false;
  }
}
