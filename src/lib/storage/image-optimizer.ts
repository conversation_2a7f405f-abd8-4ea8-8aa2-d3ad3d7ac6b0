/**
 * 이미지 최적화 옵션 인터페이스
 */
export interface ImageOptimizeOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
}

/**
 * 이미지 최적화 결과 인터페이스
 */
export interface OptimizedImage {
  file: File;
  width: number;
  height: number;
  size: number;
  originalSize: number;
  format: string;
  error?: string;
}

/**
 * 이미지 파일 최적화
 * @param file 이미지 파일
 * @param options 최적화 옵션
 * @returns 최적화된 이미지
 */
export async function optimizeImage(
  file: File,
  options: ImageOptimizeOptions = {}
): Promise<OptimizedImage> {
  // 기본 옵션 설정
  const defaultOptions: ImageOptimizeOptions = {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.8,
    format: 'webp',
  };
  
  // 옵션 병합
  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    // 이미지 파일인지 확인
    if (!file.type.startsWith('image/')) {
      return {
        file,
        width: 0,
        height: 0,
        size: file.size,
        originalSize: file.size,
        format: file.type.split('/')[1],
        error: '이미지 파일만 최적화할 수 있습니다.',
      };
    }
    
    // 이미지 로드
    const image = await createImageBitmap(file);
    const { width: originalWidth, height: originalHeight } = image;
    
    // 새 크기 계산
    let { width, height } = calculateNewDimensions(
      originalWidth,
      originalHeight,
      finalOptions.maxWidth!,
      finalOptions.maxHeight!
    );
    
    // 캔버스 생성
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    
    // 이미지 그리기
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Canvas 2D context를 생성할 수 없습니다.');
    }
    
    ctx.drawImage(image, 0, 0, width, height);
    
    // 이미지 포맷 및 MIME 타입 설정
    let mimeType: string;
    switch (finalOptions.format) {
      case 'jpeg':
        mimeType = 'image/jpeg';
        break;
      case 'png':
        mimeType = 'image/png';
        break;
      case 'webp':
        mimeType = 'image/webp';
        break;
      default:
        mimeType = 'image/webp';
    }
    
    // 캔버스를 Blob으로 변환
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob(
        (blob) => resolve(blob!),
        mimeType,
        finalOptions.quality
      );
    });
    
    // 파일 이름 생성
    const fileNameParts = file.name.split('.');
    const extension = finalOptions.format || 'webp';
    const fileName = `${fileNameParts[0]}.${extension}`;
    
    // Blob을 File로 변환
    const optimizedFile = new File([blob], fileName, { type: mimeType });
    
    return {
      file: optimizedFile,
      width,
      height,
      size: optimizedFile.size,
      originalSize: file.size,
      format: finalOptions.format!,
    };
  } catch (error) {
    console.error('이미지 최적화 중 오류 발생:', error);
    return {
      file,
      width: 0,
      height: 0,
      size: file.size,
      originalSize: file.size,
      format: file.type.split('/')[1],
      error: '이미지 최적화 중 오류가 발생했습니다.',
    };
  }
}

/**
 * 새 이미지 크기 계산
 * @param originalWidth 원본 너비
 * @param originalHeight 원본 높이
 * @param maxWidth 최대 너비
 * @param maxHeight 최대 높이
 * @returns 새 크기
 */
function calculateNewDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  // 원본 이미지가 최대 크기보다 작은 경우 원본 크기 유지
  if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
    return { width: originalWidth, height: originalHeight };
  }
  
  // 비율 계산
  const widthRatio = maxWidth / originalWidth;
  const heightRatio = maxHeight / originalHeight;
  
  // 더 작은 비율 선택 (이미지가 최대 크기를 초과하지 않도록)
  const ratio = Math.min(widthRatio, heightRatio);
  
  // 새 크기 계산
  const width = Math.round(originalWidth * ratio);
  const height = Math.round(originalHeight * ratio);
  
  return { width, height };
}

/**
 * 이미지 파일 리사이징
 * @param file 이미지 파일
 * @param width 너비
 * @param height 높이
 * @param quality 품질 (0-1)
 * @returns 리사이징된 이미지
 */
export async function resizeImage(
  file: File,
  width: number,
  height: number,
  quality: number = 0.8
): Promise<OptimizedImage> {
  return optimizeImage(file, { maxWidth: width, maxHeight: height, quality });
}

/**
 * 이미지 파일 포맷 변환
 * @param file 이미지 파일
 * @param format 변환할 포맷
 * @param quality 품질 (0-1)
 * @returns 변환된 이미지
 */
export async function convertImageFormat(
  file: File,
  format: 'jpeg' | 'png' | 'webp',
  quality: number = 0.8
): Promise<OptimizedImage> {
  return optimizeImage(file, { format, quality });
}
