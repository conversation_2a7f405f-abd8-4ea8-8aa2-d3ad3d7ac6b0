import { supabase } from '../supabase';
import { StorageBucket } from './buckets';

/**
 * 파일 정보 인터페이스
 */
export interface FileInfo {
  name: string;
  id: string;
  size: number;
  created_at: string;
  last_modified: string;
  metadata: Record<string, any>;
}

/**
 * 파일 목록 조회
 * @param bucket 버킷 이름
 * @param path 경로 (기본값: '')
 * @returns 파일 목록
 */
export async function listFiles(
  bucket: StorageBucket,
  path: string = ''
): Promise<{ files: FileInfo[]; error?: string }> {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(path);
    
    if (error) {
      console.error('파일 목록 조회 중 오류 발생:', error);
      return { files: [], error: error.message };
    }
    
    return { files: data || [] };
  } catch (error) {
    console.error('파일 목록 조회 중 예외 발생:', error);
    return { files: [], error: '파일 목록 조회 중 오류가 발생했습니다.' };
  }
}

/**
 * 파일 삭제
 * @param paths 삭제할 파일 경로 배열
 * @param bucket 버킷 이름
 * @returns 삭제 성공 여부
 */
export async function deleteFiles(
  paths: string[],
  bucket: StorageBucket
): Promise<{ success: boolean; error?: string }> {
  try {
    // 경로가 비어있는 경우
    if (paths.length === 0) {
      return { success: false, error: '삭제할 파일 경로가 지정되지 않았습니다.' };
    }
    
    // 파일 삭제
    const { data, error } = await supabase.storage
      .from(bucket)
      .remove(paths);
    
    if (error) {
      console.error('파일 삭제 중 오류 발생:', error);
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    console.error('파일 삭제 중 예외 발생:', error);
    return { success: false, error: '파일 삭제 중 오류가 발생했습니다.' };
  }
}

/**
 * 단일 파일 삭제
 * @param path 삭제할 파일 경로
 * @param bucket 버킷 이름
 * @returns 삭제 성공 여부
 */
export async function deleteFile(
  path: string,
  bucket: StorageBucket
): Promise<{ success: boolean; error?: string }> {
  return deleteFiles([path], bucket);
}

/**
 * 파일 URL에서 경로 추출
 * @param fileUrl 파일 URL
 * @returns 파일 경로
 */
export function extractPathFromUrl(fileUrl: string): string {
  try {
    // URL 객체 생성
    const url = new URL(fileUrl);
    
    // 경로 추출
    const pathParts = url.pathname.split('/');
    
    // 버킷 이름 이후의 경로 반환
    const bucketIndex = pathParts.findIndex(part => 
      Object.values(StorageBucket).includes(part as StorageBucket)
    );
    
    if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
      return pathParts.slice(bucketIndex + 1).join('/');
    }
    
    // 경로를 찾을 수 없는 경우 원본 URL 반환
    return fileUrl;
  } catch (error) {
    console.error('파일 URL에서 경로 추출 중 오류 발생:', error);
    return fileUrl;
  }
}

/**
 * 파일 URL에서 버킷 추출
 * @param fileUrl 파일 URL
 * @returns 버킷 이름
 */
export function extractBucketFromUrl(fileUrl: string): StorageBucket | null {
  try {
    // URL 객체 생성
    const url = new URL(fileUrl);
    
    // 경로 추출
    const pathParts = url.pathname.split('/');
    
    // 버킷 이름 찾기
    for (const part of pathParts) {
      if (Object.values(StorageBucket).includes(part as StorageBucket)) {
        return part as StorageBucket;
      }
    }
    
    return null;
  } catch (error) {
    console.error('파일 URL에서 버킷 추출 중 오류 발생:', error);
    return null;
  }
}

/**
 * 파일 URL에서 삭제
 * @param fileUrl 파일 URL
 * @returns 삭제 성공 여부
 */
export async function deleteFileByUrl(
  fileUrl: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const bucket = extractBucketFromUrl(fileUrl);
    const path = extractPathFromUrl(fileUrl);
    
    if (!bucket) {
      return { success: false, error: '파일 URL에서 버킷을 추출할 수 없습니다.' };
    }
    
    return deleteFile(path, bucket);
  } catch (error) {
    console.error('파일 URL로 삭제 중 오류 발생:', error);
    return { success: false, error: '파일 삭제 중 오류가 발생했습니다.' };
  }
}

/**
 * 파일 이동/복사
 * @param fromPath 원본 경로
 * @param toPath 대상 경로
 * @param bucket 버킷 이름
 * @param isCopy 복사 여부 (기본값: false, 이동)
 * @returns 이동/복사 성공 여부
 */
export async function moveOrCopyFile(
  fromPath: string,
  toPath: string,
  bucket: StorageBucket,
  isCopy: boolean = false
): Promise<{ success: boolean; error?: string }> {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .move(fromPath, toPath);
    
    if (error) {
      console.error(`파일 ${isCopy ? '복사' : '이동'} 중 오류 발생:`, error);
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    console.error(`파일 ${isCopy ? '복사' : '이동'} 중 예외 발생:`, error);
    return { success: false, error: `파일 ${isCopy ? '복사' : '이동'} 중 오류가 발생했습니다.` };
  }
}
