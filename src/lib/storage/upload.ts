import { supabase } from '../supabase';
import { v4 as uuidv4 } from 'uuid';
import { StorageBucket, BUCKET_CONFIGS } from './buckets';

/**
 * 파일 업로드 결과 인터페이스
 */
export interface FileUploadResult {
  url: string;
  path: string;
  size: number;
  mimeType: string;
  error?: string;
}

/**
 * 파일 확장자 가져오기
 * @param filename 파일 이름
 * @returns 파일 확장자
 */
export function getFileExtension(filename: string): string {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
}

/**
 * 파일 크기 검증
 * @param file 파일 객체
 * @param maxSizeMB 최대 파일 크기 (MB)
 * @returns 유효성 검사 결과
 */
export function validateFileSize(file: File, maxSizeMB: number = 5): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

/**
 * 파일 타입 검증
 * @param file 파일 객체
 * @param allowedMimeTypes 허용된 MIME 타입 배열
 * @returns 유효성 검사 결과
 */
export function validateFileType(file: File, allowedMimeTypes: string[] = []): boolean {
  // 모든 타입 허용
  if (allowedMimeTypes.includes('*/*')) {
    return true;
  }
  
  return allowedMimeTypes.includes(file.type);
}

/**
 * 파일 유효성 검사
 * @param file 파일 객체
 * @param bucket 버킷 이름
 * @returns 유효성 검사 결과 및 오류 메시지
 */
export function validateFile(file: File, bucket: StorageBucket): { isValid: boolean; error?: string } {
  const config = BUCKET_CONFIGS[bucket];
  
  // 파일 크기 검증
  if (!validateFileSize(file, config.maxFileSize)) {
    return { 
      isValid: false, 
      error: `파일 크기는 ${config.maxFileSize}MB 이하여야 합니다.` 
    };
  }
  
  // 파일 타입 검증
  if (!validateFileType(file, config.allowedMimeTypes)) {
    return { 
      isValid: false, 
      error: `지원되지 않는 파일 형식입니다. 허용된 형식: ${config.allowedMimeTypes.join(', ')}` 
    };
  }
  
  return { isValid: true };
}

/**
 * 파일 업로드
 * @param file 파일 객체
 * @param bucket 버킷 이름
 * @param path 저장 경로 (기본값: '')
 * @param options 추가 옵션
 * @returns 업로드 결과
 */
export async function uploadFile(
  file: File,
  bucket: StorageBucket,
  path: string = '',
  options: {
    upsert?: boolean;
    cacheControl?: string;
    contentType?: string;
  } = {}
): Promise<FileUploadResult> {
  try {
    // 파일 유효성 검사
    const { isValid, error } = validateFile(file, bucket);
    if (!isValid) {
      return { 
        url: '', 
        path: '', 
        size: 0, 
        mimeType: file.type,
        error 
      };
    }
    
    // 파일 이름 생성 (중복 방지를 위해 UUID 사용)
    const extension = getFileExtension(file.name);
    const filename = `${uuidv4()}.${extension}`;
    const fullPath = path ? `${path}/${filename}` : filename;
    
    // 기본 옵션 설정
    const defaultOptions = {
      cacheControl: '3600',
      upsert: false,
      contentType: file.type,
    };
    
    // 옵션 병합
    const uploadOptions = { ...defaultOptions, ...options };
    
    // Supabase Storage에 파일 업로드
    const { data, error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(fullPath, file, uploadOptions);
    
    if (uploadError) {
      console.error('파일 업로드 오류:', uploadError);
      return { 
        url: '', 
        path: '', 
        size: 0, 
        mimeType: file.type,
        error: uploadError.message 
      };
    }
    
    // 업로드된 파일의 공개 URL 생성
    const { data: publicUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);
    
    return { 
      url: publicUrlData.publicUrl,
      path: data.path,
      size: file.size,
      mimeType: file.type
    };
  } catch (error) {
    console.error('파일 업로드 중 예외 발생:', error);
    return { 
      url: '', 
      path: '', 
      size: 0, 
      mimeType: file.type,
      error: '파일 업로드 중 오류가 발생했습니다.' 
    };
  }
}

/**
 * 이미지 파일 업로드
 * @param file 이미지 파일
 * @param bucket 버킷 이름
 * @param path 저장 경로
 * @returns 업로드 결과
 */
export async function uploadImage(
  file: File,
  bucket: StorageBucket = StorageBucket.LIFE_INFO,
  path: string = 'images'
): Promise<FileUploadResult> {
  // 이미지 파일인지 확인
  if (!file.type.startsWith('image/')) {
    return { 
      url: '', 
      path: '', 
      size: 0, 
      mimeType: file.type,
      error: '이미지 파일만 업로드 가능합니다.' 
    };
  }
  
  return uploadFile(file, bucket, path);
}

/**
 * 문서 파일 업로드
 * @param file 문서 파일
 * @param bucket 버킷 이름
 * @param path 저장 경로
 * @returns 업로드 결과
 */
export async function uploadDocument(
  file: File,
  bucket: StorageBucket = StorageBucket.DOCUMENTS,
  path: string = 'documents'
): Promise<FileUploadResult> {
  return uploadFile(file, bucket, path);
}

/**
 * 프로필 이미지 업로드
 * @param file 이미지 파일
 * @param userId 사용자 ID
 * @returns 업로드 결과
 */
export async function uploadProfileImage(
  file: File,
  userId: string
): Promise<FileUploadResult> {
  // 이미지 파일인지 확인
  if (!file.type.startsWith('image/')) {
    return { 
      url: '', 
      path: '', 
      size: 0, 
      mimeType: file.type,
      error: '이미지 파일만 업로드 가능합니다.' 
    };
  }
  
  // 사용자 ID 기반 경로 생성
  const path = `users/${userId}`;
  
  return uploadFile(file, StorageBucket.PROFILES, path);
}
