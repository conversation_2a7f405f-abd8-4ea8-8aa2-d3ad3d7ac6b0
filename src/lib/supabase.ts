import { createClient } from '@supabase/supabase-js';

// Supabase 클라이언트 생성
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 환경 변수가 설정되지 않은 경우 에러 로깅
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase URL 또는 Anon Key가 설정되지 않았습니다.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * 파일 업로드 함수
 * @param file 업로드할 파일
 * @param bucket 버킷 이름
 * @param path 파일 경로
 * @returns 업로드된 파일 URL
 */
export async function uploadFile(
  file: File,
  bucket: string = 'profiles',
  path: string = ''
): Promise<string> {
  try {
    // 파일 확장자 추출
    const fileExt = file.name.split('.').pop();
    
    // 고유한 파일 이름 생성
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    
    // 파일 경로 생성
    const filePath = path ? `${path}/${fileName}` : fileName;
    
    // 파일 업로드
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });
    
    if (error) {
      throw error;
    }
    
    // 파일 URL 생성
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);
    
    return urlData.publicUrl;
  } catch (error) {
    console.error('파일 업로드 중 오류 발생:', error);
    throw error;
  }
}

/**
 * 프로필 이미지 업로드 함수
 * @param file 업로드할 이미지 파일
 * @param userId 사용자 ID
 * @returns 업로드된 이미지 URL
 */
export async function uploadProfileImage(file: File, userId: string): Promise<string> {
  try {
    // 파일 타입 검증
    if (!file.type.startsWith('image/')) {
      throw new Error('이미지 파일만 업로드 가능합니다.');
    }
    
    // 파일 크기 검증 (2MB 제한)
    if (file.size > 2 * 1024 * 1024) {
      throw new Error('이미지 크기는 최대 2MB까지 가능합니다.');
    }
    
    // 사용자 ID 기반 경로 생성
    const path = `users/${userId}`;
    
    // 파일 업로드
    return await uploadFile(file, 'profiles', path);
  } catch (error) {
    console.error('프로필 이미지 업로드 중 오류 발생:', error);
    throw error;
  }
}

/**
 * 파일 삭제 함수
 * @param filePath 삭제할 파일 경로
 * @param bucket 버킷 이름
 * @returns 삭제 성공 여부
 */
export async function deleteFile(
  filePath: string,
  bucket: string = 'profiles'
): Promise<boolean> {
  try {
    // 파일 URL에서 경로 추출
    const path = filePath.includes('/')
      ? filePath.split('/').slice(-2).join('/')
      : filePath;
    
    // 파일 삭제
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);
    
    if (error) {
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('파일 삭제 중 오류 발생:', error);
    return false;
  }
}
