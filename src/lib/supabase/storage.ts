import { createClient } from '@supabase/supabase-js';

// Supabase 클라이언트 생성
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Supabase Storage에 파일 업로드
 * @param bucket 버킷 이름
 * @param path 파일 경로
 * @param file 업로드할 파일
 * @returns 업로드된 파일의 URL 또는 에러
 */
export async function uploadToSupabase(bucket: string, path: string, file: File) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      throw error;
    }

    // 업로드된 파일의 공개 URL 가져오기
    const { data: urlData } = supabase.storage.from(bucket).getPublicUrl(data.path);

    return { url: urlData.publicUrl, error: null };
  } catch (error) {
    console.error('Supabase Storage 업로드 오류:', error);
    return { url: null, error };
  }
}

/**
 * Supabase Storage에서 파일 삭제
 * @param bucket 버킷 이름
 * @param path 파일 경로
 * @returns 성공 여부 또는 에러
 */
export async function deleteFromSupabase(bucket: string, path: string) {
  try {
    const { data, error } = await supabase.storage.from(bucket).remove([path]);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Supabase Storage 삭제 오류:', error);
    return { success: false, error };
  }
}

/**
 * Supabase Storage에서 파일 목록 가져오기
 * @param bucket 버킷 이름
 * @param path 폴더 경로 (선택사항)
 * @returns 파일 목록 또는 에러
 */
export async function listFilesFromSupabase(bucket: string, path?: string) {
  try {
    const { data, error } = await supabase.storage.from(bucket).list(path || '');

    if (error) {
      throw error;
    }

    return { files: data, error: null };
  } catch (error) {
    console.error('Supabase Storage 목록 조회 오류:', error);
    return { files: null, error };
  }
}
