import { SWRConfiguration } from "swr";
import { fetcher } from "./fetcher";
import { createLocalStorageProvider } from "./provider";

/**
 * SWR 글로벌 설정
 *
 * 개발 환경과 프로덕션 환경에 따라 다른 설정을 적용합니다.
 * 성능 최적화를 위한 설정이 포함되어 있습니다.
 */
export const swrConfig: SWRConfiguration = {
  fetcher,
  // 기본 재시도 설정: 최대 3번 재시도, 지수 백오프 방식으로 대기 시간 증가
  errorRetryCount: 3,
  // 포커스 시 자동 재검증 (사용자가 탭/창으로 돌아올 때)
  // 성능 최적화: 포커스 시 재검증 빈도 제한 (3초 내에 여러 번 포커스 변경 시 한 번만 재검증)
  revalidateOnFocus: true,
  focusThrottleInterval: 3000,
  // 네트워크 재연결 시 자동 재검증
  revalidateOnReconnect: true,
  // 마운트 시 자동 재검증
  revalidateOnMount: true,
  // 기본 캐시 시간: 10분 (밀리초 단위) - 성능 최적화를 위해 증가
  dedupingInterval: 10 * 60 * 1000,
  // 개발 환경에서는 더 짧은 캐시 시간 적용
  ...(process.env.NODE_ENV === "development" && {
    dedupingInterval: 1000, // 개발 환경에서는 1초
    errorRetryCount: 1, // 개발 환경에서는 1번만 재시도
  }),
  // 오류 발생 시 이전 데이터 유지
  keepPreviousData: true,
  // 로딩 상태 표시 지연 시간 (밀리초 단위)
  // 이 시간 내에 데이터가 로드되면 로딩 상태가 표시되지 않음
  // 성능 최적화: 로딩 상태 표시 지연 시간 감소 (더 빠른 UI 피드백)
  loadingTimeout: 1000,
  // 오류 발생 시 콘솔에 로그 출력
  onError: (error, key) => {
    if (process.env.NODE_ENV !== "production") {
      console.error(`SWR 오류 (${key}):`, error);
    }
  },
  // 캐시 제공자 설정: localStorage를 사용하여 페이지 새로고침 후에도 캐시 유지
  // 성능 최적화: 페이지 로드 시 네트워크 요청 감소
  provider: createLocalStorageProvider(),
  // 성능 최적화: 병렬 요청 수 제한 (동시에 최대 5개의 요청만 처리)
  // 이는 브라우저의 연결 제한을 고려한 설정입니다
  parallel: 5,
};
