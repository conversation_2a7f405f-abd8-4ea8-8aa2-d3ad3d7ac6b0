/**
 * SWR에서 사용할 기본 fetcher 함수
 * 
 * @param url 데이터를 가져올 URL
 * @returns 응답 데이터를 JSON으로 파싱한 결과
 * @throws 네트워크 오류 또는 응답이 ok가 아닌 경우 에러 발생
 */
export const fetcher = async <T>(url: string): Promise<T> => {
  const response = await fetch(url);
  
  // 응답이 성공적이지 않은 경우 에러 발생
  if (!response.ok) {
    const error = new Error('API 요청 중 오류가 발생했습니다') as Error & { status?: number, info?: any };
    error.status = response.status;
    
    try {
      error.info = await response.json();
    } catch (e) {
      error.info = await response.text();
    }
    
    throw error;
  }
  
  return response.json();
};

/**
 * 인증이 필요한 API 요청을 위한 fetcher 함수
 * 
 * @param url 데이터를 가져올 URL
 * @returns 응답 데이터를 JSON으로 파싱한 결과
 * @throws 네트워크 오류 또는 응답이 ok가 아닌 경우 에러 발생
 */
export const authFetcher = async <T>(url: string): Promise<T> => {
  const response = await fetch(url, {
    credentials: 'include', // 쿠키 포함
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  // 응답이 성공적이지 않은 경우 에러 발생
  if (!response.ok) {
    const error = new Error('API 요청 중 오류가 발생했습니다') as Error & { status?: number, info?: any };
    error.status = response.status;
    
    try {
      error.info = await response.json();
    } catch (e) {
      error.info = await response.text();
    }
    
    throw error;
  }
  
  return response.json();
};

/**
 * POST 요청을 위한 fetcher 함수
 * 
 * @param url 데이터를 전송할 URL
 * @param data 전송할 데이터 객체
 * @returns 응답 데이터를 JSON으로 파싱한 결과
 * @throws 네트워크 오류 또는 응답이 ok가 아닌 경우 에러 발생
 */
export const postFetcher = async <T, D>(url: string, { arg }: { arg: D }): Promise<T> => {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(arg),
    credentials: 'include',
  });
  
  // 응답이 성공적이지 않은 경우 에러 발생
  if (!response.ok) {
    const error = new Error('API 요청 중 오류가 발생했습니다') as Error & { status?: number, info?: any };
    error.status = response.status;
    
    try {
      error.info = await response.json();
    } catch (e) {
      error.info = await response.text();
    }
    
    throw error;
  }
  
  return response.json();
};
