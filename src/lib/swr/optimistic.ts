import { KeyedMutator } from "swr";

/**
 * 배열에 새 항목을 추가하는 낙관적 업데이트 함수
 *
 * @param mutate SWR mutate 함수
 * @param newItem 추가할 새 항목
 * @param prepend 배열의 앞에 추가할지 여부 (기본값: true)
 * @param revalidateAfter 낙관적 업데이트 후 서버에서 데이터를 다시 가져올지 여부 (기본값: true)
 * @param revalidateDelay 서버 재검증 전 지연 시간 (밀리초, 기본값: 1000)
 */
export async function optimisticAddItem<T>(
  mutate: KeyedMutator<{ items: T[]; [key: string]: any }>,
  newItem: T,
  prepend: boolean = true,
  revalidateAfter: boolean = true,
  revalidateDelay: number = 1000
): Promise<void> {
  // 낙관적 업데이트 수행
  await mutate(
    (currentData) => {
      if (!currentData) return currentData;

      const items = [...currentData.items];
      if (prepend) {
        items.unshift(newItem);
      } else {
        items.push(newItem);
      }

      return {
        ...currentData,
        items,
      };
    },
    { revalidate: false }
  );

  // 지연된 서버 재검증 (필요한 경우)
  if (revalidateAfter) {
    setTimeout(() => {
      mutate();
    }, revalidateDelay);
  }
}

/**
 * 배열에서 항목을 업데이트하는 낙관적 업데이트 함수
 *
 * @param mutate SWR mutate 함수
 * @param updatedItem 업데이트된 항목
 * @param idField 항목의 ID 필드 이름 (기본값: 'id')
 * @param revalidateAfter 낙관적 업데이트 후 서버에서 데이터를 다시 가져올지 여부 (기본값: true)
 * @param revalidateDelay 서버 재검증 전 지연 시간 (밀리초, 기본값: 1000)
 */
export async function optimisticUpdateItem<T extends { [key: string]: any }>(
  mutate: KeyedMutator<{ items: T[]; [key: string]: any }>,
  updatedItem: T,
  idField: string = "id",
  revalidateAfter: boolean = true,
  revalidateDelay: number = 1000
): Promise<void> {
  // 낙관적 업데이트 수행
  await mutate(
    (currentData) => {
      if (!currentData) return currentData;

      const items = currentData.items.map((item) =>
        item[idField] === updatedItem[idField] ? updatedItem : item
      );

      return {
        ...currentData,
        items,
      };
    },
    { revalidate: false }
  );

  // 지연된 서버 재검증 (필요한 경우)
  if (revalidateAfter) {
    setTimeout(() => {
      mutate();
    }, revalidateDelay);
  }
}

/**
 * 배열에서 항목을 삭제하는 낙관적 업데이트 함수
 *
 * @param mutate SWR mutate 함수
 * @param itemId 삭제할 항목의 ID
 * @param idField 항목의 ID 필드 이름 (기본값: 'id')
 * @param revalidateAfter 낙관적 업데이트 후 서버에서 데이터를 다시 가져올지 여부 (기본값: true)
 * @param revalidateDelay 서버 재검증 전 지연 시간 (밀리초, 기본값: 1000)
 */
export async function optimisticRemoveItem<T extends { [key: string]: any }>(
  mutate: KeyedMutator<{ items: T[]; [key: string]: any }>,
  itemId: string | number,
  idField: string = "id",
  revalidateAfter: boolean = true,
  revalidateDelay: number = 1000
): Promise<void> {
  // 낙관적 업데이트 수행
  await mutate(
    (currentData) => {
      if (!currentData) return currentData;

      const items = currentData.items.filter(
        (item) => item[idField] !== itemId
      );

      return {
        ...currentData,
        items,
      };
    },
    { revalidate: false }
  );

  // 지연된 서버 재검증 (필요한 경우)
  if (revalidateAfter) {
    setTimeout(() => {
      mutate();
    }, revalidateDelay);
  }
}

/**
 * 단일 항목을 업데이트하는 낙관적 업데이트 함수
 *
 * @param mutate SWR mutate 함수
 * @param updatedData 업데이트된 데이터
 * @param revalidateAfter 낙관적 업데이트 후 서버에서 데이터를 다시 가져올지 여부 (기본값: true)
 * @param revalidateDelay 서버 재검증 전 지연 시간 (밀리초, 기본값: 1000)
 */
export async function optimisticUpdateData<T>(
  mutate: KeyedMutator<T>,
  updatedData: Partial<T>,
  revalidateAfter: boolean = true,
  revalidateDelay: number = 1000
): Promise<void> {
  // 낙관적 업데이트 수행
  await mutate(
    (currentData) => {
      if (!currentData) return currentData as T;

      return {
        ...currentData,
        ...updatedData,
      };
    },
    { revalidate: false }
  );

  // 지연된 서버 재검증 (필요한 경우)
  if (revalidateAfter) {
    setTimeout(() => {
      mutate();
    }, revalidateDelay);
  }
}

/**
 * 좋아요/북마크 등 토글 상태를 업데이트하는 낙관적 업데이트 함수
 *
 * @param mutate SWR mutate 함수
 * @param field 토글할 필드 이름
 * @param countField 카운트 필드 이름 (예: '_count.likes')
 * @param revalidateAfter 낙관적 업데이트 후 서버에서 데이터를 다시 가져올지 여부 (기본값: true)
 * @param revalidateDelay 서버 재검증 전 지연 시간 (밀리초, 기본값: 1000)
 */
export async function optimisticToggle<T extends { [key: string]: any }>(
  mutate: KeyedMutator<T>,
  field: string,
  countField?: string,
  revalidateAfter: boolean = true,
  revalidateDelay: number = 1000
): Promise<void> {
  // 낙관적 업데이트 수행
  await mutate(
    (currentData) => {
      if (!currentData) return currentData;

      const newData = { ...currentData };
      const currentValue = newData[field];
      newData[field] = !currentValue;

      // 카운트 필드가 있으면 업데이트
      if (countField) {
        const parts = countField.split(".");
        let target = newData;

        // 중첩 객체 접근 (예: _count.likes)
        for (let i = 0; i < parts.length - 1; i++) {
          if (!target[parts[i]]) {
            target[parts[i]] = {};
          }
          target = target[parts[i]];
        }

        const lastPart = parts[parts.length - 1];
        if (target[lastPart] === undefined) {
          target[lastPart] = 0;
        }

        // 토글 상태에 따라 카운트 증감
        target[lastPart] += currentValue ? -1 : 1;
      }

      return newData;
    },
    { revalidate: false }
  );

  // 지연된 서버 재검증 (필요한 경우)
  if (revalidateAfter) {
    setTimeout(() => {
      mutate();
    }, revalidateDelay);
  }
}
