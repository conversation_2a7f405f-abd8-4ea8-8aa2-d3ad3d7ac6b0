/**
 * SWR 캐시 제공자 구현
 * 
 * localStorage를 사용하여 SWR 캐시를 유지하는 제공자를 구현합니다.
 * 페이지 새로고침 후에도 캐시가 유지되어 성능이 향상됩니다.
 */

import { Cache, SWRConfiguration } from 'swr';

// 캐시 키 접두사
const CACHE_KEY_PREFIX = 'swr-cache-';

// 캐시 만료 시간 (24시간)
const CACHE_TTL = 24 * 60 * 60 * 1000;

// 캐시 항목 인터페이스
interface CacheItem {
  data: any;
  timestamp: number;
}

/**
 * localStorage를 사용하는 SWR 캐시 제공자 생성
 * 
 * @returns SWR 캐시 제공자 함수
 */
export function createLocalStorageProvider(): SWRConfiguration['provider'] {
  // 브라우저 환경인지 확인
  const isClient = typeof window !== 'undefined';
  
  // 캐시 맵 생성
  return () => {
    // 메모리 캐시 맵
    const map = new Map<string, any>();
    
    // 브라우저 환경이 아니면 메모리 캐시만 사용
    if (!isClient) {
      return map;
    }
    
    // 페이지 로드 시 localStorage에서 캐시 복원
    try {
      const keys = Object.keys(localStorage);
      const swrKeys = keys.filter(key => key.startsWith(CACHE_KEY_PREFIX));
      
      const now = Date.now();
      
      // 각 캐시 항목 처리
      swrKeys.forEach(key => {
        const value = localStorage.getItem(key);
        if (!value) return;
        
        try {
          const cacheItem: CacheItem = JSON.parse(value);
          
          // 만료된 캐시 항목 제거
          if (now - cacheItem.timestamp > CACHE_TTL) {
            localStorage.removeItem(key);
            return;
          }
          
          // 유효한 캐시 항목 복원
          const k = key.slice(CACHE_KEY_PREFIX.length);
          map.set(k, cacheItem.data);
        } catch (e) {
          // 잘못된 JSON 형식의 캐시 항목 제거
          localStorage.removeItem(key);
        }
      });
    } catch (e) {
      // localStorage 접근 오류 (예: 개인정보 보호 모드)
      console.warn('SWR 캐시를 복원하는 중 오류가 발생했습니다:', e);
    }
    
    // 캐시 맵 래퍼 생성
    const cache: Cache = {
      get: key => map.get(key),
      set: (key, value) => {
        map.set(key, value);
        
        // localStorage에 캐시 저장
        try {
          const cacheItem: CacheItem = {
            data: value,
            timestamp: Date.now(),
          };
          
          localStorage.setItem(
            `${CACHE_KEY_PREFIX}${key}`,
            JSON.stringify(cacheItem)
          );
        } catch (e) {
          // localStorage 저장 오류 (예: 용량 초과)
          console.warn('SWR 캐시를 저장하는 중 오류가 발생했습니다:', e);
        }
      },
      delete: key => {
        map.delete(key);
        
        // localStorage에서 캐시 삭제
        try {
          localStorage.removeItem(`${CACHE_KEY_PREFIX}${key}`);
        } catch (e) {
          console.warn('SWR 캐시를 삭제하는 중 오류가 발생했습니다:', e);
        }
      },
      keys: () => Array.from(map.keys()),
    };
    
    return cache;
  };
}

/**
 * 모든 SWR 캐시 항목 삭제
 */
export function clearSWRCache(): void {
  // 브라우저 환경인지 확인
  if (typeof window === 'undefined') return;
  
  try {
    const keys = Object.keys(localStorage);
    const swrKeys = keys.filter(key => key.startsWith(CACHE_KEY_PREFIX));
    
    // 각 캐시 항목 삭제
    swrKeys.forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (e) {
    console.warn('SWR 캐시를 삭제하는 중 오류가 발생했습니다:', e);
  }
}
