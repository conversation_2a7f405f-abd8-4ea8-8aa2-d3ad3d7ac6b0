import { cache } from 'swr';
import { mutate } from 'swr';

/**
 * 특정 키 패턴과 일치하는 모든 SWR 캐시를 재검증합니다.
 * 
 * @param keyPattern 재검증할 키 패턴 (정규식 또는 문자열)
 * @param options 재검증 옵션
 */
export function revalidateByPattern(
  keyPattern: RegExp | string,
  options?: { revalidate?: boolean }
): Promise<unknown[]> {
  const pattern = keyPattern instanceof RegExp ? keyPattern : new RegExp(keyPattern);
  const cacheKeys = Array.from(cache.keys());
  const matchingKeys = cacheKeys.filter(key => pattern.test(key));
  
  return Promise.all(
    matchingKeys.map(key => mutate(key, undefined, options))
  );
}

/**
 * 특정 리소스 타입과 관련된 모든 SWR 캐시를 재검증합니다.
 * 
 * @param resourceType 리소스 타입 (예: 'posts', 'comments', 'users')
 * @param options 재검증 옵션
 */
export function revalidateResourceType(
  resourceType: string,
  options?: { revalidate?: boolean }
): Promise<unknown[]> {
  // Server Actions 기반으로 변경: API 경로 대신 SWR 키 패턴 사용
  return revalidateByPattern(`${resourceType}`, options);
}

/**
 * 특정 리소스 ID와 관련된 모든 SWR 캐시를 재검증합니다.
 * 
 * @param resourceType 리소스 타입 (예: 'posts', 'comments', 'users')
 * @param id 리소스 ID
 * @param options 재검증 옵션
 */
export function revalidateResource(
  resourceType: string,
  id: string,
  options?: { revalidate?: boolean }
): Promise<unknown[]> {
  // Server Actions 기반으로 변경: API 경로 대신 SWR 키 패턴 사용
  return revalidateByPattern(`${resourceType}.*${id}`, options);
}

/**
 * 사용자와 관련된 모든 SWR 캐시를 재검증합니다.
 * 
 * @param userId 사용자 ID
 * @param options 재검증 옵션
 */
export function revalidateUserData(
  userId: string,
  options?: { revalidate?: boolean }
): Promise<unknown[]> {
  // Server Actions 기반으로 변경: SWR 키 패턴 사용
  const userPromise = mutate(['user', userId], undefined, options);
  
  // 사용자가 작성한 게시글 데이터 재검증
  const postsPromise = revalidateByPattern(`user-posts.*${userId}`, options);
  
  // 사용자가 작성한 댓글 데이터 재검증
  const commentsPromise = revalidateByPattern(`user-comments.*${userId}`, options);
  
  return Promise.all([userPromise, postsPromise, commentsPromise]);
}

/**
 * 모든 SWR 캐시를 재검증합니다.
 * 
 * @param options 재검증 옵션
 */
export function revalidateAll(
  options?: { revalidate?: boolean }
): Promise<unknown[]> {
  const cacheKeys = Array.from(cache.keys());
  return Promise.all(
    cacheKeys.map(key => mutate(key, undefined, options))
  );
}

/**
 * 특정 이벤트 발생 시 관련 데이터를 재검증하는 함수
 * 
 * @param event 이벤트 타입
 * @param payload 이벤트 페이로드
 */
export async function revalidateOnEvent(
  event: string,
  payload: any
): Promise<void> {
  switch (event) {
    case 'post_created':
    case 'post_updated':
    case 'post_deleted':
      // 게시글 목록 및 특정 게시글 재검증
      await revalidateResourceType('posts');
      if (payload.id) {
        await revalidateResource('posts', payload.id);
      }
      break;
      
    case 'comment_created':
    case 'comment_updated':
    case 'comment_deleted':
      // 댓글 목록 및 특정 댓글 재검증
      await revalidateResourceType('comments');
      if (payload.id) {
        await revalidateResource('comments', payload.id);
      }
      // 관련 게시글의 댓글 수 업데이트
      if (payload.postId) {
        await revalidateResource('posts', payload.postId);
      }
      break;
      
    case 'user_updated':
      // 사용자 데이터 재검증
      if (payload.id) {
        await revalidateUserData(payload.id);
      }
      break;
      
    case 'auth_signout':
      // 로그아웃 시 모든 데이터 재검증
      await revalidateAll();
      break;
      
    default:
      console.warn(`Unknown event type: ${event}`);
  }
}
