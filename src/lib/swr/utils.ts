/**
 * SWR 유틸리티 함수
 * 
 * SWR 사용을 최적화하기 위한 유틸리티 함수들을 제공합니다.
 */

import { SWRConfiguration } from 'swr';

/**
 * 페이지 프리페칭을 위한 설정 생성
 * 
 * @param options 기본 SWR 옵션
 * @returns 프리페칭을 위한 SWR 설정
 */
export function createPrefetchConfig(options?: SWRConfiguration): SWRConfiguration {
  return {
    // 프리페칭은 백그라운드에서 수행되므로 로딩 상태를 표시하지 않음
    loadingTimeout: 0,
    // 프리페칭은 포커스 시 재검증하지 않음
    revalidateOnFocus: false,
    // 프리페칭은 마운트 시 재검증하지 않음 (이미 데이터가 있으므로)
    revalidateOnMount: false,
    // 프리페칭은 네트워크 재연결 시 재검증하지 않음
    revalidateOnReconnect: false,
    // 프리페칭은 오류 발생 시 재시도하지 않음
    errorRetryCount: 0,
    // 사용자 정의 옵션 병합
    ...options,
  };
}

/**
 * 무한 스크롤을 위한 설정 생성
 * 
 * @param options 기본 SWR 옵션
 * @returns 무한 스크롤을 위한 SWR 설정
 */
export function createInfiniteConfig(options?: SWRConfiguration): SWRConfiguration {
  return {
    // 첫 페이지만 재검증 (성능 최적화)
    revalidateFirstPage: true,
    // 포커스 시 재검증하지 않음 (성능 최적화)
    revalidateOnFocus: false,
    // 이전 데이터 유지 (UX 개선)
    keepPreviousData: true,
    // 사용자 정의 옵션 병합
    ...options,
  };
}

/**
 * 실시간 데이터를 위한 설정 생성
 * 
 * @param intervalMs 폴링 간격 (밀리초)
 * @param options 기본 SWR 옵션
 * @returns 실시간 데이터를 위한 SWR 설정
 */
export function createRealtimeConfig(
  intervalMs: number = 3000,
  options?: SWRConfiguration
): SWRConfiguration {
  return {
    // 주기적으로 데이터 새로고침
    refreshInterval: intervalMs,
    // 포커스 시 재검증
    revalidateOnFocus: true,
    // 네트워크 재연결 시 재검증
    revalidateOnReconnect: true,
    // 이전 데이터 유지 (UX 개선)
    keepPreviousData: true,
    // 사용자 정의 옵션 병합
    ...options,
  };
}

/**
 * 정적 데이터를 위한 설정 생성 (자주 변경되지 않는 데이터)
 * 
 * @param cacheDurationMs 캐시 유지 시간 (밀리초)
 * @param options 기본 SWR 옵션
 * @returns 정적 데이터를 위한 SWR 설정
 */
export function createStaticConfig(
  cacheDurationMs: number = 24 * 60 * 60 * 1000, // 24시간
  options?: SWRConfiguration
): SWRConfiguration {
  return {
    // 긴 캐시 시간 설정
    dedupingInterval: cacheDurationMs,
    // 포커스 시 재검증하지 않음
    revalidateOnFocus: false,
    // 네트워크 재연결 시 재검증하지 않음
    revalidateOnReconnect: false,
    // 사용자 정의 옵션 병합
    ...options,
  };
}

/**
 * 키 생성 함수 (캐시 키 생성 헬퍼)
 * 
 * @param baseKey 기본 키
 * @param params 파라미터 객체
 * @returns 캐시 키
 */
export function createKey(baseKey: string, params?: Record<string, any>): string {
  if (!params) return baseKey;
  
  // 파라미터가 있는 경우 키에 추가
  const queryParams = new URLSearchParams();
  
  // 파라미터 정렬 (일관된 키 생성을 위해)
  const sortedParams = Object.keys(params).sort().reduce(
    (obj, key) => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        obj[key] = params[key];
      }
      return obj;
    },
    {} as Record<string, any>
  );
  
  // 파라미터 추가
  Object.entries(sortedParams).forEach(([key, value]) => {
    queryParams.append(key, String(value));
  });
  
  const queryString = queryParams.toString();
  return queryString ? `${baseKey}?${queryString}` : baseKey;
}
