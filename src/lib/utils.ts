import { type ClassValue, clsx } from 'clsx';
import { formatDistanceToNow } from 'date-fns';
import { ko } from 'date-fns/locale';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 주어진 날짜를 기준으로 상대 시간을 계산하여 반환합니다.
 * 예: "5분 전", "어제", "3일 전"
 * @param date - 기준 날짜 (Date 객체 또는 문자열)
 * @returns 포맷된 상대 시간 문자열
 */
export function formatRelativeTime(date: Date | string): string {
  return formatDistanceToNow(new Date(date), {
    addSuffix: true,
    locale: ko,
  });
}
