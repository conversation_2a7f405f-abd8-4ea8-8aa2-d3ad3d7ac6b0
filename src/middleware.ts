import { getToken } from 'next-auth/jwt';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export const runtime = 'experimental-edge';

// 인증이 필요한 경로 목록
// GET /api/users는 인증 없이 접근 가능하도록 변경
const protectedPaths = ['/protected', '/community/post/new'];

// 인증 페이지 경로
const authRoutes = ['/auth/signin', '/auth/login'];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 인증 페이지는 미들웨어 처리 건너뛰기
  if (authRoutes.some((route) => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // 보호된 경로인지 확인
  const isProtectedPath = protectedPaths.some((path) =>
    pathname.startsWith(path)
  );

  if (isProtectedPath) {
    // JWT 토큰 확인 (Edge 환경에서 호환되는 방식)
    const token = await getToken({
      req: request,
      secret: process.env.AUTH_SECRET,
    });

    // 토큰이 없는 경우 로그인 페이지로 리다이렉트
    if (!token) {
      const url = new URL('/auth/login', request.url);
      url.searchParams.set('callbackUrl', encodeURI(request.url));
      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next();
}

// 미들웨어가 적용될 경로 지정
export const config = {
  matcher: [
    // 보호된 페이지 경로
    '/protected/:path*',
    // 게시글 작성 페이지
    '/community/post/new',
  ],
};
