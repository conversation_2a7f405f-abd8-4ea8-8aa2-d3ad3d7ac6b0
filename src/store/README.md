# Zustand 상태 관리 가이드

이 문서는 Sodamm 프로젝트의 Zustand 기반 상태 관리 시스템에 대한 가이드입니다.

## 개요

Sodamm 프로젝트는 다음과 같은 상태 관리 도구를 사용합니다:

- **Zustand**: 전역 상태 관리
- **SWR**: 데이터 페칭 및 캐싱
- **next-auth**: 인증 상태 관리

이 중 Zustand는 UI 상태, 사용자 상태, 데이터 캐싱 등 다양한 영역의 상태를 관리하는 데 사용됩니다.

## 스토어 구조

### 1. 스토어 타입 (`types.ts`)

모든 스토어에서 공통으로 사용하는 타입 정의를 포함합니다:

```typescript
// 스토어 슬라이스 타입
export type StoreSlice<T> = StateCreator<T, [], [], T>;

// 스토어 리셋 기능
export interface WithReset {
  reset: () => void;
}

// 비동기 데이터 상태
export interface AsyncState<T> extends WithLoading, WithError {
  data: T | null;
  setData: (data: T | null) => void;
}
```

### 2. UI 스토어 (`ui-store.ts`)

UI 관련 상태를 관리합니다:

- 테마 설정
- 모달 상태
- 토스트 메시지
- 사이드바 상태
- 로딩 상태

```typescript
// 사용 예시
import { useTheme, useSetTheme, useModal, useOpenModal } from '@/store';

// 테마 변경
const setTheme = useSetTheme();
setTheme('dark');

// 모달 열기
const openModal = useOpenModal();
openModal('confirm', { title: '확인', message: '계속 진행하시겠습니까?' });
```

### 3. 사용자 스토어 (`user-store.ts`)

사용자 관련 상태를 관리합니다:

- 세션 정보
- 사용자 설정
- 인증 상태

```typescript
// 사용 예시
import { useUserSession, useUserPreferences, useUpdateUserPreferences } from '@/store';

// 사용자 세션 정보 가져오기
const session = useUserSession();

// 사용자 설정 업데이트
const updatePreferences = useUpdateUserPreferences();
updatePreferences({ notificationsEnabled: false });
```

### 4. 커뮤니티 스토어 (`community-store.ts`)

커뮤니티 관련 상태를 관리합니다:

- 게시물 목록 새로고침 상태
- 필터 상태
- 최근 본 게시물
- 임시 저장된 게시물 내용

```typescript
// 사용 예시
import { useTriggerPostListRefresh, useCommunityFilters, useUpdateCommunityFilters } from '@/store';

// 게시물 목록 새로고침 트리거
const triggerRefresh = useTriggerPostListRefresh();
triggerRefresh();

// 필터 업데이트
const updateFilters = useUpdateCommunityFilters();
updateFilters({ country: 'KR', sortBy: 'latest' });
```

### 5. 데이터 스토어 (`data-store.ts`)

데이터 캐싱 및 상태를 관리합니다:

- 캐시 저장소
- 데이터 로딩 상태
- 데이터 에러 상태

```typescript
// 사용 예시
import { useSetCache, useGetCache, useInvalidateCache } from '@/store';

// 데이터 캐싱
const setCache = useSetCache();
setCache('posts', postsData, 5 * 60 * 1000); // 5분 TTL

// 캐시된 데이터 가져오기
const getCache = useGetCache();
const cachedPosts = getCache('posts');

// 캐시 무효화
const invalidateCache = useInvalidateCache();
invalidateCache('posts');
```

## SWR과 Zustand 통합

`use-zustand-swr.ts` 파일에서 SWR과 Zustand를 통합하여 사용할 수 있는 커스텀 훅을 제공합니다:

```typescript
// 사용 예시
import { useZustandSWR } from '@/hooks/use-zustand-swr';

const { data, error, isLoading } = useZustandSWR(
  'posts',
  () => fetchPosts(),
  { dedupingInterval: 5000 }
);
```

## 스토어 프로바이더

`StoreProvider` 컴포넌트는 모든 Zustand 스토어를 초기화하고 관리합니다:

```tsx
// src/app/providers.tsx에서 사용
<SessionProvider>
  <StoreProvider>
    {children}
  </StoreProvider>
</SessionProvider>
```

## 모범 사례

1. **선택자 함수 사용**: 컴포넌트 리렌더링을 최소화하기 위해 선택자 함수를 사용하세요.
   ```typescript
   // 좋은 예
   const theme = useTheme();
   
   // 나쁜 예
   const theme = useUIStore(state => state.theme);
   ```

2. **상태 분리**: 관련 있는 상태끼리 그룹화하고, 독립적인 상태는 분리하세요.

3. **불변성 유지**: 상태를 업데이트할 때 항상 불변성을 유지하세요.
   ```typescript
   // 좋은 예
   set((state) => ({ filters: { ...state.filters, country: 'KR' } }));
   
   // 나쁜 예
   set((state) => {
     state.filters.country = 'KR';
     return { filters: state.filters };
   });
   ```

4. **지연 초기화**: 무거운 계산이 필요한 상태는 지연 초기화를 사용하세요.

5. **미들웨어 활용**: `devtools`, `persist` 등의 미들웨어를 활용하세요.

## 문제 해결

- **상태 업데이트가 반영되지 않는 경우**: 선택자 함수가 올바르게 구현되었는지 확인하세요.
- **불필요한 리렌더링**: 선택자 함수가 너무 많은 상태를 구독하고 있는지 확인하세요.
- **상태 초기화 문제**: `reset` 함수를 호출하여 상태를 초기화할 수 있습니다.
