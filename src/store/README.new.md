# 상태 관리 아키텍처

이 문서는 애플리케이션의 상태 관리 아키텍처에 대한 설명입니다.

## 목차

1. [개요](#개요)
2. [디렉토리 구조](#디렉토리-구조)
3. [스토어 구성](#스토어-구성)
4. [스토어 사용 방법](#스토어-사용-방법)
5. [모범 사례](#모범-사례)

## 개요

이 애플리케이션은 [Zustand](https://github.com/pmndrs/zustand)를 사용하여 상태 관리를 구현합니다. Zustand는 간단하고 직관적인 API를 제공하며, React의 Context API보다 성능이 우수합니다.

주요 특징:
- 도메인 중심 스토어 구조
- 팩토리 패턴을 통한 일관된 스토어 생성
- 선택자 함수를 통한 성능 최적화
- 불변성 관리를 위한 Immer 통합
- 개발 도구 및 지속성 미들웨어 지원

## 디렉토리 구조

```
src/store/
├── core/                 # 코어 유틸리티 및 타입
│   ├── create-store.ts   # 스토어 팩토리 함수
│   └── types.ts          # 공통 타입 정의
├── ui/                   # UI 관련 상태
│   ├── ui-store.ts       # UI 스토어 구현
│   └── types.ts          # UI 타입 정의
├── user/                 # 사용자 관련 상태
│   ├── user-store.ts     # 사용자 스토어 구현
│   └── types.ts          # 사용자 타입 정의
├── community/            # 커뮤니티 관련 상태
│   ├── community-store.ts # 커뮤니티 스토어 구현
│   └── types.ts          # 커뮤니티 타입 정의
├── data/                 # 데이터 관련 상태
│   ├── data-store.ts     # 데이터 스토어 구현
│   └── types.ts          # 데이터 타입 정의
├── index.ts              # 모든 스토어 내보내기
└── README.md             # 문서
```

## 스토어 구성

### 1. 코어 (`core/`)

스토어 생성 및 공통 타입을 정의합니다:

- `create-store.ts`: 스토어 팩토리 함수
- `types.ts`: 공통 타입 정의 (AsyncState, WithReset 등)

```typescript
// 스토어 생성 예시
export const useCounterStore = createStore(
  {
    name: 'counter-store',
    initialState: { count: 0 },
    persistOptions: {
      partialize: (state) => ({ count: state.count }),
    },
  },
  (set) => ({
    count: 0,
    increment: () => set((state) => ({ count: state.count + 1 })),
    decrement: () => set((state) => ({ count: state.count - 1 })),
    reset: () => set({ count: 0 }),
  })
);
```

### 2. UI 스토어 (`ui/`)

UI 관련 상태를 관리합니다:

- 테마 설정
- 모달 상태
- 토스트 메시지
- 사이드바 상태
- 로딩 상태

```typescript
// 사용 예시
import { useTheme, useSetTheme, useModal, useOpenModal } from '@/store';

// 테마 변경
const setTheme = useSetTheme();
setTheme('dark');

// 모달 열기
const openModal = useOpenModal();
openModal('confirm', { title: '확인', message: '계속 진행하시겠습니까?' });
```

### 3. 사용자 스토어 (`user/`)

사용자 관련 상태를 관리합니다:

- 세션 정보
- 사용자 설정
- 사용자 활동 정보
- 인증 상태

```typescript
// 사용 예시
import { useUserSession, useUserPreferences, useUpdateUserPreferences } from '@/store';

// 사용자 세션 정보 가져오기
const session = useUserSession();

// 사용자 설정 업데이트
const updatePreferences = useUpdateUserPreferences();
updatePreferences({ notificationsEnabled: false });
```

### 4. 커뮤니티 스토어 (`community/`)

커뮤니티 관련 상태를 관리합니다:

- 게시물 목록 새로고침 상태
- 필터 상태
- 최근 본 게시물
- 임시 저장된 게시물 내용

```typescript
// 사용 예시
import { useTriggerPostListRefresh, useCommunityFilters, useUpdateCommunityFilters } from '@/store';

// 게시물 목록 새로고침 트리거
const triggerRefresh = useTriggerPostListRefresh();
triggerRefresh();

// 필터 업데이트
const updateFilters = useUpdateCommunityFilters();
updateFilters({ country: 'KR', sortBy: 'latest' });
```

### 5. 데이터 스토어 (`data/`)

데이터 캐싱 및 상태를 관리합니다:

- 캐시 저장소
- 데이터 로딩 상태
- 데이터 에러 상태

```typescript
// 사용 예시
import { useSetCache, useGetCache, useInvalidateCache } from '@/store';

// 데이터 캐싱
const setCache = useSetCache();
setCache('posts', postsData, 5 * 60 * 1000); // 5분 TTL

// 캐시된 데이터 가져오기
const getCache = useGetCache();
const cachedPosts = getCache('posts');

// 캐시 무효화
const invalidateCache = useInvalidateCache();
invalidateCache('posts');
```

## 스토어 사용 방법

### 기본 사용법

```tsx
import { useTheme, useSetTheme } from '@/store';

function ThemeToggle() {
  const theme = useTheme();
  const setTheme = useSetTheme();
  
  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      {theme === 'dark' ? '라이트 모드로 전환' : '다크 모드로 전환'}
    </button>
  );
}
```

### SWR과 통합

```tsx
import { useSetCache, useGetCache, useSetDataLoading } from '@/store';
import useSWR from 'swr';

function PostList() {
  const setCache = useSetCache();
  const getCache = useGetCache();
  const setLoading = useSetDataLoading();
  
  const { data, error } = useSWR('posts', async () => {
    setLoading('posts', true);
    try {
      const data = await fetchPosts();
      setCache('posts', data);
      return data;
    } finally {
      setLoading('posts', false);
    }
  }, {
    initialData: () => getCache('posts'),
  });
  
  // ...
}
```

## 모범 사례

1. **선택자 함수 사용**: 컴포넌트 리렌더링을 최소화하기 위해 선택자 함수를 사용하세요.
   ```typescript
   // 좋은 예
   const theme = useTheme();
   
   // 나쁜 예
   const theme = useUIStore(state => state.theme);
   ```

2. **상태 분리**: 관련 있는 상태끼리 그룹화하고, 독립적인 상태는 분리하세요.
   - 글로벌 상태: 여러 컴포넌트에서 공유되는 상태 (Zustand 스토어)
   - 로컬 상태: 단일 컴포넌트 내에서만 사용되는 상태 (useState/useReducer)

3. **불변성 유지**: 상태를 업데이트할 때 항상 불변성을 유지하세요.
   ```typescript
   // 좋은 예 (Immer 사용)
   set((state) => {
     state.filters.country = 'KR';
   });
   
   // 좋은 예 (수동 불변성)
   set((state) => ({ 
     filters: { ...state.filters, country: 'KR' } 
   }));
   
   // 나쁜 예
   set((state) => {
     state.filters.country = 'KR';
     return state; // 직접 변경 후 반환 (불변성 위반)
   });
   ```

4. **성능 모니터링**: React DevTools Profiler를 사용하여 불필요한 리렌더링을 모니터링하세요.

5. **상태 지속성 관리**: 필요한 상태만 localStorage에 저장하고, 민감한 정보는 저장하지 마세요.
   ```typescript
   persistOptions: {
     partialize: (state) => ({
       // 필요한 상태만 선택적으로 저장
       theme: state.theme,
       // 민감한 정보 제외
       // user: state.user,
     }),
   }
   ```
