/**
 * 커뮤니티 상태 관리 API
 */

import {
  useCommunityStore,
  useNeedsPostListRefresh,
  useTriggerPostListRefresh,
  useResetPostListRefresh,
  useCommunityFilters,
  useUpdateCommunityFilters,
  useResetCommunityFilters,
  useRecentlyViewedPosts,
  useAddRecentlyViewedPost,
  useDraftPost,
  useSaveDraft,
  useClearDraft,
  useSelectedPostId,
  useSetSelectedPostId,
} from '../community/community-store';

export const CommunityStateAPI = {
  // 스토어 접근
  getStore: () => useCommunityStore.getState(),
  
  // 게시물 목록 새로고침 관리
  useNeedsPostListRefresh,
  useTriggerPostListRefresh,
  useResetPostListRefresh,
  triggerPostListRefresh: () => useCommunityStore.getState().triggerPostListRefresh(),
  resetPostListRefresh: () => useCommunityStore.getState().resetPostListRefresh(),
  
  // 필터 관리
  useCommunityFilters,
  useUpdateCommunityFilters,
  useResetCommunityFilters,
  updateFilters: (filters: Parameters<typeof useCommunityStore.getState().updateFilters>[0]) => 
    useCommunityStore.getState().updateFilters(filters),
  resetFilters: () => useCommunityStore.getState().resetFilters(),
  
  // 최근 본 게시물 관리
  useRecentlyViewedPosts,
  useAddRecentlyViewedPost,
  addRecentlyViewedPost: (postId: Parameters<typeof useCommunityStore.getState().addRecentlyViewedPost>[0]) => 
    useCommunityStore.getState().addRecentlyViewedPost(postId),
  clearRecentlyViewedPosts: () => useCommunityStore.getState().clearRecentlyViewedPosts(),
  
  // 임시 저장 게시물 관리
  useDraftPost,
  useSaveDraft,
  useClearDraft,
  saveDraft: (draft: Parameters<typeof useCommunityStore.getState().saveDraft>[0]) => 
    useCommunityStore.getState().saveDraft(draft),
  clearDraft: () => useCommunityStore.getState().clearDraft(),
  
  // 선택된 게시물 관리
  useSelectedPostId,
  useSetSelectedPostId,
  setSelectedPostId: (postId: Parameters<typeof useCommunityStore.getState().setSelectedPostId>[0]) => 
    useCommunityStore.getState().setSelectedPostId(postId),
  
  // 상태 리셋
  reset: () => useCommunityStore.getState().reset(),
};
