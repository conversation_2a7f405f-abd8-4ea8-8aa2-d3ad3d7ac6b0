/**
 * 데이터 상태 관리 API
 */

import {
  useDataStore,
  useSetCache,
  useGetCache,
  useInvalidateCache,
  useClearCache,
  useIsDataLoading,
  useSetDataLoading,
  useDataError,
  useSetDataError,
  useClearDataErrors,
} from '../data/data-store';

export const DataStateAPI = {
  // 스토어 접근
  getStore: () => useDataStore.getState(),
  
  // 캐시 관리
  useSetCache,
  useGetCache,
  useInvalidateCache,
  useClearCache,
  setCache: <T>(
    key: Parameters<typeof useDataStore.getState().setCache<T>>[0],
    data: Parameters<typeof useDataStore.getState().setCache<T>>[1],
    ttl?: Parameters<typeof useDataStore.getState().setCache<T>>[2]
  ) => useDataStore.getState().setCache(key, data, ttl),
  getCache: <T>(key: Parameters<typeof useDataStore.getState().getCache<T>>[0]) => 
    useDataStore.getState().getCache<T>(key),
  invalidateCache: (key: Parameters<typeof useDataStore.getState().invalidateCache>[0]) => 
    useDataStore.getState().invalidateCache(key),
  clearCache: () => useDataStore.getState().clearCache(),
  
  // 로딩 상태 관리
  useIsDataLoading,
  useSetDataLoading,
  setLoading: (
    key: Parameters<typeof useDataStore.getState().setLoading>[0],
    isLoading: Parameters<typeof useDataStore.getState().setLoading>[1]
  ) => useDataStore.getState().setLoading(key, isLoading),
  isLoading: (key: Parameters<typeof useDataStore.getState().isLoading>[0]) => 
    useDataStore.getState().isLoading(key),
  
  // 에러 상태 관리
  useDataError,
  useSetDataError,
  useClearDataErrors,
  setError: (
    key: Parameters<typeof useDataStore.getState().setError>[0],
    error: Parameters<typeof useDataStore.getState().setError>[1]
  ) => useDataStore.getState().setError(key, error),
  getError: (key: Parameters<typeof useDataStore.getState().getError>[0]) => 
    useDataStore.getState().getError(key),
  clearErrors: () => useDataStore.getState().clearErrors(),
  
  // 상태 리셋
  reset: () => useDataStore.getState().reset(),
};
