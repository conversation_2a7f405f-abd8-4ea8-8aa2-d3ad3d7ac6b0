/**
 * 상태 관리 API 레이어
 *
 * 이 모듈은 애플리케이션의 상태 관리를 위한 중앙화된 API를 제공합니다.
 * 컴포넌트는 직접 스토어에 접근하는 대신 이 API를 통해 상태를 관리해야 합니다.
 */

// 스토어 내보내기
export * from '../community/community-store';
export * from '../data/data-store';
export * from '../ui/ui-store';
export * from '../user/user-store';

// 유틸리티 내보내기
export * from '../utils/immer-utils';
export * from '../utils/performance-utils';
export * from '../utils/selector-utils';

// 타입 내보내기
export * from '../community/types';
export * from '../core/types';
export * from '../data/types';
export * from '../ui/types';
export * from '../user/types';

// API 내보내기
export { CommunityStateAPI } from './community-state-api';
export { DataStateAPI } from './data-state-api';
export { StateUtilsAPI } from './state-utils-api';
export { UIStateAPI } from './ui-state-api';
export { UserStateAPI } from './user-state-api';
