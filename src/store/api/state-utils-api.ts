/**
 * 상태 관리 유틸리티 API
 */

import { batchUpdates, useDerivedState, useSelectors } from '../utils/selector-utils';
import { immerUpdate, useImmerUpdater } from '../utils/immer-utils';
import { useRenderTracker, useStateChangeTracker, measureStateUpdate } from '../utils/performance-utils';

export const StateUtilsAPI = {
  // 선택자 유틸리티
  useSelectors,
  useDerivedState,
  batchUpdates,
  
  // Immer 유틸리티
  immerUpdate,
  useImmerUpdater,
  
  // 성능 모니터링 유틸리티
  useRenderTracker,
  useStateChangeTracker,
  measureStateUpdate,
};
