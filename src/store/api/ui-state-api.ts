/**
 * UI 상태 관리 API
 */

import {
  useUIStore,
  useTheme,
  useSetTheme,
  useModal,
  useOpenModal,
  useCloseModal,
  useToasts,
  useAddToast,
  useRemoveToast,
  useIsSidebarOpen,
  useToggleSidebar,
  useSetSidebarOpen,
  useIsLoading,
  useSetLoading,
} from '../ui/ui-store';

export const UIStateAPI = {
  // 스토어 접근
  getStore: () => useUIStore.getState(),
  
  // 테마 관리
  useTheme,
  useSetTheme,
  setTheme: (theme: Parameters<typeof useUIStore.getState().setTheme>[0]) => 
    useUIStore.getState().setTheme(theme),
  
  // 모달 관리
  useModal,
  useOpenModal,
  useCloseModal,
  openModal: (
    type: Parameters<typeof useUIStore.getState().openModal>[0],
    props?: Parameters<typeof useUIStore.getState().openModal>[1]
  ) => useUIStore.getState().openModal(type, props),
  closeModal: () => useUIStore.getState().closeModal(),
  
  // 토스트 관리
  useToasts,
  useAddToast,
  useRemoveToast,
  addToast: (toast: Parameters<typeof useUIStore.getState().addToast>[0]) => 
    useUIStore.getState().addToast(toast),
  removeToast: (id: Parameters<typeof useUIStore.getState().removeToast>[0]) => 
    useUIStore.getState().removeToast(id),
  
  // 사이드바 관리
  useIsSidebarOpen,
  useToggleSidebar,
  useSetSidebarOpen,
  toggleSidebar: () => useUIStore.getState().toggleSidebar(),
  setSidebarOpen: (isOpen: Parameters<typeof useUIStore.getState().setSidebarOpen>[0]) => 
    useUIStore.getState().setSidebarOpen(isOpen),
  
  // 로딩 상태 관리
  useIsLoading,
  useSetLoading,
  setLoading: (isLoading: Parameters<typeof useUIStore.getState().setLoading>[0]) => 
    useUIStore.getState().setLoading(isLoading),
  
  // 상태 리셋
  reset: () => useUIStore.getState().reset(),
};
