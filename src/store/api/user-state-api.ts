/**
 * 사용자 상태 관리 API
 */

import {
  useUserStore,
  useUserSession,
  useUserIsAuthenticated,
  useUserPreferences,
  useUpdateUserPreferences,
  useUserActivity,
  useUserIsLoading,
  useUserError,
  useInitializeFromSession,
  useClearUserData,
} from '../user/user-store';

export const UserStateAPI = {
  // 스토어 접근
  getStore: () => useUserStore.getState(),
  
  // 세션 관리
  useUserSession,
  useUserIsAuthenticated,
  useInitializeFromSession,
  useClearUserData,
  initializeFromSession: (session: Parameters<typeof useUserStore.getState().initializeFromSession>[0]) => 
    useUserStore.getState().initializeFromSession(session),
  clearUserData: () => useUserStore.getState().clearUserData(),
  
  // 사용자 설정 관리
  useUserPreferences,
  useUpdateUserPreferences,
  updatePreferences: (preferences: Parameters<typeof useUserStore.getState().updatePreferences>[0]) => 
    useUserStore.getState().updatePreferences(preferences),
  
  // 사용자 활동 관리
  useUserActivity,
  updateLastActivity: () => useUserStore.getState().updateLastActivity(),
  incrementVisitCount: () => useUserStore.getState().incrementVisitCount(),
  
  // 로딩 및 에러 상태 관리
  useUserIsLoading,
  useUserError,
  setLoading: (isLoading: Parameters<typeof useUserStore.getState().setLoading>[0]) => 
    useUserStore.getState().setLoading(isLoading),
  setError: (error: Parameters<typeof useUserStore.getState().setError>[0]) => 
    useUserStore.getState().setError(error),
  
  // 상태 리셋
  reset: () => useUserStore.getState().reset(),
};
