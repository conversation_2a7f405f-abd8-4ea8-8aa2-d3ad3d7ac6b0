/**
 * 커뮤니티 상태 관리를 위한 Zustand 스토어
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { WithReset } from './types';

// 필터 옵션 인터페이스
export interface CommunityFilters {
  country: string | null;
  sortBy: 'latest' | 'popular' | 'comments';
  searchQuery: string | null;
}

// 커뮤니티 상태 인터페이스
interface CommunityState extends WithReset {
  // 게시물 목록 새로고침 상태 (기존 기능)
  needsPostListRefresh: boolean;
  triggerPostListRefresh: () => void;
  resetPostListRefresh: () => void;

  // 필터 상태
  filters: CommunityFilters;
  updateFilters: (filters: Partial<CommunityFilters>) => void;
  resetFilters: () => void;

  // 최근 본 게시물
  recentlyViewedPosts: string[];
  addRecentlyViewedPost: (postId: string) => void;
  clearRecentlyViewedPosts: () => void;

  // 임시 저장된 게시물 내용
  draftPost: {
    content: string;
    title: string;
    tags: string[];
  } | null;
  saveDraft: (draft: {
    content: string;
    title: string;
    tags: string[];
  }) => void;
  clearDraft: () => void;

  // 선택된 게시물
  selectedPostId: string | null;
  setSelectedPostId: (postId: string | null) => void;
}

// 초기 필터 상태
const defaultFilters: CommunityFilters = {
  country: null,
  sortBy: 'latest',
  searchQuery: null,
};

// 초기 상태
const initialState: Omit<
  CommunityState,
  | 'triggerPostListRefresh'
  | 'resetPostListRefresh'
  | 'updateFilters'
  | 'resetFilters'
  | 'addRecentlyViewedPost'
  | 'clearRecentlyViewedPosts'
  | 'saveDraft'
  | 'clearDraft'
  | 'setSelectedPostId'
  | 'reset'
> = {
  needsPostListRefresh: false,
  filters: defaultFilters,
  recentlyViewedPosts: [],
  draftPost: null,
  selectedPostId: null,
};

// 커뮤니티 스토어 생성
export const useCommunityStore = create<CommunityState>()(
  devtools(
    persist(
      (set) => ({
        ...initialState,

        // 게시물 목록 새로고침 (기존 기능)
        triggerPostListRefresh: () => set({ needsPostListRefresh: true }),
        resetPostListRefresh: () => set({ needsPostListRefresh: false }),

        // 필터 업데이트
        updateFilters: (filters) =>
          set((state) => ({
            filters: { ...state.filters, ...filters },
          })),
        resetFilters: () => set({ filters: defaultFilters }),

        // 최근 본 게시물 관리
        addRecentlyViewedPost: (postId) =>
          set((state) => {
            // 중복 제거 및 최대 10개 유지
            const updatedPosts = [
              postId,
              ...state.recentlyViewedPosts.filter((id) => id !== postId),
            ].slice(0, 10);

            return { recentlyViewedPosts: updatedPosts };
          }),
        clearRecentlyViewedPosts: () => set({ recentlyViewedPosts: [] }),

        // 임시 저장 게시물 관리
        saveDraft: (draft) => set({ draftPost: draft }),
        clearDraft: () => set({ draftPost: null }),

        // 선택된 게시물 관리
        setSelectedPostId: (postId) => set({ selectedPostId: postId }),

        // 상태 리셋
        reset: () => set(initialState),
      }),
      {
        name: 'community-store',
        partialize: (state) => ({
          filters: state.filters,
          recentlyViewedPosts: state.recentlyViewedPosts,
          draftPost: state.draftPost,
        }),
      }
    )
  )
);

// 선택자 함수들 (성능 최적화를 위해)
export const useNeedsPostListRefresh = () =>
  useCommunityStore((state) => state.needsPostListRefresh);
export const useTriggerPostListRefresh = () =>
  useCommunityStore((state) => state.triggerPostListRefresh);
export const useResetPostListRefresh = () =>
  useCommunityStore((state) => state.resetPostListRefresh);

export const useCommunityFilters = () =>
  useCommunityStore((state) => state.filters);
export const useUpdateCommunityFilters = () =>
  useCommunityStore((state) => state.updateFilters);
export const useResetCommunityFilters = () =>
  useCommunityStore((state) => state.resetFilters);

export const useRecentlyViewedPosts = () =>
  useCommunityStore((state) => state.recentlyViewedPosts);
export const useAddRecentlyViewedPost = () =>
  useCommunityStore((state) => state.addRecentlyViewedPost);

export const useDraftPost = () => useCommunityStore((state) => state.draftPost);
export const useSaveDraft = () => useCommunityStore((state) => state.saveDraft);
export const useClearDraft = () =>
  useCommunityStore((state) => state.clearDraft);

export const useSelectedPostId = () =>
  useCommunityStore((state) => state.selectedPostId);
export const useSetSelectedPostId = () =>
  useCommunityStore((state) => state.setSelectedPostId);
