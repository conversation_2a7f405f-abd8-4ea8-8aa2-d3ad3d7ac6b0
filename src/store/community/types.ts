/**
 * 커뮤니티 스토어 타입 정의
 */

import { WithReset } from '../core/types';

/**
 * 필터 옵션 인터페이스
 */
export interface CommunityFilters {
  country: string | null;
  sortBy: 'latest' | 'popular' | 'comments';
  searchQuery: string | null;
}

/**
 * 임시 저장 게시물 인터페이스
 */
export interface DraftPost {
  content: string;
  title: string;
  tags: string[];
}

/**
 * 커뮤니티 상태 인터페이스
 */
export interface CommunityState extends WithReset {
  // 게시물 목록 새로고침 상태
  needsPostListRefresh: boolean;
  triggerPostListRefresh: () => void;
  resetPostListRefresh: () => void;

  // 필터 상태
  filters: CommunityFilters;
  updateFilters: (filters: Partial<CommunityFilters>) => void;
  resetFilters: () => void;

  // 최근 본 게시물
  recentlyViewedPosts: string[];
  addRecentlyViewedPost: (postId: string) => void;
  clearRecentlyViewedPosts: () => void;

  // 임시 저장된 게시물 내용
  draftPost: DraftPost | null;
  saveDraft: (draft: DraftPost) => void;
  clearDraft: () => void;

  // 선택된 게시물
  selectedPostId: string | null;
  setSelectedPostId: (postId: string | null) => void;
}
