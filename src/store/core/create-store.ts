/**
 * 스토어 팩토리 함수
 * 일관된 Zustand 스토어 생성을 위한 유틸리티
 */

import { create, StateCreator, StoreApi, UseBoundStore } from 'zustand';
import { devtools, persist, PersistOptions } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

/**
 * 모든 스토어에 공통으로 적용되는 리셋 기능 인터페이스
 */
export interface WithReset {
  reset: () => void;
}

/**
 * 비동기 상태 관리를 위한 인터페이스
 */
export interface AsyncState<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  setData: (data: T | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: Error | null) => void;
}

/**
 * 스토어 생성 옵션 인터페이스
 */
export interface CreateStoreOptions<T> {
  /** 스토어 이름 (디버깅 및 지속성에 사용) */
  name: string;
  /** 스토어 초기 상태 */
  initialState: T;
  /** 지속성 옵션 (localStorage에 상태 저장) */
  persistOptions?: Partial<PersistOptions<T>>;
  /** 개발 도구 사용 여부 */
  devtools?: boolean;
  /** Immer 미들웨어 사용 여부 (불변성 관리 간소화) */
  useImmer?: boolean;
}

/**
 * 스토어 생성 팩토리 함수
 * 
 * @param options 스토어 생성 옵션
 * @param storeCreator 스토어 상태 및 액션 생성 함수
 * @returns Zustand 스토어 훅
 * 
 * @example
 * ```ts
 * // 스토어 생성 예시
 * export const useCounterStore = createStore(
 *   {
 *     name: 'counter-store',
 *     initialState: { count: 0 },
 *     persistOptions: {
 *       partialize: (state) => ({ count: state.count }),
 *     },
 *   },
 *   (set) => ({
 *     count: 0,
 *     increment: () => set((state) => ({ count: state.count + 1 })),
 *     decrement: () => set((state) => ({ count: state.count - 1 })),
 *     reset: () => set({ count: 0 }),
 *   })
 * );
 * ```
 */
export function createStore<T extends object>(
  options: CreateStoreOptions<T>,
  storeCreator: StateCreator<T, [['zustand/immer', never]], []>
): UseBoundStore<StoreApi<T>> {
  const {
    name,
    initialState,
    persistOptions,
    devtools: useDevtools = true,
    useImmer: shouldUseImmer = true,
  } = options;

  // 미들웨어 체인 구성
  let middleware = storeCreator;

  // Immer 미들웨어 추가 (불변성 관리 간소화)
  if (shouldUseImmer) {
    middleware = immer(middleware) as any;
  }

  // 개발 도구 미들웨어 추가 (디버깅)
  if (useDevtools) {
    middleware = devtools(middleware, { name }) as any;
  }

  // 지속성 미들웨어 추가 (상태 저장)
  if (persistOptions) {
    middleware = persist(middleware, {
      name,
      ...persistOptions,
    }) as any;
  }

  // 최종 스토어 생성 및 반환
  return create(middleware);
}

/**
 * 비동기 상태 초기값 생성 유틸리티
 */
export function createAsyncState<T>(): AsyncState<T> {
  return {
    data: null,
    isLoading: false,
    error: null,
    setData: () => {},
    setLoading: () => {},
    setError: () => {},
  };
}
