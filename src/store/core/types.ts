/**
 * 스토어 공통 타입 정의
 */

/**
 * 모든 스토어에 공통으로 적용되는 리셋 기능 인터페이스
 */
export interface WithReset {
  reset: () => void;
}

/**
 * 비동기 상태 관리를 위한 인터페이스
 */
export interface AsyncState<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  setData: (data: T | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: Error | null) => void;
}

/**
 * 상태 업데이트 결과 타입
 */
export type UpdateResult = {
  success: boolean;
  message?: string;
};

/**
 * 페이지네이션 상태 인터페이스
 */
export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  hasMore: boolean;
}

/**
 * 필터 상태 기본 인터페이스
 */
export interface BaseFilterState {
  searchQuery: string | null;
  sortBy: string;
}

/**
 * 캐시 항목 인터페이스
 */
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}
