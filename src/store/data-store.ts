/**
 * 데이터 상태 관리를 위한 Zustand 스토어
 * SWR과 Zustand를 통합하여 데이터 캐싱 및 상태 관리를 최적화
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AsyncState, WithReset } from './types';

// 캐시 항목 인터페이스
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// 데이터 스토어 상태 인터페이스
interface DataState extends WithReset {
  // 캐시 저장소
  cache: Record<string, CacheItem<any>>;
  
  // 캐시 관리 함수
  setCache: <T>(key: string, data: T, ttl?: number) => void;
  getCache: <T>(key: string) => T | null;
  invalidateCache: (key: string) => void;
  clearCache: () => void;
  
  // 데이터 로딩 상태
  loadingKeys: string[];
  setLoading: (key: string, isLoading: boolean) => void;
  isLoading: (key: string) => boolean;
  
  // 데이터 에러 상태
  errors: Record<string, Error | null>;
  setError: (key: string, error: Error | null) => void;
  getError: (key: string) => Error | null;
  clearErrors: () => void;
}

// 초기 상태
const initialState: Omit<DataState, 'setCache' | 'getCache' | 'invalidateCache' | 'clearCache' | 'setLoading' | 'isLoading' | 'setError' | 'getError' | 'clearErrors' | 'reset'> = {
  cache: {},
  loadingKeys: [],
  errors: {},
};

// 데이터 스토어 생성
export const useDataStore = create<DataState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // 캐시 관리 함수
      setCache: (key, data, ttl = 5 * 60 * 1000) => {
        const now = Date.now();
        set((state) => ({
          cache: {
            ...state.cache,
            [key]: {
              data,
              timestamp: now,
              expiresAt: now + ttl,
            },
          },
        }));
      },
      
      getCache: (key) => {
        const cacheItem = get().cache[key];
        if (!cacheItem) return null;
        
        // 캐시 만료 확인
        if (Date.now() > cacheItem.expiresAt) {
          get().invalidateCache(key);
          return null;
        }
        
        return cacheItem.data;
      },
      
      invalidateCache: (key) => {
        set((state) => {
          const { [key]: _, ...restCache } = state.cache;
          return { cache: restCache };
        });
      },
      
      clearCache: () => set({ cache: {} }),
      
      // 로딩 상태 관리
      setLoading: (key, isLoading) => {
        set((state) => {
          if (isLoading) {
            return {
              loadingKeys: [...state.loadingKeys, key],
            };
          } else {
            return {
              loadingKeys: state.loadingKeys.filter((k) => k !== key),
            };
          }
        });
      },
      
      isLoading: (key) => {
        return get().loadingKeys.includes(key);
      },
      
      // 에러 상태 관리
      setError: (key, error) => {
        set((state) => ({
          errors: {
            ...state.errors,
            [key]: error,
          },
        }));
      },
      
      getError: (key) => {
        return get().errors[key] || null;
      },
      
      clearErrors: () => set({ errors: {} }),
      
      // 상태 리셋
      reset: () => set(initialState),
    })
  )
);

// 선택자 함수들 (성능 최적화를 위해)
export const useSetCache = () => useDataStore((state) => state.setCache);
export const useGetCache = () => useDataStore((state) => state.getCache);
export const useInvalidateCache = () => useDataStore((state) => state.invalidateCache);
export const useClearCache = () => useDataStore((state) => state.clearCache);

export const useIsDataLoading = (key: string) => useDataStore((state) => state.isLoading(key));
export const useSetDataLoading = () => useDataStore((state) => state.setLoading);

export const useDataError = (key: string) => useDataStore((state) => state.getError(key));
export const useSetDataError = () => useDataStore((state) => state.setError);
export const useClearDataErrors = () => useDataStore((state) => state.clearErrors);
