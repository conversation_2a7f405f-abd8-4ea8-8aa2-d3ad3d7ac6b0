/**
 * 데이터 상태 관리를 위한 Zustand 스토어
 * SWR과 Zustand를 통합하여 데이터 캐싱 및 상태 관리를 최적화
 */

import { createStore } from '../core/create-store';
import { DataState } from './types';

// 초기 상태
const initialState: Omit<
  DataState,
  | 'setCache'
  | 'getCache'
  | 'invalidateCache'
  | 'clearCache'
  | 'setLoading'
  | 'isLoading'
  | 'setError'
  | 'getError'
  | 'clearErrors'
  | 'reset'
> = {
  cache: {},
  loadingKeys: [],
  errors: {},
};

/**
 * 데이터 스토어 생성
 * 데이터 캐싱, 로딩 상태, 에러 상태 등을 관리
 */
export const useDataStore = createStore<DataState>(
  {
    name: 'data-store',
    initialState: initialState as DataState,
    // 데이터 스토어는 영구 저장하지 않음 (세션 내에서만 유효)
    persistOptions: undefined,
  },
  (set, get) => ({
    ...initialState,
    
    // 캐시 관리 함수
    setCache: (key, data, ttl = 5 * 60 * 1000) => {
      const now = Date.now();
      set((state) => ({
        cache: {
          ...state.cache,
          [key]: {
            data,
            timestamp: now,
            expiresAt: now + ttl,
          },
        },
      }));
    },
    
    getCache: (key) => {
      const cacheItem = get().cache[key];
      if (!cacheItem) return null;
      
      // 캐시 만료 확인
      if (Date.now() > cacheItem.expiresAt) {
        get().invalidateCache(key);
        return null;
      }
      
      return cacheItem.data;
    },
    
    invalidateCache: (key) => {
      set((state) => {
        const { [key]: _, ...restCache } = state.cache;
        return { cache: restCache };
      });
    },
    
    clearCache: () => set({ cache: {} }),
    
    // 로딩 상태 관리
    setLoading: (key, isLoading) => {
      set((state) => {
        if (isLoading) {
          return {
            loadingKeys: [...state.loadingKeys, key],
          };
        } else {
          return {
            loadingKeys: state.loadingKeys.filter((k) => k !== key),
          };
        }
      });
    },
    
    isLoading: (key) => {
      return get().loadingKeys.includes(key);
    },
    
    // 에러 상태 관리
    setError: (key, error) => {
      set((state) => ({
        errors: {
          ...state.errors,
          [key]: error,
        },
      }));
    },
    
    getError: (key) => {
      return get().errors[key] || null;
    },
    
    clearErrors: () => set({ errors: {} }),
    
    // 상태 리셋
    reset: () => set(initialState),
  })
);

// 선택자 함수들 (성능 최적화를 위해)
export const useSetCache = () => useDataStore((state) => state.setCache);
export const useGetCache = () => useDataStore((state) => state.getCache);
export const useInvalidateCache = () => useDataStore((state) => state.invalidateCache);
export const useClearCache = () => useDataStore((state) => state.clearCache);

export const useIsDataLoading = () => useDataStore((state) => state.isLoading);
export const useSetDataLoading = () => useDataStore((state) => state.setLoading);

export const useDataError = () => useDataStore((state) => state.getError);
export const useSetDataError = () => useDataStore((state) => state.setError);
export const useClearDataErrors = () => useDataStore((state) => state.clearErrors);
