/**
 * 데이터 스토어 타입 정의
 */

import { CacheItem, WithReset } from '../core/types';

/**
 * 데이터 스토어 상태 인터페이스
 */
export interface DataState extends WithReset {
  // 캐시 저장소
  cache: Record<string, CacheItem<any>>;
  
  // 캐시 관리 함수
  setCache: <T>(key: string, data: T, ttl?: number) => void;
  getCache: <T>(key: string) => T | null;
  invalidateCache: (key: string) => void;
  clearCache: () => void;
  
  // 데이터 로딩 상태
  loadingKeys: string[];
  setLoading: (key: string, isLoading: boolean) => void;
  isLoading: (key: string) => boolean;
  
  // 데이터 에러 상태
  errors: Record<string, Error | null>;
  setError: (key: string, error: Error | null) => void;
  getError: (key: string) => Error | null;
  clearErrors: () => void;
}
