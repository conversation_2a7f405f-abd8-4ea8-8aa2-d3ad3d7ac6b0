# 상태 관리 가이드라인

이 문서는 애플리케이션에서 글로벌 상태와 로컬 상태를 효과적으로 분리하고 관리하기 위한 가이드라인을 제공합니다.

## 목차

1. [글로벌 vs 로컬 상태](#글로벌-vs-로컬-상태)
2. [상태 관리 결정 트리](#상태-관리-결정-트리)
3. [상태 관리 패턴](#상태-관리-패턴)
4. [성능 최적화 기법](#성능-최적화-기법)
5. [모범 사례](#모범-사례)

## 글로벌 vs 로컬 상태

### 글로벌 상태

글로벌 상태는 애플리케이션의 여러 부분에서 공유되는 상태입니다. 이러한 상태는 Zustand 스토어를 통해 관리됩니다.

**글로벌 상태의 특징:**
- 여러 컴포넌트에서 접근 필요
- 페이지 간 지속되어야 함
- 애플리케이션 전체에 영향을 미침

**글로벌 상태의 예:**
- 사용자 인증 정보
- 테마 설정
- 언어 설정
- 전역 알림/토스트 메시지
- 애플리케이션 설정

### 로컬 상태

로컬 상태는 단일 컴포넌트 또는 컴포넌트 트리 내에서만 관련이 있는 상태입니다. 이러한 상태는 React의 `useState` 또는 `useReducer` 훅을 통해 관리됩니다.

**로컬 상태의 특징:**
- 단일 컴포넌트 또는 제한된 컴포넌트 트리에서만 사용
- 컴포넌트 마운트/언마운트 시 초기화 가능
- 다른 컴포넌트에 영향을 미치지 않음

**로컬 상태의 예:**
- 폼 입력 값
- UI 상태 (열림/닫힘, 활성/비활성)
- 컴포넌트별 로딩 상태
- 임시 데이터

## 상태 관리 결정 트리

다음 결정 트리를 사용하여 상태를 어떻게 관리할지 결정하세요:

1. **이 상태가 여러 컴포넌트에서 사용되나요?**
   - 예 → 2단계로 이동
   - 아니오 → **로컬 상태** 사용 (`useState` 또는 `useReducer`)

2. **이 상태가 페이지 새로고침 후에도 유지되어야 하나요?**
   - 예 → **글로벌 상태** 사용 (Zustand + persist)
   - 아니오 → 3단계로 이동

3. **이 상태가 깊은 컴포넌트 트리를 통과해야 하나요?**
   - 예 → **글로벌 상태** 사용 (Zustand)
   - 아니오 → 4단계로 이동

4. **이 상태가 복잡한 로직을 포함하나요?**
   - 예 → **글로벌 상태** 사용 (Zustand)
   - 아니오 → **로컬 상태** 사용 (`useState` 또는 컨텍스트)

## 상태 관리 패턴

### 로컬 상태 관리

#### 1. useState

간단한 상태에 적합합니다.

```tsx
function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
}
```

#### 2. useReducer

복잡한 상태 로직에 적합합니다.

```tsx
const initialState = { count: 0 };

function reducer(state, action) {
  switch (action.type) {
    case 'increment':
      return { count: state.count + 1 };
    case 'decrement':
      return { count: state.count - 1 };
    default:
      throw new Error();
  }
}

function Counter() {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  return (
    <div>
      <p>Count: {state.count}</p>
      <button onClick={() => dispatch({ type: 'increment' })}>Increment</button>
      <button onClick={() => dispatch({ type: 'decrement' })}>Decrement</button>
    </div>
  );
}
```

#### 3. 커스텀 훅

재사용 가능한 상태 로직에 적합합니다.

```tsx
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);
  
  return { count, increment, decrement, reset };
}

function Counter() {
  const { count, increment, decrement, reset } = useCounter(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Increment</button>
      <button onClick={decrement}>Decrement</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}
```

### 글로벌 상태 관리

#### 1. Zustand 스토어

```tsx
// src/store/counter/counter-store.ts
import { createStore } from '../core/create-store';

interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}

export const useCounterStore = createStore<CounterState>(
  {
    name: 'counter-store',
    initialState: { count: 0 } as CounterState,
  },
  (set) => ({
    count: 0,
    increment: () => set((state) => ({ count: state.count + 1 })),
    decrement: () => set((state) => ({ count: state.count - 1 })),
    reset: () => set({ count: 0 }),
  })
);

// 선택자 함수
export const useCount = () => useCounterStore((state) => state.count);
export const useCounterActions = () => ({
  increment: useCounterStore((state) => state.increment),
  decrement: useCounterStore((state) => state.decrement),
  reset: useCounterStore((state) => state.reset),
});
```

## 성능 최적화 기법

### 1. 선택자 함수 사용

컴포넌트가 필요한 상태만 구독하도록 선택자 함수를 사용하세요.

```tsx
// 나쁜 예 - 전체 상태 구독
function UserProfile() {
  const userStore = useUserStore();
  return <div>{userStore.data?.name}</div>;
}

// 좋은 예 - 필요한 상태만 구독
function UserProfile() {
  const userName = useUserStore((state) => state.data?.name);
  return <div>{userName}</div>;
}

// 더 좋은 예 - 선택자 함수 사용
function UserProfile() {
  const userName = useUserName();
  return <div>{userName}</div>;
}
```

### 2. 메모이제이션 사용

불필요한 리렌더링을 방지하기 위해 `useMemo`, `useCallback`, `React.memo`를 사용하세요.

```tsx
// 파생 데이터 메모이제이션
const filteredItems = useMemo(() => {
  return items.filter(item => item.active);
}, [items]);

// 이벤트 핸들러 메모이제이션
const handleClick = useCallback(() => {
  console.log('Clicked!');
}, []);

// 컴포넌트 메모이제이션
const MemoizedComponent = React.memo(MyComponent);
```

## 모범 사례

1. **상태는 필요한 곳에 가깝게 유지하세요.**
   - 글로벌 상태 스토어에 모든 것을 넣지 마세요.
   - 컴포넌트 로컬 상태로 충분한 경우 로컬 상태를 사용하세요.

2. **상태 업데이트는 원자적으로 유지하세요.**
   - 여러 상태 업데이트를 하나의 액션으로 그룹화하세요.
   - 불변성을 유지하세요.

3. **파생 상태는 계산하세요, 저장하지 마세요.**
   - 다른 상태에서 계산할 수 있는 상태는 `useMemo`를 사용하여 계산하세요.
   - 중복 상태를 피하세요.

4. **상태 접근 패턴을 일관되게 유지하세요.**
   - 직접 스토어 접근보다 선택자 함수를 사용하세요.
   - 컴포넌트 간에 일관된 패턴을 사용하세요.

5. **상태 변경을 추적하세요.**
   - 개발 중에는 devtools 미들웨어를 사용하여 상태 변경을 추적하세요.
   - 복잡한 상태 변경에는 로깅을 추가하세요.
