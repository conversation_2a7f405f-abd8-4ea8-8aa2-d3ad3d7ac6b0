/**
 * 스토어 모듈 인덱스 파일
 * 모든 스토어를 한 곳에서 내보냄
 */

// 코어 타입 내보내기
export * from './core/types';

// UI 스토어 내보내기
export {
  useUIStore,
  useTheme,
  useSetTheme,
  useModal,
  useOpenModal,
  useCloseModal,
  useToasts,
  useAddToast,
  useRemoveToast,
  useIsSidebarOpen,
  useToggleSidebar,
  useSetSidebarOpen,
  useIsLoading,
  useSetLoading,
} from './ui/ui-store';
export type { ModalType, Toast, ToastType, Theme } from './ui/types';

// 사용자 스토어 내보내기
export {
  useUserStore,
  useUserSession,
  useUserIsAuthenticated,
  useUserPreferences,
  useUpdateUserPreferences,
  useUserActivity,
  useUserIsLoading,
  useUserError,
  useInitializeFromSession,
  useClearUserData,
} from './user/user-store';
export type { UserPreferences, UserActivity } from './user/types';

// 커뮤니티 스토어 내보내기
export {
  useCommunityStore,
  useNeedsPostListRefresh,
  useTriggerPostListRefresh,
  useResetPostListRefresh,
  useCommunityFilters,
  useUpdateCommunityFilters,
  useResetCommunityFilters,
  useRecentlyViewedPosts,
  useAddRecentlyViewedPost,
  useDraftPost,
  useSaveDraft,
  useClearDraft,
  useSelectedPostId,
  useSetSelectedPostId,
} from './community/community-store';
export type { CommunityFilters, DraftPost } from './community/types';

// 데이터 스토어 내보내기
export {
  useDataStore,
  useSetCache,
  useGetCache,
  useInvalidateCache,
  useClearCache,
  useIsDataLoading,
  useSetDataLoading,
  useDataError,
  useSetDataError,
  useClearDataErrors,
} from './data/data-store';
