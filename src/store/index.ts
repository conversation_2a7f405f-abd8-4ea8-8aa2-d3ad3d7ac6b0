/**
 * 스토어 모듈 인덱스 파일
 * 모든 스토어를 한 곳에서 내보냄
 */

// 타입 내보내기
export * from './types';

// UI 스토어 내보내기
export {
  useUIStore,
  useTheme,
  useSetTheme,
  useModal,
  useOpenModal,
  useCloseModal,
  useToasts,
  useAddToast,
  useRemoveToast,
  useIsSidebarOpen,
  useToggleSidebar,
  useSetSidebarOpen,
  useIsLoading,
  useSetLoading,
  type ModalType,
  type Toast,
  type ToastType,
} from './ui-store';

// 사용자 스토어 내보내기
export {
  useUserStore,
  useUserSession,
  useUserIsAuthenticated,
  useUserPreferences,
  useUpdateUserPreferences,
  useUserIsLoading,
  useUserError,
  useInitializeFromSession,
  useClearUserData,
  type UserPreferences,
} from './user-store';

// 커뮤니티 스토어 내보내기
export {
  useCommunityStore,
  useNeedsPostListRefresh,
  useTriggerPostListRefresh,
  useResetPostListRefresh,
  useCommunityFilters,
  useUpdateCommunityFilters,
  useResetCommunityFilters,
  useRecentlyViewedPosts,
  useAddRecentlyViewedPost,
  useDraftPost,
  useSaveDraft,
  useClearDraft,
  useSelectedPostId,
  useSetSelectedPostId,
  type CommunityFilters,
} from './community-store';

// 데이터 스토어 내보내기
export {
  useDataStore,
  useSetCache,
  useGetCache,
  useInvalidateCache,
  useClearCache,
  useIsDataLoading,
  useSetDataLoading,
  useDataError,
  useSetDataError,
  useClearDataErrors,
} from './data-store';
