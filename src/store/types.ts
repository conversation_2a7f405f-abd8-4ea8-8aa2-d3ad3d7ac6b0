/**
 * Zustand 스토어 타입 정의
 */

import { type StateCreator } from 'zustand';

/**
 * 스토어 슬라이스 타입 정의
 * 여러 스토어를 조합할 때 사용하는 타입
 */
export type StoreSlice<T> = StateCreator<T, [], [], T>;

/**
 * 스토어 리셋 기능을 위한 타입
 */
export interface WithReset {
  reset: () => void;
}

/**
 * 스토어 초기화 기능을 위한 타입
 */
export interface WithInitialize<T> {
  initialize: (data: T) => void;
}

/**
 * 스토어 업데이트 기능을 위한 타입
 */
export interface WithUpdate<T> {
  update: (data: Partial<T>) => void;
}

/**
 * 스토어 로딩 상태 관리를 위한 타입
 */
export interface WithLoading {
  isLoading: boolean;
  setLoading: (isLoading: boolean) => void;
}

/**
 * 스토어 에러 상태 관리를 위한 타입
 */
export interface WithError {
  error: Error | null;
  setError: (error: Error | null) => void;
}

/**
 * 비동기 데이터 상태 관리를 위한 타입
 */
export interface AsyncState<T> extends WithLoading, WithError {
  data: T | null;
  setData: (data: T | null) => void;
}
