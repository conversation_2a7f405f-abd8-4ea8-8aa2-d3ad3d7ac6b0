/**
 * UI 상태 관리를 위한 Zustand 스토어
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { WithReset } from './types';

// 모달 타입 정의
export type ModalType = 
  | 'confirm'
  | 'alert'
  | 'post-create'
  | 'post-edit'
  | 'comment-create'
  | 'comment-edit'
  | 'user-profile'
  | 'image-preview';

// 모달 상태 인터페이스
interface ModalState {
  type: ModalType | null;
  props: Record<string, any>;
  isOpen: boolean;
}

// 토스트 메시지 타입
export type ToastType = 'success' | 'error' | 'info' | 'warning';

// 토스트 메시지 인터페이스
export interface Toast {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
}

// UI 스토어 상태 인터페이스
interface UIState extends WithReset {
  // 테마 관련 상태
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  
  // 모달 관련 상태
  modal: ModalState;
  openModal: (type: ModalType, props?: Record<string, any>) => void;
  closeModal: () => void;
  
  // 토스트 관련 상태
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  
  // 사이드바 관련 상태
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  
  // 로딩 상태
  isLoading: boolean;
  setLoading: (isLoading: boolean) => void;
  
  // 리셋 함수 (WithReset 인터페이스에서 상속)
}

// 초기 상태
const initialState: Omit<UIState, 'setTheme' | 'openModal' | 'closeModal' | 'addToast' | 'removeToast' | 'toggleSidebar' | 'setSidebarOpen' | 'setLoading' | 'reset'> = {
  theme: 'system',
  modal: {
    type: null,
    props: {},
    isOpen: false,
  },
  toasts: [],
  isSidebarOpen: true,
  isLoading: false,
};

// UI 스토어 생성
export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set) => ({
        ...initialState,
        
        // 테마 설정
        setTheme: (theme) => set({ theme }),
        
        // 모달 관련 함수
        openModal: (type, props = {}) => set({ 
          modal: { type, props, isOpen: true } 
        }),
        closeModal: () => set({ 
          modal: { type: null, props: {}, isOpen: false } 
        }),
        
        // 토스트 관련 함수
        addToast: (toast) => set((state) => ({ 
          toasts: [...state.toasts, { ...toast, id: Date.now().toString() }] 
        })),
        removeToast: (id) => set((state) => ({ 
          toasts: state.toasts.filter((toast) => toast.id !== id) 
        })),
        
        // 사이드바 관련 함수
        toggleSidebar: () => set((state) => ({ 
          isSidebarOpen: !state.isSidebarOpen 
        })),
        setSidebarOpen: (isOpen) => set({ isSidebarOpen: isOpen }),
        
        // 로딩 상태 설정
        setLoading: (isLoading) => set({ isLoading }),
        
        // 상태 리셋
        reset: () => set(initialState),
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          theme: state.theme,
          isSidebarOpen: state.isSidebarOpen,
        }),
      }
    )
  )
);

// 선택자 함수들 (성능 최적화를 위해)
export const useTheme = () => useUIStore((state) => state.theme);
export const useSetTheme = () => useUIStore((state) => state.setTheme);

export const useModal = () => useUIStore((state) => state.modal);
export const useOpenModal = () => useUIStore((state) => state.openModal);
export const useCloseModal = () => useUIStore((state) => state.closeModal);

export const useToasts = () => useUIStore((state) => state.toasts);
export const useAddToast = () => useUIStore((state) => state.addToast);
export const useRemoveToast = () => useUIStore((state) => state.removeToast);

export const useIsSidebarOpen = () => useUIStore((state) => state.isSidebarOpen);
export const useToggleSidebar = () => useUIStore((state) => state.toggleSidebar);
export const useSetSidebarOpen = () => useUIStore((state) => state.setSidebarOpen);

export const useIsLoading = () => useUIStore((state) => state.isLoading);
export const useSetLoading = () => useUIStore((state) => state.setLoading);
