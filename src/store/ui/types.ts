/**
 * UI 스토어 타입 정의
 */

import { WithReset } from '../core/types';

/**
 * 테마 타입
 */
export type Theme = 'light' | 'dark' | 'system';

/**
 * 모달 타입 정의
 */
export type ModalType = 
  | 'confirm'
  | 'alert'
  | 'post-create'
  | 'post-edit'
  | 'comment-create'
  | 'comment-edit'
  | 'user-profile'
  | 'image-preview';

/**
 * 모달 상태 인터페이스
 */
export interface ModalState {
  type: ModalType | null;
  props: Record<string, any>;
  isOpen: boolean;
}

/**
 * 토스트 메시지 타입
 */
export type ToastType = 'success' | 'error' | 'info' | 'warning';

/**
 * 토스트 메시지 인터페이스
 */
export interface Toast {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
}

/**
 * UI 상태 인터페이스
 */
export interface UIState extends WithReset {
  // 테마 관련
  theme: Theme;
  setTheme: (theme: Theme) => void;
  
  // 모달 관련
  modal: ModalState;
  openModal: (type: ModalType, props?: Record<string, any>) => void;
  closeModal: () => void;
  
  // 토스트 관련
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  
  // 사이드바 관련
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  
  // 로딩 관련
  isLoading: boolean;
  setLoading: (isLoading: boolean) => void;
}
