/**
 * UI 상태 관리를 위한 Zustand 스토어
 */

import { createStore } from '../core/create-store';
import { ModalState, Theme, Toast, UIState } from './types';

// 초기 모달 상태
const initialModalState: ModalState = {
  type: null,
  props: {},
  isOpen: false,
};

// 초기 UI 상태
const initialState: Omit<
  UIState,
  | 'setTheme'
  | 'openModal'
  | 'closeModal'
  | 'addToast'
  | 'removeToast'
  | 'toggleSidebar'
  | 'setSidebarOpen'
  | 'setLoading'
  | 'reset'
> = {
  theme: 'system',
  modal: initialModalState,
  toasts: [],
  isSidebarOpen: false,
  isLoading: false,
};

/**
 * UI 스토어 생성
 * 테마, 모달, 토스트, 사이드바, 로딩 상태 등 UI 관련 상태를 관리
 */
export const useUIStore = createStore<UIState>(
  {
    name: 'ui-store',
    initialState: initialState as UIState,
    persistOptions: {
      partialize: (state) => ({
        theme: state.theme,
        isSidebarOpen: state.isSidebarOpen,
      }),
    },
  },
  (set) => ({
    ...initialState,
    
    // 테마 설정
    setTheme: (theme) => set({ theme }),
    
    // 모달 관련 함수
    openModal: (type, props = {}) => set({ 
      modal: { type, props, isOpen: true } 
    }),
    closeModal: () => set({ 
      modal: { type: null, props: {}, isOpen: false } 
    }),
    
    // 토스트 관련 함수
    addToast: (toast) => set((state) => ({ 
      toasts: [...state.toasts, { ...toast, id: Date.now().toString() }] 
    })),
    removeToast: (id) => set((state) => ({ 
      toasts: state.toasts.filter((toast) => toast.id !== id) 
    })),
    
    // 사이드바 관련 함수
    toggleSidebar: () => set((state) => ({ 
      isSidebarOpen: !state.isSidebarOpen 
    })),
    setSidebarOpen: (isOpen) => set({ 
      isSidebarOpen: isOpen 
    }),
    
    // 로딩 상태 관련 함수
    setLoading: (isLoading) => set({ isLoading }),
    
    // 상태 리셋
    reset: () => set(initialState),
  })
);

// 선택자 함수들 (성능 최적화를 위해)
export const useTheme = () => useUIStore((state) => state.theme);
export const useSetTheme = () => useUIStore((state) => state.setTheme);

export const useModal = () => useUIStore((state) => state.modal);
export const useOpenModal = () => useUIStore((state) => state.openModal);
export const useCloseModal = () => useUIStore((state) => state.closeModal);

export const useToasts = () => useUIStore((state) => state.toasts);
export const useAddToast = () => useUIStore((state) => state.addToast);
export const useRemoveToast = () => useUIStore((state) => state.removeToast);

export const useIsSidebarOpen = () => useUIStore((state) => state.isSidebarOpen);
export const useToggleSidebar = () => useUIStore((state) => state.toggleSidebar);
export const useSetSidebarOpen = () => useUIStore((state) => state.setSidebarOpen);

export const useIsLoading = () => useUIStore((state) => state.isLoading);
export const useSetLoading = () => useUIStore((state) => state.setLoading);
