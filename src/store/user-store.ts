/**
 * 사용자 상태 관리를 위한 Zustand 스토어
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AsyncState, WithReset } from './types';
import { Session } from 'next-auth';

// 사용자 설정 인터페이스
export interface UserPreferences {
  notificationsEnabled: boolean;
  emailNotificationsEnabled: boolean;
  defaultCountry: string | null;
  language: string;
}

// 사용자 상태 인터페이스
export interface UserState extends AsyncState<Session>, WithReset {
  // 사용자 세션 정보 (AsyncState에서 상속)
  
  // 사용자 설정
  preferences: UserPreferences;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  
  // 사용자 활동 정보
  lastActivity: Date | null;
  updateLastActivity: () => void;
  
  // 인증 상태
  isAuthenticated: boolean;
  setAuthenticated: (isAuthenticated: boolean) => void;
  
  // 사용자 정보 초기화 (세션 정보로부터)
  initializeFromSession: (session: Session | null) => void;
  
  // 로그아웃
  clearUserData: () => void;
}

// 초기 사용자 설정
const defaultPreferences: UserPreferences = {
  notificationsEnabled: true,
  emailNotificationsEnabled: false,
  defaultCountry: null,
  language: 'ko',
};

// 초기 상태
const initialState: Omit<UserState, 'setData' | 'setLoading' | 'setError' | 'updatePreferences' | 'updateLastActivity' | 'setAuthenticated' | 'initializeFromSession' | 'clearUserData' | 'reset'> = {
  data: null,
  isLoading: false,
  error: null,
  preferences: defaultPreferences,
  lastActivity: null,
  isAuthenticated: false,
};

// 사용자 스토어 생성
export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        // AsyncState 구현
        setData: (data) => set({ data }),
        setLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        
        // 사용자 설정 업데이트
        updatePreferences: (preferences) => set((state) => ({
          preferences: { ...state.preferences, ...preferences },
        })),
        
        // 마지막 활동 시간 업데이트
        updateLastActivity: () => set({ lastActivity: new Date() }),
        
        // 인증 상태 설정
        setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
        
        // 세션 정보로부터 사용자 정보 초기화
        initializeFromSession: (session) => {
          if (session?.user) {
            set({
              data: session,
              isAuthenticated: true,
              isLoading: false,
              error: null,
              lastActivity: new Date(),
            });
          } else {
            get().clearUserData();
          }
        },
        
        // 사용자 데이터 초기화 (로그아웃)
        clearUserData: () => set({
          data: null,
          isAuthenticated: false,
          lastActivity: null,
          // 사용자 설정은 유지 (preferences)
        }),
        
        // 상태 리셋
        reset: () => set(initialState),
      }),
      {
        name: 'user-store',
        partialize: (state) => ({
          preferences: state.preferences,
          // 민감한 정보는 저장하지 않음
        }),
      }
    )
  )
);

// 선택자 함수들 (성능 최적화를 위해)
export const useUserSession = () => useUserStore((state) => state.data);
export const useUserIsAuthenticated = () => useUserStore((state) => state.isAuthenticated);
export const useUserPreferences = () => useUserStore((state) => state.preferences);
export const useUpdateUserPreferences = () => useUserStore((state) => state.updatePreferences);
export const useUserIsLoading = () => useUserStore((state) => state.isLoading);
export const useUserError = () => useUserStore((state) => state.error);
export const useInitializeFromSession = () => useUserStore((state) => state.initializeFromSession);
export const useClearUserData = () => useUserStore((state) => state.clearUserData);
