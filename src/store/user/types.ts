/**
 * 사용자 스토어 타입 정의
 */

import { Session } from 'next-auth';
import { AsyncState, WithReset } from '../core/types';

/**
 * 사용자 설정 인터페이스
 */
export interface UserPreferences {
  notificationsEnabled: boolean;
  emailNotificationsEnabled: boolean;
  defaultCountry: string | null;
  language: string;
}

/**
 * 사용자 활동 정보 인터페이스
 */
export interface UserActivity {
  lastActivity: Date | null;
  lastLogin: Date | null;
  visitCount: number;
}

/**
 * 사용자 상태 인터페이스
 */
export interface UserState extends AsyncState<Session>, WithReset {
  // 사용자 세션 정보 (AsyncState에서 상속)
  
  // 사용자 설정
  preferences: UserPreferences;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  
  // 사용자 활동 정보
  activity: UserActivity;
  updateLastActivity: () => void;
  incrementVisitCount: () => void;
  
  // 인증 상태
  isAuthenticated: boolean;
  setAuthenticated: (isAuthenticated: boolean) => void;
  
  // 사용자 정보 초기화 (세션 정보로부터)
  initializeFromSession: (session: Session | null) => void;
  
  // 로그아웃
  clearUserData: () => void;
}
