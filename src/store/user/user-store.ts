/**
 * 사용자 상태 관리를 위한 Zustand 스토어
 */

import { Session } from 'next-auth';
import { createStore } from '../core/create-store';
import { UserActivity, UserPreferences, UserState } from './types';

// 초기 사용자 설정
const defaultPreferences: UserPreferences = {
  notificationsEnabled: true,
  emailNotificationsEnabled: false,
  defaultCountry: null,
  language: 'ko',
};

// 초기 사용자 활동 정보
const defaultActivity: UserActivity = {
  lastActivity: null,
  lastLogin: null,
  visitCount: 0,
};

// 초기 상태
const initialState: Omit<
  UserState,
  | 'setData'
  | 'setLoading'
  | 'setError'
  | 'updatePreferences'
  | 'updateLastActivity'
  | 'incrementVisitCount'
  | 'setAuthenticated'
  | 'initializeFromSession'
  | 'clearUserData'
  | 'reset'
> = {
  data: null,
  isLoading: false,
  error: null,
  preferences: defaultPreferences,
  activity: defaultActivity,
  isAuthenticated: false,
};

/**
 * 사용자 스토어 생성
 * 사용자 세션, 설정, 활동 정보 등을 관리
 */
export const useUserStore = createStore<UserState>(
  {
    name: 'user-store',
    initialState: initialState as UserState,
    persistOptions: {
      partialize: (state) => ({
        preferences: state.preferences,
        activity: {
          ...state.activity,
          visitCount: state.activity.visitCount,
        },
      }),
    },
  },
  (set, get) => ({
    ...initialState,
    
    // AsyncState 구현
    setData: (data) => set({ data }),
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error }),
    
    // 사용자 설정 업데이트
    updatePreferences: (preferences) => set((state) => ({
      preferences: { ...state.preferences, ...preferences },
    })),
    
    // 사용자 활동 정보 업데이트
    updateLastActivity: () => set((state) => ({
      activity: { ...state.activity, lastActivity: new Date() },
    })),
    
    incrementVisitCount: () => set((state) => ({
      activity: { ...state.activity, visitCount: state.activity.visitCount + 1 },
    })),
    
    // 인증 상태 설정
    setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
    
    // 세션 정보로부터 사용자 정보 초기화
    initializeFromSession: (session) => {
      if (session?.user) {
        set({
          data: session,
          isAuthenticated: true,
          isLoading: false,
          error: null,
          activity: {
            ...get().activity,
            lastActivity: new Date(),
            lastLogin: new Date(),
          },
        });
      } else {
        get().clearUserData();
      }
    },
    
    // 사용자 데이터 초기화 (로그아웃)
    clearUserData: () => set({
      data: null,
      isAuthenticated: false,
      activity: {
        ...get().activity,
        lastActivity: null,
        lastLogin: null,
      },
      // 사용자 설정은 유지 (preferences)
    }),
    
    // 상태 리셋
    reset: () => set(initialState),
  })
);

// 선택자 함수들 (성능 최적화를 위해)
export const useUserSession = () => useUserStore((state) => state.data);
export const useUserIsAuthenticated = () => useUserStore((state) => state.isAuthenticated);
export const useUserPreferences = () => useUserStore((state) => state.preferences);
export const useUpdateUserPreferences = () => useUserStore((state) => state.updatePreferences);
export const useUserActivity = () => useUserStore((state) => state.activity);
export const useUserIsLoading = () => useUserStore((state) => state.isLoading);
export const useUserError = () => useUserStore((state) => state.error);
export const useInitializeFromSession = () => useUserStore((state) => state.initializeFromSession);
export const useClearUserData = () => useUserStore((state) => state.clearUserData);
