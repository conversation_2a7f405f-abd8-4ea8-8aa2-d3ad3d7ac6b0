/**
 * Immer 기반 상태 업데이트 유틸리티
 * 
 * 이 모듈은 Immer를 사용하여 불변성을 유지하면서 상태를 업데이트하기 위한 유틸리티 함수를 제공합니다.
 */

import { produce, Draft } from 'immer';
import { StoreApi, UseBoundStore } from 'zustand';

/**
 * Immer를 사용하여 상태를 업데이트하는 유틸리티 함수
 * 
 * @param store Zustand 스토어
 * @param recipe Immer 레시피 함수
 * 
 * @example
 * ```tsx
 * const addTodo = (text: string) => {
 *   immerUpdate(useTodoStore.getState(), (draft) => {
 *     draft.todos.push({ id: Date.now(), text, completed: false });
 *   });
 * };
 * ```
 */
export function immerUpdate<S>(
  store: S & { setState: (updater: (state: S) => S) => void },
  recipe: (draft: Draft<S>) => void
): void {
  store.setState(produce(recipe));
}

/**
 * Immer 기반 상태 업데이트 훅
 * 
 * @param store Zustand 스토어 훅
 * @returns Immer 레시피를 사용하여 상태를 업데이트하는 함수
 * 
 * @example
 * ```tsx
 * function TodoList() {
 *   const todos = useTodoStore((state) => state.todos);
 *   const updateTodos = useImmerUpdater(useTodoStore);
 *   
 *   const toggleTodo = (id: number) => {
 *     updateTodos((draft) => {
 *       const todo = draft.todos.find(t => t.id === id);
 *       if (todo) {
 *         todo.completed = !todo.completed;
 *       }
 *     });
 *   };
 *   
 *   return (
 *     <ul>
 *       {todos.map(todo => (
 *         <li key={todo.id} onClick={() => toggleTodo(todo.id)}>
 *           {todo.text} {todo.completed ? '✓' : ''}
 *         </li>
 *       ))}
 *     </ul>
 *   );
 * }
 * ```
 */
export function useImmerUpdater<S>(
  store: UseBoundStore<StoreApi<S>>
): (recipe: (draft: Draft<S>) => void) => void {
  const { setState } = store;
  
  return (recipe: (draft: Draft<S>) => void) => {
    setState(produce(recipe));
  };
}

/**
 * 중첩된 객체 상태를 업데이트하는 유틸리티 함수
 * 
 * @param store Zustand 스토어
 * @param path 업데이트할 속성 경로 (점 표기법)
 * @param value 새 값
 * 
 * @example
 * ```tsx
 * const updateUserEmail = (email: string) => {
 *   updateNestedState(useUserStore.getState(), 'user.contact.email', email);
 * };
 * ```
 */
export function updateNestedState<S, V>(
  store: S & { setState: (updater: (state: S) => S) => void },
  path: string,
  value: V
): void {
  immerUpdate(store, (draft) => {
    const keys = path.split('.');
    let current: any = draft;
    
    // 마지막 키 이전까지 탐색
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (current[key] === undefined) {
        current[key] = {};
      }
      current = current[key];
    }
    
    // 마지막 키에 값 할당
    const lastKey = keys[keys.length - 1];
    current[lastKey] = value;
  });
}

/**
 * 배열 상태를 업데이트하는 유틸리티 함수
 * 
 * @param store Zustand 스토어
 * @param path 배열이 위치한 속성 경로 (점 표기법)
 * @param operation 배열 작업 함수
 * 
 * @example
 * ```tsx
 * // 항목 추가
 * updateArrayState(useTodoStore.getState(), 'todos', (todos) => {
 *   todos.push({ id: Date.now(), text: 'New Todo', completed: false });
 * });
 * 
 * // 항목 제거
 * updateArrayState(useTodoStore.getState(), 'todos', (todos) => {
 *   const index = todos.findIndex(todo => todo.id === 123);
 *   if (index !== -1) {
 *     todos.splice(index, 1);
 *   }
 * });
 * 
 * // 항목 업데이트
 * updateArrayState(useTodoStore.getState(), 'todos', (todos) => {
 *   const todo = todos.find(todo => todo.id === 123);
 *   if (todo) {
 *     todo.completed = true;
 *   }
 * });
 * ```
 */
export function updateArrayState<S, T>(
  store: S & { setState: (updater: (state: S) => S) => void },
  path: string,
  operation: (array: Draft<T[]>) => void
): void {
  immerUpdate(store, (draft) => {
    const keys = path.split('.');
    let current: any = draft;
    
    // 경로 탐색
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      if (current[key] === undefined) {
        current[key] = i === keys.length - 1 ? [] : {};
      }
      current = current[key];
    }
    
    // 배열 작업 수행
    operation(current);
  });
}
