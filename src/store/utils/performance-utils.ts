/**
 * 상태 관리 성능 모니터링 유틸리티
 * 
 * 이 모듈은 상태 관리 관련 성능을 모니터링하고 최적화하기 위한 유틸리티 함수를 제공합니다.
 */

import { useEffect, useRef } from 'react';
import { StoreApi, UseBoundStore } from 'zustand';

/**
 * 개발 환경에서만 활성화되는 상태 변경 로깅 미들웨어
 * 
 * @param name 스토어 이름
 * @returns Zustand 미들웨어
 */
export const devLogMiddleware = (name: string) => <T>(
  config: (set: any, get: any, api: any) => T
) => (set: any, get: any, api: any) => {
  // 개발 환경에서만 로깅
  const isDev = process.env.NODE_ENV === 'development';
  
  return config(
    (args: any) => {
      if (isDev) {
        console.group(`[${name}] 상태 변경`);
        console.log('이전 상태:', get());
        console.log('변경 사항:', args);
      }
      
      set(args);
      
      if (isDev) {
        console.log('다음 상태:', get());
        console.groupEnd();
      }
      
      return get();
    },
    get,
    api
  );
};

/**
 * 상태 변경 성능 측정 미들웨어
 * 
 * @param name 스토어 이름
 * @returns Zustand 미들웨어
 */
export const performanceMiddleware = (name: string) => <T>(
  config: (set: any, get: any, api: any) => T
) => (set: any, get: any, api: any) => {
  // 개발 환경에서만 성능 측정
  const isDev = process.env.NODE_ENV === 'development';
  
  return config(
    (args: any) => {
      if (!isDev) {
        set(args);
        return get();
      }
      
      const label = `[${name}] 상태 업데이트 성능`;
      console.time(label);
      
      set(args);
      
      console.timeEnd(label);
      return get();
    },
    get,
    api
  );
};

/**
 * 컴포넌트 리렌더링 횟수를 추적하는 훅
 * 
 * @param componentName 컴포넌트 이름
 * @param enabled 활성화 여부 (기본값: true)
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   useRenderTracker('MyComponent');
 *   // ...
 * }
 * ```
 */
export function useRenderTracker(componentName: string, enabled = true): void {
  const renderCount = useRef(0);
  
  useEffect(() => {
    if (enabled && process.env.NODE_ENV === 'development') {
      renderCount.current += 1;
      console.log(`[${componentName}] 렌더링 횟수: ${renderCount.current}`);
    }
  });
}

/**
 * 상태 변경 시 리렌더링 횟수를 추적하는 훅
 * 
 * @param store Zustand 스토어 훅
 * @param selector 선택자 함수
 * @param componentName 컴포넌트 이름
 * 
 * @example
 * ```tsx
 * function ThemeDisplay() {
 *   const theme = useStateChangeTracker(
 *     useUIStore,
 *     (state) => state.theme,
 *     'ThemeDisplay'
 *   );
 *   
 *   return <div>Current theme: {theme}</div>;
 * }
 * ```
 */
export function useStateChangeTracker<S, T>(
  store: UseBoundStore<StoreApi<S>>,
  selector: (state: S) => T,
  componentName: string
): T {
  const renderCount = useRef(0);
  const prevValue = useRef<T | null>(null);
  
  // 선택자를 통해 상태 가져오기
  const value = store(selector);
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      renderCount.current += 1;
      
      if (prevValue.current !== null && prevValue.current !== value) {
        console.log(`[${componentName}] 상태 변경 감지:`, {
          이전: prevValue.current,
          현재: value,
          렌더링횟수: renderCount.current,
        });
      }
      
      prevValue.current = value;
    }
  }, [value, componentName]);
  
  return value;
}

/**
 * 상태 변경 시간을 측정하는 유틸리티 함수
 * 
 * @param name 작업 이름
 * @param fn 실행할 함수
 * @returns 함수의 반환값
 * 
 * @example
 * ```tsx
 * const addTodo = (text: string) => {
 *   return measureStateUpdate('Todo 추가', () => {
 *     const newTodo = { id: Date.now(), text, completed: false };
 *     useTodoStore.setState((state) => ({
 *       todos: [...state.todos, newTodo],
 *     }));
 *     return newTodo;
 *   });
 * };
 * ```
 */
export function measureStateUpdate<T>(name: string, fn: () => T): T {
  if (process.env.NODE_ENV !== 'development') {
    return fn();
  }
  
  const label = `[상태 업데이트] ${name}`;
  console.time(label);
  
  try {
    return fn();
  } finally {
    console.timeEnd(label);
  }
}
