/**
 * Zustand 선택자 최적화 유틸리티
 * 
 * 이 모듈은 Zustand 스토어의 선택자 패턴을 최적화하기 위한 유틸리티 함수를 제공합니다.
 */

import { useCallback, useMemo } from 'react';
import { StoreApi, UseBoundStore } from 'zustand';

/**
 * 여러 선택자를 한 번에 사용하기 위한 유틸리티 훅
 * 
 * 이 훅은 여러 선택자를 한 번에 사용할 수 있게 해주며,
 * 각 선택자의 결과가 변경될 때만 리렌더링을 트리거합니다.
 * 
 * @param store Zustand 스토어 훅
 * @param selectors 선택자 함수 객체
 * @returns 각 선택자의 결과를 포함하는 객체
 * 
 * @example
 * ```tsx
 * const { theme, modal, isSidebarOpen } = useSelectors(useUIStore, {
 *   theme: (state) => state.theme,
 *   modal: (state) => state.modal,
 *   isSidebarOpen: (state) => state.isSidebarOpen,
 * });
 * ```
 */
export function useSelectors<S, T extends Record<string, (state: S) => any>>(
  store: UseBoundStore<StoreApi<S>>,
  selectors: T
): { [K in keyof T]: ReturnType<T[K]> } {
  // 각 선택자를 개별적으로 구독
  const selectedValues = {} as { [K in keyof T]: ReturnType<T[K]> };
  
  // 각 선택자에 대해 개별적으로 useStore를 호출
  for (const key in selectors) {
    if (Object.prototype.hasOwnProperty.call(selectors, key)) {
      // @ts-ignore - 동적 키 접근
      selectedValues[key] = store(selectors[key]);
    }
  }
  
  return selectedValues;
}

/**
 * 메모이제이션된 선택자 생성 유틸리티
 * 
 * 이 함수는 의존성 배열이 변경될 때만 재계산되는 메모이제이션된 선택자를 생성합니다.
 * 
 * @param selector 기본 선택자 함수
 * @param dependencies 의존성 배열
 * @returns 메모이제이션된 선택자 함수
 * 
 * @example
 * ```tsx
 * const useFilteredItems = (filter) => {
 *   const selector = useMemoizedSelector(
 *     (state) => state.items.filter(item => item.category === filter),
 *     [filter]
 *   );
 *   return useItemsStore(selector);
 * };
 * ```
 */
export function useMemoizedSelector<S, T>(
  selector: (state: S) => T,
  dependencies: any[] = []
): (state: S) => T {
  return useMemo(() => selector, dependencies);
}

/**
 * 파생 상태 계산을 위한 유틸리티 훅
 * 
 * 이 훅은 스토어 상태에서 파생된 값을 계산하고 메모이제이션합니다.
 * 
 * @param store Zustand 스토어 훅
 * @param selector 선택자 함수
 * @param compute 계산 함수
 * @param dependencies 추가 의존성 배열
 * @returns 계산된 파생 상태
 * 
 * @example
 * ```tsx
 * const completedTodos = useDerivedState(
 *   useTodoStore,
 *   (state) => state.todos,
 *   (todos) => todos.filter(todo => todo.completed),
 *   []
 * );
 * ```
 */
export function useDerivedState<S, T, R>(
  store: UseBoundStore<StoreApi<S>>,
  selector: (state: S) => T,
  compute: (selected: T) => R,
  dependencies: any[] = []
): R {
  // 선택자를 통해 상태 가져오기
  const selected = store(selector);
  
  // 계산 함수 메모이제이션
  const memoizedCompute = useCallback(compute, dependencies);
  
  // 파생 상태 계산 및 메모이제이션
  return useMemo(() => memoizedCompute(selected), [selected, memoizedCompute]);
}

/**
 * 배치 업데이트를 위한 유틸리티 함수
 * 
 * 이 함수는 여러 상태 업데이트를 하나의 배치로 처리합니다.
 * 
 * @param store Zustand 스토어
 * @param updates 업데이트 함수 배열
 * 
 * @example
 * ```tsx
 * const resetAllState = () => {
 *   batchUpdates(useUIStore.getState(), [
 *     (state) => ({ theme: 'system' }),
 *     (state) => ({ modal: { type: null, props: {}, isOpen: false } }),
 *     (state) => ({ isSidebarOpen: false }),
 *   ]);
 * };
 * ```
 */
export function batchUpdates<S>(
  store: S & { setState: (updater: (state: S) => Partial<S>) => void },
  updates: Array<(state: S) => Partial<S>>
): void {
  store.setState((state) => {
    let newState = { ...state };
    
    for (const update of updates) {
      newState = { ...newState, ...update(newState) };
    }
    
    return newState;
  });
}

/**
 * 상태 변경 로깅을 위한 미들웨어 생성 함수
 * 
 * @param name 스토어 이름
 * @returns Zustand 미들웨어
 * 
 * @example
 * ```tsx
 * const useCounterStore = create(
 *   logMiddleware('counter')(
 *     (set) => ({
 *       count: 0,
 *       increment: () => set((state) => ({ count: state.count + 1 })),
 *     })
 *   )
 * );
 * ```
 */
export const logMiddleware = (name: string) => <T>(
  config: (set: any, get: any, api: any) => T
) => (set: any, get: any, api: any) =>
  config(
    (args: any) => {
      console.log(`[${name}] 이전 상태:`, get());
      console.log(`[${name}] 변경 사항:`, args);
      set(args);
      console.log(`[${name}] 다음 상태:`, get());
      return get();
    },
    get,
    api
  );
