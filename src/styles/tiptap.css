/* Tiptap ProseMirror 스타일 - 게시글 상세 페이지 기준 */

/* 기본 편집기 스타일 */
.ProseMirror {
  font-family: var(--font-pretendard);
  line-height: 1.5;
  color: inherit;
  outline: none;
}

.ProseMirror:focus {
  outline: none;
  box-shadow: none;
}

/* Tiptap 컨테이너 스타일 */
.tiptap-container .ProseMirror {
  font-family: var(--font-pretendard);
  line-height: 1.5;
  color: inherit;
  outline: none;
}

.tiptap-container .ProseMirror:focus {
  outline: none;
  box-shadow: none;
}

/* 헤딩 스타일 */
.ProseMirror h1,
.tiptap-container .ProseMirror h1 {
  font-size: 1.25rem;
  color: #ff8383 !important;
  font-weight: 900 !important; /* font-black */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ProseMirror h2,
.tiptap-container .ProseMirror h2 {
  font-size: 1rem;
  color: #fff574 !important;
  font-weight: 700 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ProseMirror h3,
.tiptap-container .ProseMirror h3 {
  font-size: 0.875rem;
  color: #94ffd8 !important;
  font-weight: 500 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 빈 헤딩에 대한 스타일 */
.ProseMirror h1.is-empty::before,
.ProseMirror h1.is-empty:not(:focus)::before,
.tiptap-container .ProseMirror h1.is-empty::before,
.tiptap-container .ProseMirror h1.is-empty:not(:focus)::before {
  color: #ff8383 !important;
  font-weight: 900 !important; /* font-black */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ProseMirror h2.is-empty::before,
.ProseMirror h2.is-empty:not(:focus)::before,
.tiptap-container .ProseMirror h2.is-empty::before,
.tiptap-container .ProseMirror h2.is-empty:not(:focus)::before {
  color: #fff574 !important;
  font-weight: 700 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ProseMirror h3.is-empty::before,
.ProseMirror h3.is-empty:not(:focus)::before,
.tiptap-container .ProseMirror h3.is-empty::before,
.tiptap-container .ProseMirror h3.is-empty:not(:focus)::before {
  color: #94ffd8 !important;
  font-weight: 500 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* tiptap-heading 클래스 스타일 */
.tiptap-heading.level-1 {
  font-size: 1.25rem;
  color: #ff8383 !important;
  font-weight: 900 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.tiptap-heading.level-2 {
  font-size: 1rem;
  color: #fff574 !important;
  font-weight: 700 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.tiptap-heading.level-3 {
  font-size: 0.875rem;
  color: #94ffd8 !important;
  font-weight: 500 !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 일반 텍스트 스타일 */
.ProseMirror p {
  margin-bottom: 0.5rem;
  font-size: 0.875rem; /* 14px */
}

/* 목록 스타일 */
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.ProseMirror li {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

/* 인용구 스타일 */
.ProseMirror blockquote {
  border-left: 3px solid #ccc;
  padding-left: 1rem;
  font-style: italic;
  color: #666;
  margin: 1rem;
}

/* 링크 스타일 */
.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

/* 게시글과 댓글에서 공통으로 사용될 수 있는 스타일 */
.post-content .ProseMirror,
.comment-content .ProseMirror {
  padding: 0.5rem;
}

/* 에디터 컨텐츠 스타일 */
.tiptap-editor-content .ProseMirror {
  outline: none;
}

.tiptap-editor-content .ProseMirror:focus {
  outline: none;
  box-shadow: none;
}

/* 에디터 컨테이너 스타일 */
.tiptap-editor-container {
  border-radius: 0.375rem; /* rounded-md */
}

.tiptap-editor-wrapper {
  min-height: 200px;
  max-width: none;
  padding: 1rem;
}

/* Tailwind Typography 스타일 */
.tiptap-prose {
  font-size: 0.875rem; /* prose-sm */
}

.tiptap-prose.dark {
  color-scheme: dark;
}

.tiptap-prose h1,
.tiptap-prose h2,
.tiptap-prose h3,
.tiptap-prose h4,
.tiptap-prose h5,
.tiptap-prose h6 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.tiptap-prose p {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.tiptap-prose blockquote {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.tiptap-prose ol,
.tiptap-prose ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

/* 플레이스홀더 스타일 */
.tiptap-placeholder:before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 이미지 스타일 */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem; /* rounded-md */
}
