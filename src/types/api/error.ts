/**
 * API 에러 관련 타입 정의
 * 
 * API 에러의 표준 형식을 정의합니다.
 */

import { HTTP_STATUS, THttpStatusCode } from './response';

/**
 * API 에러 코드
 */
export const API_ERROR_CODE = {
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
} as const;

/**
 * API 에러 코드 타입
 */
export type TApiErrorCode = typeof API_ERROR_CODE[keyof typeof API_ERROR_CODE];

/**
 * API 에러 클래스
 */
export class ApiError extends Error {
  /** HTTP 상태 코드 */
  statusCode: THttpStatusCode;
  /** 에러 코드 */
  code: TApiErrorCode;
  /** 추가 데이터 */
  data?: any;

  /**
   * API 에러 생성자
   * 
   * @param message 에러 메시지
   * @param statusCode HTTP 상태 코드
   * @param code 에러 코드
   * @param data 추가 데이터
   */
  constructor(
    message: string,
    statusCode: THttpStatusCode,
    code: TApiErrorCode = API_ERROR_CODE.INTERNAL_ERROR,
    data?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.data = data;
  }
}

/**
 * 내부 서버 에러 클래스
 */
export class InternalServerError extends ApiError {
  /**
   * 내부 서버 에러 생성자
   * 
   * @param message 에러 메시지
   * @param data 추가 데이터
   */
  constructor(message = '내부 서버 오류가 발생했습니다.', data?: any) {
    super(
      message,
      HTTP_STATUS.INTERNAL_SERVER_ERROR,
      API_ERROR_CODE.INTERNAL_ERROR,
      data
    );
    this.name = 'InternalServerError';
  }
}

/**
 * 인증 에러 클래스
 */
export class UnauthorizedError extends ApiError {
  /**
   * 인증 에러 생성자
   * 
   * @param message 에러 메시지
   * @param data 추가 데이터
   */
  constructor(message = '인증이 필요합니다.', data?: any) {
    super(
      message,
      HTTP_STATUS.UNAUTHORIZED,
      API_ERROR_CODE.UNAUTHORIZED,
      data
    );
    this.name = 'UnauthorizedError';
  }
}

/**
 * 권한 에러 클래스
 */
export class ForbiddenError extends ApiError {
  /**
   * 권한 에러 생성자
   * 
   * @param message 에러 메시지
   * @param data 추가 데이터
   */
  constructor(message = '접근 권한이 없습니다.', data?: any) {
    super(
      message,
      HTTP_STATUS.FORBIDDEN,
      API_ERROR_CODE.FORBIDDEN,
      data
    );
    this.name = 'ForbiddenError';
  }
}

/**
 * 리소스 찾을 수 없음 에러 클래스
 */
export class NotFoundError extends ApiError {
  /**
   * 리소스 찾을 수 없음 에러 생성자
   * 
   * @param message 에러 메시지
   * @param data 추가 데이터
   */
  constructor(message = '요청한 리소스를 찾을 수 없습니다.', data?: any) {
    super(
      message,
      HTTP_STATUS.NOT_FOUND,
      API_ERROR_CODE.NOT_FOUND,
      data
    );
    this.name = 'NotFoundError';
  }
}

/**
 * 유효성 검사 에러 클래스
 */
export class ValidationError extends ApiError {
  /**
   * 유효성 검사 에러 생성자
   * 
   * @param message 에러 메시지
   * @param data 추가 데이터
   */
  constructor(message = '입력 값이 유효하지 않습니다.', data?: any) {
    super(
      message,
      HTTP_STATUS.BAD_REQUEST,
      API_ERROR_CODE.VALIDATION_FAILED,
      data
    );
    this.name = 'ValidationError';
  }
}

/**
 * 네트워크 에러 클래스
 */
export class NetworkError extends ApiError {
  /**
   * 네트워크 에러 생성자
   * 
   * @param message 에러 메시지
   * @param data 추가 데이터
   */
  constructor(message = '네트워크 오류가 발생했습니다.', data?: any) {
    super(
      message,
      HTTP_STATUS.SERVICE_UNAVAILABLE,
      API_ERROR_CODE.NETWORK_ERROR,
      data
    );
    this.name = 'NetworkError';
  }
}
