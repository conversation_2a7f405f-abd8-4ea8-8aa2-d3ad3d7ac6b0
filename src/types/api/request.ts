/**
 * API 요청 관련 타입 정의
 * 
 * API 요청의 표준 형식을 정의합니다.
 */

import { z } from 'zod';
import { ICursorPaginationParams, IPaginationParams } from '../common/pagination';

/**
 * API 요청 메서드
 */
export type TApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * API 요청 옵션
 */
export interface IApiRequestOptions {
  /** 요청 메서드 */
  method?: TApiMethod;
  /** 요청 헤더 */
  headers?: Record<string, string>;
  /** 요청 바디 */
  body?: any;
  /** 쿼리 파라미터 */
  params?: Record<string, string | number | boolean | undefined>;
  /** 캐시 전략 */
  cache?: RequestCache;
  /** 인증 토큰 포함 여부 */
  withAuth?: boolean;
  /** 타임아웃 (밀리초) */
  timeout?: number;
  /** 재시도 횟수 */
  retries?: number;
}

/**
 * 페이지 기반 API 요청 옵션
 */
export interface IPaginatedApiRequestOptions extends IApiRequestOptions {
  /** 페이지네이션 파라미터 */
  pagination?: IPaginationParams;
}

/**
 * 커서 기반 API 요청 옵션
 */
export interface ICursorPaginatedApiRequestOptions<C = string> extends IApiRequestOptions {
  /** 커서 기반 페이지네이션 파라미터 */
  pagination?: ICursorPaginationParams<C>;
}

/**
 * Zod 스키마: API 요청 옵션
 */
export const apiRequestOptionsSchema = z.object({
  method: z.enum(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']).optional(),
  headers: z.record(z.string()).optional(),
  body: z.any().optional(),
  params: z.record(z.union([z.string(), z.number(), z.boolean()])).optional(),
  cache: z.string().optional(),
  withAuth: z.boolean().optional(),
  timeout: z.number().optional(),
  retries: z.number().optional(),
});

/**
 * Zod 스키마: 페이지네이션 파라미터
 */
export const paginationParamsSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).optional(),
});

/**
 * Zod 스키마: 페이지 기반 API 요청 옵션
 */
export const paginatedApiRequestOptionsSchema = apiRequestOptionsSchema.extend({
  pagination: paginationParamsSchema.optional(),
});

/**
 * Zod 스키마: 커서 기반 페이지네이션 파라미터
 */
export const cursorPaginationParamsSchema = z.object({
  cursor: z.string().optional(),
  limit: z.number().optional(),
  direction: z.enum(['next', 'prev']).optional(),
});

/**
 * Zod 스키마: 커서 기반 API 요청 옵션
 */
export const cursorPaginatedApiRequestOptionsSchema = apiRequestOptionsSchema.extend({
  pagination: cursorPaginationParamsSchema.optional(),
});
