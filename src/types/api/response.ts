/**
 * API 응답 관련 타입 정의
 * 
 * API 응답의 표준 형식을 정의합니다.
 */

import { z } from 'zod';
import { ICursorPaginationMeta, IPaginationMeta } from '../common/pagination';

/**
 * HTTP 상태 코드
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

/**
 * HTTP 상태 코드 타입
 */
export type THttpStatusCode = typeof HTTP_STATUS[keyof typeof HTTP_STATUS];

/**
 * 기본 API 응답 속성
 */
export interface IBaseApiResponse {
  /** HTTP 상태 코드 */
  status: THttpStatusCode;
  /** 응답 메시지 */
  message?: string;
  /** 응답 타임스탬프 */
  timestamp: string;
}

/**
 * 일반 API 응답 타입
 */
export interface IApiResponse<T> extends IBaseApiResponse {
  /** 응답 데이터 */
  data: T;
  /** 메타데이터 */
  meta?: Record<string, any>;
}

/**
 * API 에러 응답 타입
 */
export interface IApiErrorResponse extends IBaseApiResponse {
  /** 에러 정보 */
  error: {
    /** 에러 메시지 */
    message: string;
    /** HTTP 상태 코드 */
    status: THttpStatusCode;
    /** 에러 코드 */
    code: string;
    /** 에러 카테고리 */
    category?: string;
    /** 추가 에러 데이터 */
    data?: any;
  };
}

/**
 * 페이지 기반 리스트 응답 타입
 */
export interface IApiListResponse<T> extends IBaseApiResponse {
  /** 응답 데이터 항목 배열 */
  items: T[];
  /** 페이지네이션 메타데이터 */
  pagination: IPaginationMeta;
  /** 추가 메타데이터 */
  meta?: Record<string, any>;
}

/**
 * 커서 기반 리스트 응답 타입
 */
export interface IApiCursorResponse<T, C = string> extends IBaseApiResponse {
  /** 응답 데이터 항목 배열 */
  items: T[];
  /** 커서 기반 페이지네이션 메타데이터 */
  pagination: ICursorPaginationMeta<C>;
  /** 추가 메타데이터 */
  meta?: Record<string, any>;
}

/**
 * Zod 스키마: 기본 API 응답
 */
export const baseApiResponseSchema = z.object({
  status: z.number(),
  message: z.string().optional(),
  timestamp: z.string(),
});

/**
 * Zod 스키마: 일반 API 응답
 */
export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  baseApiResponseSchema.extend({
    data: dataSchema,
    meta: z.record(z.string(), z.any()).optional(),
  });

/**
 * Zod 스키마: API 에러 응답
 */
export const apiErrorResponseSchema = baseApiResponseSchema.extend({
  error: z.object({
    message: z.string(),
    status: z.number(),
    code: z.string(),
    category: z.string().optional(),
    data: z.any().optional(),
  }),
});

/**
 * Zod 스키마: 페이지네이션 메타데이터
 */
export const paginationMetaSchema = z.object({
  total: z.number(),
  page: z.number(),
  size: z.number(),
  totalPages: z.number(),
});

/**
 * Zod 스키마: 페이지 기반 리스트 응답
 */
export const apiListResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  baseApiResponseSchema.extend({
    items: z.array(itemSchema),
    pagination: paginationMetaSchema,
    meta: z.record(z.string(), z.any()).optional(),
  });

/**
 * Zod 스키마: 커서 기반 페이지네이션 메타데이터
 */
export const cursorPaginationMetaSchema = <C extends z.ZodTypeAny = z.ZodString>(
  cursorSchema: C = z.string() as any
) =>
  z.object({
    nextCursor: cursorSchema.nullable().optional(),
    prevCursor: cursorSchema.nullable().optional(),
    hasMore: z.boolean(),
  });

/**
 * Zod 스키마: 커서 기반 리스트 응답
 */
export const apiCursorResponseSchema = <
  T extends z.ZodTypeAny,
  C extends z.ZodTypeAny = z.ZodString,
>(
  itemSchema: T,
  cursorSchema: C = z.string() as any
) =>
  baseApiResponseSchema.extend({
    items: z.array(itemSchema),
    pagination: cursorPaginationMetaSchema(cursorSchema),
    meta: z.record(z.string(), z.any()).optional(),
  });

/**
 * API 응답 유효성 검사
 * 
 * @param response 검사할 응답 객체
 * @param schema Zod 스키마
 * @returns 유효성 검사를 통과한 응답 객체
 * @throws Error 유효성 검사 실패 시 에러 발생
 */
export function validateApiResponse<T>(
  response: unknown,
  schema: z.ZodType<T>
): T {
  try {
    return schema.parse(response);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`API 응답 유효성 검사 실패: ${error.message}`);
    }
    throw error;
  }
}
