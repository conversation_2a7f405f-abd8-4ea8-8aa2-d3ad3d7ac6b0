/**
 * 페이지네이션 관련 타입 정의
 * 
 * 페이지 기반 및 커서 기반 페이지네이션에 사용되는 타입을 정의합니다.
 */

/**
 * 페이지 기반 페이지네이션 파라미터
 */
export interface IPaginationParams {
  /** 페이지 번호 */
  page?: number;
  /** 페이지 크기 */
  size?: number;
  /** 정렬 기준 */
  sort?: string;
  /** 정렬 방향 */
  order?: 'asc' | 'desc';
}

/**
 * 페이지네이션 메타데이터
 */
export interface IPaginationMeta {
  /** 전체 항목 수 */
  total: number;
  /** 현재 페이지 번호 */
  page: number;
  /** 페이지당 항목 수 */
  size: number;
  /** 전체 페이지 수 */
  totalPages: number;
}

/**
 * 커서 기반 페이지네이션 파라미터
 */
export interface ICursorPaginationParams<C = string> {
  /** 커서 */
  cursor?: C;
  /** 항목 수 제한 */
  limit?: number;
  /** 방향 */
  direction?: 'next' | 'prev';
}

/**
 * 커서 기반 페이지네이션 메타데이터
 */
export interface ICursorPaginationMeta<C = string> {
  /** 다음 페이지 커서 */
  nextCursor?: C | null;
  /** 이전 페이지 커서 */
  prevCursor?: C | null;
  /** 다음 페이지 존재 여부 */
  hasMore: boolean;
}

/**
 * 무한 스크롤 상태
 */
export interface IInfiniteScrollState<T, C = string> {
  /** 항목 배열 */
  items: T[];
  /** 다음 커서 */
  nextCursor?: C | null;
  /** 로딩 상태 */
  isLoading: boolean;
  /** 에러 상태 */
  isError: boolean;
  /** 에러 */
  error?: Error;
  /** 더 많은 항목 존재 여부 */
  hasMore: boolean;
}

/**
 * 다음 페이지 파라미터 가져오기 함수 타입
 */
export type TGetNextPageParamFunction<T, C = string> = (
  lastPage: T,
  pages: T[]
) => C | null | undefined;
