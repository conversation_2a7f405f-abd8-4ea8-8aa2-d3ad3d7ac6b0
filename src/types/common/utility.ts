/**
 * 유틸리티 타입 정의
 * 
 * 프로젝트 전반에서 사용되는 공통 유틸리티 타입을 정의합니다.
 */

/**
 * 객체의 모든 속성을 선택적(optional)으로 만드는 타입
 */
export type TPartial<T> = {
  [P in keyof T]?: T[P];
};

/**
 * 객체의 모든 속성을 필수(required)로 만드는 타입
 */
export type TRequired<T> = {
  [P in keyof T]-?: T[P];
};

/**
 * 객체에서 지정된 키만 선택하는 타입
 */
export type TPick<T, K extends keyof T> = {
  [P in K]: T[P];
};

/**
 * 객체에서 지정된 키를 제외한 나머지를 선택하는 타입
 */
export type TOmit<T, K extends keyof T> = {
  [P in Exclude<keyof T, K>]: T[P];
};

/**
 * 객체의 모든 속성을 읽기 전용으로 만드는 타입
 */
export type TReadonly<T> = {
  readonly [P in keyof T]: T[P];
};

/**
 * 비동기 상태를 나타내는 인터페이스
 */
export interface IAsyncState<T> {
  /** 데이터 */
  data: T | null;
  /** 로딩 상태 */
  isLoading: boolean;
  /** 에러 */
  error: Error | null;
  /** 데이터 설정 함수 */
  setData: (data: T | null) => void;
  /** 로딩 상태 설정 함수 */
  setLoading: (isLoading: boolean) => void;
  /** 에러 설정 함수 */
  setError: (error: Error | null) => void;
}

/**
 * 리셋 기능을 가진 인터페이스
 */
export interface IWithReset {
  /** 상태 리셋 함수 */
  reset: () => void;
}

/**
 * 초기화 기능을 가진 인터페이스
 */
export interface IWithInitialize<T> {
  /** 상태 초기화 함수 */
  initialize: (data: T) => void;
}

/**
 * 업데이트 기능을 가진 인터페이스
 */
export interface IWithUpdate<T> {
  /** 상태 업데이트 함수 */
  update: (data: Partial<T>) => void;
}

/**
 * 로딩 상태를 가진 인터페이스
 */
export interface IWithLoading {
  /** 로딩 상태 */
  isLoading: boolean;
  /** 로딩 상태 설정 함수 */
  setLoading: (isLoading: boolean) => void;
}

/**
 * 에러 상태를 가진 인터페이스
 */
export interface IWithError {
  /** 에러 */
  error: Error | null;
  /** 에러 설정 함수 */
  setError: (error: Error | null) => void;
}

/**
 * 업데이트 결과 타입
 */
export type TUpdateResult = {
  /** 성공 여부 */
  success: boolean;
  /** 메시지 */
  message?: string;
};
