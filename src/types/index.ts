import { Post, PostComment, User } from '@prisma/client';

// getUserPosts에서 반환하는 데이터 타입을 정의합니다.

// 1. 포함될 user 필드의 타입 정의 (선택된 필드만)
type PostAuthor = Pick<User, 'id' | 'name' | 'displayName' | 'image'>;

// 2. 포함될 _count 필드의 타입 정의
type PostCounts = {
  _count: {
    postComments: number;
    postLikes: number;
  };
};

// 3. 기본 Post 타입과 위에서 정의한 타입들을 결합 (Intersection Type)
// export type PostWithAuthorAndCounts = Post & {
//   user: PostAuthor;
// } & PostCounts;

// 임시로 기본 Post 타입으로 변경하여 테스트
export type PostWithAuthorAndCounts = Post;

// 기존 Prisma.PostGetPayload 방식 주석 처리
// export type PostWithAuthorAndCounts = Prisma.PostGetPayload<{
//   include: {
//     user: {
//       select: {
//         id: true;
//         name: true;
//         displayName: true;
//         image: true;
//       };
//     };
//     _count: {
//       select: {
//         postComments: true;
//         postLikes: true;
//       };
//     };
//   };
// }>;

// getUserComments에서 반환하는 데이터 타입을 정의합니다.

// 1. 포함될 post 필드의 타입 정의 (선택된 필드만)
type CommentPostInfo = Pick<Post, 'id'>;

// 2. 기본 PostComment 타입과 위에서 정의한 타입을 결합
export type CommentWithPostInfo = PostComment & {
  post: CommentPostInfo;
};

// 기존 Prisma.PostCommentGetPayload 방식 주석 처리
// export type CommentWithPostInfo = Prisma.PostCommentGetPayload<{
//   include: {
//     post: {
//       select: {
//         id: true;
//       };
//     };
//   };
// }>;
