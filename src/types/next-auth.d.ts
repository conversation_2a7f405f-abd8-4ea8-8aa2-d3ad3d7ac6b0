import 'next-auth';

export type Role = 'USER' | 'ADMIN';

declare module 'next-auth' {
  interface User {
    id?: string;
    name?: string | null;
    displayName?: string | null;
    email?: string | null;
    image?: string | null;
    bio?: string | null;
    isAdmin?: boolean;
    isTester?: boolean;
  }

  interface Session {
    user: User;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id?: string;
    name?: string | null;
    displayName?: string | null;
    image?: string | null;
    bio?: string | null;
    isAdmin?: boolean;
    isTester?: boolean;
  }
}
