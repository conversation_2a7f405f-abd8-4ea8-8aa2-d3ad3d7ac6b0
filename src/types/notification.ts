import { Notification, NotificationType } from '@/generated/prisma';

export type { Notification, NotificationType };

export interface NotificationWithSender extends Notification {
  sender?: {
    id: string;
    name: string;
    image?: string | null;
    displayName?: string | null;
  } | null;
}

export interface CreateNotificationParams {
  userId: string;
  senderId?: string;
  type: NotificationType;
  title?: string;
  content: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
  link?: string;
}

export interface NotificationSettings {
  userId: string;
  enableCommentNotifications: boolean;
  enableReplyNotifications: boolean;
  enableLikeNotifications: boolean;
  enableAnnouncementNotifications: boolean;
  enableMentionNotifications: boolean;
  enableSystemNotifications: boolean;
  enableEmailNotifications: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpdateNotificationSettingsParams {
  enableCommentNotifications?: boolean;
  enableReplyNotifications?: boolean;
  enableLikeNotifications?: boolean;
  enableAnnouncementNotifications?: boolean;
  enableMentionNotifications?: boolean;
  enableSystemNotifications?: boolean;
  enableEmailNotifications?: boolean;
}
