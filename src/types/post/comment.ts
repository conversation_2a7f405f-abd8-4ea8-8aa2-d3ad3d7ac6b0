/**
 * 댓글 관련 타입 정의
 * 
 * 게시물 댓글 관련 타입을 정의합니다.
 */

import { Post, PostComment, User } from '@prisma/client';

/**
 * 댓글 작성자 타입
 */
export type TCommentAuthor = Pick<User, 'id' | 'name' | 'displayName' | 'image'>;

/**
 * 댓글 게시물 정보 타입
 */
export type TCommentPostInfo = Pick<Post, 'id' | 'title'>;

/**
 * 댓글 카운트 타입
 */
export type TCommentCounts = {
  /** 좋아요 수 */
  _count: {
    /** 댓글 좋아요 수 */
    commentLikes: number;
  };
};

/**
 * 작성자 및 카운트 정보가 포함된 댓글 타입
 */
export type TCommentWithAuthorAndCounts = PostComment & {
  /** 작성자 정보 */
  user: TCommentAuthor;
  /** 게시물 정보 */
  post: TCommentPostInfo;
} & TCommentCounts;

/**
 * 댓글 생성 입력 타입
 */
export interface ICreateCommentInput {
  /** 게시물 ID */
  postId: string;
  /** 댓글 내용 */
  content: string;
  /** 부모 댓글 ID (대댓글인 경우) */
  parentId?: string | null;
}

/**
 * 댓글 수정 입력 타입
 */
export interface IUpdateCommentInput {
  /** 댓글 ID */
  id: string;
  /** 댓글 내용 */
  content: string;
}

/**
 * 댓글 삭제 입력 타입
 */
export interface IDeleteCommentInput {
  /** 댓글 ID */
  id: string;
}

/**
 * 댓글 좋아요 입력 타입
 */
export interface ILikeCommentInput {
  /** 댓글 ID */
  commentId: string;
}

/**
 * 댓글 타입 가드 함수
 * 
 * @param comment 확인할 객체
 * @returns 댓글 객체인지 여부
 */
export function isComment(comment: any): comment is PostComment {
  return (
    comment &&
    typeof comment === 'object' &&
    typeof comment.id === 'string' &&
    typeof comment.content === 'string' &&
    typeof comment.postId === 'string' &&
    typeof comment.userId === 'string'
  );
}

/**
 * 작성자 및 카운트 정보가 포함된 댓글 타입 가드 함수
 * 
 * @param comment 확인할 객체
 * @returns 작성자 및 카운트 정보가 포함된 댓글 객체인지 여부
 */
export function isCommentWithAuthorAndCounts(
  comment: any
): comment is TCommentWithAuthorAndCounts {
  return (
    isComment(comment) &&
    comment.user &&
    typeof comment.user === 'object' &&
    typeof comment.user.id === 'string' &&
    comment._count &&
    typeof comment._count === 'object' &&
    typeof comment._count.commentLikes === 'number'
  );
}
