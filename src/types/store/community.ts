/**
 * 커뮤니티 상태 관련 타입 정의
 */

export interface DraftPost {
  id?: string;
  title?: string;
  content?: string;
  contentHtml?: string;
  tags?: string[];
}

export interface CommunityFilters {
  sort?: 'latest' | 'popular' | 'trending';
  category?: string;
  tag?: string;
  search?: string;
}

export interface CommunityState {
  needsPostListRefresh: boolean;
  filters: CommunityFilters;
  recentlyViewedPosts: string[];
  draftPost: DraftPost | null;
  selectedPostId: string | null;
}
