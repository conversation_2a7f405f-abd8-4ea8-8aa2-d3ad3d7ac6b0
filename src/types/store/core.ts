/**
 * 코어 스토어 타입 정의
 * 
 * 모든 스토어에서 공통으로 사용하는 타입을 정의합니다.
 */

import { StateCreator } from 'zustand';
import { IAsyncState, IWithReset } from '../common/utility';

/**
 * 스토어 슬라이스 타입 정의
 * 여러 스토어를 조합할 때 사용하는 타입
 */
export type TStoreSlice<T> = StateCreator<T, [], [], T>;

/**
 * 페이지네이션 상태 인터페이스
 */
export interface IPaginationState {
  /** 현재 페이지 */
  page: number;
  /** 페이지당 항목 수 */
  limit: number;
  /** 전체 항목 수 */
  total: number;
  /** 더 많은 항목 존재 여부 */
  hasMore: boolean;
}

/**
 * 필터 상태 기본 인터페이스
 */
export interface IBaseFilterState {
  /** 검색어 */
  searchQuery: string | null;
  /** 정렬 기준 */
  sortBy: string;
}

/**
 * 스토어 초기화 기능을 위한 타입
 */
export interface IWithInitialize<T> {
  /** 초기화 함수 */
  initialize: (data: T) => void;
}

/**
 * 스토어 업데이트 기능을 위한 타입
 */
export interface IWithUpdate<T> {
  /** 업데이트 함수 */
  update: (data: Partial<T>) => void;
}

/**
 * 스토어 로딩 상태 관리를 위한 타입
 */
export interface IWithLoading {
  /** 로딩 상태 */
  isLoading: boolean;
  /** 로딩 상태 설정 함수 */
  setLoading: (isLoading: boolean) => void;
}

/**
 * 스토어 에러 상태 관리를 위한 타입
 */
export interface IWithError {
  /** 에러 */
  error: Error | null;
  /** 에러 설정 함수 */
  setError: (error: Error | null) => void;
}

/**
 * 비동기 데이터 상태 인터페이스
 */
export type TAsyncDataState<T> = IAsyncState<T> & IWithReset;
