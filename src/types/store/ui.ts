/**
 * UI 상태 관련 타입 정의
 */

export type Theme = 'light' | 'dark' | 'system';

export interface Toast {
  id: string;
  title: string;
  description?: string;
  type?: 'default' | 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export type ModalType = 'login' | 'signup' | 'confirm' | 'alert' | 'custom';

export interface ModalState {
  isOpen: boolean;
  type: ModalType | null;
  props?: Record<string, any>;
}

export interface UIState {
  theme: Theme;
  modal: ModalState;
  toasts: Toast[];
  isSidebarOpen: boolean;
  isLoading: boolean;
}
