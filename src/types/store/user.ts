/**
 * 사용자 상태 관련 타입 정의
 */

import { Session } from 'next-auth';

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'system';
  notifications?: boolean;
  emailUpdates?: boolean;
  language?: string;
}

export interface UserActivity {
  lastActive: Date;
  visitCount: number;
}

export interface UserState {
  session: Session | null;
  isAuthenticated: boolean;
  preferences: UserPreferences;
  activity: UserActivity;
  isLoading: boolean;
  error: Error | null;
}
