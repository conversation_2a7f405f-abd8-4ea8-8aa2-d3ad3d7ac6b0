/**
 * 인증 관련 타입 정의
 * 
 * 사용자 인증 및 권한 관련 타입을 정의합니다.
 */

import { User } from '@prisma/client';
import { Session } from 'next-auth';

/**
 * 사용자 역할 타입
 */
export type TRole = 'USER' | 'ADMIN';

/**
 * 인증된 사용자 세션 타입
 */
export interface IAuthSession extends Session {
  /** 사용자 정보 */
  user: {
    /** 사용자 ID */
    id?: string;
    /** 사용자 이름 */
    name?: string | null;
    /** 표시 이름 */
    displayName?: string | null;
    /** 이메일 */
    email?: string | null;
    /** 프로필 이미지 */
    image?: string | null;
    /** 자기소개 */
    bio?: string | null;
    /** 관리자 여부 */
    isAdmin?: boolean;
    /** 테스터 여부 */
    isTester?: boolean;
  };
}

/**
 * 사용자 인증 상태 타입
 */
export interface IAuthState {
  /** 인증 여부 */
  isAuthenticated: boolean;
  /** 로딩 상태 */
  isLoading: boolean;
  /** 사용자 세션 */
  session: IAuthSession | null;
  /** 에러 */
  error: Error | null;
}

/**
 * 사용자 프로필 타입
 */
export type TUserProfile = Pick<
  User,
  'id' | 'name' | 'displayName' | 'email' | 'image' | 'bio'
> & {
  /** 관리자 여부 */
  isAdmin: boolean;
  /** 테스터 여부 */
  isTester: boolean;
};

/**
 * 사용자 설정 인터페이스
 */
export interface IUserPreferences {
  /** 알림 활성화 여부 */
  notificationsEnabled: boolean;
  /** 이메일 알림 활성화 여부 */
  emailNotificationsEnabled: boolean;
  /** 기본 국가 */
  defaultCountry: string | null;
  /** 언어 */
  language: string;
}

/**
 * 사용자 활동 정보 인터페이스
 */
export interface IUserActivity {
  /** 마지막 활동 시간 */
  lastActivity: Date | null;
  /** 마지막 로그인 시간 */
  lastLogin: Date | null;
  /** 방문 횟수 */
  visitCount: number;
}

/**
 * 사용자 타입 가드 함수
 * 
 * @param user 확인할 객체
 * @returns 사용자 객체인지 여부
 */
export function isUser(user: any): user is TUserProfile {
  return (
    user &&
    typeof user === 'object' &&
    typeof user.id === 'string' &&
    typeof user.name === 'string'
  );
}

/**
 * 관리자 타입 가드 함수
 * 
 * @param user 확인할 사용자 객체
 * @returns 관리자인지 여부
 */
export function isAdmin(user: TUserProfile | null): boolean {
  return Boolean(user && user.isAdmin);
}

/**
 * 테스터 타입 가드 함수
 * 
 * @param user 확인할 사용자 객체
 * @returns 테스터인지 여부
 */
export function isTester(user: TUserProfile | null): boolean {
  return Boolean(user && user.isTester);
}
