/**
 * 성공 응답 생성
 */
export function createSuccessResponse<T>(data?: T): ActionResponse<T> {
  return {
    success: true,
    data,
  };
}

/**
 * 에러 응답 생성
 */
export function createErrorResponse(
  message: string,
  code: ActionErrorCode = ActionErrorCode.INTERNAL_SERVER_ERROR,
  field?: string
): ActionResponse {
  return {
    success: false,
    error: {
      message,
      code,
      field,
    },
  };
}

/**
 * 유효성 검사 에러 응답 생성
 */
export function createValidationErrorResponse(
  message: string,
  field?: string
): ActionResponse {
  return createErrorResponse(message, ActionErrorCode.VALIDATION_ERROR, field);
}

/**
 * 인증 에러 응답 생성
 */
export function createUnauthorizedErrorResponse(
  message: string = '로그인이 필요합니다.'
): ActionResponse {
  return createErrorResponse(message, ActionErrorCode.UNAUTHORIZED);
}

/**
 * 권한 에러 응답 생성
 */
export function createForbiddenErrorResponse(
  message: string = '권한이 없습니다.'
): ActionResponse {
  return createErrorResponse(message, ActionErrorCode.FORBIDDEN);
}

/**
 * 리소스 없음 에러 응답 생성
 */
export function createNotFoundErrorResponse(
  message: string = '리소스를 찾을 수 없습니다.'
): ActionResponse {
  return createErrorResponse(message, ActionErrorCode.NOT_FOUND);
}

/**
 * 충돌 에러 응답 생성
 */
export function createConflictErrorResponse(
  message: string,
  field?: string
): ActionResponse {
  return createErrorResponse(message, ActionErrorCode.CONFLICT, field);
}

/**
 * Server Action 응답 타입
 */
export type ActionResponse<T = unknown> = {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    field?: string;
  };
};

/**
 * Server Action 에러 코드
 */
export enum ActionErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
}
